import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/providers/ai_function_provider.dart';

/// 笔记智能问答对话框
class NoteQADialog extends StatefulWidget {
  final String noteContent;
  final AIFunctionProvider aiService;

  const NoteQADialog({
    Key? key,
    required this.noteContent,
    required this.aiService,
  }) : super(key: key);

  @override
  State<NoteQADialog> createState() => _NoteQADialogState();
}

class _NoteQADialogState extends State<NoteQADialog> {
  final TextEditingController _questionController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // 添加系统消息，说明功能
    _messages.add(
      ChatMessage(
        text: '您可以根据笔记内容提问，AI将尝试回答您的问题。',
        isUser: false,
        isSystemMessage: true,
      ),
    );
  }

  @override
  void dispose() {
    _questionController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // 发送问题
  void _sendQuestion() async {
    final question = _questionController.text.trim();
    if (question.isEmpty) return;

    // 添加用户问题到消息列表
    setState(() {
      _messages.add(ChatMessage(text: question, isUser: true));
      _isLoading = true;
      _questionController.clear();
    });

    // 滚动到底部
    _scrollToBottom();

    try {
      // 调用AI服务回答问题
      final result = await widget.aiService.askQuestion(
        question: question,
        context: widget.noteContent, // 传递笔记内容作为上下文
      );

      // 检查组件是否已经被卸载
      if (!mounted) return;

      if (result['success'] == true && result['data'] != null) {
        // 添加AI回答到消息列表
        setState(() {
          _messages.add(ChatMessage(
            text: result['data']['answer'] ?? '抱歉，我无法回答这个问题。',
            isUser: false,
          ));
          _isLoading = false;
        });
      } else {
        // 添加错误消息
        setState(() {
          _messages.add(ChatMessage(
            text: result['error']?['message'] ?? '回答问题时出错',
            isUser: false,
            isError: true,
          ));
          _isLoading = false;
        });
      }
    } catch (e) {
      // 检查组件是否已经被卸载
      if (!mounted) return;

      // 添加错误消息
      setState(() {
        _messages.add(ChatMessage(
          text: '回答问题时出错: $e',
          isUser: false,
          isError: true,
        ));
        _isLoading = false;
      });
    }

    // 检查组件是否已经被卸载
    if (!mounted) return;

    // 滚动到底部
    _scrollToBottom();
  }

  // 滚动到底部
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: Theme.of(context).cardColor,
      child: Container(
        width: double.maxFinite,
        constraints: BoxConstraints(
          maxWidth: 600,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 对话框标题
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  Icon(
                    Icons.question_answer,
                    color: AppTheme.primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '笔记智能问答',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).textTheme.titleLarge?.color,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).iconTheme.color,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            const Divider(height: 1),

            // 消息列表
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount: _messages.length + (_isLoading ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index < _messages.length) {
                    return _buildMessageItem(_messages[index]);
                  } else {
                    // 显示加载指示器
                    return _buildLoadingIndicator();
                  }
                },
              ),
            ),

            // 输入区域
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _questionController,
                      decoration: InputDecoration(
                        hintText: '输入您的问题...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(24),
                          borderSide: BorderSide(
                            color: Theme.of(context).dividerColor,
                          ),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      maxLines: 1,
                      textInputAction: TextInputAction.send,
                      onSubmitted: (_) => _sendQuestion(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  FloatingActionButton(
                    onPressed: _isLoading ? null : _sendQuestion,
                    backgroundColor: _isLoading
                        ? Colors.grey
                        : AppTheme.primaryColor,
                    mini: true,
                    child: const Icon(Icons.send),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建消息项
  Widget _buildMessageItem(ChatMessage message) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) _buildAvatar(message.isUser),
          const SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: message.isSystemMessage
                    ? Theme.of(context).brightness == Brightness.dark
                        ? Colors.grey.shade800
                        : Colors.grey.shade100
                    : message.isUser
                        ? AppTheme.primaryColor.withOpacity(
                            Theme.of(context).brightness == Brightness.dark ? 0.2 : 0.1
                          )
                        : message.isError
                            ? Theme.of(context).brightness == Brightness.dark
                                ? Colors.red.shade900.withOpacity(0.2)
                                : Colors.red.shade50
                            : Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: message.isSystemMessage
                      ? Theme.of(context).brightness == Brightness.dark
                          ? Colors.grey.shade700
                          : Colors.grey.shade300
                      : message.isUser
                          ? AppTheme.primaryColor.withOpacity(0.3)
                          : message.isError
                              ? Theme.of(context).brightness == Brightness.dark
                                  ? Colors.red.shade700
                                  : Colors.red.shade200
                              : Theme.of(context).dividerColor,
                ),
              ),
              child: Text(
                message.text,
                style: TextStyle(
                  color: message.isError
                      ? Theme.of(context).brightness == Brightness.dark
                          ? Colors.red.shade300
                          : Colors.red.shade700
                      : Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (message.isUser) _buildAvatar(message.isUser),
        ],
      ),
    );
  }

  // 构建头像
  Widget _buildAvatar(bool isUser) {
    return CircleAvatar(
      radius: 16,
      backgroundColor: isUser ? AppTheme.primaryColor : Colors.grey.shade200,
      child: Icon(
        isUser ? Icons.person : Icons.smart_toy,
        size: 16,
        color: isUser ? Colors.white : AppTheme.primaryColor,
      ),
    );
  }

  // 构建加载指示器
  Widget _buildLoadingIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAvatar(false),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Theme.of(context).dividerColor),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '思考中...',
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 聊天消息模型
class ChatMessage {
  final String text;
  final bool isUser;
  final bool isError;
  final bool isSystemMessage;

  ChatMessage({
    required this.text,
    required this.isUser,
    this.isError = false,
    this.isSystemMessage = false,
  });
}
