@echo off
echo ========================================
echo 智云笔记应用测试运行器
echo ========================================
echo.

:menu
echo 请选择要运行的测试：
echo.
echo 1. 运行所有测试
echo 2. 运行用户认证模块测试
echo 3. 运行单元测试
echo 4. 运行组件测试
echo 5. 运行集成测试
echo 6. 生成测试覆盖率报告
echo 7. 运行测试并监听文件变化
echo 8. 清理测试缓存
echo 9. 退出
echo.
set /p choice=请输入选项 (1-9): 

if "%choice%"=="1" goto run_all_tests
if "%choice%"=="2" goto run_auth_tests
if "%choice%"=="3" goto run_unit_tests
if "%choice%"=="4" goto run_widget_tests
if "%choice%"=="5" goto run_integration_tests
if "%choice%"=="6" goto run_coverage
if "%choice%"=="7" goto run_watch
if "%choice%"=="8" goto clean_cache
if "%choice%"=="9" goto exit
echo 无效选项，请重新选择
goto menu

:run_all_tests
echo.
echo 🚀 运行所有测试...
echo ========================================
flutter test test/test_suites/all_tests.dart
echo.
echo ✅ 所有测试完成
goto menu

:run_auth_tests
echo.
echo 🔐 运行用户认证模块测试...
echo ========================================
flutter test test/test_suites/auth_test_suite.dart
echo.
echo ✅ 认证模块测试完成
goto menu

:run_unit_tests
echo.
echo 📦 运行单元测试...
echo ========================================
flutter test test/unit/
echo.
echo ✅ 单元测试完成
goto menu

:run_widget_tests
echo.
echo 🎨 运行组件测试...
echo ========================================
flutter test test/widget/
echo.
echo ✅ 组件测试完成
goto menu

:run_integration_tests
echo.
echo 🔄 运行集成测试...
echo ========================================
flutter test test/integration/
echo.
echo ✅ 集成测试完成
goto menu

:run_coverage
echo.
echo 📊 生成测试覆盖率报告...
echo ========================================
flutter test --coverage test/test_suites/all_tests.dart
echo.
echo 📈 覆盖率报告已生成到 coverage/lcov.info
echo 💡 提示：可以使用 genhtml 工具生成HTML报告
echo    genhtml coverage/lcov.info -o coverage/html
echo.
echo ✅ 覆盖率报告生成完成
goto menu

:run_watch
echo.
echo 👀 运行测试并监听文件变化...
echo ========================================
echo 💡 提示：修改文件时测试会自动重新运行
echo 按 Ctrl+C 停止监听
flutter test --watch test/test_suites/all_tests.dart
goto menu

:clean_cache
echo.
echo 🧹 清理测试缓存...
echo ========================================
flutter clean
flutter pub get
echo.
echo ✅ 缓存清理完成
goto menu

:exit
echo.
echo 👋 感谢使用智云笔记测试运行器！
echo.
pause
exit

:error
echo.
echo ❌ 发生错误，请检查：
echo 1. 确保在项目根目录运行此脚本
echo 2. 确保已安装 Flutter SDK
echo 3. 确保已运行 flutter pub get
echo 4. 检查网络连接
echo.
pause
goto menu
