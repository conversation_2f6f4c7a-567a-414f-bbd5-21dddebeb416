import express from 'express';
import { body } from 'express-validator';
import * as notesController from '../controllers/notes.controller';
import { authenticate } from '../middlewares/auth.middleware';
import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';

// 扩展 Request 类型以包含文件字段
declare global {
  namespace Express {
    interface Request {
      file?: Express.Multer.File;
    }
  }
}

const router = express.Router();

// 配置图片上传的存储
const imageUploadsDir = path.join(__dirname, '../../uploads/notes');

// 确保上传目录存在
if (!fs.existsSync(imageUploadsDir)) {
  fs.mkdirSync(imageUploadsDir, { recursive: true });
}

// 配置 multer 存储
const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    cb(null, imageUploadsDir);
  },
  filename: (_req, file, cb) => {
    const ext = path.extname(file.originalname);
    cb(null, `${uuidv4()}${ext}`);
  }
});

// 创建 multer 实例
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB 大小限制
  },
  fileFilter: (_req, file, cb) => {
    // 允许的图片类型
    const allowedTypes = /jpeg|jpg|png|gif/;
    const ext = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (ext && mimetype) {
      return cb(null, true);
    } else {
      cb(new Error('仅支持图片文件 (jpeg, jpg, png, gif)'));
    }
  }
});

// 所有笔记路由都需要认证，除了获取共享笔记和上传图片
router.use((req, res, next) => {
  // 获取分享笔记的路由通常不需要认证 (例如 GET 请求查看)
  // 图片上传操作通常需要用户身份认证，或基于特定分享令牌的授权
  if (req.path.startsWith('/shared/') && req.method === 'GET') { // 示例：仅对查看分享笔记的GET请求跳过认证
    next();
  } else {
    authenticate(req, res, next);
  }
});

/**
 * 上传笔记图片
 * POST /api/notes/upload-image
 */
router.post('/upload-image', upload.single('image'), notesController.uploadImage);

/**
 * 获取笔记列表
 * GET /api/notes
 */
router.get('/', notesController.getNotes);

/**
 * 创建笔记
 * POST /api/notes
 */
router.post(
  '/',
  [
    // 验证标题
    body('title')
      .notEmpty()
      .withMessage('标题不能为空')
      .isLength({ max: 100 })
      .withMessage('标题最多100个字符'),

    // 内容类型验证（如果提供）
    body('contentType')
      .optional()
      .isIn(['plain-text', 'rich-text', 'markdown'])
      .withMessage('内容类型必须是 plain-text, rich-text 或 markdown'),

    // 标签验证（如果提供）
    body('tags')
      .optional()
      .isArray()
      .withMessage('标签必须是数组'),
  ],
  notesController.createNote
);

/**
 * 获取笔记统计信息
 * GET /api/notes/stats
 */
router.get('/stats', notesController.getNoteStats);

/**
 * 笔记导出
 * GET /api/notes/export
 */
router.get('/export', notesController.exportNotes);

/**
 * 笔记导入
 * POST /api/notes/import
 */
router.post(
  '/import',
  [
    body('notes')
      .isArray()
      .withMessage('必须提供笔记数组'),
  ],
  notesController.importNotes
);

/**
 * 获取上次同步后的变更
 * GET /api/notes/sync
 */
router.get('/sync', notesController.getChanges);

/**
 * 上传本地变更
 * POST /api/notes/sync
 */
router.post('/sync', notesController.pushChanges);

/**
 * 批量操作笔记(删除/归档/收藏)
 * POST /api/notes/batch
 */
router.post(
  '/batch',
  [
    body('noteIds')
      .isArray()
      .withMessage('笔记ID必须是数组')
      .notEmpty()
      .withMessage('笔记ID数组不能为空'),
    body('operation')
      .isIn(['delete', 'archive', 'unarchive', 'favorite', 'unfavorite'])
      .withMessage('操作类型无效'),
  ],
  notesController.batchOperation
);

/**
 * 获取分享的笔记
 * GET /api/notes/shared/:token
 */
router.get('/shared/:token', notesController.getSharedNote);

/**
 * 更新分享的笔记
 * PUT /api/notes/shared/:token
 */
router.put('/shared/:token', notesController.updateSharedNote);

/**
 * 获取单个笔记
 * GET /api/notes/:id
 */
router.get('/:id', notesController.getNoteById);

/**
 * 更新笔记
 * PUT /api/notes/:id
 */
router.put(
  '/:id',
  [
    // 标题验证（如果提供）
    body('title')
      .optional()
      .notEmpty()
      .withMessage('标题不能为空')
      .isLength({ max: 100 })
      .withMessage('标题最多100个字符'),

    // 内容类型验证（如果提供）
    body('contentType')
      .optional()
      .isIn(['plain-text', 'rich-text', 'markdown'])
      .withMessage('内容类型必须是 plain-text, rich-text 或 markdown'),

    // 标签验证（如果提供）
    body('tags')
      .optional()
      .isArray()
      .withMessage('标签必须是数组'),
  ],
  notesController.updateNote
);

/**
 * 删除笔记
 * DELETE /api/notes/:id
 */
router.delete('/:id', notesController.deleteNote);

/**
 * 切换收藏状态
 * PATCH /api/notes/:id/favorite
 */
router.patch('/:id/favorite', notesController.toggleFavorite);

/**
 * 切换归档状态
 * PATCH /api/notes/:id/archive
 */
router.patch('/:id/archive', notesController.toggleArchive);

/**
 * 创建分享链接
 * POST /api/notes/:id/share
 */
router.post(
  '/:id/share',
  [
    body('expireHours')
      .optional()
      .isNumeric()
      .withMessage('过期小时数必须是数字'),
    body('isPublic')
      .optional()
      .isBoolean()
      .withMessage('isPublic必须是布尔值'),
  ],
  notesController.createShareLink
);

/**
 * 取消分享
 * DELETE /api/notes/:id/share
 */
router.delete('/:id/share', notesController.cancelShare);

/**
 * 获取笔记历史版本
 * GET /api/notes/:id/history
 */
router.get('/:id/history', notesController.getNoteHistory);

/**
 * 恢复历史版本
 * POST /api/notes/:id/restore/:version
 */
router.post('/:id/restore/:version', notesController.restoreNoteVersion);

export default router;