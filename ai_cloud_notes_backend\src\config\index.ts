import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

/**
 * JWT配置接口
 */
interface JWTConfig {
  secret: string;
  expiresIn: string;
}

/**
 * 邮件配置接口
 */
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  }
}

/**
 * Redis配置接口
 */
interface RedisConfig {
  uri: string;
  prefix: string;
  expire: number;
}

/**
 * 文件上传配置接口
 */
interface FileUploadConfig {
  maxSize: number;
  allowedTypes: string[];
}

/**
 * 安全配置接口
 */
interface SecurityConfig {
  corsOrigin: string;
  rateLimitWindow: number;
  rateLimitMax: number;
}

/**
 * AI服务配置接口
 */
interface AIConfig {
  serviceUrl: string;
  serviceKey: string;
}

/**
 * 应用配置
 */
export const config = {
  // 服务器配置
  server: {
    port: process.env.PORT || 8081, // 修改默认端口为8081
    nodeEnv: process.env.NODE_ENV || 'development',
  },

  // 数据库配置
  database: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/ai_cloud_notes',
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'default_jwt_secret',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  } as JWTConfig,

  // 邮件配置
  email: {
    host: process.env.EMAIL_HOST || 'smtp.qq.com',
    port: parseInt(process.env.EMAIL_PORT || '465'),
    secure: true,
    auth: {
      user: process.env.EMAIL_USERNAME || '',
      pass: process.env.EMAIL_PASSWORD || '',
    }
  } as EmailConfig,

  // Redis配置
  redis: {
    uri: process.env.REDIS_URI || 'redis://localhost:6379',
    prefix: process.env.REDIS_PREFIX || 'ai_cloud_notes:',
    expire: parseInt(process.env.REDIS_EXPIRE || '300'),
  } as RedisConfig,

  // 前端配置
  frontend: {
    url: process.env.FRONTEND_URL || 'http://localhost:3000',
  },

  // 日志配置
  logger: {
    level: process.env.LOG_LEVEL || 'info',
  },

  // 文件上传配置
  fileUpload: {
    maxSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 默认5MB
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/gif,application/pdf').split(','),
  } as FileUploadConfig,

  // 安全配置
  security: {
    corsOrigin: process.env.CORS_ORIGIN || '*',
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '15'),
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100'),
  } as SecurityConfig,

  // AI功能配置
  ai: {
    serviceUrl: process.env.AI_SERVICE_URL || 'http://localhost:5000',
    serviceKey: process.env.AI_SERVICE_KEY || '',
  } as AIConfig,
};