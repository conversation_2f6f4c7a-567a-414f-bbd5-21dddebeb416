/// 验证工具类
/// 提供各种输入验证方法
class Validators {
  /// 验证邮箱格式
  static bool isValidEmail(String email) {
    // 邮箱正则表达式
    final RegExp emailRegExp = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegExp.hasMatch(email);
  }

  /// 验证用户名格式
  /// 只允许字母、数字和下划线
  static bool isValidUsername(String username) {
    final RegExp usernameRegExp = RegExp(r'^[a-zA-Z0-9_]+$');
    return usernameRegExp.hasMatch(username);
  }

  /// 验证密码强度
  /// 至少包含一个大写字母、一个小写字母、一个数字和一个特殊字符
  static bool isStrongPassword(String password) {
    // 包含至少一个大写字母
    final RegExp hasUppercase = RegExp(r'[A-Z]');
    // 包含至少一个小写字母
    final RegExp hasLowercase = RegExp(r'[a-z]');
    // 包含至少一个数字
    final RegExp hasDigit = RegExp(r'\d');
    // 包含至少一个特殊字符
    final RegExp hasSpecialChar = RegExp(r'[!@#$%^&*(),.?":{}|<>]');
    
    return hasUppercase.hasMatch(password) &&
        hasLowercase.hasMatch(password) &&
        hasDigit.hasMatch(password) &&
        hasSpecialChar.hasMatch(password);
  }

  /// 验证密码格式
  /// 简化版：密码长度至少为8位
  static bool isValidPassword(String password) {
    return password.length >= 8;
  }

  /// 验证手机号格式
  static bool isValidPhoneNumber(String phoneNumber) {
    // 中国大陆手机号正则
    final RegExp phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegExp.hasMatch(phoneNumber);
  }

  /// 验证URL格式
  static bool isValidUrl(String url) {
    // URL正则表达式
    final RegExp urlRegExp = RegExp(
      r'^(http|https)://[a-zA-Z0-9-\.]+\.[a-zA-Z]{2,}(:[0-9]+)?(\/[a-zA-Z0-9\-\._~:\/\?#\[\]@!$&\(\)\*\+,;=]*)?$',
    );
    return urlRegExp.hasMatch(url);
  }

  /// 验证是否为空或空白字符
  static bool isNotEmpty(String? text) {
    return text != null && text.trim().isNotEmpty;
  }

  /// 验证是否为数字
  static bool isNumeric(String text) {
    final RegExp numericRegExp = RegExp(r'^-?[0-9]+$');
    return numericRegExp.hasMatch(text);
  }

  /// 验证是否为浮点数
  static bool isDouble(String text) {
    final RegExp doubleRegExp = RegExp(r'^-?[0-9]+(\.[0-9]+)?$');
    return doubleRegExp.hasMatch(text);
  }
} 