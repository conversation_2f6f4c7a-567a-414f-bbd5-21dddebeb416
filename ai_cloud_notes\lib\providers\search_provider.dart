import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/services/api_service.dart';

/// 搜索历史项
class SearchHistoryItem {
  final String query;
  final DateTime timestamp;

  SearchHistoryItem({
    required this.query,
    required this.timestamp,
  });

  factory SearchHistoryItem.fromJson(Map<String, dynamic> json) {
    var timestamp = json['timestamp'];
    DateTime parsedTime;
    
    if (timestamp is int) {
      // 处理整数时间戳（毫秒）
      parsedTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    } else if (timestamp is String) {
      // 处理字符串格式的时间
      try {
        parsedTime = DateTime.parse(timestamp);
      } catch (e) {
        parsedTime = DateTime.now();
      }
    } else {
      parsedTime = DateTime.now();
    }
    
    return SearchHistoryItem(
      query: json['query'] ?? '',
      timestamp: parsedTime,
    );
  }
}

/// 热门搜索项
class PopularSearchItem {
  final String query;
  final int count;

  PopularSearchItem({
    required this.query,
    required this.count,
  });

  factory PopularSearchItem.fromJson(Map<String, dynamic> json) {
    var count = json['count'];
    int parsedCount;
    
    if (count is int) {
      parsedCount = count;
    } else if (count is String) {
      parsedCount = int.tryParse(count) ?? 0;
    } else {
      parsedCount = 0;
    }
    
    return PopularSearchItem(
      query: json['query'] ?? '',
      count: parsedCount,
    );
  }
}

/// 搜索提供者
/// 管理笔记搜索相关的状态和操作
class SearchProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  // 搜索结果
  List<Note> _searchResults = [];
  List<Note> get searchResults => _searchResults;
  
  // 搜索历史
  List<SearchHistoryItem> _searchHistory = [];
  List<SearchHistoryItem> get searchHistory => _searchHistory;
  
  // 热门搜索
  List<PopularSearchItem> _popularSearches = [];
  List<PopularSearchItem> get popularSearches => _popularSearches;
  
  // 状态管理
  bool _isSearching = false;
  bool get isSearching => _isSearching;
  
  bool _isLoadingResults = false;
  bool get isLoadingResults => _isLoadingResults;
  
  bool _isLoadingHistory = false;
  bool get isLoadingHistory => _isLoadingHistory;
  
  bool _isLoadingPopular = false;
  bool get isLoadingPopular => _isLoadingPopular;
  
  bool _isLoadingMore = false;
  bool get isLoadingMore => _isLoadingMore;
  
  String? _error;
  String? get error => _error;
  
  // 分页
  int _currentPage = 1;
  final int _pageSize = 20;
  bool _hasMore = true;
  bool get hasMore => _hasMore;
  
  // 搜索配置
  String _searchQuery = '';
  String get searchQuery => _searchQuery;
  
  String _filter = 'all'; // all, title, content, recent
  String get filter => _filter;
  
  DateTime? _startDate;
  DateTime? get startDate => _startDate;
  
  DateTime? _endDate;
  DateTime? get endDate => _endDate;
  
  List<String>? _tags;
  List<String>? get tags => _tags;
  
  bool? _favorite;
  bool? get favorite => _favorite;
  
  /// 初始化搜索提供者
  Future<void> initialize() async {
    await Future.wait([
      getSearchHistory(),
      getPopularSearches(),
    ]);
  }
  
  /// 设置搜索关键词
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }
  
  /// 设置搜索筛选器
  void setFilter(String filter) {
    _filter = filter;
    notifyListeners();
  }
  
  /// 设置日期范围
  void setDateRange(DateTime? start, DateTime? end) {
    _startDate = start;
    _endDate = end;
    notifyListeners();
  }
  
  /// 清除搜索
  void clearSearch() {
    _searchResults = [];
    _searchQuery = '';
    _error = null;
    _currentPage = 1;
    _hasMore = true;
    _isSearching = false;
    _isLoadingResults = false;
    notifyListeners();
  }
  
  /// 执行搜索
  Future<void> searchNotes({bool refresh = true}) async {
    if (_searchQuery.isEmpty) {
      _searchResults = [];
      notifyListeners();
      return;
    }
    
    if (refresh) {
      _currentPage = 1;
      _hasMore = true;
      _searchResults = [];
      _isLoadingResults = true;
      notifyListeners();
    } else {
      if (!_hasMore || _isLoadingMore) return;
      _isLoadingMore = true;
      notifyListeners();
    }
    
    _isSearching = true;
    notifyListeners();
    
    try {
      final result = await _apiService.searchNotes(
        query: _searchQuery,
        page: _currentPage,
        limit: _pageSize,
        searchIn: _filter == 'all' ? 'all' :
                 _filter == 'title' ? 'title' :
                 _filter == 'content' ? 'content' : 'all',
        startDate: _startDate,
        endDate: _endDate,
      );
      
      if (result['success'] == true) {
        final List<dynamic> notesData = result['data']['notes'] ?? [];
        final List<Note> notes = notesData
            .map((note) => Note.fromJson(note))
            .toList();
        
        if (refresh) {
          _searchResults = notes;
        } else {
          _searchResults.addAll(notes);
        }
        
        _hasMore = notes.length >= _pageSize;
        _currentPage++;
        _error = null;
      } else {
        _error = result['error']?['message'] ?? '搜索失败';
        if (!refresh) {
          _hasMore = false;
        }
      }
    } catch (e) {
      _error = '搜索失败：$e';
      if (!refresh) {
        _hasMore = false;
      }
    } finally {
      _isSearching = false;
      _isLoadingResults = false;
      _isLoadingMore = false;
      notifyListeners();
    }
  }
  
  /// 加载更多搜索结果
  Future<void> loadMoreResults() async {
    if (!_hasMore || _isLoadingMore) return;
    
    await searchNotes(refresh: false);
  }
  
  /// 获取搜索历史
  Future<void> getSearchHistory() async {
    _isLoadingHistory = true;
    notifyListeners();
    
    try {
      final result = await _apiService.getSearchHistory();
      
      if (result['success'] == true) {
        final List<dynamic> historyData = result['data']['history'] ?? [];
        _searchHistory = historyData
            .map((item) => SearchHistoryItem.fromJson(item))
            .toList();
        _error = null;
      } else {
        _error = result['error']?['message'] ?? '获取搜索历史失败';
      }
    } catch (e) {
      _error = '获取搜索历史失败：$e';
    } finally {
      _isLoadingHistory = false;
      notifyListeners();
    }
  }
  
  /// 添加搜索历史
  Future<void> addSearchHistory(String query) async {
    if (query.isEmpty) return;

    try {
      final result = await _apiService.addSearchHistory(query);

      if (result['success'] == true) {
        // 更新本地搜索历史
        await getSearchHistory();
      }
    } catch (e) {
      // 静默失败，不影响用户体验
    }
  }
  
  /// 清除搜索历史
  Future<void> clearSearchHistory() async {
    _isLoadingHistory = true;
    notifyListeners();
    
    try {
      final result = await _apiService.clearSearchHistory();
      
      if (result['success'] == true) {
        _searchHistory = [];
      }
    } catch (e) {
      _error = '清除搜索历史失败：$e';
    } finally {
      _isLoadingHistory = false;
      notifyListeners();
    }
  }
  
  /// 获取热门搜索
  Future<void> getPopularSearches() async {
    _isLoadingPopular = true;
    notifyListeners();
    
    try {
      final result = await _apiService.getPopularSearches();
      
      if (result['success'] == true) {
        final List<dynamic> popularData = result['data']['popularSearches'] ?? [];
        _popularSearches = popularData
            .map((item) => PopularSearchItem.fromJson(item))
            .toList();
        _error = null;
      } else {
        _error = result['error']?['message'] ?? '获取热门搜索失败';
      }
    } catch (e) {
      _error = '获取热门搜索失败：$e';
    } finally {
      _isLoadingPopular = false;
      notifyListeners();
    }
  }
} 