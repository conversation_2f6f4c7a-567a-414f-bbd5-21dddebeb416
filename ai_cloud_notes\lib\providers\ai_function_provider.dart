import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/ai_provider.dart';
import 'package:ai_cloud_notes/services/api_service.dart';

/// AI功能服务类
/// 作为前端与后端AI接口的桥梁，并与AI设置联动
class AIFunctionProvider with ChangeNotifier {
  final BuildContext context;
  late ApiService _apiService;
  // late AIProvider _aiProvider; // 移除成员变量

  AIFunctionProvider(this.context) {
    _apiService = Provider.of<ApiService>(context, listen: false);
    // _aiProvider = Provider.of<AIProvider>(context, listen: false); // 不再初始化
  }

  AIProvider _getAIProvider() =>
      Provider.of<AIProvider>(context, listen: false);

  /// 检查AI功能是否启用
  bool get isAIEnabled => _getAIProvider().aiAssistantEnabled;

  /// 检查智能建议是否启用
  bool get isSmartSuggestionsEnabled =>
      _getAIProvider().aiAssistantEnabled &&
      _getAIProvider().smartSuggestionsEnabled;

  /// 检查自动标签是否启用
  bool get isAutoTaggingEnabled =>
      _getAIProvider().aiAssistantEnabled &&
      _getAIProvider().autoTaggingEnabled;

  /// 检查内容摘要是否启用
  bool get isContentSummaryEnabled =>
      _getAIProvider().aiAssistantEnabled &&
      _getAIProvider().contentSummaryEnabled;

  /// 获取选中的模型
  String get selectedModel => _getAIProvider().selectedModel;

  /// 获取建议频率
  String get suggestionFrequency => _getAIProvider().suggestionFrequency;

  /// 生成智能预测
  ///
  /// [content] 当前内容
  /// [cursorPosition] 光标位置
  /// [options] 选项
  Future<Map<String, dynamic>> generateCompletion({
    required String content,
    int? cursorPosition,
    Map<String, dynamic>? options,
  }) async {
    // 检查AI功能是否启用
    if (!isAIEnabled || !isSmartSuggestionsEnabled) {
      return {
        'success': false,
        'error': {'message': 'AI功能未启用，请在设置中启用'}
      };
    }

    try {
      // 添加超时处理，AI预测最多等待10秒
      return await _apiService
          .generateCompletion(
            content: content,
            cursorPosition: cursorPosition,
            options: options ?? {'temperature': 0.3}, // 降低温度以获得更精确的预测
          )
          .timeout(
            const Duration(seconds: 10),
            onTimeout: () => {
              'success': false,
              'error': {'message': 'AI预测超时，请稍后重试'}
            },
          );
    } catch (e) {
      return {
        'success': false,
        'error': {'message': 'AI预测失败: $e'}
      };
    }
  }

  /// 生成内容摘要
  ///
  /// [content] 笔记内容
  /// [options] 选项
  Future<Map<String, dynamic>> generateSummary({
    required String content,
    Map<String, dynamic>? options,
  }) async {
    // 检查AI功能是否启用
    if (!isAIEnabled || !isContentSummaryEnabled) {
      return {
        'success': false,
        'error': {'message': 'AI摘要功能未启用，请在设置中启用'}
      };
    }

    // 调用API服务生成摘要
    return await _apiService.generateSummary(
      content: content,
      options: options ?? {'temperature': 0.3},
    );
  }

  /// 生成标签建议
  ///
  /// [content] 笔记内容
  /// [options] 选项
  Future<Map<String, dynamic>> generateTagSuggestions({
    required String content,
    Map<String, dynamic>? options,
  }) async {
    // 检查AI功能是否启用
    if (!isAIEnabled || !isAutoTaggingEnabled) {
      return {
        'success': false,
        'error': {'message': '自动标签功能未启用，请在设置中启用'}
      };
    }

    try {
      // 添加超时处理，标签建议最多等待15秒
      return await _apiService
          .generateTagSuggestions(
            content: content,
            options: options ?? {'temperature': 0.3},
          )
          .timeout(
            const Duration(seconds: 15),
            onTimeout: () => {
              'success': false,
              'error': {'message': '标签建议超时，请稍后重试'}
            },
          );
    } catch (e) {
      return {
        'success': false,
        'error': {'message': '标签建议失败: $e'}
      };
    }
  }

  /// 智能问答
  ///
  /// [question] 问题
  /// [context] 上下文（可选）
  /// [options] 选项
  Future<Map<String, dynamic>> askQuestion({
    required String question,
    String? context,
    Map<String, dynamic>? options,
  }) async {
    // 检查AI功能是否启用
    if (!isAIEnabled) {
      return {
        'success': false,
        'error': {'message': 'AI功能未启用，请在设置中启用'}
      };
    }

    try {
      // 添加超时处理，智能问答最多等待30秒
      return await _apiService
          .askQuestion(
            question: question,
            context: context,
            options: options ?? {'temperature': 0.7},
          )
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () => {
              'success': false,
              'error': {'message': '智能问答超时，请稍后重试'}
            },
          );
    } catch (e) {
      return {
        'success': false,
        'error': {'message': '智能问答失败: $e'}
      };
    }
  }

  /// 根据建议频率获取延迟时间（毫秒）
  int getSuggestionDelay() {
    switch (suggestionFrequency) {
      case '低':
        return 10000; // 10秒
      case '中':
        return 5000; // 5秒
      case '高':
        return 3000; // 3秒
      default:
        return 5000; // 默认5秒
    }
  }
}
