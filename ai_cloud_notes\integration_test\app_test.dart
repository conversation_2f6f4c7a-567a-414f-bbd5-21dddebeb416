import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ai_cloud_notes/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('端到端测试', () {
    testWidgets('验证应用启动并显示欢迎页面', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();

      // 验证欢迎页面元素
      expect(find.text('智云笔记'), findsOneWidget);
      
      // 注意：这里的具体验证取决于您的欢迎页面实际内容
      // 您可能需要根据实际情况调整这些断言
    });
  });
}
