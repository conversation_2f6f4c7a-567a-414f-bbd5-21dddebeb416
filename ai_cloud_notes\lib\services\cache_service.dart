import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';

/// 缓存策略枚举
enum CacheStrategy {
  memory,      // 内存缓存
  disk,        // 磁盘缓存
  hybrid,      // 混合缓存
  network,     // 网络优先
}

/// 缓存项
class CacheItem<T> {
  final T data;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final int accessCount;
  final DateTime lastAccessedAt;

  CacheItem({
    required this.data,
    required this.createdAt,
    this.expiresAt,
    this.accessCount = 1,
    required this.lastAccessedAt,
  });

  CacheItem<T> copyWith({
    T? data,
    DateTime? createdAt,
    DateTime? expiresAt,
    int? accessCount,
    DateTime? lastAccessedAt,
  }) {
    return CacheItem<T>(
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      accessCount: accessCount ?? this.accessCount,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
    );
  }

  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'expiresAt': expiresAt?.millisecondsSinceEpoch,
      'accessCount': accessCount,
      'lastAccessedAt': lastAccessedAt.millisecondsSinceEpoch,
    };
  }

  static CacheItem<T> fromJson<T>(Map<String, dynamic> json, T Function(dynamic) fromJsonData) {
    return CacheItem<T>(
      data: fromJsonData(json['data']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
      expiresAt: json['expiresAt'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(json['expiresAt'])
          : null,
      accessCount: json['accessCount'] ?? 1,
      lastAccessedAt: DateTime.fromMillisecondsSinceEpoch(json['lastAccessedAt']),
    );
  }
}

/// 智能缓存服务
class CacheService {
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  // 内存缓存
  final Map<String, CacheItem> _memoryCache = {};
  
  // 缓存配置
  static const int _maxMemoryCacheSize = 100; // 最大内存缓存项数
  static const int _maxDiskCacheSize = 500;   // 最大磁盘缓存项数
  static const Duration _defaultExpiration = Duration(hours: 1);
  static const Duration _longTermExpiration = Duration(days: 7);
  
  // 缓存统计
  int _hitCount = 0;
  int _missCount = 0;
  
  /// 获取缓存命中率
  double get hitRate {
    final total = _hitCount + _missCount;
    return total > 0 ? _hitCount / total : 0.0;
  }

  /// 设置缓存
  Future<void> set<T>(
    String key, 
    T data, {
    CacheStrategy strategy = CacheStrategy.hybrid,
    Duration? expiration,
    bool forceUpdate = false,
  }) async {
    final now = DateTime.now();
    final expiresAt = expiration != null ? now.add(expiration) : null;
    
    final cacheItem = CacheItem<T>(
      data: data,
      createdAt: now,
      expiresAt: expiresAt,
      lastAccessedAt: now,
    );

    switch (strategy) {
      case CacheStrategy.memory:
        await _setMemoryCache(key, cacheItem, forceUpdate);
        break;
      case CacheStrategy.disk:
        await _setDiskCache(key, cacheItem, forceUpdate);
        break;
      case CacheStrategy.hybrid:
        await _setMemoryCache(key, cacheItem, forceUpdate);
        await _setDiskCache(key, cacheItem, forceUpdate);
        break;
      case CacheStrategy.network:
        // 网络优先策略不缓存
        break;
    }
  }

  /// 获取缓存
  Future<T?> get<T>(
    String key, {
    CacheStrategy strategy = CacheStrategy.hybrid,
    T Function(dynamic)? fromJson,
  }) async {
    CacheItem? item;

    switch (strategy) {
      case CacheStrategy.memory:
        item = _getMemoryCache(key);
        break;
      case CacheStrategy.disk:
        item = await _getDiskCache(key, fromJson);
        break;
      case CacheStrategy.hybrid:
        // 先尝试内存缓存
        item = _getMemoryCache(key);
        if (item == null) {
          // 再尝试磁盘缓存
          item = await _getDiskCache(key, fromJson);
          if (item != null) {
            // 将磁盘缓存提升到内存缓存
            await _setMemoryCache(key, item, true);
          }
        }
        break;
      case CacheStrategy.network:
        return null; // 网络优先策略不使用缓存
    }

    if (item != null) {
      if (item.isExpired) {
        await remove(key, strategy: strategy);
        _missCount++;
        return null;
      }
      
      // 更新访问统计
      final updatedItem = item.copyWith(
        accessCount: item.accessCount + 1,
        lastAccessedAt: DateTime.now(),
      );
      
      // 更新缓存项
      if (strategy == CacheStrategy.memory || strategy == CacheStrategy.hybrid) {
        _memoryCache[key] = updatedItem;
      }
      
      _hitCount++;
      return item.data as T?;
    }

    _missCount++;
    return null;
  }

  /// 移除缓存
  Future<void> remove(String key, {CacheStrategy strategy = CacheStrategy.hybrid}) async {
    switch (strategy) {
      case CacheStrategy.memory:
        _memoryCache.remove(key);
        break;
      case CacheStrategy.disk:
        await _removeDiskCache(key);
        break;
      case CacheStrategy.hybrid:
        _memoryCache.remove(key);
        await _removeDiskCache(key);
        break;
      case CacheStrategy.network:
        break;
    }
  }

  /// 清空所有缓存
  Future<void> clear({CacheStrategy strategy = CacheStrategy.hybrid}) async {
    switch (strategy) {
      case CacheStrategy.memory:
        _memoryCache.clear();
        break;
      case CacheStrategy.disk:
        await _clearDiskCache();
        break;
      case CacheStrategy.hybrid:
        _memoryCache.clear();
        await _clearDiskCache();
        break;
      case CacheStrategy.network:
        break;
    }
    
    // 重置统计
    _hitCount = 0;
    _missCount = 0;
  }

  /// 清理过期缓存
  Future<void> cleanExpired() async {
    final now = DateTime.now();
    
    // 清理内存缓存
    _memoryCache.removeWhere((key, item) => item.isExpired);
    
    // 清理磁盘缓存
    await _cleanExpiredDiskCache();
  }

  /// 获取缓存统计信息
  Map<String, dynamic> getStats() {
    return {
      'memorySize': _memoryCache.length,
      'hitCount': _hitCount,
      'missCount': _missCount,
      'hitRate': hitRate,
      'maxMemorySize': _maxMemoryCacheSize,
      'maxDiskSize': _maxDiskCacheSize,
    };
  }

  // 私有方法实现

  /// 设置内存缓存
  Future<void> _setMemoryCache(String key, CacheItem item, bool forceUpdate) async {
    if (!forceUpdate && _memoryCache.containsKey(key)) {
      return;
    }

    // 检查内存缓存大小限制
    if (_memoryCache.length >= _maxMemoryCacheSize) {
      await _evictMemoryCache();
    }

    _memoryCache[key] = item;
  }

  /// 获取内存缓存
  CacheItem? _getMemoryCache(String key) {
    return _memoryCache[key];
  }

  /// 设置磁盘缓存
  Future<void> _setDiskCache(String key, CacheItem item, bool forceUpdate) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'cache_$key';
      
      if (!forceUpdate && prefs.containsKey(cacheKey)) {
        return;
      }

      final jsonData = jsonEncode(item.toJson());
      await prefs.setString(cacheKey, jsonData);
    } catch (e) {
      if (kDebugMode) {
        print('设置磁盘缓存失败: $e');
      }
    }
  }

  /// 获取磁盘缓存
  Future<CacheItem?> _getDiskCache(String key, dynamic Function(dynamic)? fromJson) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = 'cache_$key';
      final jsonString = prefs.getString(cacheKey);
      
      if (jsonString != null) {
        final jsonData = jsonDecode(jsonString);
        return CacheItem.fromJson(jsonData, fromJson ?? (data) => data);
      }
    } catch (e) {
      if (kDebugMode) {
        print('获取磁盘缓存失败: $e');
      }
    }
    return null;
  }

  /// 移除磁盘缓存
  Future<void> _removeDiskCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cache_$key');
    } catch (e) {
      if (kDebugMode) {
        print('移除磁盘缓存失败: $e');
      }
    }
  }

  /// 清空磁盘缓存
  Future<void> _clearDiskCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_')).toList();
      for (final key in keys) {
        await prefs.remove(key);
      }
    } catch (e) {
      if (kDebugMode) {
        print('清空磁盘缓存失败: $e');
      }
    }
  }

  /// 清理过期的磁盘缓存
  Future<void> _cleanExpiredDiskCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('cache_')).toList();
      
      for (final key in keys) {
        final jsonString = prefs.getString(key);
        if (jsonString != null) {
          final jsonData = jsonDecode(jsonString);
          final item = CacheItem.fromJson(jsonData, (data) => data);
          if (item.isExpired) {
            await prefs.remove(key);
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('清理过期磁盘缓存失败: $e');
      }
    }
  }

  /// 内存缓存淘汰策略（LRU）
  Future<void> _evictMemoryCache() async {
    if (_memoryCache.isEmpty) return;

    // 找到最久未访问的缓存项
    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _memoryCache.entries) {
      if (oldestTime == null || entry.value.lastAccessedAt.isBefore(oldestTime)) {
        oldestTime = entry.value.lastAccessedAt;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _memoryCache.remove(oldestKey);
    }
  }
}
