import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  NOT_FOUND = 'not_found',
  CONFLICT = 'conflict',
  RATE_LIMIT = 'rate_limit',
  INTERNAL = 'internal',
  DATABASE = 'database',
  NETWORK = 'network',
}

/**
 * 应用错误类
 */
export class AppError extends Error {
  public readonly statusCode: number;
  public readonly type: ErrorType;
  public readonly isOperational: boolean;
  public readonly details?: any;
  public readonly timestamp: Date;

  constructor(
    message: string,
    statusCode: number = 500,
    type: ErrorType = ErrorType.INTERNAL,
    isOperational: boolean = true,
    details?: any
  ) {
    super(message);
    
    this.statusCode = statusCode;
    this.type = type;
    this.isOperational = isOperational;
    this.details = details;
    this.timestamp = new Date();

    // 确保错误名称正确
    this.name = this.constructor.name;

    // 捕获堆栈跟踪
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * 转换为JSON格式
   */
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      type: this.type,
      timestamp: this.timestamp.toISOString(),
      details: this.details,
      stack: this.stack,
    };
  }
}

/**
 * 预定义错误类
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, ErrorType.VALIDATION, true, details);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败') {
    super(message, 401, ErrorType.AUTHENTICATION);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403, ErrorType.AUTHORIZATION);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = '资源未找到') {
    super(message, 404, ErrorType.NOT_FOUND);
  }
}

export class ConflictError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 409, ErrorType.CONFLICT, true, details);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = '请求过于频繁') {
    super(message, 429, ErrorType.RATE_LIMIT);
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 500, ErrorType.DATABASE, true, details);
  }
}

/**
 * 错误响应格式化
 */
function formatErrorResponse(error: AppError, includeStack: boolean = false) {
  const response: any = {
    success: false,
    error: {
      message: error.message,
      type: error.type,
      timestamp: error.timestamp.toISOString(),
    },
  };

  // 在开发环境中包含更多详细信息
  if (process.env.NODE_ENV === 'development') {
    response.error.statusCode = error.statusCode;
    if (error.details) {
      response.error.details = error.details;
    }
    if (includeStack && error.stack) {
      response.error.stack = error.stack;
    }
  }

  return response;
}

/**
 * 错误日志记录
 */
function logError(error: AppError, req: Request) {
  const logData = {
    message: error.message,
    statusCode: error.statusCode,
    type: error.type,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: (req as any).user?.id,
    timestamp: error.timestamp,
    stack: error.stack,
  };

  if (error.statusCode >= 500) {
    logger.error('服务器错误', logData);
  } else if (error.statusCode >= 400) {
    logger.warn('客户端错误', logData);
  } else {
    logger.info('错误信息', logData);
  }
}

/**
 * 错误处理中间件
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let appError: AppError;

  // 如果已经是AppError，直接使用
  if (error instanceof AppError) {
    appError = error;
  } else {
    // 转换其他类型的错误
    appError = convertToAppError(error);
  }

  // 记录错误日志
  logError(appError, req);

  // 如果响应已经发送，则不能再发送响应
  if (res.headersSent) {
    return next(appError);
  }

  // 发送错误响应
  const includeStack = process.env.NODE_ENV === 'development';
  const errorResponse = formatErrorResponse(appError, includeStack);

  res.status(appError.statusCode).json(errorResponse);
};

/**
 * 将普通错误转换为AppError
 */
function convertToAppError(error: Error): AppError {
  // MongoDB错误
  if (error.name === 'MongoError' || error.name === 'MongooseError') {
    return new DatabaseError('数据库操作失败', { originalError: error.message });
  }

  // Mongoose验证错误
  if (error.name === 'ValidationError') {
    return new ValidationError('数据验证失败', { originalError: error.message });
  }

  // Mongoose重复键错误
  if (error.name === 'MongoServerError' && (error as any).code === 11000) {
    return new ConflictError('数据已存在', { originalError: error.message });
  }

  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    return new AuthenticationError('无效的认证令牌');
  }

  if (error.name === 'TokenExpiredError') {
    return new AuthenticationError('认证令牌已过期');
  }

  // 语法错误
  if (error instanceof SyntaxError) {
    return new ValidationError('请求格式错误', { originalError: error.message });
  }

  // 默认为内部服务器错误
  return new AppError(
    process.env.NODE_ENV === 'production' ? '内部服务器错误' : error.message,
    500,
    ErrorType.INTERNAL,
    false,
    { originalError: error.message, stack: error.stack }
  );
}

/**
 * 异步错误捕获装饰器
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404错误处理中间件
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new NotFoundError(`路由 ${req.originalUrl} 未找到`);
  next(error);
};

/**
 * 未捕获异常处理
 */
export const setupGlobalErrorHandlers = () => {
  // 处理未捕获的Promise拒绝
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('未处理的Promise拒绝', {
      reason: reason?.toString(),
      stack: reason?.stack,
      promise: promise.toString(),
    });

    // 优雅关闭服务器
    process.exit(1);
  });

  // 处理未捕获的异常
  process.on('uncaughtException', (error: Error) => {
    logger.error('未捕获的异常', {
      message: error.message,
      stack: error.stack,
    });

    // 优雅关闭服务器
    process.exit(1);
  });

  // 处理SIGTERM信号
  process.on('SIGTERM', () => {
    logger.info('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
  });

  // 处理SIGINT信号
  process.on('SIGINT', () => {
    logger.info('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
  });
};

/**
 * 错误恢复策略
 */
export class ErrorRecoveryStrategy {
  private static retryCount = new Map<string, number>();
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 1000; // 1秒

  /**
   * 重试策略
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    key: string,
    maxRetries: number = this.MAX_RETRIES
  ): Promise<T> {
    const currentRetries = this.retryCount.get(key) || 0;

    try {
      const result = await operation();
      // 成功后重置重试计数
      this.retryCount.delete(key);
      return result;
    } catch (error) {
      if (currentRetries < maxRetries) {
        this.retryCount.set(key, currentRetries + 1);
        
        // 指数退避延迟
        const delay = this.RETRY_DELAY * Math.pow(2, currentRetries);
        await new Promise(resolve => setTimeout(resolve, delay));
        
        logger.warn(`操作失败，正在重试 (${currentRetries + 1}/${maxRetries})`, {
          key,
          error: error instanceof Error ? error.message : String(error),
        });
        
        return this.withRetry(operation, key, maxRetries);
      } else {
        // 达到最大重试次数，清除计数并抛出错误
        this.retryCount.delete(key);
        throw error;
      }
    }
  }

  /**
   * 断路器模式
   */
  static createCircuitBreaker<T>(
    operation: () => Promise<T>,
    threshold: number = 5,
    timeout: number = 60000 // 1分钟
  ) {
    let failureCount = 0;
    let lastFailureTime = 0;
    let state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

    return async (): Promise<T> => {
      const now = Date.now();

      // 如果断路器打开且超时时间已过，尝试半开状态
      if (state === 'OPEN' && now - lastFailureTime > timeout) {
        state = 'HALF_OPEN';
      }

      // 如果断路器打开，直接抛出错误
      if (state === 'OPEN') {
        throw new AppError('服务暂时不可用，请稍后重试', 503, ErrorType.INTERNAL);
      }

      try {
        const result = await operation();
        
        // 成功后重置状态
        if (state === 'HALF_OPEN') {
          state = 'CLOSED';
          failureCount = 0;
        }
        
        return result;
      } catch (error) {
        failureCount++;
        lastFailureTime = now;

        // 如果失败次数达到阈值，打开断路器
        if (failureCount >= threshold) {
          state = 'OPEN';
        }

        throw error;
      }
    };
  }
}
