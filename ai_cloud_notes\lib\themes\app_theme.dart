import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/providers/theme_service.dart';

/// 应用程序主题类
/// 定义应用的颜色、字体、样式等主题元素
class AppTheme {
  // 私有构造函数，防止外部实例化
  AppTheme._();

  // 主题色定义
  static const Color primaryColor = Color(0xFF5D5FEF); // 主色调：紫蓝色
  static const Color secondaryColor = Color(0xFF06B6D4); // 次要色调：青色
  static const Color accentColor = Color(0xFFFFA500); // 强调色：橙色
  static const Color successColor = Color(0xFF4CAF50); // 成功色：绿色
  static const Color dangerColor = Color(0xFFEF4444); // 危险色：红色
  static const Color warningColor = Color(0xFFFF9800); // 警告色：橙色
  static const Color starColor = Color(0xFFFFB800); // 星标色：金色

  // 中性色-亮色模式
  static const Color blackColor = Color(0xFF333333); // 黑色
  static const Color darkGrayColor = Color(0xFF666666); // 深灰
  static const Color mediumGrayColor = Color(0xFF999999); // 中灰
  static const Color lightGrayColor = Color(0xFFEEEEEE); // 浅灰
  static const Color whiteColor = Color(0xFFFFFFFF); // 白色
  static const Color bgColor = Color(0xFFFAFAFA); // 背景色

  // 中性色-暗色模式
  static const Color darkBlackColor = Color(0xFF121212); // 暗黑色
  static const Color darkDarkGrayColor = Color(0xFF1E1E1E); // 暗深灰
  static const Color darkMediumGrayColor = Color(0xFF2C2C2C); // 暗中灰
  static const Color darkLightGrayColor = Color(0xFF3C3C3C); // 暗浅灰
  static const Color darkWhiteColor = Color(0xFFEEEEEE); // 暗白色

  // 通用配置
  static const double borderRadiusSmall = 4.0; // 小圆角
  static const double borderRadiusMedium = 8.0; // 中圆角
  static const double borderRadiusLarge = 12.0; // 大圆角
  static const double smallRadius = 8.0; // 小圆角
  static const double mediumRadius = 12.0; // 中圆角
  static const double largeRadius = 16.0; // 大圆角

  // 间距
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;

  // 阴影定义
  static final List<BoxShadow> smallShadow = [
    BoxShadow(
      color: Colors.black.withOpacity(0.1),
      blurRadius: 6,
      offset: const Offset(0, 2),
    ),
  ];

  static final List<BoxShadow> mediumShadow = [
    BoxShadow(
      color: Colors.black.withOpacity(0.15),
      blurRadius: 12,
      offset: const Offset(0, 4),
    ),
  ];

  // 排版样式
  static const TextStyle headingOneStyle = TextStyle(
    fontSize: 24.0,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle headingTwoStyle = TextStyle(
    fontSize: 20.0,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle headingThreeStyle = TextStyle(
    fontSize: 18.0,
    fontWeight: FontWeight.w600,
  );

  static const TextStyle bodyStyle = TextStyle(
    fontSize: 16.0,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle smallStyle = TextStyle(
    fontSize: 14.0,
    fontWeight: FontWeight.w400,
  );

  static const TextStyle xSmallStyle = TextStyle(
    fontSize: 12.0,
    fontWeight: FontWeight.w400,
  );

  // 浅色主题
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primaryColor: primaryColor,
    scaffoldBackgroundColor: bgColor,
    colorScheme: ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      surface: whiteColor,
      background: bgColor,
      error: dangerColor,
      onPrimary: whiteColor,
      onSecondary: whiteColor,
      onSurface: blackColor,
      onBackground: blackColor,
      onError: whiteColor,
      brightness: Brightness.light,
    ),
    textTheme: ThemeData.light().textTheme.copyWith(
      displayLarge: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: blackColor,
      ),
      displayMedium: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: blackColor,
      ),
      displaySmall: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: blackColor,
      ),
      headlineMedium: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: blackColor,
      ),
      titleLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: blackColor,
      ),
      bodyLarge: TextStyle(
        fontSize: 16,
        color: blackColor,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        color: darkGrayColor,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        color: mediumGrayColor,
      ),
    ),
    cardTheme: CardTheme(
      color: whiteColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(mediumRadius),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: whiteColor,
        elevation: 0,
        padding: const EdgeInsets.symmetric(
          horizontal: 24.0,
          vertical: 14.0,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(smallRadius),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 14.0,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(smallRadius),
        borderSide: BorderSide(color: Colors.transparent),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(smallRadius),
        borderSide: BorderSide(color: Colors.grey.withOpacity(0.2)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(smallRadius),
        borderSide: BorderSide(color: primaryColor.withOpacity(0.8), width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(smallRadius),
        borderSide: const BorderSide(color: Colors.red),
      ),
      labelStyle: TextStyle(color: darkGrayColor),
      hintStyle: TextStyle(color: mediumGrayColor),
      isDense: true,
      prefixIconColor: darkGrayColor,
      suffixIconColor: darkGrayColor,
    ),
    dividerTheme: DividerThemeData(
      color: lightGrayColor,
      thickness: 1,
      space: 1,
    ),
    appBarTheme: AppBarTheme(
      backgroundColor: whiteColor,
      elevation: 0,
      iconTheme: IconThemeData(color: blackColor),
      titleTextStyle: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: blackColor,
      ),
    ),
  );

  // 用于认证/引导页的固定亮色主题
  static final ThemeData fixedLightTheme = lightTheme;

  // 深色主题
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primaryColor: primaryColor,
    scaffoldBackgroundColor: darkBlackColor,
    colorScheme: ColorScheme.dark(
      primary: primaryColor,
      secondary: secondaryColor,
      surface: darkDarkGrayColor,
      background: darkBlackColor,
      error: dangerColor,
      onPrimary: whiteColor,
      onSecondary: whiteColor,
      onSurface: whiteColor,
      onBackground: whiteColor,
      onError: whiteColor,
      brightness: Brightness.dark,
    ),
    textTheme: ThemeData.dark().textTheme.copyWith(
      displayLarge: const TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: whiteColor,
      ),
      displayMedium: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: whiteColor,
      ),
      displaySmall: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: whiteColor,
      ),
      headlineMedium: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: whiteColor,
      ),
      titleLarge: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: whiteColor,
      ),
      bodyLarge: const TextStyle(
        fontSize: 16,
        color: whiteColor,
      ),
      bodyMedium: const TextStyle(
        fontSize: 14,
        color: lightGrayColor,
      ),
      bodySmall: const TextStyle(
        fontSize: 12,
        color: Colors.grey,
      ),
    ),
    cardTheme: CardTheme(
      color: darkDarkGrayColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(mediumRadius),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: whiteColor,
        elevation: 0,
        padding: const EdgeInsets.symmetric(
          horizontal: 24.0,
          vertical: 14.0,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(smallRadius),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: secondaryColor,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: darkMediumGrayColor,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 16.0,
        vertical: 14.0,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(smallRadius),
        borderSide: BorderSide(color: Colors.transparent),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(smallRadius),
        borderSide: BorderSide(color: Colors.white.withOpacity(0.15)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(smallRadius),
        borderSide: BorderSide(color: primaryColor.withOpacity(0.8), width: 1.5),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(smallRadius),
        borderSide: BorderSide(color: dangerColor),
      ),
      labelStyle: const TextStyle(color: lightGrayColor),
      hintStyle: const TextStyle(color: mediumGrayColor),
      isDense: true,
      prefixIconColor: mediumGrayColor,
      suffixIconColor: mediumGrayColor,
    ),
    dividerTheme: const DividerThemeData(
      color: darkLightGrayColor,
      thickness: 1,
      space: 1,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: darkDarkGrayColor,
      elevation: 0,
      iconTheme: IconThemeData(color: whiteColor),
      titleTextStyle: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: whiteColor,
      ),
    ),
  );

  /// 根据 ThemeService 和 Brightness 动态生成 ThemeData
  static ThemeData getDynamicThemeData(ThemeService themeService, Brightness brightness) {
    final bool isDarkMode = brightness == Brightness.dark;
    final ThemeData baseTheme = isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme;

    final Color currentFontColor = themeService.fontColor;
    final double currentTextSize = themeService.textSize;
    final double currentTitleSize = themeService.titleSize;

    // 确定 AppBar 和 Icon 的前景颜色，确保可读性
    Color appBarForegroundColor;
    Color generalIconColor;

    if (isDarkMode) {
      // 暗色模式下，如果用户选择的字体颜色太暗（例如黑色），AppBar 文字和图标应保持亮色（例如白色）
      appBarForegroundColor = (ThemeData.estimateBrightnessForColor(currentFontColor) == Brightness.dark && currentFontColor != Colors.transparent)
          ? AppTheme.whiteColor // 用户选了深色字体，AppBar 用白色以保证可见
          : currentFontColor; // 用户选了浅色字体，AppBar 可以用该字体颜色
      // 如果 currentFontColor 是黑色，则 AppBar 强制为白色
      if (currentFontColor == Colors.black) {
        appBarForegroundColor = AppTheme.whiteColor;
      }
      generalIconColor = AppTheme.whiteColor; // 暗色模式下，通用图标通常为白色或浅色
    } else {
      // 亮色模式下，如果用户选择的字体颜色太亮（例如白色），AppBar 文字和图标应保持暗色（例如黑色）
      appBarForegroundColor = (ThemeData.estimateBrightnessForColor(currentFontColor) == Brightness.light && currentFontColor != Colors.transparent)
          ? AppTheme.blackColor // 用户选了浅色字体，AppBar 用黑色以保证可见
          : currentFontColor; // 用户选了深色字体，AppBar 可以用该字体颜色
      // 如果 currentFontColor 是白色，则 AppBar 强制为黑色
      if (currentFontColor == Colors.white) {
        appBarForegroundColor = AppTheme.blackColor;
      }
      generalIconColor = AppTheme.blackColor; // 亮色模式下，通用图标通常为黑色或深色
    }

    return baseTheme.copyWith(
      // 基本颜色保持不变，除非特定组件需要调整
      // scaffoldBackgroundColor: isDarkMode ? AppTheme.darkBlackColor : AppTheme.bgColor,
      // cardColor: isDarkMode ? AppTheme.darkDarkGrayColor : AppTheme.whiteColor,

      iconTheme: baseTheme.iconTheme.copyWith(color: generalIconColor), // 通用图标颜色

      appBarTheme: baseTheme.appBarTheme.copyWith(
        // AppBar 背景色保持基础主题的设定
        // backgroundColor: isDarkMode ? AppTheme.darkDarkGrayColor : AppTheme.whiteColor,
        titleTextStyle: baseTheme.appBarTheme.titleTextStyle?.copyWith(
          color: appBarForegroundColor, // AppBar 标题文字颜色
          fontSize: currentTitleSize,   // AppBar 标题字体大小
        ),
        iconTheme: baseTheme.appBarTheme.iconTheme?.copyWith(
          color: appBarForegroundColor, // AppBar 图标颜色
        ),
      ),

      textTheme: baseTheme.textTheme.copyWith(
        displayLarge: baseTheme.textTheme.displayLarge?.copyWith(color: currentFontColor, fontSize: currentTitleSize + 6),
        displayMedium: baseTheme.textTheme.displayMedium?.copyWith(color: currentFontColor, fontSize: currentTitleSize + 4),
        displaySmall: baseTheme.textTheme.displaySmall?.copyWith(color: currentFontColor, fontSize: currentTitleSize + 2),
        headlineLarge: baseTheme.textTheme.headlineLarge?.copyWith(color: currentFontColor, fontSize: currentTitleSize + 2),
        headlineMedium: baseTheme.textTheme.headlineMedium?.copyWith(color: currentFontColor, fontSize: currentTitleSize),
        headlineSmall: baseTheme.textTheme.headlineSmall?.copyWith(color: currentFontColor, fontSize: currentTitleSize -2),
        titleLarge: baseTheme.textTheme.titleLarge?.copyWith(color: currentFontColor, fontSize: currentTitleSize),
        titleMedium: baseTheme.textTheme.titleMedium?.copyWith(color: currentFontColor, fontSize: currentTitleSize),
        titleSmall: baseTheme.textTheme.titleSmall?.copyWith(color: currentFontColor, fontSize: currentTextSize), // 通常标题使用 titleSize，小标题可能用 textSize
        bodyLarge: baseTheme.textTheme.bodyLarge?.copyWith(color: currentFontColor, fontSize: currentTextSize),
        bodyMedium: baseTheme.textTheme.bodyMedium?.copyWith(color: currentFontColor, fontSize: currentTextSize),
        bodySmall: baseTheme.textTheme.bodySmall?.copyWith(color: currentFontColor, fontSize: currentTextSize - 2),
        labelLarge: baseTheme.textTheme.labelLarge?.copyWith(color: currentFontColor, fontSize: currentTextSize), // 按钮等标签文字
        labelMedium: baseTheme.textTheme.labelMedium?.copyWith(color: currentFontColor, fontSize: currentTextSize - 2),
        labelSmall: baseTheme.textTheme.labelSmall?.copyWith(color: currentFontColor, fontSize: currentTextSize - 4),
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: baseTheme.elevatedButtonTheme.style?.copyWith(
          textStyle: MaterialStateProperty.resolveWith<TextStyle?>((states) {
            return TextStyle(fontSize: currentTextSize); // 按钮文字大小
          }),
          // 按钮的前景色（文字/图标）和背景色通常由基础主题的 colorScheme 控制，
          // 这里不直接覆盖，除非有非常特殊的需求。
          // foregroundColor: MaterialStateProperty.all(currentFontColor == AppTheme.primaryColor ? AppTheme.whiteColor : currentFontColor),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: baseTheme.textButtonTheme.style?.copyWith(
          textStyle: MaterialStateProperty.resolveWith<TextStyle?>((states) {
            return TextStyle(fontSize: currentTextSize); // 文本按钮文字大小
          }),
          // foregroundColor: MaterialStateProperty.all(currentFontColor) // 避免直接覆盖，可能导致对比度问题
        ),
      ),

      // 确保应用的主色调等核心颜色不受影响
      primaryColor: AppTheme.primaryColor,
      // colorScheme onSurface 和 onBackground 会影响默认文本颜色，这里用 currentFontColor
      colorScheme: baseTheme.colorScheme.copyWith(
        onSurface: currentFontColor,    // Card, Dialog 等表面上的文字颜色
        onBackground: currentFontColor, // Scaffold 背景上的文字颜色
        // 其他 colorScheme 颜色（如 primary, secondary, error）保持基础主题的设定
      ),
    );
  }

  // @Deprecated('Use getDynamicThemeData instead. This will be removed in a future version.')
  // static ThemeData getSettingsPageTheme(BuildContext context, ThemeService themeService) {
  //   final bool isDarkMode = themeService.themeMode == ThemeMode.dark ||
  //       (themeService.themeMode == ThemeMode.system && MediaQuery.of(context).platformBrightness == Brightness.dark);
  //
  //   final Color currentFontColor = themeService.fontColor;
  //   final double currentTextSize = themeService.textSize;
  //   final double currentTitleSize = themeService.titleSize;
  //
  //   final ThemeData baseTheme = isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme;
  //
  //   Color iconColor;
  //   Color appBarForegroundColor;
  //
  //   if (isDarkMode) {
  //     appBarForegroundColor = AppTheme.whiteColor; // Default for dark mode AppBar
  //     iconColor = AppTheme.whiteColor; // Default for dark mode icons
  //
  //     // If user explicitly chose a light font color for dark mode, use it, otherwise stick to white for readability
  //     if (ThemeData.estimateBrightnessForColor(currentFontColor) == Brightness.light) {
  //       // User chose a light font, suitable for dark background
  //       // For general text, currentFontColor will be used by textTheme.
  //       // For AppBar title/icons, if currentFontColor is light enough, use it.
  //       // However, the request was "深色主题，然后字体颜色是黑色", which is a dark font on dark bg.
  //       // So, if currentFontColor is black (or very dark), appBarForegroundColor should remain white.
  //       if (currentFontColor != Colors.black && currentFontColor.opacity > 0.5) { // Heuristic for "light enough"
  //            // appBarForegroundColor = currentFontColor; // This might be too complex, stick to white for now for AppBar
  //       }
  //     }
  //     // If font color is black on dark mode, ensure app bar text is white
  //     if (currentFontColor == Colors.black) {
  //       appBarForegroundColor = AppTheme.whiteColor;
  //     }
  //
  //   } else {
  //     // Light mode
  //     appBarForegroundColor = AppTheme.blackColor; // Default for light mode AppBar
  //     iconColor = AppTheme.blackColor; // Default for light mode icons
  //
  //     // If user explicitly chose a dark font color for light mode, use it
  //     if (ThemeData.estimateBrightnessForColor(currentFontColor) == Brightness.dark) {
  //       // User chose a dark font, suitable for light background
  //       // if (currentFontColor != Colors.white) { // Heuristic
  //            // appBarForegroundColor = currentFontColor; // Stick to black for now for AppBar
  //       // }
  //     }
  //      // If font color is white on light mode, ensure app bar text is black
  //     if (currentFontColor == Colors.white) {
  //       appBarForegroundColor = AppTheme.blackColor;
  //     }
  //   }
  //
  //   return baseTheme.copyWith(
  //     scaffoldBackgroundColor: isDarkMode ? AppTheme.darkBlackColor : AppTheme.bgColor,
  //     cardColor: isDarkMode ? AppTheme.darkDarkGrayColor : AppTheme.whiteColor,
  //     iconTheme: baseTheme.iconTheme.copyWith(color: iconColor), // General icon theme
  //     appBarTheme: baseTheme.appBarTheme.copyWith(
  //       backgroundColor: isDarkMode ? AppTheme.darkDarkGrayColor : AppTheme.whiteColor,
  //       titleTextStyle: baseTheme.appBarTheme.titleTextStyle?.copyWith(
  //         color: appBarForegroundColor, // Apply calculated AppBar foreground color
  //         fontSize: currentTitleSize,
  //       ),
  //       iconTheme: baseTheme.appBarTheme.iconTheme?.copyWith(
  //         color: appBarForegroundColor, // Apply calculated AppBar foreground color for icons too
  //       ),
  //     ),
  //     textTheme: baseTheme.textTheme.copyWith(
  //       displayLarge: baseTheme.textTheme.displayLarge?.copyWith(color: currentFontColor, fontSize: currentTitleSize + 6),
  //       displayMedium: baseTheme.textTheme.displayMedium?.copyWith(color: currentFontColor, fontSize: currentTitleSize + 4),
  //       displaySmall: baseTheme.textTheme.displaySmall?.copyWith(color: currentFontColor, fontSize: currentTitleSize + 2),
  //       headlineLarge: baseTheme.textTheme.headlineLarge?.copyWith(color: currentFontColor, fontSize: currentTitleSize + 2),
  //       headlineMedium: baseTheme.textTheme.headlineMedium?.copyWith(color: currentFontColor, fontSize: currentTitleSize),
  //       headlineSmall: baseTheme.textTheme.headlineSmall?.copyWith(color: currentFontColor, fontSize: currentTitleSize -2),
  //       titleLarge: baseTheme.textTheme.titleLarge?.copyWith(color: currentFontColor, fontSize: currentTitleSize),
  //       titleMedium: baseTheme.textTheme.titleMedium?.copyWith(color: currentFontColor, fontSize: currentTitleSize), // Changed from currentTextSize + 2
  //       titleSmall: baseTheme.textTheme.titleSmall?.copyWith(color: currentFontColor, fontSize: currentTextSize),
  //       bodyLarge: baseTheme.textTheme.bodyLarge?.copyWith(color: currentFontColor, fontSize: currentTextSize),
  //       bodyMedium: baseTheme.textTheme.bodyMedium?.copyWith(color: currentFontColor, fontSize: currentTextSize),
  //       bodySmall: baseTheme.textTheme.bodySmall?.copyWith(color: currentFontColor, fontSize: currentTextSize - 2),
  //       labelLarge: baseTheme.textTheme.labelLarge?.copyWith(color: currentFontColor, fontSize: currentTextSize),
  //       labelMedium: baseTheme.textTheme.labelMedium?.copyWith(color: currentFontColor, fontSize: currentTextSize - 2),
  //       labelSmall: baseTheme.textTheme.labelSmall?.copyWith(color: currentFontColor, fontSize: currentTextSize - 4),
  //     ),
  //      elevatedButtonTheme: ElevatedButtonThemeData(
  //       style: baseTheme.elevatedButtonTheme.style?.copyWith(
  //         textStyle: MaterialStateProperty.all(TextStyle(fontSize: currentTextSize, color: currentFontColor == AppTheme.primaryColor ? AppTheme.whiteColor : currentFontColor)),
  //         // Potentially adjust button foreground/background based on currentFontColor for readability
  //       ),
  //     ),
  //     textButtonTheme: TextButtonThemeData(
  //        style: baseTheme.textButtonTheme.style?.copyWith(
  //         textStyle: MaterialStateProperty.all(TextStyle(fontSize: currentTextSize)),
  //         // Foreground color for TextButton might need adjustment based on currentFontColor
  //         // foregroundColor: MaterialStateProperty.all(currentFontColor) // This might not always be readable
  //       ),
  //     ),
  //      // Ensure primaryColor is still applied correctly, especially for buttons or highlights
  //     primaryColor: AppTheme.primaryColor, // Retain the app's primary color
  //     colorScheme: baseTheme.colorScheme.copyWith(
  //       // If fontColor is very dark on dark theme, onSurface might need to be light
  //       // If fontColor is very light on light theme, onSurface might need to be dark
  //       onSurface: currentFontColor, // Text on cards, dialogs etc.
  //       onBackground: currentFontColor, // Text on scaffold background
  //       // primary: AppTheme.primaryColor, // Already set in baseTheme
  //       // secondary: AppTheme.secondaryColor, // Already set in baseTheme
  //     )
  //   );
  // }
}