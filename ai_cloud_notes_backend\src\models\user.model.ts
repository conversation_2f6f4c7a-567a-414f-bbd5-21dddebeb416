import mongoose, { Schema, Document } from 'mongoose';
import bcrypt from 'bcryptjs';

/**
 * 用户接口，定义用户文档结构
 */
export interface IUser extends Document {
  username: string;
  email: string;
  passwordHash: string;
  avatar?: string; 
  bio?: string;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  resetPasswordToken?: string;
  resetPasswordExpires?: Date;
  comparePassword: (password: string) => Promise<boolean>;
}

/**
 * 用户模式，定义MongoDB集合结构
 */
const UserSchema: Schema = new Schema(
  {
    username: {
      type: String,
      required: [true, '用户名是必需的'],
      unique: true,
      trim: true,
      minlength: [3, '用户名至少需要3个字符'],
      maxlength: [20, '用户名不能超过20个字符']
    },
    email: {
      type: String,
      required: [true, '邮箱是必需的'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/, '请提供有效的邮箱地址']
    },
    passwordHash: {
      type: String,
      required: [true, '密码是必需的'],
      minlength: [6, '密码至少需要6个字符']
    },
    avatar: {
      type: String,
      default: '',
    },
    bio: {
      type: String,
      default: '',
      maxlength: [200, '个人简介不能超过200个字符']
    },
    lastLoginAt: {
      type: Date,
      default: null
    },
    resetPasswordToken: {
      type: String,
      default: null
    },
    resetPasswordExpires: {
      type: Date,
      default: null
    }
  },
  {
    timestamps: true // 自动添加 createdAt 和 updatedAt 字段
  }
);

/**
 * 中间件，保存前对密码进行加密
 */
UserSchema.pre<IUser>('save', async function(next) {
  // 只在密码被修改时才重新加密
  if (!this.isModified('passwordHash')) return next();
  
  try {
    // 生成盐值
    const salt = await bcrypt.genSalt(10);
    // 哈希密码
    this.passwordHash = await bcrypt.hash(this.passwordHash, salt);
    next();
  } catch (error: any) {
    next(error);
  }
});

/**
 * 比较密码与哈希是否匹配
 */
UserSchema.methods.comparePassword = async function(password: string): Promise<boolean> {
  return bcrypt.compare(password, this.passwordHash);
};

export default mongoose.model<IUser>('User', UserSchema); 