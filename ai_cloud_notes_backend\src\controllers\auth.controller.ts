import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import User from '../models/user.model';
import { generateToken, verifyToken } from '../utils/auth';
import { logger } from '../utils/logger';
import { sendPasswordResetEmail, sendWelcomeEmail, sendVerificationCodeEmail, sendPasswordChangedEmail } from '../utils/email';
import { generateVerificationCode, setEmailVerificationCode, verifyEmailCode, addTokenToBlacklist } from '../utils/redis';
import { v4 as uuidv4 } from 'uuid';
import { JwtPayload } from 'jsonwebtoken';

/**
 * 发送邮箱验证码控制器
 */
export const sendVerificationCode = async (req: Request, res: Response) => {
  try {
    // 验证请求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { email } = req.body;

    // 检查邮箱是否已注册
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: {
          message: '该邮箱已被注册'
        }
      });
    }

    // 生成6位数验证码
    const verificationCode = generateVerificationCode(6);

    // 发送验证码邮件
    const emailSent = await sendVerificationCodeEmail(email, verificationCode);

    if (!emailSent) {
      return res.status(500).json({
        success: false,
        error: {
          message: '验证码发送失败，请稍后再试'
        }
      });
    }

    // 将验证码存储到Redis，有效期5分钟
    await setEmailVerificationCode(email, verificationCode);

    return res.status(200).json({
      success: true,
      message: '验证码已发送到您的邮箱，请查收',
      data: {
        expiresIn: 300 // 5分钟过期
      }
    });
  } catch (error: any) {
    logger.error(`发送验证码失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '发送验证码失败，请稍后再试'
      }
    });
  }
};

/**
 * 用户注册控制器
 */
export const register = async (req: Request, res: Response) => {
  try {
    // 检查请求数据验证结果
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { username, email, password, verificationCode } = req.body;

    // 验证邮箱验证码
    const isCodeValid = await verifyEmailCode(email, verificationCode);
    if (!isCodeValid) {
      return res.status(400).json({
        success: false,
        error: {
          message: '验证码无效或已过期'
        }
      });
    }

    // 检查用户是否已存在
    const userExists = await User.findOne({
      $or: [{ email }, { username }]
    });

    if (userExists) {
      return res.status(400).json({
        success: false,
        error: {
          message: '用户名或邮箱已被注册',
        }
      });
    }

    // 创建新用户
    const user = new User({
      username,
      email,
      passwordHash: password, // 会在保存前通过中间件自动加密
    });

    await user.save();

    // 发送欢迎邮件（异步发送，不阻塞响应）
    sendWelcomeEmail(email, username).catch(err => {
      logger.error(`发送欢迎邮件失败: ${err.message}`);
    });

    // 返回成功响应（不包含令牌，需要用户登录）
    return res.status(201).json({
      success: true,
      message: '注册成功，请登录',
    });
  } catch (error: any) {
    logger.error(`注册失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '注册失败，请稍后再试',
      }
    });
  }
};

/**
 * 用户登录控制器
 */
export const login = async (req: Request, res: Response) => {
  try {
    // 检查请求数据验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { usernameOrEmail, password } = req.body;

    // 查找用户
    const user = await User.findOne({
      $or: [
        { email: usernameOrEmail },
        { username: usernameOrEmail }
      ]
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: {
          message: '用户名/邮箱或密码不正确',
        }
      });
    }

    // 验证密码
    const isMatch = await user.comparePassword(password);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        error: {
          message: '用户名/邮箱或密码不正确',
        }
      });
    }

    // 更新最后登录时间
    user.lastLoginAt = new Date();
    await user.save();

    // 生成JWT令牌
    const tokenData = generateToken(user);

    // 返回用户数据和令牌
    return res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          bio: user.bio,
        },
        token: tokenData.token,
        expiresIn: tokenData.expiresIn,
      },
    });
  } catch (error: any) {
    logger.error(`登录失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '登录失败，请稍后再试',
      }
    });
  }
};

/**
 * 忘记密码控制器
 */
export const forgotPassword = async (req: Request, res: Response) => {
  try {
    // 检查请求数据验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { email } = req.body;

    // 检查用户是否存在
    const user = await User.findOne({ email });

    if (!user) {
      // 即使用户不存在，也返回成功响应
      // 这样可以防止枚举攻击（知道哪些邮箱已注册）
      return res.status(200).json({
        success: true,
        message: '如果该邮箱已注册，我们将发送一封密码重置邮件',
      });
    }

    // 生成重置令牌
    const resetToken = uuidv4();

    // 设置令牌过期时间（1小时后）
    const resetExpires = new Date();
    resetExpires.setHours(resetExpires.getHours() + 1);

    // 保存重置令牌和过期时间
    user.resetPasswordToken = resetToken;
    user.resetPasswordExpires = resetExpires;
    await user.save();

    // 发送重置密码邮件
    const emailSent = await sendPasswordResetEmail(email, resetToken, user.username);

    if (!emailSent) {
      return res.status(500).json({
        success: false,
        error: {
          message: '邮件发送失败，请稍后再试',
        }
      });
    }

    return res.status(200).json({
      success: true,
      message: '如果该邮箱已注册，我们将发送一封密码重置邮件',
    });
  } catch (error: any) {
    logger.error(`忘记密码处理失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '请求处理失败，请稍后再试',
      }
    });
  }
};

/**
 * 重置密码控制器
 */
export const resetPassword = async (req: Request, res: Response) => {
  try {
    // 检查请求数据验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { email, token, newPassword } = req.body;

    // 查找用户和有效的重置令牌
    const user = await User.findOne({
      email,
      resetPasswordToken: token,
      resetPasswordExpires: { $gt: new Date() } // 确保令牌未过期
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效或已过期的重置链接',
        }
      });
    }

    // 更新密码并清除重置令牌
    user.passwordHash = newPassword; // 会在保存前通过中间件自动加密
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    await user.save();

    return res.status(200).json({
      success: true,
      message: '密码重置成功，请使用新密码登录',
    });
  } catch (error: any) {
    logger.error(`重置密码失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '密码重置失败，请稍后再试',
      }
    });
  }
};

/**
 * 修改密码控制器
 */
export const changePassword = async (req: Request, res: Response) => {
  try {
    // 检查请求数据验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    // 从请求对象中获取用户ID（由认证中间件设置）
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权',
        }
      });
    }

    const { currentPassword, newPassword } = req.body;

    // 查找用户
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: '用户不存在',
        }
      });
    }

    // 验证当前密码
    const isMatch = await user.comparePassword(currentPassword);

    if (!isMatch) {
      return res.status(400).json({
        success: false,
        error: {
          message: '当前密码不正确',
        }
      });
    }

    // 检查新密码是否与当前密码相同
    const isSamePassword = await user.comparePassword(newPassword);

    if (isSamePassword) {
      return res.status(400).json({
        success: false,
        error: {
          message: '新密码不能与当前密码相同',
        }
      });
    }

    // 更新密码
    user.passwordHash = newPassword; // 会在保存前通过中间件自动加密
    await user.save();

    // 发送密码修改通知邮件（异步发送，不阻塞响应）
    sendPasswordChangedEmail(user.email, user.username, new Date()).catch(err => {
      logger.error(`发送密码修改通知邮件失败: ${err.message}`);
    });

    return res.status(200).json({
      success: true,
      message: '密码修改成功',
    });
  } catch (error: any) {
    logger.error(`修改密码失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '密码修改失败，请稍后再试',
      }
    });
  }
};

/**
 * 获取当前用户信息
 */
export const getCurrentUser = async (req: Request, res: Response) => {
  try {
    // 从请求对象中获取用户ID（由认证中间件设置）
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权',
        }
      });
    }

    // 查询用户信息（排除密码哈希）
    const user = await User.findById(userId).select('-passwordHash');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: {
          message: '用户不存在',
        }
      });
    }

    // 返回用户信息
    return res.status(200).json({
      success: true,
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          bio: user.bio,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt,
        }
      }
    });
  } catch (error: any) {
    logger.error(`获取当前用户信息失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取用户信息失败，请稍后再试',
      }
    });
  }
};

/**
 * 用户退出登录控制器
 */
export const logout = async (req: Request, res: Response) => {
  try {
    // 从请求头获取授权信息
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(400).json({
        success: false,
        error: {
          message: '未提供有效的认证令牌'
        }
      });
    }

    // 提取令牌
    const token = authHeader.slice(7);

    // 解析令牌以获取过期时间
    const { decoded, valid, expired } = verifyToken(token);

    if (!valid || expired) {
      return res.status(200).json({
        success: true,
        message: '已退出登录'
      });
    }

    // 计算令牌剩余有效期（秒）
    let expiresIn = 3600; // 默认1小时

    if (decoded && typeof decoded !== 'string') {
      const jwtPayload = decoded as JwtPayload;
      if (jwtPayload.exp) {
        const now = Math.floor(Date.now() / 1000);
        expiresIn = Math.max(1, jwtPayload.exp - now); // 至少1秒
      }
    }

    // 将令牌加入黑名单
    await addTokenToBlacklist(token, expiresIn);

    return res.status(200).json({
      success: true,
      message: '已成功退出登录'
    });
  } catch (error: any) {
    logger.error(`退出登录失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '退出登录失败，请稍后再试'
      }
    });
  }
};