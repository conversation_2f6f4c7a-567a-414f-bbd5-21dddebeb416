import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ai_cloud_notes/services/api_service.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';

/// 主题服务类，用于管理应用的主题设置
class ThemeService extends ChangeNotifier {
  // 主题模式枚举
  static const String _themeModeKey = 'theme_mode';
  static const String _fontColorKey = 'font_color';
  static const String _textSizeKey = 'text_size';
  static const String _titleSizeKey = 'title_size';

  final ApiService _apiService; // ApiService通过构造函数注入
  bool _isSyncing = false;
  bool _isCurrentlyAuthenticated = false; // 用于跟踪当前的认证状态

  // 主题模式
  ThemeMode _themeMode = ThemeMode.system;
  // 字体颜色
  Color _fontColor = Colors.black;
  // 正文字体大小
  double _textSize = 16.0;
  // 标题字体大小
  double _titleSize = 18.0;

  // 字体颜色选项
  final List<Map<String, dynamic>> _fontColorOptions = [
    {
      'name': '黑色',
      'color': Colors.black,
    },
    {
      'name': '白色',
      'color': Colors.white,
    },
    {
      'name': '灰色',
      'color': Colors.grey,
    },
    {
      'name': '蓝色',
      'color': Colors.blue,
    },
    {
      'name': '绿色',
      'color': Colors.green,
    },
  ];

  // 字体大小选项
  final List<Map<String, dynamic>> _fontSizeOptions = [
    {
      'name': '小',
      'size': 14.0,
    },
    {
      'name': '中',
      'size': 16.0,
    },
    {
      'name': '大',
      'size': 18.0,
    },
  ];

  ThemeService(this._apiService) {
    // 初始化时只从本地加载设置
    _loadSettings();
    debugPrint('ThemeService初始化，从本地加载主题设置');
  }

  /// 由 ChangeNotifierProxyProvider 调用，以更新认证状态并可能触发同步
  void updateAuthenticationStatus(bool isAuthenticated) {
    debugPrint('[ThemeService] updateAuthenticationStatus called. New status: $isAuthenticated, Current status: $_isCurrentlyAuthenticated');
    final bool wasPreviouslyAuthenticated = _isCurrentlyAuthenticated;
    _isCurrentlyAuthenticated = isAuthenticated;

    // 当用户从“未认证”变为“已认证”时，触发同步
    if (_isCurrentlyAuthenticated && !wasPreviouslyAuthenticated) {
      debugPrint('[ThemeService] User transitioned to authenticated state. Triggering syncAfterLogin.');
      syncAfterLogin();
    } else if (!_isCurrentlyAuthenticated && wasPreviouslyAuthenticated) {
      debugPrint('[ThemeService] User logged out. Resetting to local/default theme settings.');
      // 用户登出，可以考虑重置为本地默认设置或清除服务器相关的设置
      // 目前_loadSettings会从本地加载，这可能是期望的行为
      // 如果需要更明确的重置，可以在这里添加逻辑
      _loadSettings(); // 重新加载本地设置，这通常是默认或上次保存的
    }
    debugPrint('[ThemeService] updateAuthenticationStatus finished. Current status: $_isCurrentlyAuthenticated');
  }

  /// 登录成功后同步主题设置 (现在由 updateAuthenticationStatus 触发)
  Future<void> syncAfterLogin() async {
    debugPrint('[ThemeService] syncAfterLogin started.');
    if (!_isCurrentlyAuthenticated) {
      debugPrint('[ThemeService] syncAfterLogin: User is not authenticated. Skipping.');
      debugPrint('[ThemeService] syncAfterLogin finished.');
      return;
    }
    if (_isSyncing) {
      debugPrint('[ThemeService] syncAfterLogin: Already syncing. Skipping.');
      debugPrint('[ThemeService] syncAfterLogin finished.');
      return;
    }

    debugPrint('[ThemeService] syncAfterLogin: User is authenticated, preparing to sync theme settings from server.');
    try {
      await _syncFromServerWithRetry();
      debugPrint('[ThemeService] syncAfterLogin: Theme settings sync from server completed.');
    } catch (e) {
      debugPrint('[ThemeService] syncAfterLogin: Theme settings sync from server failed: $e');
    }
    debugPrint('[ThemeService] syncAfterLogin finished.');
  }

  // 带重试机制的服务器同步
  Future<void> _syncFromServerWithRetry({int retryCount = 3, int delaySeconds = 1}) async {
    debugPrint('[ThemeService] _syncFromServerWithRetry started. Retry count: $retryCount, Delay: $delaySeconds');
    // ApiService 已经通过构造函数注入，所以不需要检查 _isInitialized
    if (_isSyncing) {
      debugPrint('[ThemeService] _syncFromServerWithRetry: Already syncing. Skipping.');
      debugPrint('[ThemeService] _syncFromServerWithRetry finished.');
      return;
    }

    for (int i = 0; i < retryCount; i++) {
      debugPrint('[ThemeService] _syncFromServerWithRetry: Attempt ${i + 1}/$retryCount.');
      try {
        await _syncFromServer();
        debugPrint('[ThemeService] _syncFromServerWithRetry: Sync successful on attempt ${i + 1}.');
        debugPrint('[ThemeService] _syncFromServerWithRetry finished.');
        return; // 成功同步，返回
      } catch (e) {
        debugPrint('[ThemeService] _syncFromServerWithRetry: Sync failed on attempt ${i + 1}/$retryCount: $e');
        if (i < retryCount - 1) {
          // 如果还有重试机会，等待一段时间再尝试
          await Future.delayed(Duration(seconds: delaySeconds * (i + 1)));
        }
      }
    }

    // 所有重试都失败后，使用本地设置
    debugPrint('[ThemeService] _syncFromServerWithRetry: All sync attempts failed. Using local settings.');
    debugPrint('[ThemeService] _syncFromServerWithRetry finished.');
  }

  // 获取主题模式
  ThemeMode get themeMode => _themeMode;

  // 获取字体颜色
  Color get fontColor => _fontColor;

  // 获取正文字体大小
  double get textSize => _textSize;

  // 获取标题字体大小
  double get titleSize => _titleSize;

  // 获取字体颜色选项
  List<Map<String, dynamic>> get fontColorOptions => _fontColorOptions;

  // 获取字体大小选项
  List<Map<String, dynamic>> get fontSizeOptions => _fontSizeOptions;

  // 是否正在同步
  bool get isSyncing => _isSyncing;

  // 从本地加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载主题模式
      final themeModeIndex = prefs.getInt(_themeModeKey) ?? 0;
      _themeMode = ThemeMode.values[themeModeIndex < ThemeMode.values.length ? themeModeIndex : 0];

      // 加载字体颜色
      final fontColorValue = prefs.getInt(_fontColorKey) ?? Colors.black.value;
      try {
        _fontColor = Color(fontColorValue);
      } catch (e) {
        debugPrint('加载字体颜色错误: $e');
        _fontColor = Colors.black; // 默认为黑色
      }

      // 加载字体大小
      _textSize = prefs.getDouble(_textSizeKey) ?? 16.0;
      _titleSize = prefs.getDouble(_titleSizeKey) ?? 18.0;

      debugPrint('成功从本地加载主题设置');
    } catch (e) {
      debugPrint('从本地加载主题设置失败: $e');
      // 使用默认设置
      _themeMode = ThemeMode.system;
      _fontColor = Colors.black;
      _textSize = 16.0;
      _titleSize = 18.0;
    }

    notifyListeners();
  }

  // 保存设置到本地
  Future<void> _saveSettingsLocally({
    required ThemeMode themeMode,
    required Color fontColor,
    required double textSize,
    required double titleSize,
  }) async {
    debugPrint('[ThemeService] _saveSettingsLocally started. ThemeMode: $themeMode, FontColor: $fontColor, TextSize: $textSize, TitleSize: $titleSize');
    final prefs = await SharedPreferences.getInstance();
    debugPrint('[ThemeService] _saveSettingsLocally: SharedPreferences instance obtained.');

    // 保存主题模式
    await prefs.setInt(_themeModeKey, themeMode.index);
    _themeMode = themeMode;
    debugPrint('[ThemeService] _saveSettingsLocally: ThemeMode saved.');

    // 保存字体颜色
    await prefs.setInt(_fontColorKey, fontColor.value);
    _fontColor = fontColor;
    debugPrint('[ThemeService] _saveSettingsLocally: FontColor saved.');

    // 保存字体大小
    await prefs.setDouble(_textSizeKey, textSize);
    _textSize = textSize;
    debugPrint('[ThemeService] _saveSettingsLocally: TextSize saved.');

    await prefs.setDouble(_titleSizeKey, titleSize);
    _titleSize = titleSize;
    debugPrint('[ThemeService] _saveSettingsLocally: TitleSize saved.');
    
    debugPrint('[ThemeService] _saveSettingsLocally: Calling notifyListeners().');
    notifyListeners();
    debugPrint('[ThemeService] _saveSettingsLocally finished.');
  }

  // 从服务器同步设置
  Future<void> _syncFromServer() async {
    debugPrint('[ThemeService] _syncFromServer started.');
    // ApiService 已经通过构造函数注入
    if (_isSyncing) {
      debugPrint('[ThemeService] _syncFromServer: Already syncing. Skipping.');
      debugPrint('[ThemeService] _syncFromServer finished.');
      return;
    }

    _isSyncing = true;
    notifyListeners();
    debugPrint('[ThemeService] _syncFromServer: isSyncing set to true and listeners notified.');

    try {
      debugPrint('[ThemeService] _syncFromServer: Calling API _apiService.getUserThemeSettings().');
      final settings = await _apiService.getUserThemeSettings();
      debugPrint('[ThemeService] _syncFromServer: API call _apiService.getUserThemeSettings() completed. Settings: $settings');

      if (settings != null) {
        debugPrint('[ThemeService] _syncFromServer: Settings received from server. Parsing data...');
        try {
          // 解析主题模式
          final themeModeIndex = settings['themeMode'] ?? 0;
          final themeMode = ThemeMode.values[themeModeIndex < ThemeMode.values.length ? themeModeIndex : 0];
          debugPrint('[ThemeService] _syncFromServer: Parsed themeMode: $themeMode');

          // 解析字体颜色
          Color fontColor;
          try {
            final fontColorHex = settings['fontColor'] ?? '#000000';
            fontColor = Color(int.parse(fontColorHex.replaceFirst('#', '0xFF')));
          } catch (e) {
            debugPrint('[ThemeService] _syncFromServer: Error parsing fontColor: $e. Defaulting to black.');
            fontColor = Colors.black; // 默认为黑色
          }
          debugPrint('[ThemeService] _syncFromServer: Parsed fontColor: $fontColor');

          // 解析字体大小
          final textSize = (settings['textSize'] ?? 16.0).toDouble();
          final titleSize = (settings['titleSize'] ?? 18.0).toDouble();
          debugPrint('[ThemeService] _syncFromServer: Parsed textSize: $textSize, titleSize: $titleSize');
          
          debugPrint('[ThemeService] _syncFromServer: Calling _saveSettingsLocally().');
          // 保存到本地
          await _saveSettingsLocally(
            themeMode: themeMode,
            fontColor: fontColor,
            textSize: textSize,
            titleSize: titleSize,
          );
          debugPrint('[ThemeService] _syncFromServer: _saveSettingsLocally() completed.');

          debugPrint('[ThemeService] _syncFromServer: Successfully synced theme settings from server.');
        } catch (e) {
          debugPrint('[ThemeService] _syncFromServer: Error processing server settings data: $e');
          throw e; // 重新抛出异常以触发重试
        }
      } else {
        debugPrint('[ThemeService] _syncFromServer: No settings received from server.');
      }
    } catch (e) {
      debugPrint('[ThemeService] _syncFromServer: Failed to sync theme settings from server: $e');
      // 当发生异常时，抛出异常以便重试机制捕获
      throw e;
    } finally {
      _isSyncing = false;
      notifyListeners();
      debugPrint('[ThemeService] _syncFromServer: isSyncing set to false and listeners notified.');
      debugPrint('[ThemeService] _syncFromServer finished.');
    }
  }

  // 同步设置到服务器
  Future<void> _syncToServer({
    required ThemeMode themeMode,
    required Color fontColor,
    required double textSize,
    required double titleSize,
  }) async {
    // ApiService 已经通过构造函数注入
    try {
      // 将颜色转换为十六进制字符串
      final fontColorHex = '#${fontColor.value.toRadixString(16).substring(2)}';

      await _apiService.updateUserThemeSettings(
        themeMode: themeMode.index,
        fontColor: fontColorHex,
        textSize: textSize,
        titleSize: titleSize,
      );
    } catch (e) {
      debugPrint('同步主题设置到服务器失败: $e');
    }
  }

  // 保存设置 (本地 + 服务器)
  Future<void> saveSettings({
    required ThemeMode themeMode,
    required Color fontColor,
    required double textSize,
    required double titleSize,
  }) async {
    // 先保存到本地
    await _saveSettingsLocally(
      themeMode: themeMode,
      fontColor: fontColor,
      textSize: textSize,
      titleSize: titleSize,
    );

    // ApiService 已经通过构造函数注入，总是尝试同步（如果需要）
    // 可以在 _apiService 调用前增加网络状态检查等
    _syncToServer(
      themeMode: themeMode,
      fontColor: fontColor,
      textSize: textSize,
      titleSize: titleSize,
    );
  }

  // 根据主题模式自动调整字体颜色
  Color getAutoFontColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? Colors.white : Colors.black;
  }
}