import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:markdown/markdown.dart' as md;
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:url_launcher/url_launcher.dart';

/// 自定义有序列表构建器，实现多级有序列表不同符号显示
class OrderedListBuilder extends MarkdownElementBuilder {
  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    // 获取列表项级别 - 通过参数传递，默认顶层为0
    final int level = _currentLevel;
    _currentLevel++; // 增加层级供子列表使用

    // 创建有序列表项内容
    final result = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _buildListItems(element, level, preferredStyle),
    );

    _currentLevel--; // 恢复层级
    return result;
  }

  // 使用实例变量跟踪当前层级
  int _currentLevel = 0;

  // 构建列表项
  List<Widget> _buildListItems(
      md.Element element, int level, TextStyle? preferredStyle) {
    List<Widget> items = [];

    if (element.children != null) {
      int index = 1;

      for (var child in element.children!) {
        if (child is md.Element && child.tag == 'li') {
          // 根据级别选择不同的列表符号
          String marker = _getOrderedListMarker(index, level);

          // 创建列表项
          items.add(
            Padding(
              padding: EdgeInsets.only(left: level * 16.0, bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 列表标记
                  Container(
                    width: 20 + level * 4.0, // 根据层级调整标记宽度
                    alignment: Alignment.centerRight,
                    child: Text(
                      marker,
                      style: preferredStyle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 列表内容
                  Expanded(
                    child: _buildListItemContent(child, level, preferredStyle),
                  ),
                ],
              ),
            ),
          );

          index++;
        }
      }
    }

    return items;
  }

  // 构建列表项内容 - 需要传递 level
  Widget _buildListItemContent(
      md.Element element, int currentLevel, TextStyle? preferredStyle) {
    List<Widget> contentWidgets = [];

    if (element.children != null) {
      for (var child in element.children!) {
        if (child is md.Text) {
          // 文本内容
          contentWidgets.add(
            Text(
              child.text,
              style: preferredStyle,
            ),
          );
        } else if (child is md.Element) {
          if (child.tag == 'ol') {
            // 嵌套有序列表 - 创建新的 Builder 实例并设置层级
            final nestedBuilder = OrderedListBuilder();
            nestedBuilder._currentLevel = currentLevel + 1;
            contentWidgets.add(
              nestedBuilder.visitElementAfter(child, preferredStyle) ??
                  Container(),
            );
          } else if (child.tag == 'ul') {
            // 嵌套无序列表 - 创建新的 Builder 实例并设置层级
            final nestedBuilder = UnorderedListBuilder();
            nestedBuilder._currentLevel = currentLevel + 1;
            contentWidgets.add(
              nestedBuilder.visitElementAfter(child, preferredStyle) ??
                  Container(),
            );
          } else {
            // 其他元素（段落等）
            contentWidgets.add(
              Text(
                child.textContent,
                style: preferredStyle,
              ),
            );
          }
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: contentWidgets,
    );
  }

  // 根据级别获取不同的有序列表标记
  String _getOrderedListMarker(int index, int level) {
    switch (level % 3) {
      // 使用模3循环标记样式
      case 0: // 第一级：数字
        return '$index.';
      case 1: // 第二级：小写罗马数字
        return '${_toRoman(index).toLowerCase()}.';
      case 2: // 第三级：小写字母
        return '${String.fromCharCode(96 + index)}.';
      default: // 不会执行到这里
        return '$index.';
    }
  }

  // 转换为罗马数字
  String _toRoman(int number) {
    if (number <= 0 || number > 3999) return number.toString();

    final List<String> romanM = ['', 'M', 'MM', 'MMM'];
    final List<String> romanC = [
      '',
      'C',
      'CC',
      'CCC',
      'CD',
      'D',
      'DC',
      'DCC',
      'DCCC',
      'CM'
    ];
    final List<String> romanX = [
      '',
      'X',
      'XX',
      'XXX',
      'XL',
      'L',
      'LX',
      'LXX',
      'LXXX',
      'XC'
    ];
    final List<String> romanI = [
      '',
      'I',
      'II',
      'III',
      'IV',
      'V',
      'VI',
      'VII',
      'VIII',
      'IX'
    ];

    return romanM[(number / 1000).floor()] +
        romanC[((number % 1000) / 100).floor()] +
        romanX[((number % 100) / 10).floor()] +
        romanI[number % 10];
  }
}

/// 自定义无序列表构建器，实现多级无序列表不同符号显示
class UnorderedListBuilder extends MarkdownElementBuilder {
  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    // 获取列表项级别 - 通过参数传递，默认顶层为0
    final int level = _currentLevel;
    _currentLevel++; // 增加层级供子列表使用

    // 创建无序列表项内容
    final result = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _buildListItems(element, level, preferredStyle),
    );

    _currentLevel--; // 恢复层级
    return result;
  }

  // 使用实例变量跟踪当前层级
  int _currentLevel = 0;

  // 构建列表项
  List<Widget> _buildListItems(
      md.Element element, int level, TextStyle? preferredStyle) {
    List<Widget> items = [];

    if (element.children != null) {
      for (var child in element.children!) {
        if (child is md.Element && child.tag == 'li') {
          // 根据级别选择不同的列表符号
          String marker = _getUnorderedListMarker(level);

          // 创建列表项
          items.add(
            Padding(
              padding: EdgeInsets.only(left: level * 16.0, bottom: 4.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 列表标记
                  Container(
                    width: 20, // 固定宽度
                    alignment: Alignment.center,
                    child: Text(
                      marker,
                      style: preferredStyle?.copyWith(
                          fontSize: 10 + level * 2.0), // 根据层级调整大小
                    ),
                  ),
                  const SizedBox(width: 8),
                  // 列表内容
                  Expanded(
                    child: _buildListItemContent(child, level, preferredStyle),
                  ),
                ],
              ),
            ),
          );
        }
      }
    }

    return items;
  }

  // 构建列表项内容 - 需要传递 level
  Widget _buildListItemContent(
      md.Element element, int currentLevel, TextStyle? preferredStyle) {
    List<Widget> contentWidgets = [];

    if (element.children != null) {
      for (var child in element.children!) {
        if (child is md.Text) {
          // 文本内容
          contentWidgets.add(
            Text(
              child.text,
              style: preferredStyle,
            ),
          );
        } else if (child is md.Element) {
          if (child.tag == 'ol') {
            // 嵌套有序列表 - 创建新的 Builder 实例并设置层级
            final nestedBuilder = OrderedListBuilder();
            nestedBuilder._currentLevel = currentLevel + 1;
            contentWidgets.add(
              nestedBuilder.visitElementAfter(child, preferredStyle) ??
                  Container(),
            );
          } else if (child.tag == 'ul') {
            // 嵌套无序列表 - 创建新的 Builder 实例并设置层级
            final nestedBuilder = UnorderedListBuilder();
            nestedBuilder._currentLevel = currentLevel + 1;
            contentWidgets.add(
              nestedBuilder.visitElementAfter(child, preferredStyle) ??
                  Container(),
            );
          } else {
            // 其他元素（段落等）
            contentWidgets.add(
              Text(
                child.textContent,
                style: preferredStyle,
              ),
            );
          }
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: contentWidgets,
    );
  }

  // 根据级别获取不同的无序列表标记
  String _getUnorderedListMarker(int level) {
    switch (level % 3) {
      // 使用模3循环标记样式
      case 0: // 第一级：圆点
        return '•';
      case 1: // 第二级：中空圆点
        return '○';
      case 2: // 第三级：方块
        return '■';
      default: // 不会执行到这里
        return '•';
    }
  }
}

/// 自定义表格构建器，用于改善表格渲染
/// 实现MarkdownElementBuilder接口
class CustomTableBuilder extends MarkdownElementBuilder {
  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    return Builder(builder: (context) {
      // 分析表格结构
      List<TableRow> rows = [];
      List<Widget> children = [];
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;

      try {
        // 尝试解析表格内容
        if (element.children != null) {
          for (var child in element.children!) {
            if (child is md.Element) {
              if (child.tag == 'thead') {
                // 处理表头
                if (child.children != null && child.children!.isNotEmpty) {
                  for (var tr in child.children!) {
                    if (tr is md.Element &&
                        tr.tag == 'tr' &&
                        tr.children != null) {
                      List<Widget> cells = [];
                      for (var td in tr.children!) {
                        if (td is md.Element && td.tag == 'th') {
                          cells.add(
                            TableCell(
                              child: Container(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  td.textContent,
                                  style: (preferredStyle ?? const TextStyle())
                                      .copyWith(fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          );
                        }
                      }
                      if (cells.isNotEmpty) {
                        rows.add(TableRow(
                          decoration: BoxDecoration(
                              color: isDarkMode
                                  ? Colors.grey.shade800
                                  : Colors.grey.shade200),
                          children: cells,
                        ));
                      }
                    }
                  }
                }
              } else if (child.tag == 'tbody') {
                // 处理表体
                if (child.children != null) {
                  for (var tr in child.children!) {
                    if (tr is md.Element &&
                        tr.tag == 'tr' &&
                        tr.children != null) {
                      List<Widget> cells = [];
                      for (var td in tr.children!) {
                        if (td is md.Element && td.tag == 'td') {
                          cells.add(
                            TableCell(
                              child: Container(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  td.textContent,
                                  style: preferredStyle,
                                ),
                              ),
                            ),
                          );
                        }
                      }
                      if (cells.isNotEmpty) {
                        rows.add(TableRow(children: cells));
                      }
                    }
                  }
                }
              }
            }
          }
        }
      } catch (e) {
        print('ERROR: [CustomTableBuilder] 解析表格失败: $e');
        // 如果解析失败，创建一个简单的表格
        rows = [
          TableRow(
            decoration: BoxDecoration(color: Colors.grey.shade200),
            children: [
              for (int i = 0; i < 3; i++)
                TableCell(
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      '列 ${i + 1}',
                      style: (preferredStyle ?? const TextStyle())
                          .copyWith(fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          for (int row = 0; row < 2; row++)
            TableRow(
              children: [
                for (int col = 0; col < 3; col++)
                  TableCell(
                    child: Container(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        '单元格 ${row + 1}-${col + 1}',
                        style: preferredStyle,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
        ];
      }

      // 创建表格容器
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          border: Border.all(
              color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Table(
          border: TableBorder.all(
              color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300),
          defaultVerticalAlignment: TableCellVerticalAlignment.middle,
          children: rows,
        ),
      );
    });
  }
}

/// 自定义代码块构建器
/// 用于渲染代码块元素，支持语法高亮、行号、复制功能
class CodeBlockBuilder extends MarkdownElementBuilder {
  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    String language = '';
    String content = '';

    // 处理不同的标签类型
    if (element.tag == 'pre') {
      // 处理 pre 标签，查找其中的 code 标签
      md.Element? codeElement;
      if (element.children != null && element.children!.isNotEmpty) {
        for (var child in element.children!) {
          if (child is md.Element && child.tag == 'code') {
            codeElement = child;
            break;
          }
        }
      }

      if (codeElement == null) {
        return null;
      }

      // 尝试从 code 标签的 class 获取语言
      if (codeElement.attributes.containsKey('class')) {
        final String classAttr = codeElement.attributes['class']!;
        if (classAttr.startsWith('language-')) {
          language = classAttr.substring(9);
        }
      }

      content = codeElement.textContent;
    } else if (element.tag == 'code') {
      // 处理单独的 code 标签
      content = element.textContent;

      // 这是行内代码，不需要特殊处理
      if (!content.contains('\n')) {
        return null;
      }

      // 尝试从 class 获取语言
      if (element.attributes.containsKey('class')) {
        final String classAttr = element.attributes['class']!;
        if (classAttr.startsWith('language-')) {
          language = classAttr.substring(9);
        }
      }
    } else {
      return null;
    }

    // 处理代码内容，正确分行
    final List<String> codeLines = content.split('\n');

    // 移除最后一行空行（如果存在）
    if (codeLines.isNotEmpty && codeLines.last.trim().isEmpty) {
      codeLines.removeLast();
    }

    // 移除第一行空行（如果存在）
    if (codeLines.isNotEmpty && codeLines.first.trim().isEmpty) {
      codeLines.removeAt(0);
    }

    // 检查是否有实际的代码内容
    bool hasActualCode = false;
    for (var line in codeLines) {
      if (line.trim().isNotEmpty) {
        hasActualCode = true;
        break;
      }
    }

    // 如果没有实际代码内容，显示空代码块
    if (!hasActualCode) {
      return _buildEmptyCodeBlock(language);
    }

    // 计算行号宽度（基于最大行号的位数）
    final int maxLineNumber = codeLines.length;
    final int lineNumberWidth = maxLineNumber.toString().length;

    return _buildCodeBlock(language, codeLines, lineNumberWidth);
  }

  /// 构建空代码块
  Widget _buildEmptyCodeBlock(String language) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: const Color(0xFF1E1E1E),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCodeBlockHeader(language, ''),
          Container(
            padding: const EdgeInsets.all(16),
            height: 40,
            width: double.infinity,
            child: const Center(
              child: Text(
                '空代码块',
                style: TextStyle(
                  color: Color(0xFF6A737D),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建完整代码块
  Widget _buildCodeBlock(
      String language, List<String> codeLines, int lineNumberWidth) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: const Color(0xFF1E1E1E),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCodeBlockHeader(language, codeLines.join('\n')),
          _buildCodeBlockContent(codeLines, lineNumberWidth),
        ],
      ),
    );
  }

  /// 构建代码块头部
  Widget _buildCodeBlockHeader(String language, String content) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF2D2D2D),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade800,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 语言标识和行数信息
          Row(
            children: [
              Icon(
                _getLanguageIcon(language),
                color: Colors.white70,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                language.isNotEmpty ? language.toUpperCase() : '代码',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (content.isNotEmpty) ...[
                const SizedBox(width: 12),
                Text(
                  '${content.split('\n').length} 行',
                  style: const TextStyle(
                    color: Color(0xFF6A737D),
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
          // 复制按钮
          if (content.isNotEmpty) _buildCopyButton(content),
        ],
      ),
    );
  }

  /// 构建代码块内容
  Widget _buildCodeBlockContent(List<String> codeLines, int lineNumberWidth) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 行号列
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: List.generate(
                codeLines.length,
                (index) => Container(
                  padding: const EdgeInsets.only(right: 12),
                  height: 20,
                  alignment: Alignment.centerRight,
                  child: Text(
                    '${index + 1}'.padLeft(lineNumberWidth),
                    style: const TextStyle(
                      color: Color(0xFF6A737D),
                      fontSize: 14,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ),
            ),
            // 分隔线
            Container(
              width: 1,
              height: 20.0 * codeLines.length,
              color: const Color(0xFF3E3E3E),
              margin: const EdgeInsets.only(right: 12),
            ),
            // 代码内容
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: List.generate(
                codeLines.length,
                (index) => Container(
                  height: 20,
                  alignment: Alignment.centerLeft,
                  child: Text(
                    codeLines[index],
                    style: TextStyle(
                      color: _getCodeTextColor(codeLines[index]),
                      fontSize: 14,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取语言对应的图标
  IconData _getLanguageIcon(String language) {
    switch (language.toLowerCase()) {
      case 'javascript':
      case 'js':
        return Icons.code;
      case 'python':
      case 'py':
        return Icons.code;
      case 'java':
        return Icons.code;
      case 'dart':
        return Icons.code;
      case 'html':
        return Icons.web;
      case 'css':
        return Icons.palette;
      case 'json':
        return Icons.data_object;
      case 'xml':
        return Icons.code;
      case 'sql':
        return Icons.storage;
      case 'bash':
      case 'shell':
        return Icons.terminal;
      default:
        return Icons.code;
    }
  }

  /// 构建复制按钮
  Widget _buildCopyButton(String content) {
    return Builder(
      builder: (context) => InkWell(
        onTap: () => _copyToClipboard(context, content),
        borderRadius: BorderRadius.circular(4),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.grey.shade700,
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.copy,
                color: Colors.white70,
                size: 14,
              ),
              SizedBox(width: 4),
              Text(
                '复制',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard(BuildContext context, String content) {
    // 复制到剪贴板
    Clipboard.setData(ClipboardData(text: content));

    // 显示复制成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('代码已复制到剪贴板'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// 获取代码文本颜色（简单的语法高亮）
  Color _getCodeTextColor(String line) {
    final trimmedLine = line.trim();

    // 注释
    if (trimmedLine.startsWith('//') ||
        trimmedLine.startsWith('#') ||
        trimmedLine.startsWith('/*') ||
        trimmedLine.startsWith('*') ||
        trimmedLine.startsWith('<!--')) {
      return const Color(0xFF6A9955); // 绿色注释
    }

    // 字符串
    if (trimmedLine.contains('"') || trimmedLine.contains("'")) {
      return const Color(0xFFCE9178); // 橙色字符串
    }

    // 关键字
    if (_isKeyword(trimmedLine)) {
      return const Color(0xFF569CD6); // 蓝色关键字
    }

    // 数字
    if (RegExp(r'^\s*\d+').hasMatch(trimmedLine)) {
      return const Color(0xFFB5CEA8); // 浅绿色数字
    }

    // 默认白色
    return Colors.white;
  }

  /// 检查是否为关键字
  bool _isKeyword(String line) {
    final keywords = [
      'function',
      'var',
      'let',
      'const',
      'if',
      'else',
      'for',
      'while',
      'return',
      'class',
      'import',
      'export',
      'from',
      'def',
      'print',
      'public',
      'private',
      'static',
      'void',
      'int',
      'String',
      'bool',
      'true',
      'false',
      'null',
      'undefined',
      'this',
      'super',
      'new'
    ];

    for (String keyword in keywords) {
      if (line.contains(RegExp(r'\b' + keyword + r'\b'))) {
        return true;
      }
    }
    return false;
  }
}

/// 自定义引用块构建器
/// 用于渲染引用块元素
class BlockquoteBuilder extends MarkdownElementBuilder {
  @override
  Widget? visitElementAfter(md.Element element, TextStyle? preferredStyle) {
    return Builder(builder: (context) {
      final isDarkMode = Theme.of(context).brightness == Brightness.dark;
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(4),
          border: Border(
            left: BorderSide(
              color: AppTheme.primaryColor,
              width: 4,
            ),
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Text(
          element.textContent,
          style: (preferredStyle ?? const TextStyle()).copyWith(
            color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    });
  }
}

/// Markdown编辑器组件
/// 提供编辑和预览Markdown内容的功能
class MarkdownEditor extends StatefulWidget {
  final String initialContent;
  final FocusNode focusNode;
  final bool readOnly;
  final Function(String)? onChanged;

  const MarkdownEditor({
    Key? key,
    this.initialContent = '',
    required this.focusNode,
    this.readOnly = false,
    this.onChanged,
  }) : super(key: key);

  @override
  State<MarkdownEditor> createState() => MarkdownEditorState();
}

class MarkdownEditorState extends State<MarkdownEditor> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialContent);
    if (kDebugMode) {
      print(
          'DEBUG: [MarkdownEditor] 初始化编辑器，内容长度: ${widget.initialContent.length}');
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(MarkdownEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialContent != oldWidget.initialContent) {
      // 避免光标位置重置
      final cursorPosition = _controller.selection.baseOffset;
      _controller.text = widget.initialContent;
      if (cursorPosition >= 0 && cursorPosition <= _controller.text.length) {
        _controller.selection = TextSelection.fromPosition(
          TextPosition(offset: cursorPosition),
        );
      }
    }
  }

  // 获取当前编辑器内容，保留原始格式
  String getContent() {
    // 直接返回原始内容，不做额外处理
    String content = _controller.text;

    // 确保以换行结尾
    if (!content.endsWith('\n')) {
      content += '\n';
    }

    if (kDebugMode) {
      print('DEBUG: [MarkdownEditor] 获取原始 Markdown 内容，长度: ${content.length}');
    }
    return content;
  }

  // 获取当前光标位置
  int getCursorPosition() {
    return _controller.selection.baseOffset;
  }

  // 在光标位置插入文本
  void insertTextAtCursor(String text) {
    // 获取当前光标位置
    final cursorPosition = _controller.selection.baseOffset;
    if (cursorPosition < 0) return;

    // 获取当前文本
    final currentText = _controller.text;

    // 在光标位置插入文本
    final newText = currentText.substring(0, cursorPosition) +
        text +
        currentText.substring(cursorPosition);

    // 更新文本和光标位置
    _controller.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: cursorPosition + text.length),
    );

    // 触发onChange回调
    if (widget.onChanged != null) {
      widget.onChanged!(newText);
    }

    // 确保编辑器保持焦点
    widget.focusNode.requestFocus();
  }

  // 获取用于渲染的内容，处理换行等问题
  String getRenderContent() {
    String content = _controller.text;
    if (kDebugMode) {
      print('DEBUG_RENDER: [Step 1] 原始内容长度: ${content.length}');
      print(
          'DEBUG_RENDER: [Step 1] 原始内容前100字符: "${content.length > 100 ? content.substring(0, 100) : content}"');
    }

    // 先保护特殊结构，防止被换行处理破坏
    // 1. 保护表格
    List<String> tables = [];
    final tableRegex = RegExp(
        r'\|[^\n]*\|[\s]*\n[\s]*\|[-:| ]+\|[\s]*\n([\s]*\|[^\n]*\|[\s]*\n)*',
        multiLine: true);

    content = content.replaceAllMapped(tableRegex, (match) {
      tables.add(match.group(0)!);
      print(
          'DEBUG_RENDER: [Step 2] 找到表格 ${tables.length - 1}: "${match.group(0)!.replaceAll('\n', '\\n')}"');
      return "TABLE_PLACEHOLDER_${tables.length - 1}";
    });
    print(
        'DEBUG_RENDER: [Step 2] 表格保护后内容: "${content.replaceAll('\n', '\\n')}"');

    // 2. 保护代码块
    List<String> codeBlocks = [];
    // 使用更精确的正则表达式来匹配代码块
    // 匹配```开始和```结束的代码块，包括可能的语言标记
    // 使用非贪婪模式匹配，确保正确匹配嵌套的代码块
    final codeBlockRegex =
        RegExp(r'```([a-zA-Z0-9]*)\s*[\s\S]*?```', multiLine: true);

    content = content.replaceAllMapped(codeBlockRegex, (match) {
      String codeBlock = match.group(0)!;
      String? language = match.group(1);
      print(
          'DEBUG_RENDER: [Step 3] 找到代码块 ${codeBlocks.length}: ${codeBlock.length} 字节, 语言: $language');
      print(
          'DEBUG_RENDER: [Step 3] 代码块内容: "${codeBlock.replaceAll('\n', '\\n')}"');
      codeBlocks.add(codeBlock);
      return "CODE_PLACEHOLDER_${codeBlocks.length - 1}";
    });
    print(
        'DEBUG_RENDER: [Step 3] 代码块保护后内容: "${content.replaceAll('\n', '\\n')}"');

    // 3. 保护列表
    List<String> lists = [];
    final listRegex =
        RegExp(r'(^|\n)([ ]*[-*+][ ].+[\s\S]*?)(?=\n[^ -]|$)', multiLine: true);

    content = content.replaceAllMapped(listRegex, (match) {
      lists.add(match.group(0)!);
      print(
          'DEBUG_RENDER: [Step 4] 找到列表 ${lists.length - 1}: "${match.group(0)!.replaceAll('\n', '\\n')}"');
      return "LIST_PLACEHOLDER_${lists.length - 1}";
    });
    print(
        'DEBUG_RENDER: [Step 4] 列表保护后内容: "${content.replaceAll('\n', '\\n')}"');

    // 4. 保护有序列表
    List<String> orderedLists = [];
    final orderedListRegex =
        RegExp(r'(^|\n)([ ]*\d+\.[ ].+[\s\S]*?)(?=\n[^\d]|$)', multiLine: true);

    content = content.replaceAllMapped(orderedListRegex, (match) {
      orderedLists.add(match.group(0)!);
      print(
          'DEBUG_RENDER: [Step 5] 找到有序列表 ${orderedLists.length - 1}: "${match.group(0)!.replaceAll('\n', '\\n')}"');
      return "ORDERED_LIST_PLACEHOLDER_${orderedLists.length - 1}";
    });
    print(
        'DEBUG_RENDER: [Step 5] 有序列表保护后内容: "${content.replaceAll('\n', '\\n')}"');

    // 5. 保护空行序列
    List<String> emptyLines = [];
    // 匹配连续的空行（两个或更多）
    final emptyLineRegex = RegExp(r'\n{2,}', multiLine: true);

    content = content.replaceAllMapped(emptyLineRegex, (match) {
      emptyLines.add(match.group(0)!);
      return "EMPTY_LINE_PLACEHOLDER_${emptyLines.length - 1}";
    });

    // 处理换行，确保每个段落之间有空行
    // 对于标准 Markdown 渲染，我们需要确保段落之间有空行
    // 将单个换行替换为两个换行，但保留空行占位符
    // content = content.replaceAll('\n', '\n\n'); // 注释掉：这会导致占位符恢复失败，产生多余数字序号

    // 恢复特殊结构
    print(
        'DEBUG_RENDER: [Step 6] 开始恢复占位符，当前内容: "${content.replaceAll('\n', '\\n')}"');

    // 1. 恢复表格
    print('DEBUG_RENDER: [Step 6.1] 恢复 ${tables.length} 个表格');
    for (int i = 0; i < tables.length; i++) {
      String placeholder = "TABLE_PLACEHOLDER_$i";
      if (content.contains(placeholder)) {
        content = content.replaceAll(placeholder, tables[i]);
        print('DEBUG_RENDER: [Step 6.1] 成功恢复表格 $i');
      } else {
        print('DEBUG_RENDER: [Step 6.1] 警告：找不到表格占位符 $placeholder');
      }
    }

    // 2. 恢复代码块
    print('DEBUG_RENDER: [Step 6.2] 恢复 ${codeBlocks.length} 个代码块');
    for (int i = 0; i < codeBlocks.length; i++) {
      String placeholder = "CODE_PLACEHOLDER_$i";
      if (content.contains(placeholder)) {
        content = content.replaceAll(placeholder, codeBlocks[i]);
        print('DEBUG_RENDER: [Step 6.2] 成功恢复代码块 $i');
      } else {
        print('DEBUG_RENDER: [Step 6.2] 警告：找不到代码块占位符 $placeholder');
      }
    }

    // 3. 恢复列表
    print('DEBUG_RENDER: [Step 6.3] 恢复 ${lists.length} 个列表');
    for (int i = 0; i < lists.length; i++) {
      String placeholder = "LIST_PLACEHOLDER_$i";
      if (content.contains(placeholder)) {
        content = content.replaceAll(placeholder, lists[i]);
        print('DEBUG_RENDER: [Step 6.3] 成功恢复列表 $i');
      } else {
        print('DEBUG_RENDER: [Step 6.3] 警告：找不到列表占位符 $placeholder');
      }
    }

    // 4. 恢复有序列表
    print('DEBUG_RENDER: [Step 6.4] 恢复 ${orderedLists.length} 个有序列表');
    for (int i = 0; i < orderedLists.length; i++) {
      String placeholder = "ORDERED_LIST_PLACEHOLDER_$i";
      if (content.contains(placeholder)) {
        content = content.replaceAll(placeholder, orderedLists[i]);
        print('DEBUG_RENDER: [Step 6.4] 成功恢复有序列表 $i');
      } else {
        print('DEBUG_RENDER: [Step 6.4] 警告：找不到有序列表占位符 $placeholder');
      }
    }

    // 5. 恢复空行
    print('DEBUG_RENDER: [Step 6.5] 恢复 ${emptyLines.length} 个空行序列');
    for (int i = 0; i < emptyLines.length; i++) {
      String placeholder = "EMPTY_LINE_PLACEHOLDER_$i";
      if (content.contains(placeholder)) {
        content = content.replaceAll(placeholder, emptyLines[i]);
        print('DEBUG_RENDER: [Step 6.5] 成功恢复空行序列 $i');
      } else {
        print('DEBUG_RENDER: [Step 6.5] 警告：找不到空行占位符 $placeholder');
      }
    }

    print(
        'DEBUG_RENDER: [Step 6] 占位符恢复完成，当前内容: "${content.replaceAll('\n', '\\n')}"');

    // 确保以换行结尾
    if (!content.endsWith('\n')) {
      content += '\n';
    }

    // 移除可能导致问题的尾部数字（0-9）- 占位符恢复失败的残留
    for (int i = 0; i <= 9; i++) {
      if (content.endsWith('$i\n')) {
        content = content.substring(0, content.length - 2) + '\n';
        break;
      }
    }

    // 移除任何单独的数字（0-9）- 这些可能是占位符恢复失败后的残留
    for (int i = 0; i <= 9; i++) {
      content = content.replaceAll(RegExp(r'\n$i\n'), '\n\n');
      content = content.replaceAll(RegExp(r'^$i\n'), '\n'); // 处理开头的数字
    }

    if (kDebugMode) {
      print('DEBUG: [MarkdownEditor] 处理后的渲染内容长度: ${content.length}');
    }
    return content;
  }

  // 获取文本控制器，用于外部操作
  TextEditingController getTextController() {
    return _controller;
  }

  @override
  Widget build(BuildContext context) {
    return widget.readOnly ? _buildMarkdownPreview() : _buildMarkdownEditor();
  }

  Widget _buildMarkdownEditor() {
    return Container(
      height: double.infinity,
      child: TextField(
        controller: _controller,
        focusNode: widget.focusNode,
        maxLines: null,
        expands: true,
        textAlignVertical: TextAlignVertical.top, // 确保文本从顶部开始
        readOnly: widget.readOnly,
        style: TextStyle(
          fontSize: 16,
          height: 1.5,
          fontFamily: 'monospace', // 使用等宽字体以便于编辑代码块
          color: Theme.of(context).textTheme.bodyMedium?.color,
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
          hintText: '输入Markdown内容...',
          hintStyle: TextStyle(
            color: Theme.of(context).hintColor,
          ),
          fillColor: Theme.of(context).scaffoldBackgroundColor,
          filled: true,
          alignLabelWithHint: true, // 确保提示文本也从顶部开始
          isCollapsed: false, // 不折叠装饰，保持内边距
        ),
        onChanged: (value) {
          if (widget.onChanged != null) {
            widget.onChanged!(value);
          }
        },
        // 添加onTap处理器，确保点击编辑器时不会关闭预测栏
        onTap: () {
          // 点击编辑器时，确保焦点在编辑器上
          widget.focusNode.requestFocus();
        },
      ),
    );
  }

  Widget _buildMarkdownPreview() {
    if (kDebugMode) {
      print(
          'DEBUG: [MarkdownEditor] 渲染Markdown预览，原始内容长度: ${_controller.text.length}');
    }

    // 使用专门的渲染内容处理方法
    String markdownContent = getRenderContent();

    if (kDebugMode) {
      print(
          'DEBUG: [MarkdownEditor] 准备渲染Markdown预览，处理后内容长度: ${markdownContent.length}');
    }

    return Markdown(
      data: markdownContent,
      selectable: true,
      padding: const EdgeInsets.all(16),
      softLineBreak: true, // 将单个换行视为硬换行
      builders: {
        'table': CustomTableBuilder(),
        'blockquote': BlockquoteBuilder(),
        'ol': OrderedListBuilder(),
        'ul': UnorderedListBuilder(),
      },
      extensionSet: md.ExtensionSet.gitHubWeb,
      onTapLink: (text, href, title) {
        if (href != null) {
          launchUrl(Uri.parse(href));
        }
      },
      styleSheet: MarkdownStyleSheet(
        h1: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
        h2: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
        h3: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
        h4: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
        h5: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
        h6: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          color: AppTheme.primaryColor,
        ),
        p: TextStyle(
          fontSize: 16,
          height: 1.5,
          color: Theme.of(context).textTheme.bodyMedium?.color,
        ),
        code: TextStyle(
          // 行内代码样式
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade800
              : const Color(0xFFf7f7f7),
          fontFamily: 'monospace',
          fontSize: 14,
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.orange.shade300
              : const Color(0xFFD14836),
        ),
        // 代码块样式
        codeblockDecoration: BoxDecoration(
          color: const Color(0xFF1E1E1E), // 深色背景
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey.shade700,
            width: 1,
          ),
        ),
        codeblockPadding: const EdgeInsets.all(16),
        blockquote: const TextStyle(
          color: Colors.grey,
          fontStyle: FontStyle.italic,
        ),
        blockquoteDecoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(4),
          border: Border(
            left: BorderSide(
              color: AppTheme.primaryColor,
              width: 4,
            ),
          ),
        ),
        tableHead: const TextStyle(fontWeight: FontWeight.bold),
        tableBody: const TextStyle(),
        tableBorder: TableBorder.all(
          color: Colors.grey.shade300,
          width: 1,
        ),
      ),
    );
  }
}
