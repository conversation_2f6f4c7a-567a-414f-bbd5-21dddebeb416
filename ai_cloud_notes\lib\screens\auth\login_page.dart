import 'dart:ui'; // Required for ImageFilter
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/providers/theme_service.dart'; // 导入主题服务
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isObscure = true; // 密码是否可见

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// 执行登录操作
  void _login() async {
    if (_formKey.currentState!.validate()) {
      // 获取AuthProvider实例
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // 调用登录方法，传递上下文以便登录成功后同步主题设置
      final success = await authProvider.login(
        usernameOrEmail: _usernameController.text.trim(),
        password: _passwordController.text,
        context: context, // 传递上下文
      );

      if (mounted) {
        if (success) {
          // 登录成功，跳转到首页
          Navigator.of(context).pushReplacementNamed(AppRoutes.home);
        } else {
          // 登录失败，显示错误提示
          SnackbarHelper.showError(
            context: context,
            message: authProvider.error ?? '登录失败，请稍后再试',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取AuthProvider，监听状态变化
    final authProvider = Provider.of<AuthProvider>(context);
    final isLoading = authProvider.isLoading;

    return Theme(
      data: AppTheme.fixedLightTheme,
      child: Scaffold(
        appBar: AppBar(
        // 登录页通常不需要返回按钮，注释掉或移除
        // leading: IconButton(
        //   icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
        //   onPressed: () => Navigator.of(context).pop(),
        // ),
        title: Text(
          '账号登录', // 将标题移到 AppBar
          style: TextStyle(
            color: AppTheme.blackColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.transparent, // 设置 AppBar 背景透明
        elevation: 0, // 移除 AppBar 阴影
        centerTitle: true, // 标题居中，与其他页面统一
        // 添加毛玻璃效果
        flexibleSpace: ClipRect( // ClipRect 用于裁剪 BackdropFilter 的区域
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0), // 应用模糊效果
            child: Container(
              color: Colors.white.withOpacity(0.3), // 毛玻璃上方的半透明层
            ),
          ),
        ),
      ),
      body: SingleChildScrollView( // 保留 SingleChildScrollView
        child: Padding( // 直接使用 Padding 替代 Card 和外部容器
          padding: const EdgeInsets.all(20.0), // 统一使用 20.0 的内边距
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 表单标题已移至 AppBar，移除此处的标题和间距
                            // const SizedBox(height: 24), // 移除多余间距，后续统一调整
                            // 用户名输入框
                            Text(
                              '用户名/邮箱',
                              style: TextStyle(
                                fontSize: 16,
                                color: AppTheme.darkGrayColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextFormField(
                              controller: _usernameController,
                              decoration: InputDecoration(
                                hintText: '请输入账号',
                                prefixIcon: Icon(Icons.person_outline),
                                // 使用下划线边框
                                border: UnderlineInputBorder(
                                  borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                                ),
                                focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                                  borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                                ),
                                // 调整内边距
                                contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return '请输入用户名或邮箱';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 24),

                            // 密码输入框
                            Text(
                              '密码',
                              style: TextStyle(
                                fontSize: 16,
                                color: AppTheme.darkGrayColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextFormField(
                              controller: _passwordController,
                              obscureText: _isObscure,
                              decoration: InputDecoration(
                                hintText: '请输入密码',
                                prefixIcon: Icon(Icons.lock_outline),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    _isObscure ? Icons.visibility_off : Icons.visibility,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _isObscure = !_isObscure;
                                    });
                                  },
                                ),
                                // 使用下划线边框
                                border: UnderlineInputBorder(
                                  borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                                ),
                                focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                                  borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                                ),
                                // 调整内边距
                                contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return '请输入密码';
                                }
                                return null;
                              },
                            ),

                            // 忘记密码链接
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton(
                                onPressed: isLoading
                                  ? null
                                  : () {
                                      // 使用命名路由导航到忘记密码页面
                                      Navigator.of(context).pushNamed(AppRoutes.forgotPassword);
                                    },
                                child: Text(
                                  '忘记密码？',
                                  style: TextStyle(
                                    color: AppTheme.primaryColor,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 24),

                            // 登录按钮
                            SizedBox(
                              width: double.infinity,
                              height: 50,
                              child: ElevatedButton(
                                onPressed: isLoading ? null : _login,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.primaryColor,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    // 调整圆角
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 0, // 保持无阴影
                                  // 可以考虑微调 padding 或 textStyle
                                  // padding: EdgeInsets.symmetric(horizontal: 32, vertical: 14),
                                  // textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                ),
                                child: isLoading
                                    ? SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : Text(
                                        '登 录',
                                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                                      ),
                              ),
                            ),

                            const SizedBox(height: 20),

                            // 注册链接
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '没有账号？',
                                  style: TextStyle(
                                    color: AppTheme.darkGrayColor,
                                    fontSize: 14,
                                  ),
                                ),
                                TextButton(
                                  onPressed: isLoading
                                    ? null
                                    : () {
                                        // 使用命名路由导航到注册页面
                                        Navigator.of(context).pushNamed(AppRoutes.register);
                                      },
                                  child: Text(
                                    '立即注册',
                                    style: TextStyle(
                                      color: AppTheme.primaryColor,
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ), // Column closing bracket
                      ), // Form closing bracket
                    ), // Padding closing bracket
                  ), // SingleChildScrollView closing bracket
        ) // Scaffold closing bracket
  );    // Closes Theme and return statement
  }
}