import { Router } from 'express';
import { body } from 'express-validator';
import AI<PERSON>ontroller from '../controllers/ai.controller';
import { authenticate } from '../middlewares/auth.middleware';

const router = Router();

/**
 * @swagger
 * /api/ai/completion:
 *   post:
 *     summary: 生成智能续写
 *     tags: [AI功能]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 description: 需要续写的内容
 *               options:
 *                 type: object
 *                 description: 续写选项
 *                 properties:
 *                   temperature:
 *                     type: number
 *                     description: 生成多样性，值越大多样性越高
 *                   max_tokens:
 *                     type: number
 *                     description: 生成的最大token数
 *     responses:
 *       200:
 *         description: 成功生成续写
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post(
  '/completion',
  [
    authenticate,
    body('content').isString().notEmpty().withMessage('内容不能为空'),
    body('options').optional().isObject().withMessage('选项必须是对象')
  ],
  AIController.generateCompletion
);

/**
 * @swagger
 * /api/ai/summary:
 *   post:
 *     summary: 生成内容摘要
 *     tags: [AI功能]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 description: 需要生成摘要的内容
 *               options:
 *                 type: object
 *                 description: 摘要选项
 *                 properties:
 *                   temperature:
 *                     type: number
 *                     description: 生成多样性，值越大多样性越高
 *                   max_tokens:
 *                     type: number
 *                     description: 生成的最大token数
 *     responses:
 *       200:
 *         description: 成功生成摘要
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post(
  '/summary',
  [
    authenticate,
    body('content').isString().notEmpty().withMessage('内容不能为空'),
    body('options').optional().isObject().withMessage('选项必须是对象')
  ],
  AIController.generateSummary
);

/**
 * @swagger
 * /api/ai/tags:
 *   post:
 *     summary: 生成标签建议
 *     tags: [AI功能]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - content
 *             properties:
 *               content:
 *                 type: string
 *                 description: 需要生成标签的内容
 *               options:
 *                 type: object
 *                 description: 标签选项
 *                 properties:
 *                   temperature:
 *                     type: number
 *                     description: 生成多样性，值越大多样性越高
 *                   max_tokens:
 *                     type: number
 *                     description: 生成的最大token数
 *     responses:
 *       200:
 *         description: 成功生成标签建议
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post(
  '/tags',
  [
    authenticate,
    body('content').isString().notEmpty().withMessage('内容不能为空'),
    body('options').optional().isObject().withMessage('选项必须是对象')
  ],
  AIController.generateTagSuggestions
);

/**
 * @swagger
 * /api/ai/ask:
 *   post:
 *     summary: 智能问答
 *     tags: [AI功能]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - question
 *             properties:
 *               question:
 *                 type: string
 *                 description: 问题
 *               context:
 *                 type: string
 *                 description: 上下文内容
 *               options:
 *                 type: object
 *                 description: 问答选项
 *                 properties:
 *                   temperature:
 *                     type: number
 *                     description: 生成多样性，值越大多样性越高
 *                   max_tokens:
 *                     type: number
 *                     description: 生成的最大token数
 *     responses:
 *       200:
 *         description: 成功回答问题
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post(
  '/ask',
  [
    authenticate,
    body('question').isString().notEmpty().withMessage('问题不能为空'),
    body('context').optional().isString().withMessage('上下文必须是字符串'),
    body('options').optional().isObject().withMessage('选项必须是对象')
  ],
  AIController.askQuestion
);

export default router;
