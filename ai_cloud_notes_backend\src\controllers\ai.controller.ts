import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { logger } from '../utils/logger';
import AIService from '../services/ai.service';

// 扩展Request类型，添加用户信息
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    username: string;
    email: string;
  };
}

/**
 * AI功能控制器
 */
class AIController {
  /**
   * 智能续写
   * @param req 请求对象
   * @param res 响应对象
   */
  async generateCompletion(req: AuthenticatedRequest, res: Response) {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          errors: errors.array()
        });
      }

      // 获取用户ID
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: {
            message: '未授权，请先登录'
          }
        });
      }

      // 获取请求参数
      const { content, options } = req.body;

      // 调用AI服务生成续写
      const result = await AIService.generateCompletion(userId, content, options);

      // 返回结果
      if (result.success) {
        return res.status(200).json({
          success: true,
          data: result.data
        });
      } else {
        return res.status(500).json({
          success: false,
          error: result.error
        });
      }
    } catch (error: any) {
      logger.error(`生成续写失败: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: {
          message: '生成续写失败，请稍后再试'
        }
      });
    }
  }

  /**
   * 生成内容摘要
   * @param req 请求对象
   * @param res 响应对象
   */
  async generateSummary(req: AuthenticatedRequest, res: Response) {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          errors: errors.array()
        });
      }

      // 获取用户ID
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: {
            message: '未授权，请先登录'
          }
        });
      }

      // 获取请求参数
      const { content, options } = req.body;

      // 调用AI服务生成摘要
      const result = await AIService.generateSummary(userId, content, options);

      // 返回结果
      if (result.success) {
        return res.status(200).json({
          success: true,
          data: result.data
        });
      } else {
        return res.status(500).json({
          success: false,
          error: result.error
        });
      }
    } catch (error: any) {
      logger.error(`生成摘要失败: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: {
          message: '生成摘要失败，请稍后再试'
        }
      });
    }
  }

  /**
   * 生成标签建议
   * @param req 请求对象
   * @param res 响应对象
   */
  async generateTagSuggestions(req: AuthenticatedRequest, res: Response) {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          errors: errors.array()
        });
      }

      // 获取用户ID
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: {
            message: '未授权，请先登录'
          }
        });
      }

      // 获取请求参数
      const { content, options } = req.body;

      // 调用AI服务生成标签建议
      const result = await AIService.generateTagSuggestions(userId, content, options);

      // 返回结果
      if (result.success) {
        return res.status(200).json({
          success: true,
          data: result.data
        });
      } else {
        return res.status(500).json({
          success: false,
          error: result.error
        });
      }
    } catch (error: any) {
      logger.error(`生成标签建议失败: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: {
          message: '生成标签建议失败，请稍后再试'
        }
      });
    }
  }

  /**
   * 智能问答
   * @param req 请求对象
   * @param res 响应对象
   */
  async askQuestion(req: AuthenticatedRequest, res: Response) {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          errors: errors.array()
        });
      }

      // 获取用户ID
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: {
            message: '未授权，请先登录'
          }
        });
      }

      // 获取请求参数
      const { question, context, options } = req.body;

      // 调用AI服务回答问题
      const result = await AIService.askQuestion(userId, question, context, options);

      // 返回结果
      if (result.success) {
        return res.status(200).json({
          success: true,
          data: result.data
        });
      } else {
        return res.status(500).json({
          success: false,
          error: result.error
        });
      }
    } catch (error: any) {
      logger.error(`回答问题失败: ${error.message}`);
      return res.status(500).json({
        success: false,
        error: {
          message: '回答问题失败，请稍后再试'
        }
      });
    }
  }
}

export default new AIController();
