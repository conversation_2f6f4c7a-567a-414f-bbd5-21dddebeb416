import 'package:flutter_test/flutter_test.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import '../../mocks/mock_api_service.dart';
import '../../test_config/mock_data.dart';

void main() {
  group('AuthProvider 单元测试', () {
    late AuthProvider authProvider;
    late MockApiService mockApiService;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider(mockApiService);
    });

    tearDown(() {
      mockApiService.reset();
    });

    group('初始状态', () {
      test('应该有正确的初始状态', () {
        expect(authProvider.status, AuthStatus.uninitialized);
        expect(authProvider.user, isNull);
        expect(authProvider.error, isNull);
        expect(authProvider.isLoading, isFalse);
        expect(authProvider.isAuthenticated, isFalse);
      });
    });

    group('登录功能', () {
      test('登录成功时应该更新状态', () async {
        // 设置Mock API返回成功响应
        mockApiService.shouldSucceed = true;

        // 执行登录
        final result = await authProvider.login(
          usernameOrEmail: 'testuser',
          password: 'password123',
        );

        // 验证结果
        expect(result, isTrue);
        expect(authProvider.status, AuthStatus.authenticated);
        expect(authProvider.user, isNotNull);
        expect(authProvider.user!['username'], 'testuser');
        expect(authProvider.error, isNull);
        expect(authProvider.isLoading, isFalse);
        expect(authProvider.isAuthenticated, isTrue);
      });

      test('登录失败时应该设置错误信息', () async {
        // 设置Mock API返回失败响应
        mockApiService.shouldSucceed = false;

        // 执行登录
        final result = await authProvider.login(
          usernameOrEmail: 'wronguser',
          password: 'wrongpassword',
        );

        // 验证结果
        expect(result, isFalse);
        expect(authProvider.status, AuthStatus.unauthenticated);
        expect(authProvider.user, isNull);
        expect(authProvider.error, isNotNull);
        expect(authProvider.error, contains('用户名或密码错误'));
        expect(authProvider.isLoading, isFalse);
        expect(authProvider.isAuthenticated, isFalse);
      });

      test('登录时应该显示加载状态', () async {
        // 设置延迟以便观察加载状态
        mockApiService.shouldDelay = true;
        mockApiService.delayMilliseconds = 100;

        // 开始登录（不等待完成）
        final loginFuture = authProvider.login(
          usernameOrEmail: 'testuser',
          password: 'password123',
        );

        // 验证加载状态
        expect(authProvider.isLoading, isTrue);

        // 等待登录完成
        await loginFuture;

        // 验证加载状态已结束
        expect(authProvider.isLoading, isFalse);
      });

      test('网络错误时应该处理异常', () async {
        // 设置网络错误
        mockApiService.shouldSucceed = false;
        mockApiService.forceErrorType = 'network';

        // 执行登录
        final result = await authProvider.login(
          usernameOrEmail: 'testuser',
          password: 'password123',
        );

        // 验证结果
        expect(result, isFalse);
        expect(authProvider.error, contains('网络连接失败'));
      });
    });

    group('注册功能', () {
      test('注册成功时应该返回true', () async {
        // 设置Mock API返回成功响应
        mockApiService.shouldSucceed = true;

        // 执行注册
        final result = await authProvider.register(
          username: 'newuser',
          email: '<EMAIL>',
          password: 'password123',
          verificationCode: '123456',
        );

        // 验证结果
        expect(result, isTrue);
        expect(authProvider.error, isNull);
        expect(authProvider.isLoading, isFalse);
      });

      test('注册失败时应该设置错误信息', () async {
        // 设置Mock API返回失败响应
        mockApiService.shouldSucceed = false;

        // 执行注册
        final result = await authProvider.register(
          username: 'existinguser',
          email: '<EMAIL>',
          password: 'password123',
          verificationCode: '123456',
        );

        // 验证结果
        expect(result, isFalse);
        expect(authProvider.error, isNotNull);
        expect(authProvider.isLoading, isFalse);
      });

      test('验证码错误时应该返回相应错误', () async {
        // 设置Mock API返回成功，但验证码错误
        mockApiService.shouldSucceed = true;

        // 执行注册（使用错误的验证码）
        final result = await authProvider.register(
          username: 'newuser',
          email: '<EMAIL>',
          password: 'password123',
          verificationCode: '000000',
        );

        // 验证结果
        expect(result, isFalse);
        expect(authProvider.error, contains('验证码错误'));
      });
    });

    group('发送验证码功能', () {
      test('发送验证码成功时应该返回true', () async {
        // 设置Mock API返回成功响应
        mockApiService.shouldSucceed = true;

        // 执行发送验证码
        final result = await authProvider.sendVerificationCode('<EMAIL>');

        // 验证结果
        expect(result, isTrue);
        expect(authProvider.error, isNull);
        expect(authProvider.isLoading, isFalse);
      });

      test('发送验证码失败时应该设置错误信息', () async {
        // 设置Mock API返回失败响应
        mockApiService.shouldSucceed = false;

        // 执行发送验证码
        final result = await authProvider.sendVerificationCode('<EMAIL>');

        // 验证结果
        expect(result, isFalse);
        expect(authProvider.error, isNotNull);
        expect(authProvider.isLoading, isFalse);
      });
    });

    group('忘记密码功能', () {
      test('忘记密码成功时应该返回true', () async {
        // 设置Mock API返回成功响应
        mockApiService.shouldSucceed = true;

        // 执行忘记密码
        final result = await authProvider.forgotPassword('<EMAIL>');

        // 验证结果
        expect(result, isTrue);
        expect(authProvider.error, isNull);
        expect(authProvider.isLoading, isFalse);
      });

      test('忘记密码失败时应该设置错误信息', () async {
        // 设置Mock API返回失败响应
        mockApiService.shouldSucceed = false;

        // 执行忘记密码
        final result = await authProvider.forgotPassword('<EMAIL>');

        // 验证结果
        expect(result, isFalse);
        expect(authProvider.error, isNotNull);
        expect(authProvider.isLoading, isFalse);
      });
    });

    group('重置密码功能', () {
      test('重置密码成功时应该返回true', () async {
        // 设置Mock API返回成功响应
        mockApiService.shouldSucceed = true;

        // 执行重置密码
        final result = await authProvider.resetPassword(
          email: '<EMAIL>',
          token: 'valid_reset_token',
          newPassword: 'newpassword123',
        );

        // 验证结果
        expect(result, isTrue);
        expect(authProvider.error, isNull);
        expect(authProvider.isLoading, isFalse);
      });

      test('重置密码失败时应该设置错误信息', () async {
        // 设置Mock API返回成功，但使用无效令牌
        mockApiService.shouldSucceed = true;

        // 执行重置密码（使用无效令牌）
        final result = await authProvider.resetPassword(
          email: '<EMAIL>',
          token: 'invalid_token',
          newPassword: 'newpassword123',
        );

        // 验证结果
        expect(result, isFalse);
        expect(authProvider.error, contains('重置令牌无效'));
      });
    });

    group('登出功能', () {
      test('登出应该清除用户状态', () async {
        // 先登录
        mockApiService.shouldSucceed = true;
        await authProvider.login(
          usernameOrEmail: 'testuser',
          password: 'password123',
        );

        // 验证已登录
        expect(authProvider.isAuthenticated, isTrue);
        expect(authProvider.user, isNotNull);

        // 执行登出
        await authProvider.logout();

        // 验证已登出
        expect(authProvider.status, AuthStatus.unauthenticated);
        expect(authProvider.user, isNull);
        expect(authProvider.isAuthenticated, isFalse);
      });
    });

    group('用户信息管理', () {
      test('应该能够更新用户头像', () async {
        // 先登录
        mockApiService.shouldSucceed = true;
        await authProvider.login(
          usernameOrEmail: 'testuser',
          password: 'password123',
        );

        // 更新头像
        const newAvatarUrl = 'https://example.com/new-avatar.jpg';
        authProvider.updateUserAvatar(newAvatarUrl);

        // 验证头像已更新
        expect(authProvider.user!['avatar'], newAvatarUrl);
      });

      test('应该能够刷新用户信息', () async {
        // 先登录
        mockApiService.setLoggedIn();
        mockApiService.shouldSucceed = true;
        
        // 设置认证状态
        await authProvider.login(
          usernameOrEmail: 'testuser',
          password: 'password123',
        );

        // 刷新用户信息
        await authProvider.refreshUserInfo();

        // 验证用户信息已刷新
        expect(authProvider.user, isNotNull);
        expect(authProvider.error, isNull);
      });
    });

    group('状态管理', () {
      test('应该正确管理加载状态', () async {
        // 设置延迟
        mockApiService.shouldDelay = true;
        mockApiService.delayMilliseconds = 100;

        // 开始异步操作
        final future = authProvider.login(
          usernameOrEmail: 'testuser',
          password: 'password123',
        );

        // 验证加载状态
        expect(authProvider.isLoading, isTrue);

        // 等待完成
        await future;

        // 验证加载状态已结束
        expect(authProvider.isLoading, isFalse);
      });

      test('应该在操作前清除错误信息', () async {
        // 先设置一个错误
        mockApiService.shouldSucceed = false;
        await authProvider.login(
          usernameOrEmail: 'wronguser',
          password: 'wrongpassword',
        );
        expect(authProvider.error, isNotNull);

        // 重新设置为成功
        mockApiService.shouldSucceed = true;

        // 执行新的操作
        await authProvider.login(
          usernameOrEmail: 'testuser',
          password: 'password123',
        );

        // 验证错误信息已清除
        expect(authProvider.error, isNull);
      });
    });
  });
}
