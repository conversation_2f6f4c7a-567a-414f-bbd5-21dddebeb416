import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import mongoose from 'mongoose';
import Tag from '../models/tag.model';
import Note from '../models/note.model';
import { logger } from '../utils/logger';

/**
 * 获取用户所有标签
 * GET /api/tags
 */
export const getTags = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 查询用户的所有标签
    const tags = await Tag.find({ owner: userId }).sort({ name: 1 });

    return res.status(200).json({
      success: true,
      data: {
        tags
      }
    });
  } catch (error: any) {
    logger.error(`获取标签失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取标签失败，请稍后再试'
      }
    });
  }
};

/**
 * 创建新标签
 * POST /api/tags
 */
export const createTag = async (req: Request, res: Response) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { name, color } = req.body;

    // 检查标签名是否已存在
    const existingTag = await Tag.findOne({ owner: userId, name });
    if (existingTag) {
      return res.status(400).json({
        success: false,
        error: {
          message: '该标签名已存在'
        }
      });
    }

    // 创建新标签
    const tag = new Tag({
      name,
      color: color || '#5D5FEF', // 默认颜色
      owner: userId
    });

    // 保存标签
    await tag.save();

    return res.status(201).json({
      success: true,
      message: '标签创建成功',
      data: {
        tag
      }
    });
  } catch (error: any) {
    logger.error(`创建标签失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '创建标签失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取标签详情
 * GET /api/tags/:id
 */
export const getTagById = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的标签ID'
        }
      });
    }

    // 查找标签
    const tag = await Tag.findOne({ _id: id, owner: userId });
    
    if (!tag) {
      return res.status(404).json({
        success: false,
        error: {
          message: '标签不存在或无权限'
        }
      });
    }

    // 获取使用该标签的笔记数量
    const noteCount = await Note.countDocuments({ tags: id, owner: userId });

    return res.status(200).json({
      success: true,
      data: {
        tag,
        noteCount
      }
    });
  } catch (error: any) {
    logger.error(`获取标签详情失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取标签详情失败，请稍后再试'
      }
    });
  }
};

/**
 * 更新标签
 * PUT /api/tags/:id
 */
export const updateTag = async (req: Request, res: Response) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;
    const { name, color } = req.body;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的标签ID'
        }
      });
    }

    // 如果要更新标签名，检查新的标签名是否已存在
    if (name) {
      const existingTag = await Tag.findOne({ 
        owner: userId, 
        name, 
        _id: { $ne: id } 
      });
      
      if (existingTag) {
        return res.status(400).json({
          success: false,
          error: {
            message: '该标签名已存在'
          }
        });
      }
    }

    // 构建更新对象
    const updateData: any = {};
    if (name) updateData.name = name;
    if (color) updateData.color = color;

    // 更新标签
    const updatedTag = await Tag.findOneAndUpdate(
      { _id: id, owner: userId },
      updateData,
      { new: true }
    );

    if (!updatedTag) {
      return res.status(404).json({
        success: false,
        error: {
          message: '标签不存在或无权限'
        }
      });
    }

    return res.status(200).json({
      success: true,
      message: '标签更新成功',
      data: {
        tag: updatedTag
      }
    });
  } catch (error: any) {
    logger.error(`更新标签失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '更新标签失败，请稍后再试'
      }
    });
  }
};

/**
 * 删除标签
 * DELETE /api/tags/:id
 */
export const deleteTag = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的标签ID'
        }
      });
    }

    // 删除标签
    const deletedTag = await Tag.findOneAndDelete({ _id: id, owner: userId });
    
    if (!deletedTag) {
      return res.status(404).json({
        success: false,
        error: {
          message: '标签不存在或无权限'
        }
      });
    }

    // 更新所有使用该标签的笔记，移除标签引用
    await Note.updateMany(
      { owner: userId, tags: id },
      { $pull: { tags: id } }
    );

    return res.status(200).json({
      success: true,
      message: '标签删除成功'
    });
  } catch (error: any) {
    logger.error(`删除标签失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '删除标签失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取带有特定标签的笔记列表
 * GET /api/tags/:id/notes
 */
export const getNotesByTag = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;
    const {
      limit = 20,
      page = 1,
      sortBy = 'updatedAt',
      order = 'desc'
    } = req.query;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的标签ID'
        }
      });
    }

    // 检查标签是否存在
    const tag = await Tag.findOne({ _id: id, owner: userId });
    if (!tag) {
      return res.status(404).json({
        success: false,
        error: {
          message: '标签不存在或无权限'
        }
      });
    }

    // 计算分页参数
    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const skip = (pageNumber - 1) * limitNumber;

    // 构建排序对象
    const sort: any = {};
    sort[sortBy as string] = order === 'desc' ? -1 : 1;

    // 构建查询对象
    const query = {
      owner: userId,
      tags: id,
      // 移除isArchived限制，显示所有笔记（包括归档的笔记）
      // isArchived: false // 默认不显示归档的笔记
    };

    // 获取符合条件的笔记总数
    const total = await Note.countDocuments(query);

    // 查询笔记列表
    const notes = await Note.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limitNumber)
      .populate('tags', 'name color');

    return res.status(200).json({
      success: true,
      data: {
        tag,
        notes,
        pagination: {
          total,
          page: pageNumber,
          limit: limitNumber,
          pages: Math.ceil(total / limitNumber)
        }
      }
    });
  } catch (error: any) {
    logger.error(`获取标签笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取标签笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取热门标签
 * GET /api/tags/popular
 */
export const getPopularTags = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { limit = 10 } = req.query;
    const limitNumber = parseInt(limit as string, 10);

    // 聚合查询获取使用次数最多的标签
    const popularTags = await Note.aggregate([
      // 匹配当前用户的笔记
      { $match: { owner: new mongoose.Types.ObjectId(userId) } },
      // 展开标签数组
      { $unwind: '$tags' },
      // 按标签ID分组并计数
      { 
        $group: { 
          _id: '$tags', 
          count: { $sum: 1 } 
        } 
      },
      // 排序
      { $sort: { count: -1 } },
      // 限制返回数量
      { $limit: limitNumber },
      // 查找标签信息
      {
        $lookup: {
          from: 'tags',
          localField: '_id',
          foreignField: '_id',
          as: 'tagInfo'
        }
      },
      // 展开标签信息
      { $unwind: '$tagInfo' },
      // 重塑结果
      {
        $project: {
          _id: '$tagInfo._id',
          name: '$tagInfo.name',
          color: '$tagInfo.color',
          count: 1
        }
      }
    ]);

    return res.status(200).json({
      success: true,
      data: {
        tags: popularTags
      }
    });
  } catch (error: any) {
    logger.error(`获取热门标签失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取热门标签失败，请稍后再试'
      }
    });
  }
}; 