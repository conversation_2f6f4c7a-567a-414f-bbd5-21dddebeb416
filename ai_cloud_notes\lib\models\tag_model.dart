import 'package:flutter/material.dart';

/// 标签模型类
/// 
/// 表示一个标签实体，用于对笔记进行分类。
class Tag {
  /// 标签ID
  final String id;
  
  /// 标签名称
  final String name;
  
  /// 标签颜色（十六进制格式，例如：#FF5733）
  final String color;
  
  /// 标签所属用户ID
  final String? userId;
  
  /// 标签关联的笔记数量
  final int? noteCount;
  
  /// 排序顺序
  final int? order;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 构造函数
  Tag({
    required this.id,
    required this.name,
    required this.color,
    this.userId,
    this.noteCount,
    this.order,
    required this.createdAt,
    required this.updatedAt,
  });
  
  /// 从JSON对象创建标签模型
  factory Tag.fromJson(Map<String, dynamic> json) {
    return Tag(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      color: json['color'] ?? '#5D5FEF', // 默认使用主题色
      userId: json['userId'],
      noteCount: json['noteCount'] ?? json['count'] ?? 0, // 同时处理noteCount和count字段，默认为0
      order: json['order'],
      createdAt: json['createdAt'] != null 
        ? DateTime.parse(json['createdAt']) 
        : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
        ? DateTime.parse(json['updatedAt']) 
        : DateTime.now(),
    );
  }
  
  /// 将标签模型转换为JSON对象
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'color': color,
      'userId': userId,
      'noteCount': noteCount,
      'order': order,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
  
  /// 创建标签副本
  Tag copyWith({
    String? id,
    String? name,
    String? color,
    String? userId,
    int? noteCount,
    int? order,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Tag(
      id: id ?? this.id,
      name: name ?? this.name,
      color: color ?? this.color,
      userId: userId ?? this.userId,
      noteCount: noteCount ?? this.noteCount,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  /// 解析颜色字符串为Color对象
  Color get colorValue {
    // 移除#前缀并解析十六进制颜色
    String hexColor = color.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor'; // 添加不透明度
    }
    return Color(int.parse(hexColor, radix: 16));
  }
  
  /// 重写==操作符，用于比较两个标签是否相同
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is Tag &&
        other.id == id &&
        other.name == name &&
        other.color == color;
  }
  
  /// 重写hashCode
  @override
  int get hashCode {
    return id.hashCode ^
        name.hashCode ^
        color.hashCode;
  }
  
  /// 创建一个空的Tag对象
  /// 用于在找不到标签时返回
  factory Tag.empty() {
    return Tag(
      id: '',
      name: '',
      color: '#5D5FEF',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
} 