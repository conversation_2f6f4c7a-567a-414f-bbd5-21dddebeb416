import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ai_cloud_notes/services/api_service.dart';

/// AI功能提供者类，用于管理应用的AI功能设置
class AIProvider extends ChangeNotifier {
  // 存储键
  static const String _aiAssistantKey = 'ai_assistant_enabled';
  static const String _smartSuggestionsKey = 'smart_suggestions_enabled';
  static const String _autoTaggingKey = 'auto_tagging_enabled';
  static const String _contentSummaryKey = 'content_summary_enabled';
  static const String _selectedModelKey = 'selected_model';
  static const String _suggestionFrequencyKey = 'suggestion_frequency';
  static const String _localProcessingKey = 'local_processing_enabled';
  static const String _allowDataCollectionKey = 'allow_data_collection';

  ApiService? _apiService; // 改为可空，由外部注入
  bool _isCurrentlyAuthenticated = false; // 新增：跟踪认证状态
  bool _isSyncing = false;

  // AI功能开关
  bool _aiAssistantEnabled = true;
  bool _smartSuggestionsEnabled = true;
  bool _autoTaggingEnabled = true;
  bool _contentSummaryEnabled = false;

  // 语言模型选择
  String _selectedModel = '默认模型';
  final List<Map<String, dynamic>> _modelOptions = [
    {
      'name': '默认模型',
      'description': '标准AI模型，适合大多数日常场景',
      'isPro': false,
    },
    {
      'name': '增强模型',
      'description': '更强大的AI能力，适合复杂内容分析',
      'isPro': true,
    },
    {
      'name': '轻量模型',
      'description': '速度更快，适合简单任务和低性能设备',
      'isPro': false,
    },
  ];

  // 建议频率
  String _suggestionFrequency = '输入停顿后';
  final List<String> _frequencyOptions = ['实时', '输入停顿后', '手动触发'];

  // 隐私设置
  bool _localProcessingEnabled = false;
  bool _allowDataCollection = true;

  AIProvider() {
    // 构造函数中不再立即加载或同步，等待外部通过 updateAuthenticationStatusAndService 注入依赖并触发
    debugPrint('[AIProvider] AIProvider instance created.');
  }

  // 由 ChangeNotifierProxyProvider 调用，以更新认证状态并可能触发同步
  Future<void> updateAuthenticationStatusAndService(
      bool isAuthenticated, ApiService apiService) async {
    debugPrint(
        '[AIProvider] updateAuthenticationStatusAndService called. New auth status: $isAuthenticated');
    _apiService = apiService; // 设置/更新 ApiService 实例

    final bool wasPreviouslyAuthenticated = _isCurrentlyAuthenticated;
    _isCurrentlyAuthenticated = isAuthenticated;

    if (!_isCurrentlyAuthenticated && wasPreviouslyAuthenticated) {
      // 用户登出
      debugPrint(
          '[AIProvider] User logged out. Resetting to local/default AI settings.');
      await _loadSettings(); // 加载本地（可能是默认或上次未登录时）的设置
    } else if (_isCurrentlyAuthenticated && !wasPreviouslyAuthenticated) {
      // 用户首次登录或从登出状态变为登录状态
      debugPrint(
          '[AIProvider] User transitioned to authenticated state. Triggering sync from server.');
      // 先加载本地设置，以防服务器同步失败时UI有基本数据
      await _loadSettings();
      // 然后尝试从服务器同步
      // 使用 Future.microtask 确保在当前 build 周期之外执行
      Future.microtask(() => _syncFromServerWithRetry());
    } else if (_isCurrentlyAuthenticated && wasPreviouslyAuthenticated) {
      // 用户已登录，且之前也是登录状态（例如热重载或Provider重建但用户会话未变）
      // 这种情况下，通常不需要强制再次从服务器同步，除非有特定逻辑要求
      // 但为了确保数据最新，可以考虑在某些条件下（如长时间未同步）触发
      // 为简单起见，此处可以先加载本地，如果需要，后续可以增加更复杂的逻辑
      debugPrint(
          '[AIProvider] User remains authenticated. Ensuring local settings are loaded.');
      await _loadSettings(); // 确保本地设置被加载
      // 可以考虑在这里也触发一次 _syncFromServerWithRetry，以确保数据最新，
      // 但要注意避免不必要的频繁同步。
      // Future.microtask(() => _syncFromServerWithRetry());
    } else {
      // 用户未登录，且之前也未登录
      debugPrint(
          '[AIProvider] User remains unauthenticated. Ensuring local settings are loaded.');
      await _loadSettings();
    }
    debugPrint(
        '[AIProvider] updateAuthenticationStatusAndService finished. Current auth status: $_isCurrentlyAuthenticated');
  }

  // 带重试机制的服务器同步 (下载)
  Future<void> _syncFromServerWithRetry(
      {int retryCount = 3, int delaySeconds = 1}) async {
    if (_apiService == null || !_isCurrentlyAuthenticated) {
      debugPrint(
          '[AIProvider] _syncFromServerWithRetry: ApiService not available or user not authenticated. Skipping.');
      return;
    }
    if (_isSyncing) {
      debugPrint(
          '[AIProvider] _syncFromServerWithRetry: Already syncing. Skipping.');
      return;
    }

    for (int i = 0; i < retryCount; i++) {
      try {
        await _syncFromServer();
        return; // 成功同步，返回
      } catch (e) {
        debugPrint('同步AI设置失败 (尝试 ${i + 1}/$retryCount): $e');
        if (i < retryCount - 1) {
          // 如果还有重试机会，等待一段时间再尝试
          await Future.delayed(Duration(seconds: delaySeconds * (i + 1)));
        }
      }
    }

    // 所有重试都失败后，使用本地设置
    debugPrint('同步AI设置失败，使用本地设置');
  }

  // 获取AI助手状态
  bool get aiAssistantEnabled => _aiAssistantEnabled;

  // 获取智能建议状态
  bool get smartSuggestionsEnabled => _smartSuggestionsEnabled;

  // 获取自动标签状态
  bool get autoTaggingEnabled => _autoTaggingEnabled;

  // 获取内容摘要状态
  bool get contentSummaryEnabled => _contentSummaryEnabled;

  // 获取选中的模型
  String get selectedModel => _selectedModel;

  // 获取模型选项
  List<Map<String, dynamic>> get modelOptions => _modelOptions;

  // 获取建议频率
  String get suggestionFrequency => _suggestionFrequency;

  // 获取频率选项
  List<String> get frequencyOptions => _frequencyOptions;

  // 获取本地处理状态
  bool get localProcessingEnabled => _localProcessingEnabled;

  // 获取数据收集状态
  bool get allowDataCollection => _allowDataCollection;

  // 从本地加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载AI功能开关
      _aiAssistantEnabled = prefs.getBool(_aiAssistantKey) ?? true;
      _smartSuggestionsEnabled = prefs.getBool(_smartSuggestionsKey) ?? true;
      _autoTaggingEnabled = prefs.getBool(_autoTaggingKey) ?? true;
      _contentSummaryEnabled = prefs.getBool(_contentSummaryKey) ?? false;

      // 加载语言模型选择
      _selectedModel = prefs.getString(_selectedModelKey) ?? '默认模型';

      // 加载建议频率
      _suggestionFrequency =
          prefs.getString(_suggestionFrequencyKey) ?? '输入停顿后';

      // 加载隐私设置
      _localProcessingEnabled = prefs.getBool(_localProcessingKey) ?? false;
      _allowDataCollection = prefs.getBool(_allowDataCollectionKey) ?? true;

      debugPrint('成功从本地加载AI设置');
    } catch (e) {
      debugPrint('从本地加载AI设置失败: $e');
      // 使用默认设置
      _aiAssistantEnabled = true;
      _smartSuggestionsEnabled = true;
      _autoTaggingEnabled = true;
      _contentSummaryEnabled = false;
      _selectedModel = '默认模型';
      _suggestionFrequency = '输入停顿后';
      _localProcessingEnabled = false;
      _allowDataCollection = true;
    }

    notifyListeners();
  }

  // 保存设置到本地
  Future<void> _saveSettingsLocally({
    required bool aiAssistantEnabled,
    required bool smartSuggestionsEnabled,
    required bool autoTaggingEnabled,
    required bool contentSummaryEnabled,
    required String selectedModel,
    required String suggestionFrequency,
    required bool localProcessingEnabled,
    required bool allowDataCollection,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    // 保存AI功能开关
    await prefs.setBool(_aiAssistantKey, aiAssistantEnabled);
    _aiAssistantEnabled = aiAssistantEnabled;

    await prefs.setBool(_smartSuggestionsKey, smartSuggestionsEnabled);
    _smartSuggestionsEnabled = smartSuggestionsEnabled;

    await prefs.setBool(_autoTaggingKey, autoTaggingEnabled);
    _autoTaggingEnabled = autoTaggingEnabled;

    await prefs.setBool(_contentSummaryKey, contentSummaryEnabled);
    _contentSummaryEnabled = contentSummaryEnabled;

    // 保存语言模型选择
    await prefs.setString(_selectedModelKey, selectedModel);
    _selectedModel = selectedModel;

    // 保存建议频率
    await prefs.setString(_suggestionFrequencyKey, suggestionFrequency);
    _suggestionFrequency = suggestionFrequency;

    // 保存隐私设置
    await prefs.setBool(_localProcessingKey, localProcessingEnabled);
    _localProcessingEnabled = localProcessingEnabled;

    await prefs.setBool(_allowDataCollectionKey, allowDataCollection);
    _allowDataCollection = allowDataCollection;

    notifyListeners();
  }

  // 从服务器同步设置
  Future<void> _syncFromServer() async {
    if (_apiService == null || !_isCurrentlyAuthenticated) {
      debugPrint(
          '[AIProvider] _syncFromServer: ApiService not available or user not authenticated. Skipping.');
      return;
    }
    if (_isSyncing) {
      debugPrint('[AIProvider] _syncFromServer: Already syncing. Skipping.');
      return;
    }

    _isSyncing = true;
    notifyListeners();
    debugPrint(
        '[AIProvider] _syncFromServer: isSyncing set to true, attempting to fetch settings.');

    try {
      final settings =
          await _apiService!.getUserAISettings(); // 使用 ! 因为已经检查过 _apiService
      if (settings != null) {
        await _saveSettingsLocally(
          aiAssistantEnabled: settings['aiAssistantEnabled'] ??
              _aiAssistantEnabled, // 保留本地值如果服务器对应字段为空
          smartSuggestionsEnabled:
              settings['smartSuggestionsEnabled'] ?? _smartSuggestionsEnabled,
          autoTaggingEnabled:
              settings['autoTaggingEnabled'] ?? _autoTaggingEnabled,
          contentSummaryEnabled:
              settings['contentSummaryEnabled'] ?? _contentSummaryEnabled,
          selectedModel: settings['selectedModel'] ?? _selectedModel,
          suggestionFrequency:
              settings['suggestionFrequency'] ?? _suggestionFrequency,
          localProcessingEnabled:
              settings['localProcessingEnabled'] ?? _localProcessingEnabled,
          allowDataCollection:
              settings['allowDataCollection'] ?? _allowDataCollection,
        );
        debugPrint(
            '[AIProvider] _syncFromServer: Successfully synced and saved settings from server.');
      } else {
        debugPrint(
            '[AIProvider] _syncFromServer: Received null settings from server. Will use local/default settings.');
        // 不抛出异常，允许重试机制在上层处理，或者如果所有重试失败，则保持本地设置
        throw Exception('从服务器获取的AI设置为空或API调用失败');
      }
    } catch (e) {
      debugPrint(
          '[AIProvider] _syncFromServer: Failed to sync AI settings from server: $e');
      throw e; // 重新抛出，以便_syncFromServerWithRetry可以捕获并重试
    } finally {
      _isSyncing = false;
      notifyListeners();
      debugPrint(
          '[AIProvider] _syncFromServer: Sync process finished, isSyncing set to false.');
    }
  }

  // 同步设置到服务器
  Future<void> _syncToServer({
    required bool aiAssistantEnabled,
    required bool smartSuggestionsEnabled,
    required bool autoTaggingEnabled,
    required bool contentSummaryEnabled,
    required String selectedModel,
    required String suggestionFrequency,
    required bool localProcessingEnabled,
    required bool allowDataCollection,
  }) async {
    if (_apiService == null || !_isCurrentlyAuthenticated) {
      debugPrint(
          '[AIProvider] _syncToServer: ApiService not available or user not authenticated. Skipping sync.');
      // 考虑是否应该抛出异常或返回一个状态，表明未同步
      // 为了与之前的重试逻辑兼容，这里可以抛出异常
      throw Exception('无法同步到服务器：用户未认证或服务未初始化');
    }

    debugPrint(
        '[AIProvider] _syncToServer: Attempting to update settings on server...');
    final success = await _apiService!.updateUserAISettings(
      // 使用 !
      aiAssistantEnabled: aiAssistantEnabled,
      smartSuggestionsEnabled: smartSuggestionsEnabled,
      autoTaggingEnabled: autoTaggingEnabled,
      contentSummaryEnabled: contentSummaryEnabled,
      selectedModel: selectedModel,
      suggestionFrequency: suggestionFrequency,
      localProcessingEnabled: localProcessingEnabled,
      allowDataCollection: allowDataCollection,
    );

    if (success) {
      debugPrint(
          '[AIProvider] _syncToServer: Successfully updated settings on server.');
    } else {
      debugPrint(
          '[AIProvider] _syncToServer: Failed to update settings on server. API returned false.');
      throw Exception('同步AI设置到服务器失败 (API返回false)');
    }
  }

  // 带重试机制的服务器同步 (上传)
  Future<void> _syncToServerWithRetry({
    required bool aiAssistantEnabled,
    required bool smartSuggestionsEnabled,
    required bool autoTaggingEnabled,
    required bool contentSummaryEnabled,
    required String selectedModel,
    required String suggestionFrequency,
    required bool localProcessingEnabled,
    required bool allowDataCollection,
    int retryCount = 3,
    int delaySeconds = 1,
  }) async {
    if (_apiService == null || !_isCurrentlyAuthenticated) {
      debugPrint(
          '[AIProvider] _syncToServerWithRetry: ApiService not available or user not authenticated. Skipping.');
      throw Exception('无法同步到服务器：用户未认证或服务未初始化'); // 抛出异常以便 saveSettings 捕获
    }

    for (int i = 0; i < retryCount; i++) {
      try {
        await _syncToServer(
          aiAssistantEnabled: aiAssistantEnabled,
          smartSuggestionsEnabled: smartSuggestionsEnabled,
          autoTaggingEnabled: autoTaggingEnabled,
          contentSummaryEnabled: contentSummaryEnabled,
          selectedModel: selectedModel,
          suggestionFrequency: suggestionFrequency,
          localProcessingEnabled: localProcessingEnabled,
          allowDataCollection: allowDataCollection,
        );
        return; // 成功同步，返回
      } catch (e) {
        debugPrint('同步AI设置到服务器失败 (尝试 ${i + 1}/$retryCount): $e');
        if (i < retryCount - 1) {
          // 如果还有重试机会，等待一段时间再尝试
          await Future.delayed(Duration(seconds: delaySeconds * (i + 1)));
        } else {
          // 所有重试都失败后，可以考虑通知用户或记录更严重的错误
          debugPrint('多次尝试同步AI设置到服务器失败。');
          // 重新抛出异常，以便 saveSettings 可以捕获并处理
          throw Exception('多次尝试同步AI设置到服务器后仍然失败: $e');
        }
      }
    }
  }

  // 保存设置 (本地 + 服务器)
  Future<bool> saveSettings({
    // 返回bool值表示操作是否最终成功
    required bool aiAssistantEnabled,
    required bool smartSuggestionsEnabled,
    required bool autoTaggingEnabled,
    required bool contentSummaryEnabled,
    required String selectedModel,
    required String suggestionFrequency,
    required bool localProcessingEnabled,
    required bool allowDataCollection,
  }) async {
    // 先保存到本地
    await _saveSettingsLocally(
      aiAssistantEnabled: aiAssistantEnabled,
      smartSuggestionsEnabled: smartSuggestionsEnabled,
      autoTaggingEnabled: autoTaggingEnabled,
      contentSummaryEnabled: contentSummaryEnabled,
      selectedModel: selectedModel,
      suggestionFrequency: suggestionFrequency,
      localProcessingEnabled: localProcessingEnabled,
      allowDataCollection: allowDataCollection,
    );

    // 如果已初始化API且用户已认证，尝试同步到服务器
    if (_apiService != null && _isCurrentlyAuthenticated) {
      try {
        await _syncToServerWithRetry(
          aiAssistantEnabled: aiAssistantEnabled,
          smartSuggestionsEnabled: smartSuggestionsEnabled,
          autoTaggingEnabled: autoTaggingEnabled,
          contentSummaryEnabled: contentSummaryEnabled,
          selectedModel: selectedModel,
          suggestionFrequency: suggestionFrequency,
          localProcessingEnabled: localProcessingEnabled,
          allowDataCollection: allowDataCollection,
        );
        return true; // 本地和服务器均成功
      } catch (e) {
        debugPrint('保存AI设置到服务器时发生最终错误: $e');
        // 此处可以添加用户反馈逻辑，例如显示一个SnackBar提示同步失败
        return false; // 服务器同步失败
      }
    } else {
      debugPrint('ApiService未初始化，AI设置仅保存到本地');
      return true; // 仅本地保存成功也被视为一种成功状态，但应有提示
    }
  }

  // 获取建议频率描述
  String getFrequencyDescription(String frequency) {
    switch (frequency) {
      case '实时':
        return '每次输入或光标移动时立即提供AI预测';
      case '输入停顿后':
        return '在您停止输入约800毫秒后自动提供AI预测';
      case '手动触发':
        return '仅在您点击AI预测按钮时才会生成预测';
      default:
        return '';
    }
  }
}
