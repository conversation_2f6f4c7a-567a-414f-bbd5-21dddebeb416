import { Router } from 'express';
import ThemeSettingsController from '../controllers/theme_settings.controller';
import { authenticate } from '../middlewares/auth.middleware';

const router = Router();

/**
 * @swagger
 * /api/theme-settings:
 *   get:
 *     summary: 获取用户主题设置
 *     tags: [主题设置]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取主题设置
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/', authenticate, ThemeSettingsController.getUserThemeSettings);

/**
 * @swagger
 * /api/theme-settings:
 *   post:
 *     summary: 更新用户主题设置
 *     tags: [主题设置]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               themeMode:
 *                 type: number
 *                 enum: [0, 1, 2]
 *                 description: 主题模式(0:系统, 1:浅色, 2:深色)
 *               fontColor:
 *                 type: string
 *                 description: 字体颜色(十六进制)
 *               textSize:
 *                 type: number
 *                 description: 正文字体大小
 *               titleSize:
 *                 type: number
 *                 description: 标题字体大小
 *     responses:
 *       200:
 *         description: 成功更新主题设置
 *       400:
 *         description: 请求数据无效
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/', authenticate, ThemeSettingsController.updateUserThemeSettings);

export default router; 