import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb; // 导入 kIsWeb
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';

/// 主布局组件
///
/// 提供统一的导航栏和浮动操作按钮
/// 在移动端提供底部导航栏，在 Web 端提供侧边导航栏
class MainLayout extends StatefulWidget {
  final Widget body;
  final int currentIndex;
  final bool showFab;

  const MainLayout({
    Key? key,
    required this.body,
    required this.currentIndex,
    this.showFab = true,
  }) : super(key: key);

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  // 控制侧边导航是否展开的状态
  bool _isDrawerOpen = true;

  @override
  Widget build(BuildContext context) {
    debugPrint('[MainLayout] build called. CurrentIndex: ${widget.currentIndex}, ShowFab: ${widget.showFab}. Theme - Brightness: ${Theme.of(context).brightness}, ScaffoldBG: ${Theme.of(context).scaffoldBackgroundColor}');

    // 判断是否是 Web 或桌面平台
    if (kIsWeb) {
      // Web 端布局：侧边导航 + 主内容区域
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Row(
          children: [
            // 侧边导航栏
            _buildSideNavigationBar(),
            // 主内容区域
            Expanded(
              child: widget.body,
            ),
          ],
        ),
        floatingActionButton: widget.showFab ? _buildNewNoteButton() : null,
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat, // Web 端 FAB 位置调整
      );
    } else {
      // 移动端布局：底部导航栏
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: widget.body,
        bottomNavigationBar: _buildBottomNavigationBar(),
        floatingActionButton: widget.showFab ? _buildNewNoteButton() : null,
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      );
    }
  }

  // 构建底部导航栏 (移动端)
  Widget _buildBottomNavigationBar() {
    return BottomAppBar(
      notchMargin: 10,
      elevation: 8,
      shape: const CircularNotchedRectangle(),
      color: Theme.of(context).bottomAppBarTheme.color, // Use theme color
      child: SizedBox(
        height: 60,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Expanded(
              child: InkWell(
                onTap: () {
                  if (widget.currentIndex != 0) {
                    // 使用命名路由导航到首页
                    Navigator.pushReplacementNamed(context, AppRoutes.home);
                  }
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.home,
                      color: widget.currentIndex == 0 ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                    ),
                    Text(
                      '首页',
                      style: TextStyle(
                        color: widget.currentIndex == 0 ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () {
                  if (widget.currentIndex != 1) {
                    // 添加收藏页面路由
                    Navigator.pushReplacementNamed(context, AppRoutes.favorites);

                    // 设置筛选条件为收藏
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
                      noteProvider.setFilter(NoteFilter.favorite);
                      noteProvider.fetchNotes(refresh: true);
                    });
                  }
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.favorite,
                      color: widget.currentIndex == 1 ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                    ),
                    Text(
                      '收藏',
                      style: TextStyle(
                        color: widget.currentIndex == 1 ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Expanded(
              child: SizedBox(),
            ),
            Expanded(
              child: InkWell(
                onTap: () {
                  if (widget.currentIndex != 2) {
                    // 使用命名路由导航到标签页
                    Navigator.pushReplacementNamed(context, AppRoutes.tags);
                  }
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.tag,
                      color: widget.currentIndex == 2 ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                    ),
                    Text(
                      '标签',
                      style: TextStyle(
                        color: widget.currentIndex == 2 ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Expanded(
              child: InkWell(
                onTap: () {
                  if (widget.currentIndex != 3) {
                    // 使用命名路由导航到设置页
                    Navigator.pushReplacementNamed(context, AppRoutes.settings);
                  }
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.settings,
                      color: widget.currentIndex == 3 ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                    ),
                    Text(
                      '设置',
                      style: TextStyle(
                        color: widget.currentIndex == 3 ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建侧边导航栏 (Web 端)
  Widget _buildSideNavigationBar() {
    // 根据 _isDrawerOpen 状态调整宽度
    final double drawerWidth = _isDrawerOpen ? 240 : 72; // 展开时 240，收起时 72

    return Container(
      width: drawerWidth,
      color: Theme.of(context).cardColor, // 使用卡片颜色作为背景
      child: Column(
        children: [
          // 顶部 Logo 或应用名称
          Container(
            height: 60, // 与底部导航高度一致
            alignment: _isDrawerOpen ? Alignment.centerLeft : Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: _isDrawerOpen ? 16 : 0),
            child: _isDrawerOpen
                ? Text(
                    '智云笔记',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                  )
                : Icon(
                    Icons.cloud, // 收起时显示图标
                    color: Theme.of(context).colorScheme.primary,
                  ),
          ),
          const Divider(height: 1),
          // 导航项列表
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildSideNavItem(
                  icon: Icons.home_outlined,
                  label: '首页',
                  index: 0,
                  onTap: () {
                    if (widget.currentIndex != 0) {
                      Navigator.pushReplacementNamed(context, AppRoutes.home);
                    }
                  },
                ),
                _buildSideNavItem(
                  icon: Icons.favorite_border,
                  label: '收藏',
                  index: 1,
                  onTap: () {
                    if (widget.currentIndex != 1) {
                      Navigator.pushReplacementNamed(context, AppRoutes.favorites);
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        final noteProvider = Provider.of<NoteProvider>(context, listen: false);
                        noteProvider.setFilter(NoteFilter.favorite);
                        noteProvider.fetchNotes(refresh: true);
                      });
                    }
                  },
                ),
                _buildSideNavItem(
                  icon: Icons.tag_outlined,
                  label: '标签',
                  index: 2,
                  onTap: () {
                    if (widget.currentIndex != 2) {
                      Navigator.pushReplacementNamed(context, AppRoutes.tags);
                    }
                  },
                ),
                _buildSideNavItem(
                  icon: Icons.settings_outlined,
                  label: '设置',
                  index: 3,
                  onTap: () {
                    if (widget.currentIndex != 3) {
                      Navigator.pushReplacementNamed(context, AppRoutes.settings);
                    }
                  },
                ),
                // 添加收起/展开按钮
                ListTile(
                  leading: Icon(
                    _isDrawerOpen ? Icons.chevron_left : Icons.chevron_right,
                    color: Theme.of(context).iconTheme.color,
                  ),
                  title: _isDrawerOpen ? Text('收起', style: Theme.of(context).textTheme.bodyMedium) : null,
                  onTap: () {
                    setState(() {
                      _isDrawerOpen = !_isDrawerOpen;
                    });
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建侧边导航项
  Widget _buildSideNavItem({
    required IconData icon,
    required String label,
    required int index,
    required VoidCallback onTap,
  }) {
    final bool isSelected = widget.currentIndex == index;
    return ListTile(
      leading: Icon(
        isSelected ? icon : icon, // 选中状态可以考虑使用填充图标
        color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).iconTheme.color,
      ),
      title: _isDrawerOpen ? Text(label, style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).textTheme.bodyMedium?.color,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      )) : null,
      selected: isSelected,
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: _isDrawerOpen ? 16 : 24), // 调整内边距
      dense: !_isDrawerOpen, // 收起时更紧凑
      visualDensity: VisualDensity.compact, // 视觉密度
    );
  }


  Widget _buildNewNoteButton() {
    return SizedBox(
      width: 60,
      height: 60,
      child: FloatingActionButton(
        onPressed: () {
          // 使用命名路由导航到编辑器页面，传递空参数表示创建新笔记
          // 获取Provider，避免在异步回调中使用BuildContext
          final noteProvider = Provider.of<NoteProvider>(context, listen: false);

          Navigator.pushNamed(
            context,
            AppRoutes.editor,
            arguments: {'note': null}, // 传递Map参数，与其他地方保持一致
          ).then((_) {
            // 检查组件是否已经被卸载
            if (mounted) {
              // 刷新笔记列表
              noteProvider.fetchNotes(refresh: true);
            }
          });
        },
        backgroundColor: Theme.of(context).colorScheme.primary, // Use theme color
        elevation: 4,
        child: Icon(
          Icons.add,
          size: 32,
          color: Theme.of(context).colorScheme.onPrimary, // Use theme color for icon
        ),
      ),
    );
  }
}
