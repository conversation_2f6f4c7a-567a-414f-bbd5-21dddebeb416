import 'package:flutter/foundation.dart' show kIsWeb; // 导入kIsWeb常量
import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:flutter/foundation.dart';

class DraggableAIPredictionBar extends StatefulWidget {
  final String prediction;
  final VoidCallback onAccept;
  final VoidCallback onDismiss;
  final Offset initialPosition;
  final bool preventDismissOnTapOutside; // 添加属性，防止点击外部时自动关闭
  final ValueChanged<Offset>? onDrag; // 添加拖动回调，使用更具体的类型
  final bool useLocalPositioning; // 是否使用本地位置管理而非父组件管理

  const DraggableAIPredictionBar({
    Key? key,
    required this.prediction,
    required this.onAccept,
    required this.onDismiss,
    this.initialPosition = Offset.zero,
    this.preventDismissOnTapOutside = false, // 默认为false，允许点击外部关闭
    this.onDrag, // 拖动回调
    this.useLocalPositioning = false, // 默认使用父组件管理位置
  }) : super(key: key);

  @override
  _DraggableAIPredictionBarState createState() =>
      _DraggableAIPredictionBarState();
}

class _DraggableAIPredictionBarState extends State<DraggableAIPredictionBar>
    with SingleTickerProviderStateMixin {
  // ValueNotifier<Offset>? _positionNotifier = ValueNotifier<Offset>(Offset.zero); // 原实现
  ValueNotifier<Offset>? _positionNotifier; // 懒加载

  // 添加动画控制器
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // 添加尺寸测量
  final GlobalKey _contentKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    if (widget.useLocalPositioning) {
      _positionNotifier = ValueNotifier<Offset>(widget.initialPosition);
    }
    if (kDebugMode)
      debugPrint(
          '[POSITION_DEBUG] AIPredictionBar初始化，初始位置: ${widget.initialPosition}');

    // 打印屏幕尺寸信息，帮助调试
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final screenSize = MediaQuery.of(context).size;
        if (kDebugMode)
          debugPrint(
              '[POSITION_DEBUG] 屏幕尺寸: ${screenSize.width}x${screenSize.height}');
        if (kDebugMode)
          debugPrint(
              '[POSITION_DEBUG] 建议的预测栏位置: (${screenSize.width - 300}, ${screenSize.height / 2})');
      }
    });

    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    // 淡入动画
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // 滑入动画 - 从下方滑入
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    // 启动动画
    _animationController.forward();

    // 在下一帧测量内容尺寸
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureContentSize();
    });
  }

  @override
  void didUpdateWidget(DraggableAIPredictionBar oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当预测文本变化时，重置并重新播放动画
    if (widget.prediction != oldWidget.prediction &&
        widget.prediction.isNotEmpty) {
      _animationController.reset();
      _animationController.forward();

      // 当预测文本变化时，在下一帧测量内容尺寸
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _measureContentSize();
      });
    }
  }

  // 测量内容的实际尺寸
  void _measureContentSize() {
    final RenderBox? contentBox =
        _contentKey.currentContext?.findRenderObject() as RenderBox?;
    if (contentBox != null && contentBox.hasSize) {
      // 打印实际测量的尺寸，帮助调试
      if (kDebugMode)
        debugPrint(
            '[POSITION_DEBUG] 预测栏实际尺寸: ${contentBox.size.width}x${contentBox.size.height}');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _positionNotifier?.dispose(); // 仅在已初始化时释放
    super.dispose();
  }

  // 创建预测内容组件的builder方法，支持可选key
  Widget _buildPredictionBarContent({bool withKey = true}) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.8,
      ),
      child: Container(
        key: withKey ? _contentKey : null, // 仅child带key，feedback不带
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.lightGrayColor,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(25),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.lightbulb_outline,
              color: AppTheme.primaryColor,
              size: 18,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: '预测: ',
                      style: TextStyle(
                        color: Theme.of(context).textTheme.bodyLarge?.color ??
                            AppTheme.darkGrayColor,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    TextSpan(
                      text: widget.prediction,
                      style: TextStyle(
                        color: Theme.of(context).textTheme.bodyMedium?.color ??
                            AppTheme.mediumGrayColor,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            const SizedBox(width: 8),
            Semantics(
              button: true,
              label: '接受AI预测',
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    widget.onAccept();
                  },
                  borderRadius: BorderRadius.circular(4),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (kIsWeb)
                          const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                'Tab',
                                style: TextStyle(
                                  color: AppTheme.primaryColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(width: 4),
                            ],
                          ),
                        if (kIsWeb)
                          const Text(
                            '接受',
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize: 12,
                              fontWeight: FontWeight.normal,
                            ),
                          )
                        else
                          const Text(
                            '接受',
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Semantics(
              button: true,
              enabled: true,
              label: '关闭AI预测',
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    widget.onDismiss();
                  },
                  borderRadius: BorderRadius.circular(12),
                  child: Padding(
                    padding: const EdgeInsets.all(2.0),
                    child: Icon(
                      Icons.close,
                      color: Theme.of(context).textTheme.bodyMedium?.color ??
                          AppTheme.mediumGrayColor,
                      size: 18,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.prediction.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    final screenSize = MediaQuery.of(context).size;

    // 使用builder方法创建内容
    Widget predictionBarContent = _buildPredictionBarContent(withKey: true);

    // 创建基本的可拖动内容，并添加动画效果
    Widget buildDraggableContent(BuildContext context, Offset position) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Draggable<String>(
                data: 'prediction_bar',
                feedback: Material(
                  color: Colors.transparent,
                  elevation: 4.0,
                  borderRadius: BorderRadius.circular(8),
                  child: _buildPredictionBarContent(withKey: false),
                ),
                childWhenDragging: const SizedBox.shrink(),
                onDragEnd: (details) {
                  // 拖动结束时更新位置
                  // 获取预测栏的实际尺寸，用于更准确的边界计算
                  double barWidth = 200; // 默认值
                  double barHeight = 50; // 默认值

                  // 尝试获取实际尺寸
                  final RenderBox? renderBox =
                      context.findRenderObject() as RenderBox?;
                  if (renderBox != null && renderBox.hasSize) {
                    barWidth = renderBox.size.width;
                    barHeight = renderBox.size.height;
                  }

                  // 尝试从内容key获取更准确的尺寸
                  final RenderBox? contentBox = _contentKey.currentContext
                      ?.findRenderObject() as RenderBox?;
                  if (contentBox != null && contentBox.hasSize) {
                    // 使用内容的实际尺寸，这通常更准确
                    barWidth = contentBox.size.width;
                    barHeight = contentBox.size.height;
                    // 打印实际测量的尺寸，帮助调试
                    if (kDebugMode)
                      debugPrint(
                          '[POSITION_DEBUG] 预测栏实际尺寸: ${barWidth}x${barHeight}');
                  }

                  // 确保位置在屏幕范围内，使用实际测量的尺寸
                  // 为右侧边界添加额外的安全边距，确保内容完全可见
                  const safetyMargin = 20.0; // 额外的安全边距

                  // 打印原始拖动位置
                  if (kDebugMode)
                    debugPrint('[POSITION_DEBUG] 拖动结束，原始位置: ${details.offset}');
                  if (kDebugMode)
                    debugPrint(
                        '[POSITION_DEBUG] 屏幕尺寸: ${screenSize.width}x${screenSize.height}');
                  if (kDebugMode)
                    debugPrint(
                        '[POSITION_DEBUG] 预测栏尺寸: ${barWidth}x${barHeight}');

                  // 计算边界
                  final rightBoundary =
                      screenSize.width - barWidth - safetyMargin;
                  final bottomBoundary = screenSize.height - barHeight;
                  if (kDebugMode)
                    debugPrint(
                        '[POSITION_DEBUG] 边界: 右($rightBoundary), 下($bottomBoundary)');

                  // 应用边界限制
                  final offset = Offset(
                    details.offset.dx.clamp(0, rightBoundary),
                    details.offset.dy.clamp(0, bottomBoundary),
                  );

                  if (kDebugMode)
                    debugPrint('[POSITION_DEBUG] 拖动结束，计算后的位置: $offset');

                  // 更新本地位置
                  if (widget.useLocalPositioning) {
                    _positionNotifier?.value = offset;
                  }

                  // 通知父组件 - 使用绝对位置
                  if (widget.onDrag != null) {
                    // 直接赋值，无需三元表达式
                    final absoluteOffset = offset;

                    if (kDebugMode)
                      debugPrint(
                          '[POSITION_DEBUG] 调用父组件onDrag回调，传递位置: $absoluteOffset');
                    widget.onDrag!(absoluteOffset);
                  }
                },
                child: Material(
                  color: Colors.transparent,
                  elevation: 4.0,
                  borderRadius: BorderRadius.circular(8),
                  child: predictionBarContent,
                ),
              ),
            ),
          );
        },
      );
    }

    // 如果使用本地位置管理
    if (widget.useLocalPositioning) {
      return ValueListenableBuilder<Offset>(
        valueListenable: _positionNotifier!, // 已确保初始化
        builder: (context, position, child) {
          return Positioned(
            left: position.dx,
            top: position.dy,
            child: buildDraggableContent(context, position),
          );
        },
      );
    }

    // 否则返回可拖动内容，由父组件管理位置
    return buildDraggableContent(context, Offset.zero);
  }
}
