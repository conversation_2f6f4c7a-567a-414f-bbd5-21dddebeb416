import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ai_cloud_notes/services/api_service.dart';

/// AI服务类，用于管理应用的AI功能设置
class AIService extends ChangeNotifier {
  // 存储键
  static const String _aiAssistantKey = 'ai_assistant_enabled';
  static const String _smartSuggestionsKey = 'smart_suggestions_enabled';
  static const String _autoTaggingKey = 'auto_tagging_enabled';
  static const String _contentSummaryKey = 'content_summary_enabled';
  static const String _selectedModelKey = 'selected_model';
  static const String _suggestionFrequencyKey = 'suggestion_frequency';
  static const String _localProcessingKey = 'local_processing_enabled';
  static const String _allowDataCollectionKey = 'allow_data_collection';

  late ApiService _apiService;
  bool _isInitialized = false;

  // AI功能开关
  bool _aiAssistantEnabled = true;
  bool _smartSuggestionsEnabled = true;
  bool _autoTaggingEnabled = true;
  bool _contentSummaryEnabled = false;

  // 语言模型选择
  String _selectedModel = '默认模型';
  final List<Map<String, dynamic>> _modelOptions = [
    {
      'name': '默认模型',
      'description': '标准AI模型，适合大多数日常场景',
      'isPro': false,
    },
    {
      'name': '增强模型',
      'description': '更强大的AI能力，适合复杂内容分析',
      'isPro': true,
    },
    {
      'name': '轻量模型',
      'description': '速度更快，适合简单任务和低性能设备',
      'isPro': false,
    },
  ];

  // 建议频率
  String _suggestionFrequency = '中';
  final List<String> _frequencyOptions = ['低', '中', '高'];

  // 隐私设置
  bool _localProcessingEnabled = false;
  bool _allowDataCollection = true;

  AIService() {
    _loadSettings();
  }

  // 初始化API服务
  void initApiService(BuildContext context, ApiService apiService) {
    if (!_isInitialized) {
      try {
        _apiService = apiService;
        _isInitialized = true;

        // 使用Future.microtask延迟执行，避免在build过程中更新状态
        Future.microtask(() => _syncFromServerWithRetry());
      } catch (e) {
        debugPrint('初始化AI服务失败: $e');
        // 即使初始化失败，也标记为已初始化，避免重复尝试
        _isInitialized = true;
      }
    }
  }

  // 带重试机制的服务器同步
  Future<void> _syncFromServerWithRetry({int retryCount = 3, int delaySeconds = 1}) async {
    if (!_isInitialized) return;

    for (int i = 0; i < retryCount; i++) {
      try {
        await _syncFromServer();
        return; // 成功同步，返回
      } catch (e) {
        debugPrint('同步AI设置失败 (尝试 ${i+1}/$retryCount): $e');
        if (i < retryCount - 1) {
          // 如果还有重试机会，等待一段时间再尝试
          await Future.delayed(Duration(seconds: delaySeconds * (i + 1)));
        }
      }
    }

    // 所有重试都失败后，使用本地设置
    debugPrint('同步AI设置失败，使用本地设置');
  }

  // 获取AI助手状态
  bool get aiAssistantEnabled => _aiAssistantEnabled;

  // 获取智能建议状态
  bool get smartSuggestionsEnabled => _smartSuggestionsEnabled;

  // 获取自动标签状态
  bool get autoTaggingEnabled => _autoTaggingEnabled;

  // 获取内容摘要状态
  bool get contentSummaryEnabled => _contentSummaryEnabled;

  // 获取选中的模型
  String get selectedModel => _selectedModel;

  // 获取模型选项
  List<Map<String, dynamic>> get modelOptions => _modelOptions;

  // 获取建议频率
  String get suggestionFrequency => _suggestionFrequency;

  // 获取频率选项
  List<String> get frequencyOptions => _frequencyOptions;

  // 获取本地处理状态
  bool get localProcessingEnabled => _localProcessingEnabled;

  // 获取数据收集状态
  bool get allowDataCollection => _allowDataCollection;

  // 从本地加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 加载AI功能开关
      _aiAssistantEnabled = prefs.getBool(_aiAssistantKey) ?? true;
      _smartSuggestionsEnabled = prefs.getBool(_smartSuggestionsKey) ?? true;
      _autoTaggingEnabled = prefs.getBool(_autoTaggingKey) ?? true;
      _contentSummaryEnabled = prefs.getBool(_contentSummaryKey) ?? false;

      // 加载语言模型选择
      _selectedModel = prefs.getString(_selectedModelKey) ?? '默认模型';

      // 加载建议频率
      _suggestionFrequency = prefs.getString(_suggestionFrequencyKey) ?? '中';

      // 加载隐私设置
      _localProcessingEnabled = prefs.getBool(_localProcessingKey) ?? false;
      _allowDataCollection = prefs.getBool(_allowDataCollectionKey) ?? true;

      debugPrint('成功从本地加载AI设置');
    } catch (e) {
      debugPrint('从本地加载AI设置失败: $e');
      // 使用默认设置
      _aiAssistantEnabled = true;
      _smartSuggestionsEnabled = true;
      _autoTaggingEnabled = true;
      _contentSummaryEnabled = false;
      _selectedModel = '默认模型';
      _suggestionFrequency = '中';
      _localProcessingEnabled = false;
      _allowDataCollection = true;
    }

    notifyListeners();
  }

  // 保存设置到本地
  Future<void> _saveSettingsLocally({
    required bool aiAssistantEnabled,
    required bool smartSuggestionsEnabled,
    required bool autoTaggingEnabled,
    required bool contentSummaryEnabled,
    required String selectedModel,
    required String suggestionFrequency,
    required bool localProcessingEnabled,
    required bool allowDataCollection,
  }) async {
    final prefs = await SharedPreferences.getInstance();

    // 保存AI功能开关
    await prefs.setBool(_aiAssistantKey, aiAssistantEnabled);
    _aiAssistantEnabled = aiAssistantEnabled;

    await prefs.setBool(_smartSuggestionsKey, smartSuggestionsEnabled);
    _smartSuggestionsEnabled = smartSuggestionsEnabled;

    await prefs.setBool(_autoTaggingKey, autoTaggingEnabled);
    _autoTaggingEnabled = autoTaggingEnabled;

    await prefs.setBool(_contentSummaryKey, contentSummaryEnabled);
    _contentSummaryEnabled = contentSummaryEnabled;

    // 保存语言模型选择
    await prefs.setString(_selectedModelKey, selectedModel);
    _selectedModel = selectedModel;

    // 保存建议频率
    await prefs.setString(_suggestionFrequencyKey, suggestionFrequency);
    _suggestionFrequency = suggestionFrequency;

    // 保存隐私设置
    await prefs.setBool(_localProcessingKey, localProcessingEnabled);
    _localProcessingEnabled = localProcessingEnabled;

    await prefs.setBool(_allowDataCollectionKey, allowDataCollection);
    _allowDataCollection = allowDataCollection;

    notifyListeners();
  }

  // 从服务器同步设置
  Future<void> _syncFromServer() async {
    if (!_isInitialized) return;

    try {
      debugPrint('从服务器同步AI设置');

      // 调用API获取用户的AI设置
      final settings = await _apiService.getUserAISettings();
      if (settings != null) {
        await _saveSettingsLocally(
          aiAssistantEnabled: settings['aiAssistantEnabled'] ?? true,
          smartSuggestionsEnabled: settings['smartSuggestionsEnabled'] ?? true,
          autoTaggingEnabled: settings['autoTaggingEnabled'] ?? true,
          contentSummaryEnabled: settings['contentSummaryEnabled'] ?? false,
          selectedModel: settings['selectedModel'] ?? '默认模型',
          suggestionFrequency: settings['suggestionFrequency'] ?? '中',
          localProcessingEnabled: settings['localProcessingEnabled'] ?? false,
          allowDataCollection: settings['allowDataCollection'] ?? true,
        );
        debugPrint('从服务器成功同步AI设置');
      } else {
        debugPrint('从服务器获取的AI设置为空');
        // 抛出异常以触发重试
        throw Exception('从服务器获取的AI设置为空');
      }
    } catch (e) {
      debugPrint('从服务器同步AI设置失败: $e');
      // 当发生异常时，抛出异常以便重试机制捕获
      throw e;
    }
  }

  // 同步设置到服务器
  Future<void> _syncToServer({
    required bool aiAssistantEnabled,
    required bool smartSuggestionsEnabled,
    required bool autoTaggingEnabled,
    required bool contentSummaryEnabled,
    required String selectedModel,
    required String suggestionFrequency,
    required bool localProcessingEnabled,
    required bool allowDataCollection,
  }) async {
    if (!_isInitialized) return;

    try {
      debugPrint('同步AI设置到服务器');

      // 调用API保存用户的AI设置
      final success = await _apiService.updateUserAISettings(
        aiAssistantEnabled: aiAssistantEnabled,
        smartSuggestionsEnabled: smartSuggestionsEnabled,
        autoTaggingEnabled: autoTaggingEnabled,
        contentSummaryEnabled: contentSummaryEnabled,
        selectedModel: selectedModel,
        suggestionFrequency: suggestionFrequency,
        localProcessingEnabled: localProcessingEnabled,
        allowDataCollection: allowDataCollection,
      );

      if (success) {
        debugPrint('成功同步AI设置到服务器');
      } else {
        debugPrint('同步AI设置到服务器失败');
      }
    } catch (e) {
      debugPrint('同步AI设置到服务器失败: $e');
    }
  }

  // 保存设置 (本地 + 服务器)
  Future<void> saveSettings({
    required bool aiAssistantEnabled,
    required bool smartSuggestionsEnabled,
    required bool autoTaggingEnabled,
    required bool contentSummaryEnabled,
    required String selectedModel,
    required String suggestionFrequency,
    required bool localProcessingEnabled,
    required bool allowDataCollection,
  }) async {
    // 先保存到本地
    await _saveSettingsLocally(
      aiAssistantEnabled: aiAssistantEnabled,
      smartSuggestionsEnabled: smartSuggestionsEnabled,
      autoTaggingEnabled: autoTaggingEnabled,
      contentSummaryEnabled: contentSummaryEnabled,
      selectedModel: selectedModel,
      suggestionFrequency: suggestionFrequency,
      localProcessingEnabled: localProcessingEnabled,
      allowDataCollection: allowDataCollection,
    );

    // 如果已初始化API，同步到服务器
    if (_isInitialized) {
      _syncToServer(
        aiAssistantEnabled: aiAssistantEnabled,
        smartSuggestionsEnabled: smartSuggestionsEnabled,
        autoTaggingEnabled: autoTaggingEnabled,
        contentSummaryEnabled: contentSummaryEnabled,
        selectedModel: selectedModel,
        suggestionFrequency: suggestionFrequency,
        localProcessingEnabled: localProcessingEnabled,
        allowDataCollection: allowDataCollection,
      );
    }
  }

  // 获取建议频率描述
  String getFrequencyDescription(String frequency) {
    switch (frequency) {
      case '低':
        return '仅在长时间编辑后提供建议，减少打扰';
      case '中':
        return '适度提供建议，保持良好的写作体验';
      case '高':
        return '频繁提供建议，最大化AI辅助效果';
      default:
        return '';
    }
  }
}
