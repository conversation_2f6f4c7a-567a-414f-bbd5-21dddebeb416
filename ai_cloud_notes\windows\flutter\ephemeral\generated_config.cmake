# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\cursorProject\\textTask\\ai_cloud_notes" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\flutter"
  "PROJECT_DIR=D:\\cursorProject\\textTask\\ai_cloud_notes"
  "FLUTTER_ROOT=D:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\cursorProject\\textTask\\ai_cloud_notes\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\cursorProject\\textTask\\ai_cloud_notes"
  "FLUTTER_TARGET=D:\\cursorProject\\textTask\\ai_cloud_notes\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\cursorProject\\textTask\\ai_cloud_notes\\.dart_tool\\package_config.json"
)
