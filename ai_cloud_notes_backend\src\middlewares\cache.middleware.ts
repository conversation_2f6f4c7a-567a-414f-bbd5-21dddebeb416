import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import redisClient from '../utils/redis';

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  CACHE_FIRST = 'cache-first',           // 缓存优先
  NETWORK_FIRST = 'network-first',       // 网络优先
  CACHE_ONLY = 'cache-only',             // 仅缓存
  NETWORK_ONLY = 'network-only',         // 仅网络
  STALE_WHILE_REVALIDATE = 'swr',        // 过期时重新验证
}

/**
 * 缓存配置接口
 */
interface CacheConfig {
  strategy?: CacheStrategy;
  ttl?: number;                          // 生存时间（秒）
  keyGenerator?: (req: Request) => string; // 自定义键生成器
  condition?: (req: Request, res: Response) => boolean; // 缓存条件
  vary?: string[];                       // 变化头
  tags?: string[];                       // 缓存标签
}

/**
 * 缓存项接口
 */
interface CacheItem {
  data: any;
  headers: Record<string, string>;
  statusCode: number;
  createdAt: number;
  expiresAt?: number;
  tags?: string[];
  etag?: string;
}

/**
 * 默认缓存配置
 */
const DEFAULT_CONFIG: Required<CacheConfig> = {
  strategy: CacheStrategy.CACHE_FIRST,
  ttl: 300, // 5分钟
  keyGenerator: (req: Request) => `cache:${req.method}:${req.originalUrl}`,
  condition: (req: Request, res: Response) => req.method === 'GET' && res.statusCode === 200,
  vary: ['Authorization', 'Accept-Language'],
  tags: [],
};

/**
 * 生成缓存键
 */
function generateCacheKey(req: Request, config: CacheConfig): string {
  const baseKey = config.keyGenerator ? config.keyGenerator(req) : DEFAULT_CONFIG.keyGenerator(req);
  
  // 添加变化头到键中
  const varyHeaders = config.vary || DEFAULT_CONFIG.vary;
  const varyValues = varyHeaders.map(header => req.get(header) || '').join(':');
  
  return varyValues ? `${baseKey}:${Buffer.from(varyValues).toString('base64')}` : baseKey;
}

/**
 * 生成ETag
 */
function generateETag(data: any): string {
  const crypto = require('crypto');
  return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex');
}

/**
 * 检查缓存是否过期
 */
function isCacheExpired(item: CacheItem): boolean {
  if (!item.expiresAt) return false;
  return Date.now() > item.expiresAt;
}

/**
 * 检查缓存是否陈旧
 */
function isCacheStale(item: CacheItem, staleTtl: number = 60): boolean {
  const staleTime = item.createdAt + (staleTtl * 1000);
  return Date.now() > staleTime;
}

/**
 * 缓存中间件工厂
 */
export function cache(config: CacheConfig = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  return async (req: Request, res: Response, next: NextFunction) => {
    const cacheKey = generateCacheKey(req, finalConfig);
    
    try {
      // 检查是否应该使用缓存
      if (finalConfig.strategy === CacheStrategy.NETWORK_ONLY) {
        return next();
      }

      // 尝试从缓存获取数据
      const cachedData = await redisClient.get(cacheKey);
      let cacheItem: CacheItem | null = null;

      if (cachedData) {
        try {
          cacheItem = JSON.parse(cachedData);
        } catch (e) {
          logger.warn(`解析缓存数据失败: ${e}`);
        }
      }

      // 处理不同的缓存策略
      switch (finalConfig.strategy) {
        case CacheStrategy.CACHE_ONLY:
          if (cacheItem && !isCacheExpired(cacheItem)) {
            return sendCachedResponse(res, cacheItem);
          } else {
            return res.status(404).json({
              success: false,
              error: { message: '缓存中未找到数据' }
            });
          }

        case CacheStrategy.CACHE_FIRST:
          if (cacheItem && !isCacheExpired(cacheItem)) {
            return sendCachedResponse(res, cacheItem);
          }
          break;

        case CacheStrategy.STALE_WHILE_REVALIDATE:
          if (cacheItem) {
            if (!isCacheExpired(cacheItem)) {
              return sendCachedResponse(res, cacheItem);
            } else if (isCacheStale(cacheItem)) {
              // 返回陈旧数据，同时在后台重新验证
              setImmediate(() => {
                // 这里可以触发后台更新
                logger.info(`后台重新验证缓存: ${cacheKey}`);
              });
              return sendCachedResponse(res, cacheItem);
            }
          }
          break;

        case CacheStrategy.NETWORK_FIRST:
        default:
          // 继续到下一个中间件，但准备缓存响应
          break;
      }

      // 拦截响应以进行缓存
      const originalSend = res.send;
      const originalJson = res.json;

      res.send = function(data: any) {
        cacheResponse(req, res, data, cacheKey, finalConfig);
        return originalSend.call(this, data);
      };

      res.json = function(data: any) {
        cacheResponse(req, res, data, cacheKey, finalConfig);
        return originalJson.call(this, data);
      };

      next();

    } catch (error) {
      logger.error(`缓存中间件错误: ${error}`);
      next();
    }
  };
}

/**
 * 发送缓存的响应
 */
function sendCachedResponse(res: Response, cacheItem: CacheItem) {
  // 设置缓存头
  res.set('X-Cache', 'HIT');
  res.set('X-Cache-Created', new Date(cacheItem.createdAt).toISOString());
  
  if (cacheItem.etag) {
    res.set('ETag', cacheItem.etag);
  }

  // 设置其他头
  Object.entries(cacheItem.headers).forEach(([key, value]) => {
    res.set(key, value);
  });

  res.status(cacheItem.statusCode).send(cacheItem.data);
}

/**
 * 缓存响应
 */
async function cacheResponse(
  req: Request, 
  res: Response, 
  data: any, 
  cacheKey: string, 
  config: Required<CacheConfig>
) {
  try {
    // 检查是否应该缓存
    if (!config.condition(req, res)) {
      return;
    }

    const now = Date.now();
    const etag = generateETag(data);
    
    const cacheItem: CacheItem = {
      data,
      headers: {
        'Content-Type': res.get('Content-Type') || 'application/json',
        'Cache-Control': `max-age=${config.ttl}`,
      },
      statusCode: res.statusCode,
      createdAt: now,
      expiresAt: now + (config.ttl * 1000),
      tags: config.tags,
      etag,
    };

    // 设置响应头
    res.set('X-Cache', 'MISS');
    res.set('ETag', etag);
    res.set('Cache-Control', `max-age=${config.ttl}`);

    // 存储到Redis
    await redisClient.setex(cacheKey, config.ttl, JSON.stringify(cacheItem));

    // 如果有标签，建立标签索引
    if (config.tags.length > 0) {
      for (const tag of config.tags) {
        await redisClient.sadd(`cache:tag:${tag}`, cacheKey);
        await redisClient.expire(`cache:tag:${tag}`, config.ttl);
      }
    }

    logger.debug(`缓存已设置: ${cacheKey}, TTL: ${config.ttl}秒`);

  } catch (error) {
    logger.error(`缓存响应失败: ${error}`);
  }
}

/**
 * 按标签清除缓存
 */
export async function clearCacheByTag(tag: string): Promise<void> {
  try {
    const cacheKeys = await redisClient.smembers(`cache:tag:${tag}`);
    
    if (cacheKeys.length > 0) {
      await redisClient.del(...cacheKeys);
      await redisClient.del(`cache:tag:${tag}`);
      logger.info(`已清除标签 ${tag} 的 ${cacheKeys.length} 个缓存项`);
    }
  } catch (error) {
    logger.error(`按标签清除缓存失败: ${error}`);
  }
}

/**
 * 清除匹配模式的缓存
 */
export async function clearCacheByPattern(pattern: string): Promise<void> {
  try {
    const keys = await redisClient.keys(pattern);
    
    if (keys.length > 0) {
      await redisClient.del(...keys);
      logger.info(`已清除 ${keys.length} 个匹配模式 ${pattern} 的缓存项`);
    }
  } catch (error) {
    logger.error(`按模式清除缓存失败: ${error}`);
  }
}

/**
 * 获取缓存统计信息
 */
export async function getCacheStats(): Promise<any> {
  try {
    const info = await redisClient.info('memory');
    const keyspace = await redisClient.info('keyspace');
    
    return {
      memory: info,
      keyspace: keyspace,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    logger.error(`获取缓存统计失败: ${error}`);
    return null;
  }
}

/**
 * 预设的缓存配置
 */
export const CachePresets = {
  // 短期缓存（5分钟）
  SHORT: {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 300,
  },
  
  // 中期缓存（1小时）
  MEDIUM: {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 3600,
  },
  
  // 长期缓存（24小时）
  LONG: {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 86400,
  },
  
  // 用户相关缓存
  USER: {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 1800, // 30分钟
    keyGenerator: (req: Request) => {
      const userId = (req as any).user?.id || 'anonymous';
      return `cache:user:${userId}:${req.method}:${req.originalUrl}`;
    },
  },
  
  // 静态内容缓存
  STATIC: {
    strategy: CacheStrategy.CACHE_FIRST,
    ttl: 604800, // 7天
    condition: (req: Request, res: Response) => 
      req.method === 'GET' && 
      res.statusCode === 200 && 
      (req.originalUrl.includes('/static/') || req.originalUrl.includes('/assets/')),
  },
};
