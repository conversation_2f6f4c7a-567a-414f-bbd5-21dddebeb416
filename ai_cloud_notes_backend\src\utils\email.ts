import nodemailer from 'nodemailer';
import { config } from '../config';
import { logger } from './logger';

/**
 * 创建邮件发送器
 */
export const transporter = nodemailer.createTransport({
  host: config.email.host,
  port: config.email.port,
  secure: config.email.secure, // 使用SSL
  auth: {
    user: config.email.auth.user,
    pass: config.email.auth.pass,
  },
  // 添加QQ邮箱需要的额外配置
  tls: {
    // 不验证服务器证书
    rejectUnauthorized: false
  }
});

/**
 * 发送欢迎邮件
 * @param to 收件人邮箱
 * @param username 用户名
 */
export const sendWelcomeEmail = async (to: string, username: string): Promise<boolean> => {
  try {
    // 邮件内容
    const mailOptions = {
      from: `"智云笔记" <${config.email.auth.user}>`,
      to,
      subject: '欢迎加入智云笔记',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e8e8e8; border-radius: 5px;">
          <h2 style="color: #5D5FEF; text-align: center;">欢迎加入智云笔记!</h2>
          <p>亲爱的 <strong>${username}</strong>,</p>
          <p>感谢您注册智云笔记，我们很高兴您能成为我们社区的一部分。</p>
          <p>现在，您可以：</p>
          <ul>
            <li>创建和管理您的笔记</li>
            <li>设置自定义标签进行整理</li>
            <li>在多设备间同步您的内容</li>
            <li>使用AI功能辅助您的写作</li>
          </ul>
          <p>如果您有任何问题或建议，请随时联系我们的支持团队。</p>
          <p>祝您使用愉快！</p>
          <div style="text-align: center; margin-top: 20px; color: #888;">
            <p>智云笔记团队</p>
          </div>
        </div>
      `
    };

    // 发送邮件
    const info = await transporter.sendMail(mailOptions);
    logger.info(`欢迎邮件发送成功: ${info.messageId}`);
    return true;
  } catch (error: any) {
    logger.error(`欢迎邮件发送失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};

/**
 * 发送密码重置邮件
 * @param to 收件人邮箱
 * @param token 重置令牌
 * @param username 用户名
 */
export const sendPasswordResetEmail = async (to: string, token: string, username: string): Promise<boolean> => {
  try {
    // 构建重置链接
    const resetLink = `${config.frontend.url}/forgot-password?token=${token}&email=${encodeURIComponent(to)}`;

    // 邮件内容
    const mailOptions = {
      from: `"智云笔记" <${config.email.auth.user}>`,
      to,
      subject: '智云笔记密码重置',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e8e8e8; border-radius: 5px;">
          <h2 style="color: #5D5FEF; text-align: center;">密码重置请求</h2>
          <p>亲爱的 <strong>${username}</strong>,</p>
          <p>我们收到了重置您智云笔记账户密码的请求。如果这不是您本人操作，请忽略此邮件。</p>
          <p>要重置密码，请点击下面的链接：</p>
          <p style="text-align: center;">
            <a
              href="${resetLink}"
              style="display: inline-block; padding: 10px 20px; background-color: #5D5FEF; color: white; text-decoration: none; border-radius: 5px;"
            >
              重置密码
            </a>
          </p>
          <p>或者复制以下链接到浏览器地址栏：</p>
          <p style="background-color: #f5f5f5; padding: 10px; border-radius: 5px; word-break: break-all;">
            ${resetLink}
          </p>
          <p><strong>注意：</strong> 此链接将在1小时后失效。</p>
          <p>如果您没有请求重置密码，请立即联系我们的支持团队。</p>
          <div style="text-align: center; margin-top: 20px; color: #888;">
            <p>智云笔记团队</p>
          </div>
        </div>
      `
    };

    // 发送邮件
    const info = await transporter.sendMail(mailOptions);
    logger.info(`密码重置邮件发送成功: ${info.messageId}`);
    return true;
  } catch (error: any) {
    logger.error(`密码重置邮件发送失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};

/**
 * 发送邮箱验证码
 * @param to 收件人邮箱
 * @param code 验证码
 */
export const sendVerificationCodeEmail = async (to: string, code: string): Promise<boolean> => {
  try {
    // 邮件内容
    const mailOptions = {
      from: `"智云笔记" <${config.email.auth.user}>`,
      to,
      subject: '智云笔记注册验证码',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e8e8e8; border-radius: 5px;">
          <h2 style="color: #5D5FEF; text-align: center;">邮箱验证码</h2>
          <p>您好，</p>
          <p>感谢您注册智云笔记。为了完成注册流程，请使用以下验证码：</p>
          <div style="text-align: center; margin: 30px 0;">
            <div style="display: inline-block; padding: 15px 30px; background-color: #f5f5f5; border-radius: 5px; font-size: 24px; font-weight: bold; letter-spacing: 5px; color: #5D5FEF;">
              ${code}
            </div>
          </div>
          <p>验证码将在5分钟内有效。如果您没有注册智云笔记账户，请忽略此邮件。</p>
          <p>请勿回复此邮件，此邮箱不接收邮件。</p>
          <div style="text-align: center; margin-top: 20px; color: #888;">
            <p>智云笔记团队</p>
          </div>
        </div>
      `
    };

    // 发送邮件
    const info = await transporter.sendMail(mailOptions);
    logger.info(`验证码邮件发送成功: ${info.messageId}`);
    return true;
  } catch (error: any) {
    logger.error(`验证码邮件发送失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};

/**
 * 发送密码修改通知邮件
 * @param to 收件人邮箱
 * @param username 用户名
 * @param time 修改时间
 */
export const sendPasswordChangedEmail = async (to: string, username: string, time: Date): Promise<boolean> => {
  try {
    // 格式化时间
    const formattedTime = time.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });

    // 邮件内容
    const mailOptions = {
      from: `"智云笔记" <${config.email.auth.user}>`,
      to,
      subject: '智云笔记密码已修改',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e8e8e8; border-radius: 5px;">
          <h2 style="color: #5D5FEF; text-align: center;">密码已修改</h2>
          <p>亲爱的 <strong>${username}</strong>,</p>
          <p>您的智云笔记账户密码已于 <strong>${formattedTime}</strong> 成功修改。</p>
          <p>如果这是您本人操作，您可以忽略此邮件。</p>
          <p>如果这不是您本人操作，您的账户可能已被他人访问。请立即采取以下措施：</p>
          <ol>
            <li>使用"忘记密码"功能重置您的密码</li>
            <li>检查您的账户活动，确认是否有可疑操作</li>
            <li>联系我们的支持团队报告这一情况</li>
          </ol>
          <p>为了保护您的账户安全，我们建议您：</p>
          <ul>
            <li>使用强密码（包含字母、数字和特殊字符）</li>
            <li>定期更换密码</li>
            <li>不要在不同网站使用相同的密码</li>
          </ul>
          <div style="text-align: center; margin-top: 20px; color: #888;">
            <p>智云笔记团队</p>
          </div>
        </div>
      `
    };

    // 发送邮件
    const info = await transporter.sendMail(mailOptions);
    logger.info(`密码修改通知邮件发送成功: ${info.messageId}`);
    return true;
  } catch (error: any) {
    logger.error(`密码修改通知邮件发送失败: ${error.message}`, { stack: error.stack });
    return false;
  }
};