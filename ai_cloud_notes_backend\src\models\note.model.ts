import mongoose, { Document, Schema } from 'mongoose';

/**
 * 笔记文档接口
 */
export interface INoteDocument extends Document {
  title: string;
  content: string;
  contentType: string;
  tags: mongoose.Types.ObjectId[];
  owner: mongoose.Types.ObjectId;
  isFavorite: boolean;
  isArchived: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastSyncedAt: Date;
  // 分享相关字段
  shareToken?: string;
  shareExpireAt?: Date;
  isPublicShared?: boolean;
  shareAccessType?: 'readonly' | 'editable';
  sharePassword?: string;
  // 同步相关字段
  version?: number;
  isDeleted?: boolean;
}

/**
 * 笔记模式定义
 */
const NoteSchema = new Schema(
  {
    // 笔记标题
    title: {
      type: String,
      required: [true, '标题是必填项'],
      trim: true,
      maxlength: [100, '标题最多100个字符'],
    },

    // 笔记内容
    content: {
      type: String,
      default: '',
    },

    // 内容类型（如plain-text, rich-text, markdown等）
    contentType: {
      type: String,
      enum: ['plain-text', 'rich-text', 'markdown'],
      default: 'rich-text',
    },

    // 关联的标签ID数组
    tags: [{
      type: Schema.Types.ObjectId,
      ref: 'Tag',
    }],

    // 笔记所有者
    owner: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },

    // 是否收藏
    isFavorite: {
      type: Boolean,
      default: false,
    },

    // 是否归档
    isArchived: {
      type: Boolean,
      default: false,
    },

    // 最后同步时间
    lastSyncedAt: {
      type: Date,
      default: Date.now,
    },

    // 分享令牌
    shareToken: {
      type: String,
      index: true,
      sparse: true, // 稀疏索引，只为有值的文档创建索引
    },

    // 分享过期时间
    shareExpireAt: {
      type: Date,
    },

    // 是否公开分享（不需要令牌）
    isPublicShared: {
      type: Boolean,
      default: false,
    },

    // 分享访问类型（只读或可编辑）
    shareAccessType: {
      type: String,
      enum: ['readonly', 'editable'],
      default: 'readonly',
    },

    // 分享密码
    sharePassword: {
      type: String,
      select: false, // 默认不返回密码
    },

    // 版本号（用于同步冲突解决）
    version: {
      type: Number,
      default: 1,
    },

    // 软删除标志（用于同步，允许客户端知道笔记已被删除）
    isDeleted: {
      type: Boolean,
      default: false,
    },
  },
  {
    // 自动创建createdAt和updatedAt字段
    timestamps: true,
  }
);

// 索引设置，用于提高查询性能
NoteSchema.index({ owner: 1 });
NoteSchema.index({ tags: 1 });
NoteSchema.index({ isFavorite: 1 });
NoteSchema.index({ isArchived: 1 });
NoteSchema.index({ title: 'text', content: 'text' }); // 全文索引用于搜索

/**
 * 定义笔记模型
 */
const Note = mongoose.model<INoteDocument>('Note', NoteSchema);

export default Note;