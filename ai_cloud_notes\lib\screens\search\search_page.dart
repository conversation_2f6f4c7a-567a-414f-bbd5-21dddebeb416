import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/screens/editor/editor_page.dart';
import 'package:ai_cloud_notes/providers/search_provider.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:ai_cloud_notes/utils/date_time_helper.dart';
import 'package:ai_cloud_notes/widgets/note_content_preview.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';

class SearchPage extends StatefulWidget {
  final String? initialQuery;

  const SearchPage({
    Key? key,
    this.initialQuery,
  }) : super(key: key);

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // 语音搜索
  final stt.SpeechToText _speech = stt.SpeechToText();
  bool _isListening = false;
  bool _speechAvailable = false;

  // 筛选标签
  List<String> _filterTags = ['全部', '标题', '内容', '最近7天', '最近30天'];
  int _selectedFilterIndex = 0;

  // 高级搜索选项
  bool _showAdvancedSearch = false;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();

    _searchFocusNode.addListener(_onFocusChange);
    _initializeSpeechRecognition();

    // 监听搜索输入变化
    _searchController.addListener(() {
      final searchProvider = Provider.of<SearchProvider>(context, listen: false);
      searchProvider.setSearchQuery(_searchController.text);
    });

    // 打开页面时自动聚焦搜索框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });

    // 加载搜索历史和热门搜索
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final searchProvider = Provider.of<SearchProvider>(context, listen: false);
      searchProvider.getSearchHistory();
      searchProvider.getPopularSearches();

      // 初始化搜索提供者
      Provider.of<SearchProvider>(context, listen: false).initialize();

      // 如果有初始查询参数，则设置到搜索框并执行搜索
      if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
        _searchController.text = widget.initialQuery!;

        // 设置查询参数并执行搜索
        searchProvider.setSearchQuery(widget.initialQuery!);
        searchProvider.searchNotes();
      }
    });
  }

  // 初始化语音搜索
  Future<void> _initializeSpeechRecognition() async {
    try {
      bool available = await _speech.initialize();
      setState(() {
        _speechAvailable = available;
      });
    } catch (e) {
      setState(() {
        _speechAvailable = false;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SearchProvider>(
      builder: (context, searchProvider, child) {
        final theme = Theme.of(context);
        return Scaffold(
          // backgroundColor: Colors.white, // 使用主题背景色
          body: SafeArea(
            child: Column(
              children: [
                _buildSearchHeader(),
                if (_showAdvancedSearch) _buildAdvancedSearchOptions(),
                if (_searchController.text.isNotEmpty) _buildSearchFilters(),
                if (searchProvider.isSearching && !searchProvider.isLoadingResults)
                  LinearProgressIndicator(color: theme.colorScheme.primary),
                Expanded(
                  child: _searchController.text.isNotEmpty
                      ? _buildSearchResults(searchProvider)
                      : _buildSearchSuggestions(searchProvider),
                ),
              ],
            ),
          ),
        );
      }
    );
  }

  Widget _buildSearchHeader() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.appBarTheme.backgroundColor ?? theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.05),
            offset: const Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Row(
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.arrow_back,
                color: theme.appBarTheme.iconTheme?.color ?? theme.iconTheme.color,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: theme.inputDecorationTheme.fillColor ?? theme.colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                style: theme.textTheme.bodyLarge,
                decoration: InputDecoration(
                  hintText: '搜索笔记',
                  hintStyle: theme.inputDecorationTheme.hintStyle,
                  prefixIcon: Icon(
                    Icons.search,
                    color: theme.inputDecorationTheme.prefixIconColor ?? theme.iconTheme.color?.withOpacity(0.6),
                    size: 20,
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: theme.inputDecorationTheme.suffixIconColor ?? theme.iconTheme.color?.withOpacity(0.6),
                            size: 20,
                          ),
                          onPressed: _clearSearch,
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 10),
                ),
                textInputAction: TextInputAction.search,
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    _performSearch();
                    _addToSearchHistory(value);
                  }
                },
              ),
            ),
          ),
          const SizedBox(width: 8),
          // 语音搜索按钮
          InkWell(
            onTap: _startListening,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _isListening ? theme.colorScheme.primary.withOpacity(0.2) : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _isListening ? Icons.mic : Icons.mic_none,
                color: _isListening ? theme.colorScheme.primary : theme.iconTheme.color,
                size: 24,
              ),
            ),
          ),
          const SizedBox(width: 8),
          // 高级搜索按钮
          InkWell(
            onTap: () {
              setState(() {
                _showAdvancedSearch = !_showAdvancedSearch;
              });
            },
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: _showAdvancedSearch ? theme.colorScheme.primary.withOpacity(0.2) : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.tune,
                color: _showAdvancedSearch ? theme.colorScheme.primary : theme.iconTheme.color,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSearchOptions() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border(
          bottom: BorderSide(
            color: theme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '高级搜索',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('开始日期', style: theme.textTheme.bodySmall),
                    const SizedBox(height: 8),
                    GestureDetector(
                      onTap: () => _selectDate(true),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(color: theme.dividerColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _startDate != null
                              ? DateTimeHelper.formatDateTime(_startDate!)
                              : '选择日期',
                          style: theme.textTheme.bodyMedium?.copyWith(
                                color: _startDate != null
                                    ? theme.textTheme.bodyMedium?.color
                                    : theme.hintColor,
                              ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('结束日期', style: theme.textTheme.bodySmall),
                    const SizedBox(height: 8),
                    GestureDetector(
                      onTap: () => _selectDate(false),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(color: theme.dividerColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _endDate != null
                              ? DateTimeHelper.formatDateTime(_endDate!)
                              : '选择日期',
                          style: theme.textTheme.bodyMedium?.copyWith(
                                color: _endDate != null
                                    ? theme.textTheme.bodyMedium?.color
                                    : theme.hintColor,
                              ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: _resetAdvancedSearch,
                child: Text('重置', style: TextStyle(color: theme.colorScheme.primary)),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  _performAdvancedSearch();
                  setState(() {
                    _showAdvancedSearch = false;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                ),
                child: const Text('应用'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 开始语音识别
  void _startListening() async {
    // 检查语音识别是否可用
    if (!_speech.isAvailable && !await _speech.initialize()) {
      SnackbarHelper.showError(
        context: context,
        message: '语音识别不可用'
      );
      return;
    }

    if (_isListening) {
      _speech.stop();
      setState(() {
        _isListening = false;
      });
      return;
    }

    setState(() {
      _isListening = true;
    });

    await _speech.listen(
      onResult: (result) {
        if (result.finalResult) {
          setState(() {
            _searchController.text = result.recognizedWords;
            _isListening = false;
          });
          _performSearch();
          _addToSearchHistory(result.recognizedWords);
        }
      },
      listenFor: const Duration(seconds: 30),
      pauseFor: const Duration(seconds: 5),
      localeId: 'zh_CN',
    );
  }

  // 选择日期
  Future<void> _selectDate(bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  // 重置高级搜索选项
  void _resetAdvancedSearch() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });

    // 同时重置搜索提供者中的日期范围
    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    searchProvider.setDateRange(null, null);
  }

  // 执行高级搜索
  void _performAdvancedSearch() {
    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    searchProvider.setDateRange(_startDate, _endDate);
    _performSearch();
  }

  Widget _buildSearchFilters() {
    final theme = Theme.of(context);
    return Container(
      height: 50,
      padding: const EdgeInsets.only(left: 16),
      margin: const EdgeInsets.only(bottom: 4),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filterTags.length,
        itemBuilder: (context, index) {
          final bool isSelected = index == _selectedFilterIndex;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedFilterIndex = index;
              });

              // 设置搜索过滤器
              final searchProvider = Provider.of<SearchProvider>(context, listen: false);
              switch (index) {
                case 0: // 全部
                  searchProvider.setFilter('all');
                  break;
                case 1: // 标题
                  searchProvider.setFilter('title');
                  break;
                case 2: // 内容
                  searchProvider.setFilter('content');
                  break;
                case 3: // 最近7天
                  searchProvider.setFilter('recent');
                  searchProvider.setDateRange(
                    DateTime.now().subtract(const Duration(days: 7)),
                    null
                  );
                  break;
                case 4: // 最近30天
                  searchProvider.setFilter('recent');
                  searchProvider.setDateRange(
                    DateTime.now().subtract(const Duration(days: 30)),
                    null
                  );
                  break;
              }

              _performSearch();
            },
            child: Container(
              margin: const EdgeInsets.only(right: 8),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: isSelected ? theme.colorScheme.primary.withOpacity(0.1) : theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? theme.colorScheme.primary.withOpacity(0.3) : theme.hintColor,
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                _filterTags[index],
                style: TextStyle(
                  color: isSelected ? theme.colorScheme.primary : theme.textTheme.bodyMedium?.color,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchSuggestions(SearchProvider searchProvider) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 历史搜索记录
            _buildSearchHistorySection(searchProvider),
            const SizedBox(height: 24),

            // 热门搜索
            _buildPopularSearchesSection(searchProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchHistorySection(SearchProvider searchProvider) {
    final theme = Theme.of(context);
    final searchHistoryItems = searchProvider.searchHistory;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '搜索历史',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: theme.textTheme.bodyLarge?.color,
              ),
            ),
            if (searchHistoryItems.isNotEmpty)
              GestureDetector(
                onTap: () {
                  searchProvider.clearSearchHistory();
                },
                child: Text(
                  '清除',
                  style: TextStyle(
                    fontSize: 14,
                    color: theme.hintColor,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),

        if (searchProvider.isLoadingHistory)
          Center(
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
              strokeWidth: 2,
            ),
          )
        else if (searchHistoryItems.isEmpty)
          Text(
            '暂无搜索历史',
            style: TextStyle(
              fontSize: 14,
              color: theme.hintColor,
            ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: searchHistoryItems.map((historyItem) {
              return GestureDetector(
                onTap: () {
                  _searchController.text = historyItem.query;
                  _performSearch();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    historyItem.query,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Theme.of(context).textTheme.bodyLarge?.color),
                  ),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildPopularSearchesSection(SearchProvider searchProvider) {
    final theme = Theme.of(context);
    final popularSearches = searchProvider.popularSearches;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '热门搜索',
          style: theme.textTheme.titleMedium?.copyWith(color: theme.textTheme.titleMedium?.color),
        ),
        const SizedBox(height: 16),

        if (searchProvider.isLoadingPopular)
          Center(
            child: CircularProgressIndicator(
              color: theme.colorScheme.primary,
              strokeWidth: 2,
            ),
          )
        else if (popularSearches.isEmpty)
          Text(
            '暂无热门搜索',
            style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.hintColor,
                ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: popularSearches.map((popularItem) {
              return GestureDetector(
                onTap: () => _onSearchItemSelected(popularItem.query),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primaryContainer.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    popularItem.query,
                    style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.primary,
                        ),
                  ),
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildSearchResults(SearchProvider searchProvider) {
    final theme = Theme.of(context);
    if (searchProvider.isLoadingResults) {
      return Center(
        child: CircularProgressIndicator(
          color: theme.colorScheme.primary,
        ),
      );
    }

    if (searchProvider.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              '搜索出错: ${searchProvider.error}',
              style: theme.textTheme.bodyLarge?.copyWith(color: theme.textTheme.bodyLarge?.color),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _performSearch,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (searchProvider.searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: theme.iconTheme.color?.withOpacity(0.6),
            ),
            const SizedBox(height: 16),
            Text(
              '未找到相关笔记',
              style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.hintColor,
                  ),
            ),
          ],
        ),
      );
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          if (searchProvider.hasMore && !searchProvider.isLoadingMore) {
            searchProvider.loadMoreResults();
          }
        }
        return true;
      },
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: searchProvider.searchResults.length + (searchProvider.isLoadingMore ? 1 : 0),
        separatorBuilder: (context, index) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          if (index >= searchProvider.searchResults.length) {
            return Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CircularProgressIndicator(
                  color: theme.colorScheme.primary,
                  strokeWidth: 2,
                ),
              ),
            );
          }

          final note = searchProvider.searchResults[index];
          return _buildNoteCard(note);
        },
      ),
    );
  }

  Widget _buildNoteCard(Note note) {
    final theme = Theme.of(context);
    return GestureDetector(
      onTap: () {
        // 使用命名路由导航到笔记详情
        Navigator.pushNamed(
          context,
          AppRoutes.editor,
          arguments: note,
        );
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withOpacity(0.05),
              offset: const Offset(0, 1),
              blurRadius: 3,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              note.title,
              style: theme.textTheme.titleMedium?.copyWith(color: theme.textTheme.titleMedium?.color),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            NoteContentPreview(
              note: note,
              maxLines: 2,
              fontSize: 14, // Consider using theme.textTheme.bodyMedium.fontSize
              textColor: theme.hintColor,
            ),
            if (note.tags.isNotEmpty) const SizedBox(height: 12),
            if (note.tags.isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: note.tags.map((tag) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      tag,
                      style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.primary,
                          ),
                    ),
                  );
                }).toList(),
              ),
            const SizedBox(height: 8),
            Text(
              '最后编辑: ${DateTimeHelper.formatDateTime(note.updatedAt)}',
              style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.hintColor,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  // 搜索方法
  void _performSearch() {
    if (_searchController.text.isEmpty) return;

    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    searchProvider.searchNotes();
  }

  // 清除搜索
  void _clearSearch() {
    setState(() {
      _searchController.clear();
    });

    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    searchProvider.clearSearch();
  }

  // 选择搜索项
  void _onSearchItemSelected(String query) {
    setState(() {
      _searchController.text = query;
    });
    _performSearch();
  }

  // 添加到搜索历史
  void _addToSearchHistory(String query) {
    if (query.isEmpty) return;

    final searchProvider = Provider.of<SearchProvider>(context, listen: false);
    searchProvider.addSearchHistory(query);
  }

  // 焦点变化回调
  void _onFocusChange() {
    setState(() {
      if (_searchFocusNode.hasFocus) {
        _isListening = false;
      }
    });
  }
}