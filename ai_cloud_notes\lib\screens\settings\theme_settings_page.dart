import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/theme_service.dart';

class ThemeSettingsPage extends StatefulWidget {
  const ThemeSettingsPage({Key? key}) : super(key: key);

  @override
  State<ThemeSettingsPage> createState() => _ThemeSettingsPageState();
}

class _ThemeSettingsPageState extends State<ThemeSettingsPage> {
  late ThemeService _themeService;

  // 临时存储用户选择的设置，只有在点击保存按钮时才会应用
  late ThemeMode _selectedThemeMode;
  late Color _selectedFontColor;
  late double _selectedTextSize;
  late double _selectedTitleSize;

  @override
  void initState() {
    super.initState();
    _themeService = context.read<ThemeService>();
    // _themeService.initApiService(context); // 不再需要，ApiService通过构造函数注入，认证状态由ProxyProvider管理

    // 初始化临时变量为当前设置
    _selectedThemeMode = _themeService.themeMode;

    // 安全地初始化字体颜色
    try {
      _selectedFontColor = _themeService.fontColor;
    } catch (e) {
      debugPrint('初始化字体颜色错误: $e');
      // 如果出错，使用默认颜色
      _selectedFontColor = Colors.black;
    }

    _selectedTextSize = _themeService.textSize;
    _selectedTitleSize = _themeService.titleSize;
  }

  @override
  Widget build(BuildContext context) {
    // 监听主题服务的变更，确保设置变化时UI能够重建
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        _themeService = themeService; // 更新引用

        // 当 ThemeService 更新时，同步本地选择的状态
        // 这样可以确保从服务器同步或外部更改后，UI能正确反映当前主题设置
        // 只有在 _isInitialized 之后才进行同步，以避免在 initState 之前或期间发生冲突
        // [Roo] 已移除：在 Consumer builder 中将本地选择状态与 themeService 同步的逻辑。
        // 本地选择状态 (_selectedThemeMode 等) 在 initState 中初始化，
        // 之后仅通过用户交互更新，直到用户点击“保存”。
        // 这可以防止用户当前的临时选择在保存前被 themeService 的状态覆盖，
        // 从而修复控件无法交互的问题。

        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(kToolbarHeight),
            child: _buildHeader(),
          ),
          body: SafeArea(
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          _buildThemeModeSection(),
                          const SizedBox(height: 16),
                          _buildFontColorSection(),
                          const SizedBox(height: 16),
                          _buildFontSizeSection(),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return AppBar(
      backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: Theme.of(context).iconTheme.color,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        '主题与外观',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).textTheme.titleLarge?.color,
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 16),
          child: ElevatedButton(
            onPressed: _saveSettings,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
            ),
            child: const Text(
              '保存',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildThemeModeSection() {
    return _buildSection(
      title: '主题模式',
      child: Column(
        children: [
          // 主题模式选项
          _buildThemeModeOption(
            title: '浅色模式',
            subtitle: '使用浅色主题',
            value: _selectedThemeMode == ThemeMode.light,
            onChanged: (value) {
              if (value) {
                setState(() {
                  _selectedThemeMode = ThemeMode.light;
                });
              }
            },
            icon: Icons.light_mode,
          ),
          _buildThemeModeOption(
            title: '深色模式',
            subtitle: '使用深色主题',
            value: _selectedThemeMode == ThemeMode.dark,
            onChanged: (value) {
              if (value) {
                setState(() {
                  _selectedThemeMode = ThemeMode.dark;
                });
              }
            },
            icon: Icons.dark_mode,
          ),
          _buildThemeModeOption(
            title: '跟随系统',
            subtitle: '根据系统设置自动切换亮暗模式',
            value: _selectedThemeMode == ThemeMode.system,
            onChanged: (value) {
              if (value) {
                setState(() {
                  _selectedThemeMode = ThemeMode.system;
                });
              }
            },
            icon: Icons.phone_android,
          ),
        ],
      ),
    );
  }

  Widget _buildFontColorSection() {
    return _buildSection(
      title: '字体颜色',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: _themeService.fontColorOptions.map((colorOption) {
              final bool isSelected = _selectedFontColor.value == (colorOption['color'] as Color).value;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedFontColor = colorOption['color'];
                  });
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: colorOption['color'],
                    shape: BoxShape.circle,
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: (colorOption['color'] as Color).withOpacity(0.4),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                    border: isSelected
                        ? Border.all(
                            // 如果选中的是白色，则使用灰色边框，否则使用白色
                            color: _isWhiteColor(colorOption['color'] as Color) ? Colors.grey : Colors.white,
                            width: 2,
                          )
                        : null,
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          // 如果选中的是白色，则使用黑色勾选图标，否则使用白色
                          color: _isWhiteColor(colorOption['color'] as Color) ? Colors.black : Colors.white,
                          size: 20,
                        )
                      : null,
                ),
              );
            }).toList(),
          ),
          const SizedBox(height: 16),
          Text(
            '选择的颜色: ${_getFontColorName(_selectedFontColor)}',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFontSizeSection() {
    return _buildSection(
      title: '字体大小',
      child: Column(
        children: [
          // 正文字体大小
          _buildFontSizeOption(
            title: '正文字体大小',
            selectedSize: _selectedTextSize,
            onTap: (size) {
              setState(() {
                _selectedTextSize = size;
              });
            },
          ),
          const SizedBox(height: 16),

          // 标题字体大小
          _buildFontSizeOption(
            title: '标题字体大小',
            selectedSize: _selectedTitleSize,
            onTap: (size) {
              setState(() {
                _selectedTitleSize = size;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildThemeModeOption({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required IconData icon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: value ? Theme.of(context).primaryColor : Theme.of(context).iconTheme.color,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).textTheme.titleMedium?.color,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildFontSizeOption({
    required String title,
    required double selectedSize,
    required Function(double) onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).textTheme.titleMedium?.color,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: _themeService.fontSizeOptions.map((sizeOption) {
            final bool isSelected = selectedSize == sizeOption['size'];

            return GestureDetector(
              onTap: () => onTap(sizeOption['size']),
              child: Container(
                width: 80,
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: isSelected ? Theme.of(context).primaryColor : Theme.of(context).dividerColor,
                  ),
                ),
                alignment: Alignment.center,
                child: Text(
                  sizeOption['name'],
                  style: TextStyle(
                    fontSize: sizeOption['size'],
                    color: isSelected ? Theme.of(context).primaryColor : Theme.of(context).textTheme.bodyMedium?.color,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required Widget child,
  }) {
    return Card(
      color: Theme.of(context).cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  // 判断颜色是否为白色或接近白色
  bool _isWhiteColor(Color color) {
    // 计算颜色的亮度，如果非常接近白色，则返回true
    // 使用亮度公式: 0.299*R + 0.587*G + 0.114*B
    final brightness = (0.299 * color.red + 0.587 * color.green + 0.114 * color.blue) / 255;
    // 如果亮度超过0.85，认为是白色或接近白色
    return brightness > 0.85;
  }

  // 安全地获取字体颜色名称
  String _getFontColorName(Color color) {
    try {
      // 尝试查找匹配的颜色选项
      final colorOption = _themeService.fontColorOptions.firstWhere(
        (option) => (option['color'] as Color).value == color.value,
        orElse: () => {'name': '自定义', 'color': color},
      );
      return colorOption['name'] as String;
    } catch (e) {
      // 如果出现任何错误，返回默认值
      debugPrint('获取颜色名称错误: $e');
      return '默认';
    }
  }

  void _saveSettings() {
    // 保存设置到ThemeService
    _themeService.saveSettings(
      themeMode: _selectedThemeMode,
      fontColor: _selectedFontColor,
      textSize: _selectedTextSize,
      titleSize: _selectedTitleSize,
    );

    // 显示保存成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('主题设置已保存')),
    );

    // 延迟后返回
    Future.delayed(const Duration(milliseconds: 500), () {
      Navigator.pop(context);
    });
  }
}