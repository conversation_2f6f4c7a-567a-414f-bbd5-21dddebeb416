import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import path from 'path';
import { config } from './config';
import { logger } from './utils/logger';
import apiRoutes from './api/routes';

// 导入所有模型，确保在使用前注册
import './models';

/**
 * 创建Express应用
 */
const app = express();

/**
 * 数据库连接
 */
mongoose
  .connect(config.database.uri)
  .then(() => {
    logger.info('数据库连接成功');
  })
  .catch((err) => {
    logger.error(`数据库连接失败: ${err.message}`);
    process.exit(1);
  });

/**
 * 中间件配置
 */
// 启用CORS
app.use(cors());

// 安全头配置
app.use(helmet());

// 请求体解析
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// 请求日志
app.use(morgan('dev'));

/**
 * 静态文件配置
 */
// 配置上传目录为静态资源目录
const uploadsPath = path.join(__dirname, '../uploads');
app.use('/uploads', express.static(uploadsPath));
logger.info(`静态文件目录已配置: ${uploadsPath}`);

/**
 * 路由配置
 */
// API路由
app.use('/api', apiRoutes);

// 处理404错误
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: {
      message: '未找到请求的资源',
    },
  });
});

// 全局错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error(`全局错误: ${err.message}`, { stack: err.stack });
  
  res.status(err.status || 500).json({
    success: false,
    error: {
      message: config.server.nodeEnv === 'production' 
        ? '服务器内部错误' 
        : err.message,
    },
  });
});

export default app; 