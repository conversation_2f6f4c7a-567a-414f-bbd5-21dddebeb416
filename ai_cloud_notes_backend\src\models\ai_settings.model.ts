import mongoose, { Document, Schema } from 'mongoose';

/**
 * SiliconFlow模型映射
 * 注意：FIM功能只支持特定模型，如deepseek系列和Qwen的Coder系列
 */
export const MODEL_MAPPING = {
  '默认模型': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B', // 支持FIM的模型
  '增强模型': 'Qwen/Qwen2.5-Coder-32B-Instruct', // 支持FIM的模型
  '轻量模型': 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B' // 支持FIM的模型
};

/**
 * AI设置接口
 */
export interface IAISettings extends Document {
  userId: mongoose.Types.ObjectId;
  aiAssistantEnabled: boolean;
  smartSuggestionsEnabled: boolean;
  autoTaggingEnabled: boolean;
  contentSummaryEnabled: boolean;
  selectedModel: string;
  suggestionFrequency: string;
  localProcessingEnabled: boolean;
  allowDataCollection: boolean;
  updatedAt: Date;
}

/**
 * AI设置模式
 */
const AISettingsSchema: Schema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  aiAssistantEnabled: {
    type: Boolean,
    default: true,
    required: true
  },
  smartSuggestionsEnabled: {
    type: Boolean,
    default: true,
    required: true
  },
  autoTaggingEnabled: {
    type: Boolean,
    default: true,
    required: true
  },
  contentSummaryEnabled: {
    type: Boolean,
    default: false,
    required: true
  },
  selectedModel: {
    type: String,
    default: '默认模型',
    required: true
  },
  suggestionFrequency: {
    type: String,
    enum: ['低', '中', '高'],
    default: '中',
    required: true
  },
  localProcessingEnabled: {
    type: Boolean,
    default: false,
    required: true
  },
  allowDataCollection: {
    type: Boolean,
    default: true,
    required: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// 导出模型
export default mongoose.model<IAISettings>('AISettings', AISettingsSchema);
