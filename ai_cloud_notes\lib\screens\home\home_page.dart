import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/models/tag_model.dart';
import 'package:ai_cloud_notes/screens/editor/editor_page.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/screens/home/<USER>';
import 'package:ai_cloud_notes/screens/tags/tags_page.dart';
import 'package:ai_cloud_notes/screens/profile/profile_edit_page.dart';
import 'package:ai_cloud_notes/screens/search/search_page.dart';
import 'package:ai_cloud_notes/screens/settings/settings_page.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/utils/url_helper.dart';
import 'package:ai_cloud_notes/providers/tag_provider.dart';
import 'package:ai_cloud_notes/widgets/note_content_preview.dart';
import 'package:ai_cloud_notes/widgets/main_layout.dart';
import 'package:ai_cloud_notes/services/import_export_manager.dart'; // 导入导出管理器

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  bool _isGridView = true;
  int _selectedCategoryIndex = 0;
  final List<String> _categories = ['全部', '最近', '归档'];
  String? _selectedTag; // 当前选中的标签

  // 添加批量操作相关状态
  bool _isMultiSelectMode = false; // 是否处于批量选择模式
  List<String> _selectedNoteIds = []; // 选中的笔记ID列表

  // 用于防止重复加载的标志
  bool _isFirstLoad = true;

  // 导入导出管理器
  late ImportExportManager _importExportManager;

  @override
  void initState() {
    super.initState();
    // 初始化导入导出管理器
    _importExportManager = ImportExportManager();

    // 初始化时加载笔记数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadNotes();
    });
  }

  // 记录上一次路由
  String? _previousRoute;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 获取当前路由
    final currentRoute = ModalRoute.of(context)?.settings.name;

    // 只有当从其他页面返回首页时才重新加载数据
    if ((currentRoute == AppRoutes.home || currentRoute == '/') &&
        _previousRoute != null &&
        _previousRoute != AppRoutes.home &&
        _previousRoute != '/') {
      print('DEBUG: 从${_previousRoute}返回首页，重新加载数据');
      // 使用Future.microtask确保不在build过程中调用setState
      Future.microtask(() {
        if (mounted) {
          _loadNotes();
        }
      });
    }

    // 更新上一次路由
    _previousRoute = currentRoute;
  }

  // 加载笔记数据
  Future<void> _loadNotes() async {
    if (!mounted) return;

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    final tagProvider = Provider.of<TagProvider>(context, listen: false);

    print('DEBUG: 开始加载笔记数据');

    // 设置初始筛选类型
    _updateFilterType();
    print('DEBUG: 已设置筛选类型: $_selectedCategoryIndex');

    try {
      // 先初始化笔记数据
      await noteProvider.initialize();
      print('DEBUG: 笔记提供者初始化完成，共 ${noteProvider.notes.length} 个笔记');

      // 加载标签数据
      await tagProvider.fetchTags();
      print('DEBUG: 成功加载标签数据，共 ${tagProvider.tags.length} 个标签');
    } catch (e) {
      print('DEBUG: 数据加载出错: $e');
    }
  }

  // 根据所选类别和其他筛选条件获取过滤后的笔记
  List<Note> _getFilteredNotes() {
    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    final List<Note> allNotes = noteProvider.notes;
    final List<Note> filteredNotes = _selectedTag == null
        ? allNotes
        : allNotes.where((note) => note.tagIds.contains(_selectedTag)).toList();

    switch (_selectedCategoryIndex) {
      case 0: // 全部（排除已归档的笔记）
        return filteredNotes.where((note) => !note.isArchived).toList();
      case 1: // 最近（按更新时间排序，排除已归档的笔记）
        final recentNotes = filteredNotes.where((note) => !note.isArchived).toList();
        recentNotes.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        return recentNotes;
      case 2: // 归档
        return filteredNotes.where((note) => note.isArchived).toList();
      default:
        return filteredNotes.where((note) => !note.isArchived).toList();
    }
  }

  // 更新筛选类型
  void _updateFilterType() {
    if (!mounted) return;

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);

    // 记录当前筛选状态
    final currentFilter = noteProvider.filterType;
    final currentArchived = noteProvider.archivedFilter;
    final currentSortOrder = noteProvider.sortOrder;

    // 设置对应的筛选条件
    bool needsRefresh = false;

    switch (_selectedCategoryIndex) {
      case 0: // 全部（未归档）
        if (currentFilter != NoteFilter.all || currentArchived != false) {
          noteProvider.setFilter(NoteFilter.all);
          noteProvider.setArchiveFilter(false); // 只显示未归档的笔记
          needsRefresh = true;
        }
        break;
      case 1: // 最近
        if (currentFilter != NoteFilter.all ||
            currentArchived != false ||
            currentSortOrder != NoteSortOrder.updatedAt) {
          noteProvider.setFilter(NoteFilter.all);
          noteProvider.setArchiveFilter(false); // 只显示未归档的笔记
          noteProvider.setSortOrder(NoteSortOrder.updatedAt); // 按更新时间排序
          needsRefresh = true;
        }
        break;
      case 2: // 归档
        if (currentFilter != NoteFilter.all || currentArchived != true) {
          noteProvider.setFilter(NoteFilter.all);
          noteProvider.setArchiveFilter(true); // 只显示已归档的笔记
          needsRefresh = true;
        }
        break;
      default:
        if (currentFilter != NoteFilter.all || currentArchived != false) {
          noteProvider.setFilter(NoteFilter.all);
          noteProvider.setArchiveFilter(false);
          needsRefresh = true;
        }
    }

    // 只有当筛选条件发生变化时才重新加载笔记列表
    if (needsRefresh) {
      noteProvider.fetchNotes(refresh: true);
    }
  }

  // 选择标签方法
  void _selectTag(String? tag) {
    setState(() {
      _selectedTag = tag;
    });
  }

  int _currentIndex = 0;

  // 用于控制是否需要同步路由和索引
  bool _needSyncRouteAndIndex = true;

  @override
  Widget build(BuildContext context) {
    debugPrint('[HomePage] build called. Theme - Brightness: ${Theme.of(context).brightness}, ScaffoldBG: ${Theme.of(context).scaffoldBackgroundColor}');
    // 检查当前路由，确保底部导航栏选中状态与路由一致
    // 只在第一次build或需要同步时添加回调
    if (_needSyncRouteAndIndex) {
      _needSyncRouteAndIndex = false;
      // 延迟到下一帧执行，避免在当前build过程中调用setState
      Future.microtask(() {
        if (mounted) {
          _syncCurrentIndexWithRoute();
        }
      });
    }

    // 使用批量操作栏或主页内容
    if (_isMultiSelectMode) {
      return Scaffold(
        // backgroundColor: const Color(0xFFFAFAFA), // 使用主题背景色
        body: _buildCurrentPage(),
        bottomNavigationBar: _buildBatchOperationBar(),
      );
    } else {
      // 使用MainLayout组件
      return _buildMainLayout();
    }
  }

  Widget _buildMainLayout() {
    // 导入MainLayout组件
    return MainLayout(
      currentIndex: _currentIndex,
      body: _buildCurrentPage(),
      showFab: true,
    );
  }

  // 同步当前索引与路由
  void _syncCurrentIndexWithRoute() {
    final route = ModalRoute.of(context)?.settings.name;
    if (route == null) return;

    int newIndex = _currentIndex;
    if (route == AppRoutes.home || route == '/') {
      newIndex = 0;
    } else if (route == AppRoutes.favorites) {
      newIndex = 1;
    } else if (route == AppRoutes.tags) {
      newIndex = 2;
    } else if (route == AppRoutes.settings) {
      newIndex = 3;
    }

    if (newIndex != _currentIndex) {
      // 使用Future.microtask确保不在build过程中调用setState
      Future.microtask(() {
        if (mounted) {
          setState(() {
            _currentIndex = newIndex;
          });
        }
      });
    }
  }

  Widget _buildCurrentPage() {
    switch (_currentIndex) {
      case 0:
        return _buildHomePage();
      case 1:
        return const FavoritesPage();
      case 2:
        return const TagsPage();
      case 3:
        return const SettingsPage();
      default:
        return _buildHomePage();
    }
  }

  Widget _buildHomePage() {
    final noteProvider = Provider.of<NoteProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);
    final filteredNotes = _getFilteredNotes();

    return SafeArea(
      child: Column(
        children: [
          _buildHeader(authProvider),
          _buildSearchBox(context),
          _buildCategories(),
          _buildNotesSection(noteProvider, filteredNotes),
        ],
      ),
    );
  }

  Widget _buildHeader(AuthProvider authProvider) {
    // 获取用户名首字符，或默认使用"用"
    String userInitial = "用";
    if (authProvider.user != null && authProvider.user!['username'] != null) {
      userInitial = authProvider.user!['username'][0];
    }

    // 获取用户头像URL
    String? avatarUrl;
    if (authProvider.user != null && authProvider.user!['avatar'] != null) {
      avatarUrl = authProvider.user!['avatar'];
      // 转换为完整URL
      avatarUrl = UrlHelper.getFullAvatarUrl(avatarUrl);
    }

    // 头像加载状态
    bool isAvatarError = false;

    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      decoration: BoxDecoration(
        color: Theme.of(context).appBarTheme.backgroundColor ?? Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.05),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左侧区域：返回按钮/关闭按钮+标题
          Row(
            children: [
              _isMultiSelectMode
                ? IconButton(
                    icon: Icon(Icons.close, color: Theme.of(context).iconTheme.color),
                    onPressed: _cancelMultiSelect,
                  )
                : const SizedBox(),
              const SizedBox(width: 8),
              Text(
                _isMultiSelectMode ? '已选择 ${_selectedNoteIds.length} 项' : '我的笔记',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ],
          ),
          // 中间区域：导入按钮
          if (!_isMultiSelectMode)
            InkWell(
              onTap: _importNotes,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.file_download,
                  color: Theme.of(context).iconTheme.color,
                  size: 20,
                ),
              ),
            ),
          // 右侧区域：头像
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                if (!_isMultiSelectMode) {
                  Navigator.pushNamed(context, AppRoutes.profile);
                }
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: avatarUrl == null ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.secondary,
                    ],
                  ) : null,
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: avatarUrl != null
                      ? Image.network(
                          avatarUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            // 显示加载失败提示（只显示一次）
                            if (!isAvatarError) {
                              isAvatarError = true;
                              // 延迟执行，避免在build过程中调用setState
                              Future.microtask(() {
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(content: Text('头像加载失败，已使用默认显示')),
                                  );
                                }
                              });
                            }
                            // 使用默认头像或渐变背景与文字首字母
                            return Image.asset(
                              'assets/images/avatar_placeholder.png',
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                // 如果默认头像也加载失败，则显示用户首字母
                                return Center(
                                  child: Text(
                                    userInitial,
                                    style: TextStyle(
                                      color: Theme.of(context).colorScheme.onPrimary,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                        )
                      : Center(
                          child: Text(
                            userInitial,
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBox(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(
          AppRoutes.search,
          arguments: '',  // 传递空字符串作为初始查询参数
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              Icons.search,
              color: AppTheme.mediumGrayColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              '搜索笔记',
              style: TextStyle(
                color: AppTheme.mediumGrayColor,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategories() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 8),
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          // 分类列表
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final bool isSelected = index == _selectedCategoryIndex;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategoryIndex = index;
                      // 更新筛选类型
                      _updateFilterType();
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? AppTheme.primaryColor.withOpacity(0.3) : AppTheme.mediumGrayColor,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.03),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      _categories[index],
                      style: TextStyle(
                        color: isSelected ? AppTheme.primaryColor : AppTheme.darkGrayColor,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          // 批量选择按钮
          if (!_isMultiSelectMode)
            Padding(
              padding: const EdgeInsets.only(left: 8, right: 8),
              child: InkWell(
                onTap: _enterMultiSelectMode,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_box_outline_blank,
                        color: AppTheme.primaryColor,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '批量',
                        style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(NoteProvider noteProvider, List<Note> filteredNotes) {
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _selectedCategoryIndex == 0 ? '所有笔记' :
                  _selectedCategoryIndex == 1 ? '最近笔记' : '已归档笔记',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isGridView = true;
                        });
                      },
                      child: Icon(
                        Icons.grid_view,
                        color: _isGridView ? AppTheme.primaryColor : AppTheme.darkGrayColor,
                      ),
                    ),
                    const SizedBox(width: 16),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isGridView = false;
                        });
                      },
                      child: Icon(
                        Icons.view_list,
                        color: !_isGridView ? AppTheme.primaryColor : AppTheme.darkGrayColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: noteProvider.isLoading && filteredNotes.isEmpty
                ? _buildLoadingIndicator()
                : filteredNotes.isEmpty
                    ? _buildEmptyState()
                    : _isGridView
                        ? _buildGridView(filteredNotes)
                        : _buildListView(filteredNotes),
          ),
          if (noteProvider.isLoading && filteredNotes.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: CircularProgressIndicator(),
            ),
          if (noteProvider.error.isNotEmpty)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                noteProvider.error,
                style: TextStyle(color: Colors.red),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text("正在加载笔记...", style: TextStyle(color: AppTheme.darkGrayColor)),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.note_alt_outlined,
            size: 80,
            color: AppTheme.mediumGrayColor,
          ),
          SizedBox(height: 16),
          Text(
            "暂无笔记",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.darkGrayColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            "点击下方的 + 按钮创建新笔记",
            style: TextStyle(color: AppTheme.darkGrayColor),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView(List<Note> notes) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          final noteProvider = Provider.of<NoteProvider>(context, listen: false);
          if (noteProvider.hasMore && !noteProvider.isLoading) {
            noteProvider.fetchNotes();
          }
        }
        return true;
      },
      child: GridView.builder(
        padding: const EdgeInsets.fromLTRB(20, 0, 20, 100),
        gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: 300, // 每个卡片的最大宽度
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1, // 宽高比，可能需要调整
        ),
        itemCount: notes.length,
        itemBuilder: (context, index) {
          final note = notes[index];
          return _buildGridNoteItem(note);
        },
      ),
    );
  }

  Widget _buildGridNoteItem(Note note) {
    final bool isSelected = _selectedNoteIds.contains(note.id);
    final tagProvider = Provider.of<TagProvider>(context, listen: false);

    return InkWell(
      onTap: () {
        if (_isMultiSelectMode) {
          _toggleNoteSelection(note.id);
        } else {
          // 使用命名路由导航并传递参数
          Navigator.pushNamed(
            context,
            AppRoutes.editor,
            arguments: note,
          ).then((_) {
            // 编辑完成后刷新笔记列表
            _loadNotes();
          });
        }
      },
      onLongPress: () {
        if (!_isMultiSelectMode) {
          // 长按启动多选模式并选中当前笔记
          _enterMultiSelectMode();
          _toggleNoteSelection(note.id);
        }
      },
      borderRadius: BorderRadius.circular(12),
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : AppTheme.mediumGrayColor,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.03),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        note.title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (note.isFavorite && !_isMultiSelectMode)
                      GestureDetector(
                        onTap: () => _toggleFavorite(note),
                        child: const Icon(
                          Icons.star,
                          color: Colors.orange,
                          size: 18,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: NoteContentPreview(
                    note: note,
                    maxLines: 4,
                    fontSize: 14,
                    textColor: AppTheme.darkGrayColor,
                  ),
                ),
                // 重新设计底部日期和标签区域，日期靠左，标签区域靠右，支持水平滚动
                Row(
                  children: [
                    // 日期靠左显示
                    Text(
                      note.formattedUpdatedAt,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.darkGrayColor,
                      ),
                    ),

                    // 标签区域使用Expanded和SingleChildScrollView实现右侧滚动
                    if (note.tagIds.isNotEmpty)
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          reverse: true, // 从右向左滚动
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: note.tagIds.map((tagId) {
                              // 尝试获取标签对象
                              Tag? tag = _getTagById(tagId);
                              if (tag == null) return Container();

                              return Padding(
                                padding: const EdgeInsets.only(left: 4),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: tag.colorValue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    tag.name,
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: tag.colorValue,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              );
                            }).toList().reversed.toList(), // 反转列表，使最近添加的标签靠右显示
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          if (_isMultiSelectMode)
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryColor : Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppTheme.primaryColor,
                    width: 2,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(2.0),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 14)
                      : const SizedBox(width: 14, height: 14),
                ),
              ),
            ),
          if (!_isMultiSelectMode)
            Positioned(
              top: 8,
              right: 8,
              child: InkWell(
                onTap: () => _showNoteOptions(note),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  padding: const EdgeInsets.all(4),
                  child: const Icon(
                    Icons.more_vert,
                    size: 16,
                    color: Colors.black54,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildListView(List<Note> notes) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          final noteProvider = Provider.of<NoteProvider>(context, listen: false);
          if (noteProvider.hasMore && !noteProvider.isLoading) {
            noteProvider.fetchNotes();
          }
        }
        return true;
      },
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(20, 0, 20, 100),
        itemCount: notes.length,
        itemBuilder: (context, index) {
          final note = notes[index];
          return _buildListNoteItem(note);
        },
      ),
    );
  }

  Widget _buildListNoteItem(Note note) {
    final bool isSelected = _selectedNoteIds.contains(note.id);
    final tagProvider = Provider.of<TagProvider>(context, listen: false);

    return Slidable(
      key: ValueKey(note.id),
      enabled: !_isMultiSelectMode,
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => _toggleFavorite(note),
            backgroundColor: Colors.orange.withOpacity(0.2), // 保留状态相关颜色
            foregroundColor: Colors.orange, // 保留状态相关颜色
            icon: note.isFavorite ? Icons.star : Icons.star_border,
            label: note.isFavorite ? '取消收藏' : '收藏',
          ),
          SlidableAction(
            onPressed: (_) => _toggleArchive(note),
            backgroundColor: Colors.blue.withOpacity(0.2), // 保留状态相关颜色
            foregroundColor: Colors.blue, // 保留状态相关颜色
            icon: note.isArchived ? Icons.unarchive : Icons.archive,
            label: note.isArchived ? '取消归档' : '归档',
          ),
          SlidableAction(
            onPressed: (_) => _exportSingleNote(note),
            backgroundColor: Colors.teal.withOpacity(0.2), // 保留状态相关颜色
            foregroundColor: Colors.teal, // 保留状态相关颜色
            icon: Icons.file_upload,
            label: '导出',
          ),
          SlidableAction(
            onPressed: (_) => _shareNote(note),
            backgroundColor: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.2),
            foregroundColor: Theme.of(context).colorScheme.primary,
            icon: Icons.share,
            label: '分享',
          ),
          SlidableAction(
            onPressed: (_) => _deleteNote(note),
            backgroundColor: Theme.of(context).colorScheme.errorContainer.withOpacity(0.2),
            foregroundColor: Theme.of(context).colorScheme.error,
            icon: Icons.delete,
            label: '删除',
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          if (_isMultiSelectMode) {
            _toggleNoteSelection(note.id);
          } else {
            // 使用命名路由导航并传递参数
            Navigator.pushNamed(
              context,
              AppRoutes.editor,
              arguments: note,
            ).then((_) {
              // 编辑完成后刷新笔记列表
              _loadNotes();
            });
          }
        },
        onLongPress: () {
          if (!_isMultiSelectMode) {
            // 长按启动多选模式并选中当前笔记
            _enterMultiSelectMode();
            _toggleNoteSelection(note.id);
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).dividerColor,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.03),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              if (_isMultiSelectMode)
                Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).cardColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(2.0),
                      child: isSelected
                          ? Icon(Icons.check, color: Theme.of(context).colorScheme.onPrimary, size: 16)
                          : const SizedBox(width: 16, height: 16),
                    ),
                  ),
                ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            note.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: Theme.of(context).textTheme.titleMedium?.color, //保持原有逻辑或使用主题默认
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (note.isFavorite && !_isMultiSelectMode)
                          Padding(
                            padding: const EdgeInsets.only(left: 8),
                            child: Icon(
                              Icons.star,
                              color: Colors.orange, // 保留状态色
                              size: 18,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    NoteContentPreview(
                      note: note,
                      maxLines: 4,
                      fontSize: 14, // 可以考虑从textTheme获取
                      textColor: Theme.of(context).hintColor,
                    ),
                    const SizedBox(height: 8),
                    // 重新设计底部日期和标签区域，日期靠左，标签区域靠右，支持水平滚动
                    Row(
                      children: [
                        // 日期靠左显示
                        Text(
                          note.formattedUpdatedAt,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).hintColor,
                              ),
                        ),

                        // 标签区域使用Expanded和SingleChildScrollView实现右侧滚动
                        if (note.tagIds.isNotEmpty)
                          Expanded(
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              reverse: true, // 从右向左滚动
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: note.tagIds.map((tagId) {
                                  // 尝试获取标签对象
                                  Tag? tag = _getTagById(tagId);
                                  if (tag == null) return Container();

                                  return Padding(
                                    padding: const EdgeInsets.only(left: 6),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: tag.colorValue.withOpacity(0.1), // 保留基于标签的颜色
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(
                                            Icons.label,
                                            size: 14,
                                            color: tag.colorValue, // 保留基于标签的颜色
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            tag.name,
                                            style: TextStyle( // 可以考虑从textTheme.labelSmall获取基础样式
                                              fontSize: 12,
                                              color: tag.colorValue, // 保留基于标签的颜色
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                }).toList().reversed.toList(), // 反转列表，使最近添加的标签靠右显示
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 添加根据标签ID获取标签对象的辅助方法
  Tag? _getTagById(String tagId) {
    final tagProvider = Provider.of<TagProvider>(context, listen: false);
    try {
      final tag = tagProvider.tags.firstWhere((tag) => tag.id == tagId);
      return tag;
    } catch (e) {
      // 如果未找到标签，返回null
      return null;
    }
  }

  // 优化归档功能实现，确保异常情况处理和UI刷新
  void _toggleArchive(Note note) async {
    if (note.id.isEmpty) {
      SnackbarHelper.showError(
        context: context,
        message: '无效的笔记ID'
      );
      return;
    }

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    final bool wasArchived = note.isArchived; // 保存原始状态

    try {
      final success = await noteProvider.toggleArchive(note.id);

      if (success) {
        // 显示成功消息
        SnackbarHelper.showSuccess(
          context: context,
          message: wasArchived ? '已取消归档' : '已归档'
        );

        // 在列表中更新笔记状态
        setState(() {});

        // 如果当前是在归档标签页，并且取消了归档，或者在全部/最近标签页，并且归档了笔记
        // 那么需要刷新列表以保持UI状态一致
        if ((_selectedCategoryIndex == 2 && !wasArchived) ||
            (_selectedCategoryIndex != 2 && wasArchived)) {
          // 重新加载笔记列表
          noteProvider.fetchNotes(refresh: true);
        }
      } else {
        SnackbarHelper.showError(
          context: context,
          message: '操作失败: ${noteProvider.error}'
        );

        // 发生错误时刷新数据，确保UI与服务器数据一致
        _loadNotes();
      }
    } catch (e) {
      SnackbarHelper.showError(
        context: context,
        message: '操作失败: $e'
      );

      // 发生异常时刷新数据
      _loadNotes();
    }
  }

  // 优化收藏/取消收藏笔记方法，确保异常情况处理和UI刷新
  void _toggleFavorite(Note note) async {
    if (note.id.isEmpty) {
      SnackbarHelper.showError(
        context: context,
        message: '无效的笔记ID'
      );
      return;
    }

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    final bool wasFavorite = note.isFavorite; // 保存原始状态

    try {
      final success = await noteProvider.toggleFavorite(note.id);

      if (success) {
        SnackbarHelper.showSuccess(
          context: context,
          message: wasFavorite ? '已取消收藏' : '已加入收藏'
        );

        // 操作成功后刷新列表，确保UI状态一致
        setState(() {});

        // 只有在favorites页面取消收藏时才需要刷新列表
        if (_currentIndex == 1 && wasFavorite) {
          // 刷新收藏列表
          noteProvider.fetchNotes(refresh: true);
        }
      } else {
        SnackbarHelper.showError(
          context: context,
          message: '操作失败: ${noteProvider.error}'
        );

        // 发生错误时刷新数据，确保UI与服务器数据一致
        _loadNotes();
      }
    } catch (e) {
      SnackbarHelper.showError(
        context: context,
        message: '操作失败: $e'
      );

      // 发生异常时刷新数据
      _loadNotes();
    }
  }

  void _shareNote(Note note) {
    // 直接跳转到分享页面，与编辑器页面保持一致
    Navigator.pushNamed(
      context,
      AppRoutes.shareNote,
      arguments: note,
    );
  }

  Widget _buildShareOption(IconData icon, String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(label, style: Theme.of(context).textTheme.bodySmall),
          ],
        ),
      ),
    );
  }

  void _deleteNote(Note note) {
    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    // 保存需要显示的信息，避免删除后访问已删除对象
    final String noteTitle = note.title;
    final String noteId = note.id;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('删除笔记', style: Theme.of(context).dialogTheme.titleTextStyle),
        content: Text('确定要删除笔记"$noteTitle"吗？此操作不可撤销。', style: Theme.of(context).dialogTheme.contentTextStyle),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消', style: TextStyle(color: Theme.of(context).colorScheme.primary)),
          ),
          TextButton(
            onPressed: () async {
              // 先关闭对话框，再执行删除操作
              Navigator.pop(context);

              try {
                // 显示删除中提示
                if (!mounted) return; // 检查 widget 是否还挂载

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('正在删除笔记...'),
                    duration: Duration(seconds: 1),
                  ),
                );

                // 先执行删除操作
                final success = await noteProvider.deleteNote(noteId);

                // 然后验证上下文是否仍然有效
                if (!mounted) return;

                if (success) {
                  SnackbarHelper.showSuccess(
                    context: context,
                    message: '笔记"$noteTitle"已删除'
                  );

                  // 成功删除后重新加载笔记列表
                  noteProvider.fetchNotes(refresh: true);
                } else {
                  SnackbarHelper.showError(
                    context: context,
                    message: '删除失败: ${noteProvider.error}'
                  );
                }
              } catch (e) {
                print('DEBUG: [HomePage] 删除笔记出错: $e');

                if (mounted) {
                  SnackbarHelper.showError(
                    context: context,
                    message: '删除操作异常: $e'
                  );
                }
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }



  void _enterMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = true;
      _selectedNoteIds = [];
    });
  }

  void _cancelMultiSelect() {
    setState(() {
      _isMultiSelectMode = false;
      _selectedNoteIds = [];
    });
  }

  void _toggleNoteSelection(String noteId) {
    setState(() {
      if (_selectedNoteIds.contains(noteId)) {
        _selectedNoteIds.remove(noteId);
      } else {
        _selectedNoteIds.add(noteId);
      }
    });
  }

  void _selectAllNotes(List<Note> notes) {
    setState(() {
      // 获取当前可见的笔记列表
      final visibleNotes = _getFilteredNotes();
      final visibleNoteIds = visibleNotes.map((note) => note.id).toList();

      // 检查是否已经全选
      bool allSelected = visibleNoteIds.every((id) => _selectedNoteIds.contains(id));

      if (allSelected) {
        // 如果已全选，则清空选择
        _selectedNoteIds = [];
      } else {
        // 否则，选择所有可见笔记
        _selectedNoteIds = visibleNoteIds;
      }

      // 输出调试信息
      print('DEBUG: [HomePage] 全选操作 - 可见笔记数: ${visibleNotes.length}, 已选笔记数: ${_selectedNoteIds.length}');
    });
  }

  Widget _buildBatchOperationBar() {
    return Container(
      height: 65,
      decoration: BoxDecoration(
        color: Theme.of(context).bottomAppBarTheme.color ?? Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.05),
            offset: const Offset(0, -1),
            blurRadius: 10,
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildBatchOperationItem(
              icon: Icons.star_border,
              label: '收藏',
              onTap: _batchFavorite,
            ),
            _buildBatchOperationItem(
              icon: Icons.archive,
              label: '归档',
              onTap: _batchArchive,
            ),
            _buildBatchOperationItem(
              icon: Icons.delete,
              label: '删除',
              onTap: _batchDelete,
            ),
            _buildBatchOperationItem(
              icon: Icons.file_upload,
              label: '导出',
              onTap: _batchExport,
            ),
            _buildBatchOperationItem(
              icon: Icons.select_all,
              label: '全选',
              onTap: () => _selectAllNotes(_getFilteredNotes()),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBatchOperationItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    // 特殊处理全选按钮
    if (label == '全选') {
      // 获取当前可见的笔记列表
      final visibleNotes = _getFilteredNotes();
      final visibleNoteIds = visibleNotes.map((note) => note.id).toList();

      // 检查是否已经全选
      bool allSelected = visibleNoteIds.isNotEmpty &&
                         visibleNoteIds.every((id) => _selectedNoteIds.contains(id));

      // 修改全选按钮标签
      return InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                allSelected ? Icons.deselect : Icons.select_all,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 4),
              Text(
                allSelected ? '取消全选' : '全选',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12, // 保持原有字号或从主题获取
                    ),
              ),
            ],
          ),
        ),
      );
    }

    // 其他按钮只在有选择时才能点击
    return InkWell(
      onTap: _selectedNoteIds.isEmpty ? null : onTap,
      child: Opacity(
        opacity: _selectedNoteIds.isEmpty ? 0.5 : 1.0,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 12, // 保持原有字号或从主题获取
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _batchFavorite() async {
    if (_selectedNoteIds.isEmpty) return;

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);

    try {
      final success = await noteProvider.batchOperation(
        noteIds: _selectedNoteIds,
        operation: 'favorite',
      );

      if (success) {
        SnackbarHelper.showSuccess(
          context: context,
          message: '已将${_selectedNoteIds.length}个笔记加入收藏'
        );

        _loadNotes();

        _cancelMultiSelect();
      } else {
        SnackbarHelper.showError(
          context: context,
          message: '操作失败: ${noteProvider.error}'
        );
      }
    } catch (e) {
      SnackbarHelper.showError(
        context: context,
        message: '操作失败: $e'
      );
    }
  }

  void _batchArchive() async {
    if (_selectedNoteIds.isEmpty) return;

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);

    try {
      final success = await noteProvider.batchOperation(
        noteIds: _selectedNoteIds,
        operation: 'archive',
      );

      if (success) {
        SnackbarHelper.showSuccess(
          context: context,
          message: '已将${_selectedNoteIds.length}个笔记归档'
        );

        _loadNotes();

        _cancelMultiSelect();
      } else {
        SnackbarHelper.showError(
          context: context,
          message: '操作失败: ${noteProvider.error}'
        );
      }
    } catch (e) {
      SnackbarHelper.showError(
        context: context,
        message: '操作失败: $e'
      );
    }
  }

  void _batchDelete() async {
    if (_selectedNoteIds.isEmpty) return;

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    // 保存需要显示的信息
    final int noteCount = _selectedNoteIds.length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('批量删除笔记', style: Theme.of(context).dialogTheme.titleTextStyle),
        content: Text('确定要删除这$noteCount个笔记吗？此操作不可撤销。', style: Theme.of(context).dialogTheme.contentTextStyle),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消', style: TextStyle(color: Theme.of(context).colorScheme.primary)),
          ),
          TextButton(
            onPressed: () async {
              // 先关闭对话框，再执行删除操作
              Navigator.pop(context);

              try {
                // 检查 widget 是否还挂载
                if (!mounted) return;

                // 显示删除中提示
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('正在删除笔记...'),
                    duration: Duration(seconds: 1),
                  ),
                );

                // 复制一份ID列表，避免在操作过程中修改
                final noteIdsToDelete = List<String>.from(_selectedNoteIds);

                // 执行批量删除操作
                final success = await noteProvider.batchOperation(
                  noteIds: noteIdsToDelete,
                  operation: 'delete',
                );

                // 验证上下文是否仍然有效
                if (!mounted) return;

                if (success) {
                  SnackbarHelper.showSuccess(
                    context: context,
                    message: '已删除$noteCount个笔记'
                  );

                  // 删除成功后刷新笔记列表
                  noteProvider.fetchNotes(refresh: true);

                  // 取消选择模式
                  _cancelMultiSelect();
                } else {
                  SnackbarHelper.showError(
                    context: context,
                    message: '删除失败: ${noteProvider.error}'
                  );
                }
              } catch (e) {
                print('DEBUG: [HomePage] 批量删除笔记出错: $e');

                if (mounted) {
                  SnackbarHelper.showError(
                    context: context,
                    message: '批量删除异常: $e'
                  );
                }
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showNoteOptions(Note note) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(
                  note.isFavorite ? Icons.star : Icons.star_border,
                  color: Colors.orange, // 保留状态色
                ),
                title: Text(note.isFavorite ? '取消收藏' : '收藏', style: Theme.of(context).listTileTheme.titleTextStyle),
                onTap: () {
                  Navigator.pop(context);
                  _toggleFavorite(note);
                },
              ),
              ListTile(
                leading: Icon(
                  note.isArchived ? Icons.unarchive : Icons.archive,
                  color: Colors.blue, // 保留状态色
                ),
                title: Text(note.isArchived ? '取消归档' : '归档', style: Theme.of(context).listTileTheme.titleTextStyle),
                onTap: () {
                  Navigator.pop(context);
                  _toggleArchive(note);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.file_upload,
                  color: Colors.teal, // 保留状态色
                ),
                title: Text('导出笔记', style: Theme.of(context).listTileTheme.titleTextStyle),
                onTap: () {
                  Navigator.pop(context);
                  _exportSingleNote(note);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.share,
                  color: Colors.green, // 保留状态色
                ),
                title: Text('分享', style: Theme.of(context).listTileTheme.titleTextStyle),
                onTap: () {
                  Navigator.pop(context);
                  _shareNote(note);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.delete,
                  color: Theme.of(context).colorScheme.error,
                ),
                title: Text('删除', style: Theme.of(context).listTileTheme.titleTextStyle),
                onTap: () {
                  Navigator.pop(context);
                  _deleteNote(note);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 导出单个笔记
  void _exportSingleNote(Note note) async {
    // 显示格式选择对话框，传递笔记内容类型
    await _importExportManager.showExportFormatDialog(
      context,
      (format) async {
        await _importExportManager.exportSingleNote(
          context,
          note,
          format,
        );
      },
      noteContentType: note.contentType,
    );
  }

  // 批量导出笔记
  void _batchExport() async {
    if (_selectedNoteIds.isEmpty) return;

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);

    // 获取选中的笔记
    final selectedNotes = noteProvider.notes.where(
      (note) => _selectedNoteIds.contains(note.id)
    ).toList();

    if (selectedNotes.isEmpty) {
      SnackbarHelper.showInfo(
        context: context,
        message: '未找到选中的笔记',
      );
      return;
    }

    // 直接调用导出管理器的方法，传递null表示按照笔记原始格式导出
    await _importExportManager.exportMultipleNotes(
      context,
      selectedNotes,
      null,
    );

    // 退出多选模式
    _cancelMultiSelect();
  }

  // 显示导入导出菜单
  void _showImportExportMenu() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(
                  Icons.file_download,
                  color: Theme.of(context).colorScheme.primary,
                ),
                title: Text('导入笔记', style: Theme.of(context).listTileTheme.titleTextStyle),
                subtitle: Text('从文件导入笔记', style: Theme.of(context).listTileTheme.subtitleTextStyle),
                onTap: () {
                  Navigator.pop(context);
                  _importNotes();
                },
              ),
              const Divider(),
              ListTile(
                leading: Icon(
                  Icons.select_all,
                  color: Theme.of(context).colorScheme.primary,
                ),
                title: Text('批量选择导出', style: Theme.of(context).listTileTheme.titleTextStyle),
                subtitle: Text('选择多个笔记进行导出', style: Theme.of(context).listTileTheme.subtitleTextStyle),
                onTap: () {
                  Navigator.pop(context);
                  _enterMultiSelectMode();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 导入笔记
  void _importNotes() async {
    await _importExportManager.importNotes(context);
  }
}