<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="智云笔记 - 基于Flutter开发的跨平台云笔记应用">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="智云笔记">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>智云笔记</title>
  <link rel="manifest" href="manifest.json">

  <!-- 添加本地字体替代Google Fonts -->
  <style>
    /* 使用系统默认字体 */
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    /* 避免网络字体加载 */
    @font-face {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 400;
      src: local('Roboto'), local('Roboto-Regular'), local('sans-serif');
    }

    @font-face {
      font-family: 'Noto Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Noto Sans'), local('NotoSans'), local('sans-serif');
    }
  </style>

  <script>
    // The value below is injected by flutter build, do not touch.
    // Using the recommended template token
    var serviceWorkerVersion = '{{flutter_service_worker_version}}';
  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer></script>
</head>
<body>
  <script>
    window.addEventListener('load', function(ev) {
      // 显示加载指示器
      var loadingIndicator = document.createElement('div');
      loadingIndicator.innerText = '正在加载智云笔记...';
      loadingIndicator.style.position = 'fixed';
      loadingIndicator.style.top = '50%';
      loadingIndicator.style.left = '50%';
      loadingIndicator.style.transform = 'translate(-50%, -50%)';
      loadingIndicator.style.fontSize = '18px';
      loadingIndicator.style.fontFamily = 'sans-serif';
      document.body.appendChild(loadingIndicator);

      // 使用最简单的方式初始化 Flutter
      var loading = setTimeout(function() {
        loadingIndicator.innerText = '加载时间较长，请耐心等待...';
      }, 10000);

      // 使用标准的方式加载 Flutter
      window.addEventListener('flutter-first-frame', function() {
        clearTimeout(loading);
        document.body.removeChild(loadingIndicator);
      });

      // 加载 main.dart.js
      var scriptTag = document.createElement('script');
      scriptTag.src = 'main.dart.js';
      scriptTag.type = 'application/javascript';
      document.body.appendChild(scriptTag);
    });
  </script>
</body>
</html>
