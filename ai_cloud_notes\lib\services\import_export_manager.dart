import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/models/tag_model.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/providers/tag_provider.dart';
import 'package:ai_cloud_notes/services/export_service.dart';
import 'package:ai_cloud_notes/services/image_processor.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

/// 导出模式枚举
enum ExportMode { separateFiles, singleFile }

/// 导入导出管理器
///
/// 管理笔记的导入导出操作，处理UI交互和状态管理
class ImportExportManager {
  final ExportService _exportService = ExportService();

  /// 导出单个笔记
  ///
  /// [context] 上下文
  /// [note] 要导出的笔记
  /// [format] 导出格式，'markdown'或'json'
  Future<void> exportSingleNote(BuildContext context, Note note, String format) async {
    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      final success = await _exportService.exportNoteToFile(note, exportFormat: format);

      // 关闭加载对话框
      Navigator.pop(context);

      if (success) {
        SnackbarHelper.showSuccess(
          context: context,
          message: '笔记导出成功',
        );
      } else {
        SnackbarHelper.showError(
          context: context,
          message: '笔记导出失败',
        );
      }
    } catch (e) {
      // 关闭加载对话框
      Navigator.pop(context);

      SnackbarHelper.showError(
        context: context,
        message: '导出过程中发生错误: $e',
      );
    }
  }

  /// 导出多个笔记
  ///
  /// [context] 上下文
  /// [notes] 要导出的笔记列表
  /// [format] 导出格式，'markdown'或'json'，如果为null则按照笔记原始格式导出
  Future<void> exportMultipleNotes(BuildContext context, List<Note> notes, String? format) async {
    if (notes.isEmpty) {
      SnackbarHelper.showInfo(
        context: context,
        message: '没有可导出的笔记',
      );
      return;
    }

    // 如果没有指定格式，则检查笔记类型
    if (format == null) {
      // 检查所有笔记是否都是同一种类型
      final firstContentType = notes.first.contentType;
      final allSameType = notes.every((note) => note.contentType == firstContentType);

      if (allSameType) {
        // 如果所有笔记都是同一种类型，根据类型选择格式
        format = firstContentType == 'markdown' ? 'markdown' : 'json';
      } else {
        // 如果笔记类型不同，默认使用JSON格式
        format = 'json';
      }
    }

    // 显示导出方式选择对话框
    if (!context.mounted) return;

    // 默认选择合并导出
    ExportMode selectedMode = ExportMode.singleFile;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择导出方式'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '注意：导出的笔记不包含收藏、归档和分享等状态信息',
                  style: TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 16),
                RadioListTile<ExportMode>(
                  title: const Text('合并为单个文件'),
                  subtitle: const Text('将所有笔记合并为一个文件，方便一次性导入'),
                  value: ExportMode.singleFile,
                  groupValue: selectedMode,
                  onChanged: (value) {
                    setState(() {
                      selectedMode = value!;
                    });
                  },
                ),
                RadioListTile<ExportMode>(
                  title: const Text('分别导出为单独文件'),
                  subtitle: const Text('每个笔记导出为一个单独的文件'),
                  value: ExportMode.separateFiles,
                  groupValue: selectedMode,
                  onChanged: (value) {
                    setState(() {
                      selectedMode = value!;
                    });
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context, selectedMode);
            },
            child: const Text('导出'),
          ),
        ],
      ),
    ).then((result) async {
      if (result == null) return; // 用户取消了选择

      // 显示加载对话框
      if (!context.mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      try {
        bool success;

        print('DEBUG: [ImportExportManager] 选择的导出模式: ${result == ExportMode.singleFile ? "单个文件" : "单独文件"}');

        // 获取标签提供者
        final tagProvider = Provider.of<TagProvider>(context, listen: false);

        // 创建标签ID到名称的映射
        Map<String, String> tagIdToNameMap = {};
        for (var tag in tagProvider.tags) {
          tagIdToNameMap[tag.id] = tag.name;
        }
        print('DEBUG: [ImportExportManager] 创建标签映射: $tagIdToNameMap');

        // 设置标签映射
        _exportService.setTagIdToNameMap(tagIdToNameMap);

        if (result == ExportMode.singleFile) {
          // 合并导出为单个文件
          print('DEBUG: [ImportExportManager] 调用exportNotesToZip，格式: ${format ?? 'json'}');
          success = await _exportService.exportNotesToZip(notes, exportFormat: format ?? 'json');
        } else {
          // 分别导出为单独文件
          print('DEBUG: [ImportExportManager] 调用exportNotesToSeparateFiles，笔记数量: ${notes.length}');
          success = await _exportService.exportNotesToSeparateFiles(notes);
        }

        // 关闭加载对话框
        if (!context.mounted) return;
        Navigator.pop(context);

        if (success) {
          SnackbarHelper.showSuccess(
            context: context,
            message: '成功导出 ${notes.length} 个笔记',
          );
        } else {
          SnackbarHelper.showError(
            context: context,
            message: '笔记导出失败',
          );
        }
      } catch (e) {
        // 关闭加载对话框
        if (!context.mounted) return;
        Navigator.pop(context);

        SnackbarHelper.showError(
          context: context,
          message: '导出过程中发生错误: $e',
        );
      }
    });
  }

  /// 导出所有笔记
  ///
  /// [context] 上下文
  /// [format] 导出格式，'markdown'或'json'，如果为null则按照笔记原始格式导出
  Future<void> exportAllNotes(BuildContext context, String? format) async {
    // 显示加载对话框
    if (!context.mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // 获取NoteProvider
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);

      // 调用API导出所有笔记
      final result = await noteProvider.exportNotes();

      // 关闭加载对话框
      if (!context.mounted) return;
      Navigator.pop(context);

      if (result['success'] == true) {
        final List<Note> notes = result['notes'];

        // 如果没有指定格式，则按照笔记类型分组导出
        if (format == null) {
          // 将笔记按照内容类型分组
          final Map<String, List<Note>> groupedNotes = {};

          for (final note in notes) {
            final contentType = note.contentType;
            if (!groupedNotes.containsKey(contentType)) {
              groupedNotes[contentType] = [];
            }
            groupedNotes[contentType]!.add(note);
          }

          // 分别导出不同类型的笔记
          for (final entry in groupedNotes.entries) {
            final contentType = entry.key;
            final typeNotes = entry.value;

            // 根据内容类型选择导出格式
            final typeFormat = contentType == 'markdown' ? 'markdown' : 'json';

            // 导出笔记
            await exportMultipleNotes(
              context,
              typeNotes,
              typeFormat,
            );
          }

          return;
        }

        // 如果指定了格式，则直接导出
        final success = await _exportService.exportNotesToZip(notes, exportFormat: format);

        if (success) {
          SnackbarHelper.showSuccess(
            context: context,
            message: '成功导出 ${notes.length} 个笔记',
          );
        } else {
          SnackbarHelper.showError(
            context: context,
            message: '笔记导出失败',
          );
        }
      } else {
        SnackbarHelper.showError(
          context: context,
          message: '获取笔记失败: ${result['error']}',
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (!context.mounted) return;
      Navigator.pop(context);

      SnackbarHelper.showError(
        context: context,
        message: '导出过程中发生错误: $e',
      );
    }
  }

  /// 导入笔记
  ///
  /// [context] 上下文
  Future<void> importNotes(BuildContext context) async {
    try {
      // 选择并读取文件
      final notesData = await _exportService.importNotesFromFile();

      if (notesData == null || notesData.isEmpty) {
        SnackbarHelper.showInfo(
          context: context,
          message: '没有选择文件或文件格式不支持',
        );
        return;
      }

      // 显示导入提示对话框
      bool continueImport = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('导入笔记'),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('注意事项：'),
              SizedBox(height: 8),
              Text('1. 导入的笔记将不包含原有的收藏、归档和分享等状态'),
              Text('2. 导入后的笔记将以新笔记的形式添加到您的笔记列表中'),
              Text('3. 如果笔记中包含标签，系统将尝试匹配现有标签或创建新标签'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('继续导入'),
            ),
          ],
        ),
      ) ?? false;

      if (!continueImport) {
        return; // 用户取消导入
      }

      // 显示加载对话框
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      // 获取NoteProvider和TagProvider
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      final tagProvider = Provider.of<TagProvider>(context, listen: false);

      // 处理标签名称
      for (var noteData in notesData) {
        print('DEBUG: [ImportExportManager] 处理笔记数据: $noteData');

        // 检查是否有标签名称
        List<String> tagNames = [];

        // 从tagNames字段获取标签
        if (noteData.containsKey('tagNames') && noteData['tagNames'] is List) {
          tagNames = List<String>.from(noteData['tagNames']);
          print('DEBUG: [ImportExportManager] 从tagNames字段获取标签: $tagNames');
        }
        // 从标签字段获取标签
        else if (noteData.containsKey('标签') && noteData['标签'] is String) {
          final tagsStr = noteData['标签'] as String;
          if (tagsStr.isNotEmpty) {
            tagNames = tagsStr.split(',').map((t) => t.trim()).where((t) => t.isNotEmpty).toList();
            print('DEBUG: [ImportExportManager] 从标签字段获取标签: $tagNames');
          }
        }

        List<String> tagIds = [];

        if (tagNames.isNotEmpty) {
          // 获取所有现有标签
          await tagProvider.fetchTags();
          final existingTags = tagProvider.tags;
          print('DEBUG: [ImportExportManager] 现有标签: ${existingTags.map((t) => "${t.id} - ${t.name}").join(", ")}');

          // 处理每个标签名称
          for (var tagName in tagNames) {
            // 跳过空标签名
            if (tagName.isEmpty) continue;

            print('DEBUG: [ImportExportManager] 处理标签: $tagName');

            // 查找是否已存在同名标签或ID与标签名相同的情况
            print('DEBUG: [ImportExportManager] 查找标签: $tagName');

            // 首先检查是否有同名标签
            final existingTagByName = existingTags.where((tag) => tag.name.toLowerCase() == tagName.toLowerCase()).toList();

            // 然后检查是否有ID与标签名相同的情况
            final existingTagById = existingTags.where((tag) => tag.id == tagName).toList();

            // 合并结果
            final existingTag = [...existingTagByName, ...existingTagById];

            if (existingTag.isNotEmpty) {
              // 使用现有标签ID
              print('DEBUG: [ImportExportManager] 使用现有标签: ${existingTag.first.id} - ${existingTag.first.name}');
              tagIds.add(existingTag.first.id);
            } else {
              // 创建新标签
              bool shouldCreateTag = await showDialog<bool>(
                context: context,
                barrierDismissible: false,
                builder: (context) => AlertDialog(
                  title: const Text('创建新标签'),
                  content: Text('标签"$tagName"不存在，是否创建？'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context, false),
                      child: const Text('跳过'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context, true),
                      child: const Text('创建'),
                    ),
                  ],
                ),
              ) ?? false;

              if (shouldCreateTag) {
                print('DEBUG: [ImportExportManager] 创建新标签: $tagName');
                final success = await tagProvider.createTag(tagName);
                print('DEBUG: [ImportExportManager] 创建标签结果: $success');

                if (success) {
                  // 获取新创建的标签
                  await tagProvider.fetchTags();
                  final newTag = tagProvider.tags.firstWhere(
                    (tag) => tag.name.toLowerCase() == tagName.toLowerCase(),
                    orElse: () => Tag.empty(),
                  );

                  print('DEBUG: [ImportExportManager] 获取新创建的标签: ${newTag.id} - ${newTag.name}');

                  if (newTag.id.isNotEmpty) {
                    tagIds.add(newTag.id);
                    print('DEBUG: [ImportExportManager] 添加标签ID: ${newTag.id}');
                  }
                }
              }
            }
          }
        }

        // 更新笔记数据中的标签字段
        noteData.remove('tagNames');
        if (noteData.containsKey('标签')) noteData.remove('标签');
        noteData['tags'] = tagIds;
        print('DEBUG: [ImportExportManager] 更新后的笔记数据标签: ${noteData['tags']}');
      }

      // 调用API导入笔记
      final result = await noteProvider.importNotes(notes: notesData);

      // 处理导入的笔记中的图片
      if (result['success'] == true && result['importedNotes'] != null) {
        final List<Note> importedNotes = result['importedNotes'];

        // 显示处理图片的进度对话框
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }

        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: const Text('处理图片'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                const Text('正在处理导入笔记中的图片，请稍候...'),
              ],
            ),
          ),
        );

        // 处理每个富文本笔记中的图片
        for (final note in importedNotes) {
          if (note.contentType == 'rich-text') {
            try {
              // 处理富文本笔记中的图片
              final processedContent = await ImageProcessor.processImportedRichTextImages(
                note.content,
                note.id,
                noteProvider,
              );

              // 如果内容有变化，更新笔记
              if (processedContent != note.content) {
                // 更新笔记内容
                await noteProvider.updateNote(
                  id: note.id,
                  content: processedContent,
                );
              }
            } catch (e) {
              print('处理导入笔记图片失败: $e');
              // 继续处理下一个笔记
            }
          }
        }
      }

      // 关闭加载对话框
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (result['success'] == true) {
        SnackbarHelper.showSuccess(
          context: context,
          message: '成功导入 ${result['imported']} 个笔记，失败 ${result['failed']} 个',
        );

        // 刷新笔记列表
        await noteProvider.fetchNotes(refresh: true);
      } else {
        SnackbarHelper.showError(
          context: context,
          message: '导入笔记失败: ${result['error']}',
        );
      }
    } catch (e) {
      // 关闭加载对话框
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      SnackbarHelper.showError(
        context: context,
        message: '导入过程中发生错误: $e',
      );
    }
  }

  /// 显示导出格式选择对话框
  ///
  /// [context] 上下文
  /// [onFormatSelected] 格式选择回调
  /// [noteContentType] 笔记内容类型，用于限制可选格式
  Future<void> showExportFormatDialog(
    BuildContext context,
    Function(String) onFormatSelected,
    {String? noteContentType, bool isMultiple = false}
  ) async {
    // 默认选择与笔记内容类型匹配的格式
    String selectedFormat = noteContentType == 'markdown' ? 'markdown' : 'json';

    // 如果是批量导出，默认使用JSON格式
    if (isMultiple) {
      selectedFormat = 'json';
    }

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择导出格式'),
        content: StatefulBuilder(
          builder: (context, setState) {
            // 单个笔记导出时，根据笔记类型限制格式选择
            if (!isMultiple && noteContentType != null) {
              // 如果是Markdown笔记，只能导出为Markdown格式
              if (noteContentType == 'markdown') {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      '注意：导出的笔记不包含收藏、归档和分享等状态信息',
                      style: TextStyle(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text('Markdown格式的笔记只能导出为Markdown格式(.md)'),
                    const SizedBox(height: 16),
                    RadioListTile<String>(
                      title: const Text('Markdown格式 (.md)'),
                      subtitle: const Text('适合与其他Markdown编辑器兼容'),
                      value: 'markdown',
                      groupValue: selectedFormat,
                      onChanged: null, // 禁用选择
                    ),
                  ],
                );
              }

              // 如果是富文本笔记，只能导出为JSON格式
              if (noteContentType == 'rich-text') {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      '注意：导出的笔记不包含收藏、归档和分享等状态信息',
                      style: TextStyle(
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text('富文本格式的笔记只能导出为JSON格式'),
                    const SizedBox(height: 16),
                    RadioListTile<String>(
                      title: const Text('富文本格式 (JSON)'),
                      subtitle: const Text('保留所有格式和元数据'),
                      value: 'json',
                      groupValue: selectedFormat,
                      onChanged: null, // 禁用选择
                    ),
                  ],
                );
              }
            }

            // 批量导出或未指定笔记类型时，提供两种选择
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '注意：导出的笔记不包含收藏、归档和分享等状态信息',
                  style: TextStyle(
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 16),
                RadioListTile<String>(
                  title: const Text('富文本格式 (JSON)'),
                  subtitle: const Text('保留所有格式和元数据'),
                  value: 'json',
                  groupValue: selectedFormat,
                  onChanged: (value) {
                    setState(() {
                      selectedFormat = value!;
                    });
                  },
                ),
                RadioListTile<String>(
                  title: const Text('Markdown格式 (.md)'),
                  subtitle: const Text('适合与其他Markdown编辑器兼容'),
                  value: 'markdown',
                  groupValue: selectedFormat,
                  onChanged: (value) {
                    setState(() {
                      selectedFormat = value!;
                    });
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onFormatSelected(selectedFormat);
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }
}
