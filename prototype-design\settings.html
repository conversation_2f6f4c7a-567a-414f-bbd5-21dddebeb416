<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智云笔记 - 设置</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common.css">
    <style>
        /* 设置界面 */
        .settings-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .settings-header {
            padding: 20px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
        }

        .settings-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .settings-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
            cursor: pointer;
        }

        .settings-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .settings-content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 80px; /* 为底部导航腾出空间 */
        }

        .settings-list {
            padding: 20px;
        }

        .settings-group {
            margin-bottom: 24px;
        }

        .settings-group-title {
            color: var(--dark-gray);
            font-size: 0.9rem;
            margin-bottom: 16px;
            font-weight: 500;
            letter-spacing: 0.5px;
            padding: 0 8px;
        }

        .settings-item {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: white;
            border-radius: var(--border-radius);
            margin-bottom: 12px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
            cursor: pointer;
        }

        .settings-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .settings-item-left {
            display: flex;
            align-items: center;
        }

        .settings-item-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-sm);
            background-color: var(--primary-light);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 16px;
            color: var(--primary-color);
        }

        .settings-item-info {
            display: flex;
            flex-direction: column;
        }

        .settings-item-title {
            font-weight: 500;
            color: var(--black);
            margin-bottom: 4px;
        }

        .settings-item-description {
            font-size: 0.85rem;
            color: var(--dark-gray);
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 26px;
            background-color: var(--medium-gray);
            border-radius: 20px;
            transition: var(--transition);
            cursor: pointer;
        }

        .toggle-switch.active {
            background-color: var(--primary-color);
        }

        .toggle-switch::after {
            content: "";
            position: absolute;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            left: 2px;
            transition: var(--transition);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::after {
            left: 26px;
        }

        .chevron-right {
            color: var(--dark-gray);
        }

        /* 个人资料页面 */
        .profile-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .profile-header {
            padding: 20px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--medium-gray);
            box-shadow: var(--shadow-sm);
        }

        .profile-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .profile-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
            cursor: pointer;
        }

        .profile-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .profile-save-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            font-size: 0.9rem;
            transition: var(--transition);
            cursor: pointer;
        }

        .profile-save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(93, 95, 239, 0.3);
        }

        .profile-avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px 20px;
            background-color: white;
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: var(--border-radius-lg);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            box-shadow: 0 6px 16px rgba(93, 95, 239, 0.2);
            position: relative;
        }

        .profile-avatar-edit {
            position: absolute;
            bottom: -5px;
            right: -5px;
            background-color: white;
            color: var(--primary-color);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: var(--transition);
        }

        .profile-avatar-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .profile-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
            margin-bottom: 4px;
        }

        .profile-email {
            font-size: 0.9rem;
            color: var(--dark-gray);
        }

        .profile-info-section {
            padding: 0 20px;
        }

        .profile-info-item {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
        }

        .profile-info-label {
            font-size: 0.9rem;
            color: var(--dark-gray);
            margin-bottom: 10px;
        }

        .profile-info-input {
            width: 100%;
            border: none;
            border-bottom: 1px solid var(--medium-gray);
            padding: 8px 0;
            font-size: 1rem;
            color: var(--black);
            outline: none;
            transition: var(--transition);
        }

        .profile-info-input:focus {
            border-color: var(--primary-color);
        }

        .profile-stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            margin-top: 10px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
        }

        .profile-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .profile-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .profile-stat-label {
            font-size: 0.9rem;
            color: var(--dark-gray);
        }

        /* 主题设置 */
        .theme-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .theme-header {
            padding: 20px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--medium-gray);
            box-shadow: var(--shadow-sm);
        }

        .theme-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .theme-section {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
        }

        .theme-section-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--black);
            margin-bottom: 16px;
        }

        .theme-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--medium-gray);
        }

        .theme-option:last-child {
            border-bottom: none;
        }

        .theme-option-label {
            font-size: 0.95rem;
            color: var(--black);
        }

        .theme-previews {
            display: flex;
            gap: 16px;
            margin-top: 20px;
        }

        .theme-preview {
            flex: 1;
            height: 180px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            position: relative;
            transition: var(--transition);
            cursor: pointer;
            border: 1px solid var(--medium-gray);
        }

        .theme-preview:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow);
        }

        .theme-preview.active {
            border: 2px solid var(--primary-color);
            transform: translateY(-2px);
        }

        .theme-preview.active::after {
            content: "\f00c";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .theme-preview-light {
            background-color: white;
        }

        .theme-preview-dark {
            background-color: #121212;
        }

        .theme-preview-inner {
            padding: 16px;
        }

        .theme-preview-header {
            height: 24px;
            margin-bottom: 12px;
            border-radius: 4px;
        }

        .theme-preview-light .theme-preview-header {
            background-color: var(--medium-gray);
        }

        .theme-preview-dark .theme-preview-header {
            background-color: #2D2D2D;
        }

        .theme-preview-line {
            height: 12px;
            margin-bottom: 8px;
            border-radius: 2px;
        }

        .theme-preview-light .theme-preview-line {
            background-color: var(--light-gray);
        }

        .theme-preview-dark .theme-preview-line {
            background-color: #2D2D2D;
        }

        .theme-preview-line.short {
            width: 70%;
        }

        .theme-color-options {
            display: flex;
            gap: 16px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .theme-color-option {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }

        .theme-color-option:hover {
            transform: scale(1.1);
        }

        .theme-color-option.active {
            box-shadow: 0 0 0 2px white, 0 0 0 4px var(--primary-color);
        }

        .theme-color-purple {
            background: linear-gradient(135deg, #5D5FEF, #6366F1);
        }

        .theme-color-blue {
            background: linear-gradient(135deg, #0EA5E9, #38BDF8);
        }

        .theme-color-green {
            background: linear-gradient(135deg, #10B981, #34D399);
        }

        .theme-color-orange {
            background: linear-gradient(135deg, #F59E0B, #FBBF24);
        }

        .theme-color-red {
            background: linear-gradient(135deg, #EF4444, #F87171);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title text-center">智云笔记应用原型设计</h1>
        <p class="page-description text-center">
            设置页面允许用户配置应用行为、个人信息和其他首选项，提供友好的操作界面和清晰的分类。
        </p>

        <!-- 设置主页 -->
        <div>
            <h3 class="screen-title">设置主页</h3>
            <p class="screen-description">设置项分类清晰，包含账户、应用、通知等不同类别</p>
            <div class="screen">
                <div class="settings-container">
                    <div class="settings-header">
                        <div class="settings-header-title">设置</div>
                        <i class="fas fa-question-circle" style="color: var(--dark-gray);"></i>
                    </div>
                    
                    <div class="settings-content">
                        <div class="settings-list">
                            <div class="settings-group">
                                <div class="settings-group-title">账户</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">个人资料</div>
                                            <div class="settings-item-description">编辑您的个人信息</div>
                                        </div>
                                    </div>
                                    <div class="chevron-right">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-lock"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">隐私与安全</div>
                                            <div class="settings-item-description">管理数据与安全选项</div>
                                        </div>
                                    </div>
                                    <div class="chevron-right">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-key"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">修改密码</div>
                                            <div class="settings-item-description">更新您的账户密码</div>
                                        </div>
                                    </div>
                                    <div class="chevron-right">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="settings-group">
                                <div class="settings-group-title">应用</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-palette"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">主题与外观</div>
                                            <div class="settings-item-description">自定义应用界面</div>
                                        </div>
                                    </div>
                                    <div class="chevron-right">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-robot"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">AI设置</div>
                                            <div class="settings-item-description">配置智能预测功能</div>
                                        </div>
                                    </div>
                                    <div class="chevron-right">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-cloud"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">同步设置</div>
                                            <div class="settings-item-description">配置云同步选项</div>
                                        </div>
                                    </div>
                                    <div class="chevron-right">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-bell"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">通知设置</div>
                                            <div class="settings-item-description">提醒和推送管理</div>
                                        </div>
                                    </div>
                                    <div class="toggle-switch active"></div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-language"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">语言</div>
                                            <div class="settings-item-description">选择应用界面语言</div>
                                        </div>
                                    </div>
                                    <div class="settings-item-right">
                                        <div class="settings-item-value">简体中文</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="settings-group">
                                <div class="settings-group-title">存储</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-hdd"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">存储空间</div>
                                            <div class="settings-item-description">管理应用使用的空间</div>
                                        </div>
                                    </div>
                                    <div class="settings-item-right">
                                        <div class="settings-item-value">0.8GB/5GB</div>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-archive"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">缓存</div>
                                            <div class="settings-item-description">清理应用缓存数据</div>
                                        </div>
                                    </div>
                                    <div class="settings-item-right">
                                        <div class="settings-item-value">24MB</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="settings-group">
                                <div class="settings-group-title">其他</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">帮助与反馈</div>
                                            <div class="settings-item-description">获取支持和提交问题</div>
                                        </div>
                                    </div>
                                    <div class="chevron-right">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-info-circle"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title">关于</div>
                                            <div class="settings-item-description">版本信息与开源协议</div>
                                        </div>
                                    </div>
                                    <div class="chevron-right">
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon" style="background-color: rgba(239, 68, 68, 0.1); color: var(--danger);">
                                            <i class="fas fa-sign-out-alt"></i>
                                        </div>
                                        <div class="settings-item-info">
                                            <div class="settings-item-title" style="color: var(--danger);">退出登录</div>
                                            <div class="settings-item-description">安全退出当前账号</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item active">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 个人资料页面 -->
        <div>
            <h3 class="screen-title">个人资料</h3>
            <p class="screen-description">编辑用户的个人信息和查看账户数据</p>
            <div class="screen">
                <div class="profile-container">
                    <div class="profile-header">
                        <div class="back-btn">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="profile-header-title">个人资料</div>
                        <button class="profile-save-btn">保存</button>
                    </div>
                    
                    <div class="settings-content">
                        <div class="profile-avatar-section">
                            <div class="profile-avatar">
                                <span>张</span>
                                <div class="profile-avatar-edit">
                                    <i class="fas fa-camera"></i>
                                </div>
                            </div>
                            <div class="profile-name">张三</div>
                            <div class="profile-email"><EMAIL></div>
                        </div>
                        
                        <div class="profile-info-section">
                            <div class="profile-info-item">
                                <div class="profile-info-label">用户名</div>
                                <input type="text" class="profile-info-input" value="张三">
                            </div>
                            
                            <div class="profile-info-item">
                                <div class="profile-info-label">邮箱</div>
                                <input type="email" class="profile-info-input" value="<EMAIL>">
                            </div>
                            
                            <div class="profile-info-item">
                                <div class="profile-info-label">手机号</div>
                                <input type="tel" class="profile-info-input" value="138****6666">
                            </div>
                            
                            <div class="profile-info-item">
                                <div class="profile-info-label">个人简介</div>
                                <input type="text" class="profile-info-input" value="热爱生活，记录每一刻">
                            </div>
                            
                            <div class="profile-stats">
                                <div class="profile-stat-item">
                                    <div class="profile-stat-value">32</div>
                                    <div class="profile-stat-label">笔记</div>
                                </div>
                                <div class="profile-stat-item">
                                    <div class="profile-stat-value">8</div>
                                    <div class="profile-stat-label">收藏</div>
                                </div>
                                <div class="profile-stat-item">
                                    <div class="profile-stat-value">5</div>
                                    <div class="profile-stat-label">标签</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主题设置页面 -->
        <div>
            <h3 class="screen-title">主题设置</h3>
            <p class="screen-description">自定义应用的外观和主题颜色</p>
            <div class="screen">
                <div class="theme-container">
                    <div class="theme-header">
                        <div class="back-btn">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="profile-header-title">主题与外观</div>
                        <div style="width: 40px;"></div>
                    </div>
                    
                    <div class="theme-content">
                        <div class="theme-section">
                            <div class="theme-section-title">深色模式</div>
                            <div class="theme-option">
                                <div class="theme-option-label">开启深色模式</div>
                                <div class="toggle-switch"></div>
                            </div>
                            <div class="theme-option">
                                <div class="theme-option-label">跟随系统</div>
                                <div class="toggle-switch active"></div>
                            </div>
                            <div class="theme-option">
                                <div class="theme-option-label">定时切换</div>
                                <div class="toggle-switch"></div>
                            </div>
                            
                            <div class="theme-previews">
                                <div class="theme-preview theme-preview-light active">
                                    <div class="theme-preview-inner">
                                        <div class="theme-preview-header"></div>
                                        <div class="theme-preview-line"></div>
                                        <div class="theme-preview-line short"></div>
                                        <div class="theme-preview-line"></div>
                                        <div class="theme-preview-line short"></div>
                                    </div>
                                </div>
                                <div class="theme-preview theme-preview-dark">
                                    <div class="theme-preview-inner">
                                        <div class="theme-preview-header"></div>
                                        <div class="theme-preview-line"></div>
                                        <div class="theme-preview-line short"></div>
                                        <div class="theme-preview-line"></div>
                                        <div class="theme-preview-line short"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="theme-section">
                            <div class="theme-section-title">主题颜色</div>
                            <div class="theme-color-options">
                                <div class="theme-color-option theme-color-purple active"></div>
                                <div class="theme-color-option theme-color-blue"></div>
                                <div class="theme-color-option theme-color-green"></div>
                                <div class="theme-color-option theme-color-orange"></div>
                                <div class="theme-color-option theme-color-red"></div>
                            </div>
                        </div>
                        
                        <div class="theme-section">
                            <div class="theme-section-title">字体大小</div>
                            <div class="theme-option">
                                <div class="theme-option-label">正文字体大小</div>
                                <div class="settings-item-right">
                                    <div class="settings-item-value">中</div>
                                </div>
                            </div>
                            <div class="theme-option">
                                <div class="theme-option-label">标题字体大小</div>
                                <div class="settings-item-right">
                                    <div class="settings-item-value">中</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>智云笔记应用原型设计 &copy; 2023</p>
            <p>
                <a href="welcome.html">欢迎页面</a> | 
                <a href="auth.html">认证页面</a> | 
                <a href="home.html">主页</a> | 
                <a href="editor.html">编辑器</a> | 
                <a href="settings.html">设置</a> | 
                <a href="tags.html">标签</a>
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 