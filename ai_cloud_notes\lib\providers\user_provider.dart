import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/models/user_profile.dart';
import 'package:ai_cloud_notes/services/api_service.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';

/// 用户状态管理Provider
class UserProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();
  
  UserProfile? _profile;
  bool _isLoading = false;
  String? _error;
  
  /// 获取用户资料
  UserProfile? get profile => _profile;
  
  /// 是否正在加载
  bool get isLoading => _isLoading;
  
  /// 错误信息
  String? get error => _error;
  
  /// 加载用户资料
  Future<void> loadProfile() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();
      
      final response = await _apiService.getUserProfile();
      if (response['success'] == true && response['data'] != null) {
        _profile = UserProfile.fromJson(response['data']['user']);
        
        // 加载用户统计信息
        final statsResponse = await _apiService.getUserStats();
        if (statsResponse['success'] == true && statsResponse['data'] != null) {
          _profile = _profile!.copyWith(
            stats: UserStats.fromJson(statsResponse['data']),
          );
        }
      } else {
        _error = response['error']?['message'] ?? '加载用户资料失败';
      }
    } catch (e) {
      _error = '加载用户资料失败: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  /// 更新用户资料
  Future<bool> updateProfile({
    String? username,
    String? email,
    String? bio,
  }) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();
      
      final response = await _apiService.updateUserProfile(
        username: username,
        email: email,
        bio: bio,
      );
      
      if (response['success'] == true && response['data'] != null) {
        _profile = UserProfile.fromJson(response['data']['user']);
        notifyListeners();
        return true;
      } else {
        _error = response['error']?['message'] ?? '更新用户资料失败';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '更新用户资料失败: $e';
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  /// 上传头像
  Future<bool> uploadAvatar(dynamic file, {AuthProvider? authProvider}) async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();
      
      final response = await _apiService.uploadAvatar(file);
      
      if (response['success'] == true && response['data'] != null) {
        final String? avatarUrl = response['data']['avatar'];
        
        // 更新用户资料中的头像
        if (_profile != null) {
          _profile = _profile!.copyWith(
            avatar: avatarUrl,
          );
        }
        
        // 通知AuthProvider更新用户头像
        if (authProvider != null && avatarUrl != null) {
          authProvider.updateUserAvatar(avatarUrl);
        }
        
        notifyListeners();
        return true;
      } else {
        _error = response['error']?['message'] ?? '上传头像失败';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '上传头像失败: $e';
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
  
  /// 清除错误信息
  void clearError() {
    _error = null;
    notifyListeners();
  }
} 