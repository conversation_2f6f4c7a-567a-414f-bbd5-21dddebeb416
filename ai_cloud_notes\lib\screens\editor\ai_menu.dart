import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/providers/ai_function_provider.dart';
import 'package:ai_cloud_notes/screens/editor/note_qa_dialog.dart';

/// AI功能菜单
class AIMenu extends StatelessWidget {
  final Function(String) onGenerateCompletion;
  final Function(String) onGenerateSummary;
  final Function(List<String>) onGenerateTagSuggestions;
  final Function() onClose;
  final String currentContent;
  final BuildContext parentContext; // 添加父级上下文以使用Provider

  const AIMenu({
    Key? key,
    required this.onGenerateCompletion,
    required this.onGenerateSummary,
    required this.onGenerateTagSuggestions,
    required this.onClose,
    required this.currentContent,
    required this.parentContext,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final aiService = AIFunctionProvider(parentContext);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.1),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.smart_toy,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'AI助手',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.titleLarge?.color,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: Theme.of(context).iconTheme.color,
                  size: 20,
                ),
                onPressed: onClose,
              ),
            ],
          ),
          const Divider(),
          const SizedBox(height: 8),
          _buildAIStatusOption(
            context,
            icon: Icons.auto_awesome,
            title: '智能预测',
            description: '根据当前光标位置预测下一个单词或短语，按Tab键接受',
            enabled: aiService.isSmartSuggestionsEnabled,
          ),
          const SizedBox(height: 12),
          _buildAIOption(
            context,
            icon: Icons.summarize,
            title: '生成摘要',
            description: '为当前笔记生成简洁的摘要',
            enabled: aiService.isContentSummaryEnabled,
            onTap: () async {
              if (!aiService.isContentSummaryEnabled) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('内容摘要功能未启用，请在设置中启用')),
                );
                return;
              }
              debugPrint('[AIMenu] 生成摘要 - 开始');

              // 保存对话框context的引用
              BuildContext? dialogContext;

              // 显示加载对话框并保存context
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext ctx) {
                  dialogContext = ctx; // 保存对话框的context
                  return AlertDialog(
                    backgroundColor: Theme.of(context).cardColor,
                    content: Row(
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                              AppTheme.primaryColor),
                        ),
                        const SizedBox(width: 20),
                        Text(
                          '正在生成摘要...',
                          style: TextStyle(
                            color:
                                Theme.of(context).textTheme.bodyMedium?.color,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
              debugPrint('[AIMenu] 生成摘要 - 加载对话框显示完毕');

              try {
                // 调用AI服务生成摘要
                final result = await aiService.generateSummary(
                  content: currentContent,
                );
                debugPrint('[AIMenu] 生成摘要 - API 调用完成, result: $result');

                // 关闭加载对话框 - 使用对话框的context
                if (dialogContext != null && dialogContext!.mounted) {
                  Navigator.pop(dialogContext!);
                  debugPrint('[AIMenu] 生成摘要 - 使用dialogContext关闭对话框');
                } else {
                  debugPrint('[AIMenu] 生成摘要 - dialogContext不可用，尝试使用原context');
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                }

                if (result['success'] == true) {
                  debugPrint(
                      '[AIMenu] 生成摘要 - 成功, summary: ${result['data']['summary']}');
                  // 调用回调函数
                  onGenerateSummary(result['data']['summary']);
                  // 关闭AI菜单
                  onClose();
                } else {
                  debugPrint('[AIMenu] 生成摘要 - 失败, error: ${result['error']}');
                  // 显示错误信息
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content:
                              Text(result['error']['message'] ?? '生成摘要失败')),
                    );
                  }
                }
              } catch (e, s) {
                debugPrint('[AIMenu] 生成摘要 - 发生异常: $e');
                debugPrint('[AIMenu] 生成摘要 - 异常堆栈: $s');

                // 关闭加载对话框 - 使用对话框的context
                if (dialogContext != null && dialogContext!.mounted) {
                  Navigator.pop(dialogContext!);
                  debugPrint('[AIMenu] 生成摘要 - 异常处理：使用dialogContext关闭对话框');
                } else {
                  debugPrint(
                      '[AIMenu] 生成摘要 - 异常处理：dialogContext不可用，尝试使用原context');
                  if (context.mounted) {
                    Navigator.pop(context);
                  }
                }

                // 显示错误信息
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('生成摘要失败: $e')),
                  );
                }
              } finally {
                debugPrint('[AIMenu] 生成摘要 - finally 块执行');
              }
            },
          ),
          const SizedBox(height: 12),
          _buildAIOption(
            context,
            icon: Icons.tag,
            title: '标签建议',
            description: '根据内容自动生成相关标签',
            enabled: aiService.isAutoTaggingEnabled,
            onTap: () async {
              if (!aiService.isAutoTaggingEnabled) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('自动标签功能未启用，请在设置中启用')),
                );
                return;
              }

              // 保存对话框context的引用
              BuildContext? dialogContext;

              // 显示加载对话框并保存context
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext ctx) {
                  dialogContext = ctx; // 保存对话框的context
                  return AlertDialog(
                    backgroundColor: Theme.of(context).cardColor,
                    content: Row(
                      children: [
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                              AppTheme.primaryColor),
                        ),
                        const SizedBox(width: 20),
                        Text(
                          '正在生成标签建议...',
                          style: TextStyle(
                            color:
                                Theme.of(context).textTheme.bodyMedium?.color,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );

              try {
                // 调用AI服务生成标签建议
                final result = await aiService.generateTagSuggestions(
                  content: currentContent,
                );

                // 关闭加载对话框 - 使用对话框的context
                if (dialogContext != null && dialogContext!.mounted) {
                  Navigator.pop(dialogContext!);
                } else if (context.mounted) {
                  Navigator.pop(context);
                }

                if (result['success'] == true) {
                  // 调用回调函数
                  onGenerateTagSuggestions(
                      List<String>.from(result['data']['tags']));
                  // 关闭AI菜单
                  onClose();
                } else {
                  // 显示错误信息
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                          content:
                              Text(result['error']['message'] ?? '生成标签建议失败')),
                    );
                  }
                }
              } catch (e) {
                // 关闭加载对话框 - 使用对话框的context
                if (dialogContext != null && dialogContext!.mounted) {
                  Navigator.pop(dialogContext!);
                } else if (context.mounted) {
                  Navigator.pop(context);
                }

                // 显示错误信息
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('生成标签建议失败: $e')),
                  );
                }
              }
            },
          ),

          // 添加笔记智能问答功能
          const SizedBox(height: 12),
          _buildAIOption(
            context,
            icon: Icons.question_answer,
            title: '笔记问答',
            description: '根据笔记内容进行智能问答对话',
            enabled: aiService.isAIEnabled,
            onTap: () {
              if (!aiService.isAIEnabled) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('AI功能未启用，请在设置中启用')),
                );
                return;
              }

              // 关闭AI菜单
              onClose();

              // 打开问答对话框
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return NoteQADialog(
                    noteContent: currentContent,
                    aiService: aiService,
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAIStatusOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required bool enabled,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: enabled
            ? Theme.of(context).cardColor
            : Theme.of(context).brightness == Brightness.dark
                ? Colors.grey.shade800
                : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: enabled
              ? Theme.of(context).dividerColor
              : Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade700
                  : Colors.grey.shade300,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: enabled
                  ? AppTheme.primaryColor.withOpacity(0.1)
                  : Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: enabled ? AppTheme.primaryColor : Colors.grey,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: enabled
                        ? Theme.of(context).textTheme.titleMedium?.color
                        : Colors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: enabled
                        ? Theme.of(context).textTheme.bodySmall?.color
                        : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: enabled,
            onChanged: null, // 不可交互，只显示状态
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  Widget _buildAIOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required bool enabled,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: enabled
              ? Theme.of(context).cardColor
              : Theme.of(context).brightness == Brightness.dark
                  ? Colors.grey.shade800
                  : Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: enabled
                ? Theme.of(context).dividerColor
                : Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade700
                    : Colors.grey.shade300,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: enabled
                    ? AppTheme.primaryColor.withOpacity(0.1)
                    : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: enabled ? AppTheme.primaryColor : Colors.grey,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: enabled
                          ? Theme.of(context).textTheme.titleMedium?.color
                          : Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: enabled
                          ? Theme.of(context).textTheme.bodySmall?.color
                          : Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: enabled ? Theme.of(context).iconTheme.color : Colors.grey,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
