import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
// LoginPage 将通过路由名导航，不再直接导入
// import 'package:ai_cloud_notes/screens/auth/login_page.dart';
import 'package:shared_preferences/shared_preferences.dart'; // 导入 shared_preferences

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({Key? key}) : super(key: key);

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> with SingleTickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingSlide> _slides = [
    OnboardingSlide(
      icon: Icons.edit,
      title: '轻松创建笔记',
      description: '随时随地记录您的想法和灵感，支持富文本编辑，让您的笔记更加丰富多彩。',
    ),
    OnboardingSlide(
      icon: Icons.smart_toy,
      title: 'AI智能辅助',
      description: '智能预测和内容建议，让您的写作更加高效。自动标签和分类，让笔记管理更加便捷。',
    ),
    OnboardingSlide(
      icon: Icons.cloud_upload,
      title: '云同步，多设备访问',
      description: '笔记自动同步到云端，支持多设备访问和编辑，让您的数据随时随地可用。',
    ),
  ];

  late AnimationController _controller;
  // Animation objects will be defined here based on the current slide

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600), // 动画总时长
      vsync: this,
    );

    // Start animation when the page is first loaded
    _controller.forward();

    // Add listener to reset and forward animation when page changes
    _pageController.addListener(() {
      if (_pageController.page?.round() != _currentPage) {
        setState(() {
          _currentPage = _pageController.page!.round();
        });
        _controller.reset();
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _controller.dispose(); // 释放控制器
    super.dispose();
  }

  void _navigateToLogin() async { // 改为 async
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('hasSeenOnboarding', true);
    } catch (e) {
      debugPrint('在 OnboardingPage 设置 hasSeenOnboarding 标记失败: $e');
    }
    if (mounted) { // 检查 widget 是否仍然挂载
      Navigator.of(context).pushReplacementNamed(AppRoutes.login);
    }
  }

  List<Widget> _buildPageIndicator() {
    List<Widget> indicators = [];
    for (int i = 0; i < _slides.length; i++) {
      indicators.add(
        Container(
          width: i == _currentPage ? 20 : 8,
          height: 8,
          margin: EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            color: i == _currentPage ? AppTheme.primaryColor : AppTheme.mediumGrayColor,
            borderRadius: BorderRadius.circular(i == _currentPage ? 4 : 4),
          ),
        ),
      );
    }
    return indicators;
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: AppTheme.fixedLightTheme,
      child: Scaffold(
        backgroundColor: Colors.white, // Consider if fixedLightTheme should define this
        body: SafeArea(
        child: Column(
          children: [
            // 页面指示器
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _slides.length,
                onPageChanged: (int page) {
                  // setState is handled by the listener now
                },
                itemBuilder: (context, index) {
                  // Pass animation controller to the slide
                  return _slides[index].buildAnimatedSlide(_controller);
                },
              ),
            ),

            // 底部导航点
            Container(
              padding: const EdgeInsets.only(bottom: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: _buildPageIndicator(),
              ),
            ),

            // 底部操作按钮
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 0, 24, 40),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 跳过按钮
                  TextButton(
                    onPressed: _navigateToLogin,
                    child: Text(
                      '跳过',
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),

                  // 下一步/立即开始按钮
                  ElevatedButton(
                    onPressed: () {
                      if (_currentPage < _slides.length - 1) {
                        _pageController.nextPage(
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeIn,
                        );
                      } else {
                        _navigateToLogin();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      _currentPage < _slides.length - 1 ? '下一步' : '立即开始',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }
}

class OnboardingSlide {
  final IconData icon;
  final String title;
  final String description;

  OnboardingSlide({
    required this.icon,
    required this.title,
    required this.description,
  });

  // Modified buildSlide to accept AnimationController and apply animations
  Widget buildAnimatedSlide(AnimationController controller) {
    // Define animations for elements within the slide
    final iconSlideAnimation = Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );
    final iconFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    final titleSlideAnimation = Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
      ),
    );
    final titleFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
      ),
    );

    final descriptionSlideAnimation = Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.4, 1.0, curve: Curves.easeOut),
      ),
    );
    final descriptionFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: const Interval(0.4, 1.0, curve: Curves.easeOut),
      ),
    );


    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 滑动页图标
          AnimatedBuilder(
            animation: controller,
            builder: (context, child) {
              return FadeTransition(
                opacity: iconFadeAnimation,
                child: SlideTransition(
                  position: iconSlideAnimation,
                  child: child,
                ),
              );
            },
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.primaryColor.withOpacity(0.1),
                    AppTheme.secondaryColor.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(100),
              ),
              child: Icon(
                icon,
                size: 80,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
          SizedBox(height: 40),

          // 滑动页标题
          AnimatedBuilder(
            animation: controller,
            builder: (context, child) {
              return FadeTransition(
                opacity: titleFadeAnimation,
                child: SlideTransition(
                  position: titleSlideAnimation,
                  child: child,
                ),
              );
            },
            child: Text(
              title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppTheme.blackColor,
              ),
            ),
          ),
          SizedBox(height: 16),

          // 滑动页描述
          AnimatedBuilder(
            animation: controller,
            builder: (context, child) {
              return FadeTransition(
                opacity: descriptionFadeAnimation,
                child: SlideTransition(
                  position: descriptionSlideAnimation,
                  child: child,
                ),
              );
            },
            child: Text(
              description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.darkGrayColor,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }
}