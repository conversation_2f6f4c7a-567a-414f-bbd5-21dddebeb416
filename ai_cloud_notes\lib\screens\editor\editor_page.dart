import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb; // 导入kIsWeb常量
import 'package:flutter/services.dart'; // 导入Clipboard功能和键盘事件
import 'package:ai_cloud_notes/utils/editor_utils.dart'; // 导入通用工具函数
import 'package:provider/provider.dart'; // 导入Provider
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/models/tag_model.dart'; // 导入Tag模型
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:uuid/uuid.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
// import 'package:fleather/fleather.dart'; // No longer directly used here
import 'package:parchment/parchment.dart';
import 'package:parchment_delta/parchment_delta.dart';
import 'package:flutter_markdown/flutter_markdown.dart'; // 导入Markdown渲染包
import 'package:markdown/markdown.dart' as md; // 导入Markdown核心库
import 'package:ai_cloud_notes/providers/note_provider.dart'; // 导入笔记Provider
import 'package:ai_cloud_notes/providers/tag_provider.dart'; // 导入标签Provider
import 'package:ai_cloud_notes/providers/ai_provider.dart'; // 导入AI Provider
import 'package:ai_cloud_notes/providers/ai_function_provider.dart'; // 导入AI功能 Provider
import 'package:ai_cloud_notes/utils/snackbar_helper.dart'; // 导入SnackBar辅助工具
import 'package:ai_cloud_notes/utils/date_time_helper.dart'; // 导入时间处理工具类
import 'package:ai_cloud_notes/screens/editor/ai_menu.dart'; // 导入AI菜单
import 'package:ai_cloud_notes/screens/editor/markdown_editor.dart'; // 导入Markdown编辑器组件
import 'package:ai_cloud_notes/screens/editor/ai_prediction_bar.dart'; // 导入可拖动的AI预测栏
import 'package:shared_preferences/shared_preferences.dart'; // 导入本地存储

import 'dart:convert'; // 导入JSON和Base64支持
import 'dart:math'; // 导入math包，支持min函数
import 'dart:async'; // 导入Timer类
import 'package:url_launcher/url_launcher.dart'; // 导入URL启动器

import 'rich_text_editor.dart'; // Import the new RichTextEditor

class EditorPage extends StatefulWidget {
  final Note? note;
  final String? initialTagId;
  final String? initialTagName;

  const EditorPage({
    Key? key,
    this.note,
    this.initialTagId,
    this.initialTagName,
  }) : super(key: key);

  @override
  State<EditorPage> createState() => _EditorPageState();
}

class _EditorPageState extends State<EditorPage> {
  final TextEditingController _titleController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();

  late ParchmentDocument _document;

  final GlobalKey<MarkdownEditorState> _markdownEditorKey =
      GlobalKey<MarkdownEditorState>();
  final GlobalKey<RichTextEditorState> _richTextEditorKey =
      GlobalKey<RichTextEditorState>();

  bool _isEditing = true;
  bool _showAIHint = false;
  bool _showAIMenu = false;
  List<String> _tags = [];
  String _aiSuggestion = '';
  bool _editingTitle = false;
  bool _isFavorite = false;

  String _aiPrediction = '';
  bool _showAIPrediction = false;
  bool _isGeneratingPrediction = false;
  Offset _predictionBarPosition = Offset(100, 100);

  double _aiHintBarWidth = 300.0;
  double _aiHintBarHeight = 100.0;
  final GlobalKey _aiPredictionBarKey = GlobalKey();

  Offset? _aiButtonPosition;
  bool _predictionBarPositionLoaded = false;
  late AIFunctionProvider _aiService;
  Timer? _savePositionDebounceTimer;
  Offset? _latestPredictionBarPosition;
  Timer? _debouncePredictionTimer;
  String _selectedContentType = 'rich-text';
  TextSelection? _lastSelection;

  @override
  void initState() {
    super.initState();
    _aiService = AIFunctionProvider(context);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      Offset? savedPosition = await _loadPredictionBarPosition();
      final size = MediaQuery.of(context).size;
      setState(() {
        if (savedPosition != null) {
          _predictionBarPosition = savedPosition;
        } else {
          _predictionBarPosition = Offset(size.width - 300, size.height / 2);
        }
        _predictionBarPositionLoaded = true;
      });
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      final size = MediaQuery.of(context).size;
      final aiButtonPos = Offset(size.width - 70, size.height - 200);
      setState(() {
        _aiButtonPosition = aiButtonPos;
      });
    });

    if (widget.note != null) {
      _titleController.text = widget.note!.title;
      _selectedContentType = widget.note!.contentType;

      if (widget.note!.contentType == 'markdown') {
        String content = widget.note!.content;
        print(
            '[DEBUG_ROO] initState (markdown): loading widget.note.content = "$content"'); // 日志 A
        if (!content.endsWith('\n')) content += '\n';
        _document = ParchmentDocument.fromJson([
          {"insert": content}
        ]);
      } else {
        try {
          if (widget.note!.content.trim().startsWith('[') ||
              widget.note!.content.trim().startsWith('{')) {
            dynamic deltaJson = jsonDecode(widget.note!.content);
            _document = ParchmentDocument.fromJson(deltaJson);
            String content = _document.toPlainText();
            if (!content.endsWith('\n')) {
              final delta = _document.toDelta()..insert('\n');
              _document = ParchmentDocument.fromDelta(delta);
            }
          } else {
            String content = widget.note!.content;
            if (!content.endsWith('\n')) content += '\n';
            _document = ParchmentDocument.fromJson([
              {"insert": content}
            ]);
          }
        } catch (e) {
          String content = widget.note!.content;
          if (!content.endsWith('\n')) content += '\n';
          _document = ParchmentDocument.fromJson([
            {"insert": content}
          ]);
        }
      }
      _tags = List.from(widget.note!.tagIds);
      _isFavorite = widget.note!.isFavorite;
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (mounted) {
          final confirmed = await _showFormatSelectionDialog();
          if (!confirmed) {
            Navigator.pop(context);
            return;
          }
          setState(() {});
        }
      });
      _titleController.text = '无标题';
      final delta = Delta()..insert('\n');
      _document = ParchmentDocument.fromDelta(delta);
      if (widget.initialTagId != null && widget.initialTagId!.isNotEmpty) {
        _tags.add(widget.initialTagId!);
      }
    }

    _contentFocusNode.addListener(() {
      if (!_contentFocusNode.hasFocus) {
        final contentType = widget.note?.contentType ?? _selectedContentType;
        if (contentType != 'markdown') {
          setState(() {
            _showAIPrediction = false;
          });
        }
      }
    });

    if (widget.note != null && widget.note!.tagIds.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _loadTagData());
    } else if (widget.initialTagId != null && widget.initialTagId!.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _loadTagData());
    }
  }

  @override
  void didUpdateWidget(EditorPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_showAIPrediction && _aiPrediction.isNotEmpty) {
      _measureAIPredictionBarSize();
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _savePositionDebounceTimer?.cancel();
    _debouncePredictionTimer?.cancel();
    if (_latestPredictionBarPosition != null) {
      _persistPredictionBarPosition();
    }
    _titleFocusNode.dispose();
    _contentFocusNode.dispose();
    super.dispose();
  }

  void _savePredictionBarPosition(Offset position) {
    _latestPredictionBarPosition = position;
    final size = MediaQuery.of(context).size;
    final barWidth = _aiHintBarWidth;
    final barHeight = _aiHintBarHeight;
    const safetyMargin = 20.0;
    double x = position.dx.clamp(0, size.width - barWidth - safetyMargin);
    double y = position.dy.clamp(0, size.height - barHeight);

    if (mounted) {
      setState(() {
        _predictionBarPosition = Offset(x, y);
        _predictionBarPositionLoaded = true;
      });
    }
    _savePositionDebounceTimer?.cancel();
    _savePositionDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      _persistPredictionBarPosition();
    });
  }

  Future<void> _persistPredictionBarPosition() async {
    if (_latestPredictionBarPosition == null) return;
    try {
      final prefs = await SharedPreferences.getInstance();
      if (!mounted) return;
      final positionData = {
        'dx': _latestPredictionBarPosition!.dx,
        'dy': _latestPredictionBarPosition!.dy,
        'width': _aiHintBarWidth,
        'height': _aiHintBarHeight,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      await prefs.setString('prediction_bar_data', jsonEncode(positionData));
    } catch (e) {
      print('[POSITION_DEBUG] 保存预测栏位置失败: $e');
    }
  }

  Future<Offset?> _loadPredictionBarPosition() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataStr = prefs.getString('prediction_bar_data');
      if (dataStr != null) {
        final data = jsonDecode(dataStr) as Map<String, dynamic>;
        if (data.containsKey('width') && data.containsKey('height')) {
          _aiHintBarWidth = (data['width'] as num).toDouble();
          _aiHintBarHeight = (data['height'] as num).toDouble();
        }
        _predictionBarPositionLoaded = true;
        return Offset(
            (data['dx'] as num).toDouble(), (data['dy'] as num).toDouble());
      }
      final dx = prefs.getDouble('prediction_bar_x');
      final dy = prefs.getDouble('prediction_bar_y');
      if (dx != null && dy != null) {
        _predictionBarPositionLoaded = true;
        return Offset(dx, dy);
      }
    } catch (e) {
      print('[POSITION_DEBUG] 加载预测栏位置失败: $e');
    }
    return null;
  }

  void _measureAIPredictionBarSize() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      final RenderBox? renderBox =
          _aiPredictionBarKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null && renderBox.hasSize) {
        setState(() {
          _aiHintBarWidth = renderBox.size.width;
          _aiHintBarHeight = renderBox.size.height;
        });
      }
    });
  }

  void _resetPredictionBarPosition() {
    if (!mounted) return;
    final size = MediaQuery.of(context).size;
    final defaultPosition = Offset(size.width - 300, size.height / 2);
    setState(() {
      _predictionBarPosition = defaultPosition;
      _predictionBarPositionLoaded = true;
      _latestPredictionBarPosition = defaultPosition;
    });
    _persistPredictionBarPosition();
  }

  void _onRichTextDocumentChanged(ParchmentDocument newDoc) {
    setState(() {
      _document = newDoc;
    });
    _handleAIPredictionTrigger();
  }

  void _onRichTextSelectionChanged(TextSelection selection) {
    _lastSelection = selection;
    _handleAIPredictionTrigger();
  }

  void _handleAIPredictionTrigger() {
    _debouncePredictionTimer?.cancel();
    final aiProvider = Provider.of<AIProvider>(context, listen: false);
    final freq = aiProvider.suggestionFrequency;
    if (freq == '实时') {
      _generateCursorPrediction();
    } else if (freq == '输入停顿后') {
      _debouncePredictionTimer = Timer(const Duration(milliseconds: 800), () {
        _generateCursorPrediction();
      });
    } else if (freq == '手动触发') {
      setState(() {
        _showAIPrediction = false;
      });
    }
  }

  void _generateCursorPrediction() async {
    if (!mounted ||
        !_aiService.isSmartSuggestionsEnabled ||
        _isGeneratingPrediction) {
      return;
    }

    String content;
    int cursorPosition;
    final currentEditorType = widget.note?.contentType ?? _selectedContentType;

    if (currentEditorType == 'markdown') {
      if (_markdownEditorKey.currentState != null) {
        content = _markdownEditorKey.currentState!.getContent();
        cursorPosition = _markdownEditorKey.currentState!.getCursorPosition();
        if (cursorPosition < 0) {
          setState(() => _showAIPrediction = false);
          return;
        }
      } else {
        setState(() => _showAIPrediction = false);
        return;
      }
    } else {
      if (_richTextEditorKey.currentState != null) {
        final editorState = _richTextEditorKey.currentState!;
        final selection = editorState.selection;
        if (selection == null || !selection.isCollapsed) {
          setState(() => _showAIPrediction = false);
          return;
        }
        content = editorState.currentDocument.toPlainText();
        cursorPosition = selection.baseOffset;
      } else {
        setState(() => _showAIPrediction = false);
        return;
      }
    }

    if (cursorPosition < 3) {
      setState(() => _showAIPrediction = false);
      return;
    }

    _isGeneratingPrediction = true;
    try {
      final result = await _aiService.generateCompletion(
        content: content,
        cursorPosition: cursorPosition,
      );
      if (!mounted) return;
      if (result['success'] == true && result['data']?['completion'] != null) {
        final prediction = result['data']['completion'];
        if (prediction.trim().isEmpty) {
          if (mounted) setState(() => _showAIPrediction = false);
          return;
        }
        if (mounted) {
          setState(() {
            _aiPrediction = prediction;
            _showAIPrediction = true;
            if (!_predictionBarPositionLoaded) {
              final size = MediaQuery.of(context).size;
              _predictionBarPosition = Offset(20, size.height - 100);
              _predictionBarPositionLoaded = true;
              _measureAIPredictionBarSize();
            }
          });
        }
      } else {
        if (mounted) setState(() => _showAIPrediction = false);
      }
    } catch (e) {
      print('ERROR: [EditorPage] 生成光标预测失败: $e');
      if (mounted) setState(() => _showAIPrediction = false);
    } finally {
      _isGeneratingPrediction = false;
    }
  }

  void _acceptPrediction() {
    if (!_showAIPrediction || _aiPrediction.isEmpty) return;

    final currentEditorType = widget.note?.contentType ?? _selectedContentType;
    bool predictionAccepted = false;

    if (currentEditorType == 'markdown') {
      if (_markdownEditorKey.currentState != null) {
        _markdownEditorKey.currentState!.insertTextAtCursor(_aiPrediction);
        _updateDocumentFromMarkdown();
        predictionAccepted = true;
      }
    } else {
      if (_richTextEditorKey.currentState != null) {
        final editorState = _richTextEditorKey.currentState!;
        final selection = editorState.selection;
        if (selection != null && selection.isCollapsed) {
          final int insertPosition = min(selection.baseOffset,
              editorState.currentDocument.toPlainText().length);
          editorState.replaceText(
            insertPosition,
            0,
            _aiPrediction,
            selection: TextSelection.collapsed(
                offset: insertPosition + _aiPrediction.length),
          );
          predictionAccepted = true;
          if (kIsWeb) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) _contentFocusNode.requestFocus();
            });
          }
        }
      }
    }

    if (predictionAccepted) {
      setState(() {
        _showAIPrediction = false;
        _aiPrediction = '';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final aiProvider = context.watch<AIProvider>();
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: GestureDetector(
        onTap: () {
          if (_showAIMenu || _showAIHint) {
            setState(() {
              _showAIMenu = false;
              _showAIHint = false;
            });
          }
        },
        child: Stack(
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: _isEditing
                          ? const Offset(1.0, 0.0)
                          : const Offset(-1.0, 0.0),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                        parent: animation, curve: Curves.easeOutCubic)),
                    child: child,
                  ),
                );
              },
              child: _isEditing
                  ? _buildEditor(key: const ValueKey('editor_mode'))
                  : _buildReadingMode(key: const ValueKey('reading_mode')),
            ),
            if (_showAIPrediction && _isEditing && _aiPrediction.isNotEmpty)
              Positioned(
                left: _predictionBarPosition.dx,
                top: _predictionBarPosition.dy,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.centerRight,
                      child: IconButton(
                        onPressed: _resetPredictionBarPosition,
                        icon: const Icon(Icons.refresh,
                            size: 16, color: AppTheme.primaryColor),
                        tooltip: '重置预测栏位置',
                        padding: const EdgeInsets.all(4),
                        constraints: BoxConstraints(),
                        splashRadius: 18,
                      ),
                    ),
                    const SizedBox(height: 4),
                    DraggableAIPredictionBar(
                      key: _aiPredictionBarKey,
                      prediction: _aiPrediction,
                      initialPosition: Offset.zero,
                      preventDismissOnTapOutside: false,
                      onAccept: _acceptPrediction,
                      onDismiss: () =>
                          setState(() => _showAIPrediction = false),
                      onDrag: _savePredictionBarPosition,
                      useLocalPositioning: true,
                    ),
                  ],
                ),
              ),
            if (_aiButtonPosition != null &&
                _isEditing &&
                aiProvider.aiAssistantEnabled)
              Positioned(
                left: _aiButtonPosition!.dx,
                top: _aiButtonPosition!.dy,
                child: GestureDetector(
                  onTap: () => setState(() => _showAIMenu = true),
                  onPanUpdate: (details) {
                    setState(() {
                      _aiButtonPosition = Offset(
                        (_aiButtonPosition!.dx + details.delta.dx)
                            .clamp(0.0, MediaQuery.of(context).size.width - 48),
                        (_aiButtonPosition!.dy + details.delta.dy).clamp(
                            0.0, MediaQuery.of(context).size.height - 48),
                      );
                    });
                  },
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            blurRadius: 8,
                            spreadRadius: 1,
                            offset: const Offset(0, 2))
                      ],
                      border: Border.all(
                          color: AppTheme.primaryColor.withOpacity(0.2),
                          width: 1.5),
                    ),
                    child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Image.asset('/images/AI.png',
                            width: 24,
                            height: 24,
                            color: AppTheme.primaryColor)),
                  ),
                ),
              ),
            if (_showAIMenu && _isEditing) _buildAIMenu(),
            if (_showAIHint && _isEditing) _buildAIHint(),
            if (aiProvider.suggestionFrequency == '手动触发' && _isEditing)
              Positioned(
                right: 24,
                bottom: 24 + MediaQuery.of(context).padding.bottom,
                child: FloatingActionButton(
                  onPressed: _generateCursorPrediction,
                  child: Icon(Icons.lightbulb),
                  tooltip: 'AI预测',
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  heroTag: 'aiSuggestionFab',
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditor({Key? key}) {
    return SafeArea(
      key: key,
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildEditorContent(),
            ),
          ),
          _buildToolbar(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
            bottom:
                BorderSide(color: Theme.of(context).dividerColor, width: 1)),
      ),
      child: Row(
        children: [
          InkWell(
              onTap: () => Navigator.pop(context),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                  width: 40,
                  height: 40,
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(8)),
                  child: Icon(Icons.arrow_back,
                      color: Theme.of(context).iconTheme.color, size: 20))),
          Expanded(
            child: GestureDetector(
              onDoubleTap: () {
                setState(() => _editingTitle = true);
                _titleFocusNode.requestFocus();
              },
              child: _editingTitle
                  ? TextField(
                      controller: _titleController,
                      focusNode: _titleFocusNode,
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).textTheme.titleLarge?.color),
                      decoration: const InputDecoration(
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(horizontal: 16)),
                      onSubmitted: (_) {
                        setState(() {
                          _editingTitle = false;
                          if (_titleController.text.trim().isEmpty)
                            _titleController.text = '无标题';
                        });
                      },
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(_titleController.text,
                          style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.color),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis)),
            ),
          ),
          Row(children: [
            InkWell(
                onTap: _toggleMode,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                    width: 40,
                    height: 40,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(8)),
                    child: Icon(_isEditing ? Icons.visibility : Icons.edit,
                        color: Theme.of(context).iconTheme.color, size: 20))),
            const SizedBox(width: 12),
            InkWell(
              onTap: () {
                if (widget.note != null && widget.note!.id.isNotEmpty) {
                  Provider.of<NoteProvider>(context, listen: false)
                      .fetchNoteHistory(widget.note!.id);
                  _showHistoryBottomSheet(context);
                } else {
                  SnackbarHelper.showInfo(
                      context: context, message: '请先保存笔记以查看历史版本');
                }
              },
              borderRadius: BorderRadius.circular(8),
              child: Container(
                  width: 40,
                  height: 40,
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(8)),
                  child: Icon(Icons.history,
                      color: Theme.of(context).iconTheme.color, size: 20)),
            ),
            const SizedBox(width: 12),
            InkWell(
                onTap: _shareNote,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                    width: 40,
                    height: 40,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(8)),
                    child: Icon(Icons.share,
                        color: Theme.of(context).iconTheme.color, size: 20))),
            const SizedBox(width: 12),
            InkWell(
                onTap: _saveNote,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                    width: 40,
                    height: 40,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(8)),
                    child: Icon(Icons.save,
                        color: Theme.of(context).iconTheme.color, size: 20))),
          ]),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 120),
          child: Focus(
            focusNode: FocusNode(),
            onKey: (FocusNode node, RawKeyEvent event) {
              if (event is RawKeyDownEvent &&
                  event.logicalKey == LogicalKeyboardKey.tab) {
                if (kIsWeb) {
                  if (_showAIPrediction && _aiPrediction.isNotEmpty) {
                    _acceptPrediction();
                    return KeyEventResult.handled;
                  }
                }
                return KeyEventResult.ignored;
              }
              return KeyEventResult.ignored;
            },
            child: _getEditorWidget(),
          ),
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                    top: BorderSide(
                        color: Theme.of(context).dividerColor, width: 1))),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(children: [
                ..._tags.map((tagId) => Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: _buildTag(tagId))),
                _buildAddTagButton()
              ]),
            ),
          ),
        ),
      ],
    );
  }

  Tag? _getTagById(String tagId) {
    final tagProvider = Provider.of<TagProvider>(context, listen: false);
    try {
      return tagProvider.tags.firstWhere((tag) => tag.id == tagId);
    } catch (e) {
      return null;
    }
  }

  Widget _buildTag(String tagId) {
    final tag = _getTagById(tagId);
    if (tag == null) return Container();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
          color: tag.colorValue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(6)),
      child: Row(mainAxisSize: MainAxisSize.min, children: [
        Text(tag.name,
            style: TextStyle(
                color: tag.colorValue,
                fontSize: 14,
                fontWeight: FontWeight.w500)),
        const SizedBox(width: 6),
        InkWell(
            onTap: () => setState(() => _tags.remove(tagId)),
            child: Icon(Icons.close, size: 14, color: tag.colorValue)),
      ]),
    );
  }

  Widget _buildAddTagButton() {
    return InkWell(
      onTap: _addTag,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        decoration: BoxDecoration(
            color: Theme.of(context).brightness == Brightness.dark
                ? Colors.grey.shade800
                : Colors.grey.shade200,
            borderRadius: BorderRadius.circular(6)),
        child: Row(mainAxisSize: MainAxisSize.min, children: [
          Icon(Icons.add,
              size: 14, color: Theme.of(context).textTheme.bodyMedium?.color),
          const SizedBox(width: 6),
          Text('添加标签',
              style: TextStyle(
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                  fontSize: 14)),
        ]),
      ),
    );
  }

  Widget _buildAIHint() {
    if (_aiButtonPosition == null) return const SizedBox.shrink();
    final screenSize = MediaQuery.of(context).size;
    double left = max(0, _aiButtonPosition!.dx - 150);
    double top = max(0, _aiButtonPosition!.dy + 48);
    if (left + 300 > screenSize.width) left = screenSize.width - 300;
    if (top + 100 > screenSize.height) top = _aiButtonPosition!.dy - 100;

    return Positioned(
      left: left,
      top: top,
      child: DraggableAIPredictionBar(
        prediction: _aiSuggestion,
        initialPosition: Offset.zero,
        preventDismissOnTapOutside: false,
        onAccept: () {
          if (_aiSuggestion.trim().isEmpty) {
            ScaffoldMessenger.of(context)
                .showSnackBar(const SnackBar(content: Text('AI建议内容为空')));
            return;
          }
          setState(() {
            _showAIHint = false;
            final currentText = _document.toPlainText();
            final newText = currentText +
                (_aiSuggestion.endsWith('\n')
                    ? _aiSuggestion
                    : '$_aiSuggestion\n');
            _document = ParchmentDocument.fromJson([
              {"insert": newText}
            ]);
            if (_selectedContentType == 'markdown' &&
                _markdownEditorKey.currentState != null) {
              _markdownEditorKey.currentState!.getTextController().text =
                  _document.toPlainText();
              _updateDocumentFromMarkdown(); // Ensure _document is also updated if needed
            } else if (_selectedContentType != 'markdown' &&
                _richTextEditorKey.currentState != null) {
              _richTextEditorKey.currentState!.updateDocument(_document);
            }
          });
          ScaffoldMessenger.of(context)
              .showSnackBar(const SnackBar(content: Text('已添加AI建议的内容')));
        },
        onDismiss: () => setState(() => _showAIHint = false),
      ),
    );
  }

  Widget _buildAIMenu() {
    if (_aiButtonPosition == null) return const SizedBox.shrink();
    final screenSize = MediaQuery.of(context).size;
    double menuWidth = 300.0;
    const menuHeight = 250.0;
    double left, top;

    if (_aiButtonPosition!.dx > screenSize.width / 2) {
      left = max(0, _aiButtonPosition!.dx - menuWidth);
      if (_aiButtonPosition!.dx < menuWidth) {
        menuWidth = max(200, _aiButtonPosition!.dx);
        left = max(0, _aiButtonPosition!.dx - menuWidth);
      }
    } else {
      left = _aiButtonPosition!.dx + 48;
      if (screenSize.width - left < menuWidth) {
        menuWidth = max(200, screenSize.width - left);
      }
    }
    if (_aiButtonPosition!.dy > screenSize.height / 2) {
      top = max(0, _aiButtonPosition!.dy - menuHeight);
    } else {
      top = _aiButtonPosition!.dy + 48;
      if (top + menuHeight > screenSize.height) {
        top = max(0, _aiButtonPosition!.dy - menuHeight);
      }
    }

    return Positioned(
      left: left,
      top: top,
      child: Container(
        width: menuWidth,
        child: AIMenu(
          currentContent: _document.toPlainText(),
          parentContext: context,
          onGenerateCompletion: (completion) {
            if (completion.trim().isEmpty) {
              ScaffoldMessenger.of(context)
                  .showSnackBar(const SnackBar(content: Text('生成的续写内容为空')));
              return;
            }
            final currentText = _document.toPlainText();
            final newText = currentText +
                (completion.endsWith('\n') ? completion : '$completion\n');
            _document = ParchmentDocument.fromJson([
              {"insert": newText}
            ]);
            if (_selectedContentType == 'markdown' &&
                _markdownEditorKey.currentState != null) {
              _markdownEditorKey.currentState!.getTextController().text =
                  _document.toPlainText();
              _updateDocumentFromMarkdown();
            } else if (_selectedContentType != 'markdown' &&
                _richTextEditorKey.currentState != null) {
              _richTextEditorKey.currentState!.updateDocument(_document);
            }
            ScaffoldMessenger.of(context)
                .showSnackBar(const SnackBar(content: Text('已添加智能续写内容')));
          },
          onGenerateSummary: (summary) {
            showDialog(
                context: context,
                builder: (context) => AlertDialog(
                      title: const Text('笔记摘要'),
                      content: Text(summary),
                      actions: [
                        TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: const Text('关闭')),
                        TextButton(
                          onPressed: () {
                            final summaryText = '摘要: $summary\n\n';

                            // 获取当前文档的 Delta
                            final currentDelta = _document.toDelta();

                            // 创建摘要文本的 Delta
                            final summaryDelta = Delta()..insert(summaryText);

                            // 将摘要 Delta 插入到当前 Delta 的开头
                            final newDelta = summaryDelta.concat(currentDelta);

                            // 从新的 Delta 创建 ParchmentDocument
                            _document = ParchmentDocument.fromDelta(newDelta);

                            if (mounted) {
                              setState(() {}); // 更新UI，让编辑器知道_document已更改
                            }

                            if (_selectedContentType == 'markdown' &&
                                _markdownEditorKey.currentState != null) {
                              _markdownEditorKey.currentState!
                                  .getTextController()
                                  .text = _document.toPlainText();
                              _updateDocumentFromMarkdown();
                            } else if (_selectedContentType != 'markdown' &&
                                _richTextEditorKey.currentState != null) {
                              _richTextEditorKey.currentState!
                                  .updateDocument(_document); // 更新富文本编辑器
                            }

                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('已添加摘要到笔记')));
                          },
                          child: const Text('添加到笔记'),
                        ),
                      ],
                    ));
          },
          onGenerateTagSuggestions: (tagsFromAI) async {
            // Renamed 'tags' to 'tagsFromAI'
            final tagProvider =
                Provider.of<TagProvider>(context, listen: false);
            List<bool> selectedTagsStatus =
                List.generate(tagsFromAI.length, (index) {
              final existingTag = tagProvider.tags
                  .where((tag) => tag.name == tagsFromAI[index])
                  .toList();
              return existingTag.isNotEmpty &&
                  _tags.contains(existingTag[0].id);
            });
            bool allSelected = selectedTagsStatus.every((selected) => selected);

            showDialog(
                context: context,
                builder: (dialogContext) => StatefulBuilder(
                      builder: (context, setDialogState) {
                        return AlertDialog(
                          title: const Text('标签建议'),
                          content: SizedBox(
                              width: double.maxFinite,
                              child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CheckboxListTile(
                                        title: const Text('全选'),
                                        value: allSelected,
                                        onChanged: (value) =>
                                            setDialogState(() {
                                              allSelected = value ?? false;
                                              selectedTagsStatus =
                                                  List.generate(
                                                      tagsFromAI.length,
                                                      (_) => allSelected);
                                            }),
                                        controlAffinity:
                                            ListTileControlAffinity.leading),
                                    const Divider(),
                                    Expanded(
                                        child: ListView.builder(
                                            shrinkWrap: true,
                                            itemCount: tagsFromAI.length,
                                            itemBuilder: (context, index) {
                                              return CheckboxListTile(
                                                  title:
                                                      Text(tagsFromAI[index]),
                                                  value:
                                                      selectedTagsStatus[index],
                                                  onChanged:
                                                      (value) =>
                                                          setDialogState(() {
                                                            selectedTagsStatus[
                                                                    index] =
                                                                value ?? false;
                                                            allSelected =
                                                                selectedTagsStatus.every(
                                                                    (selected) =>
                                                                        selected);
                                                          }),
                                                  controlAffinity:
                                                      ListTileControlAffinity
                                                          .leading);
                                            })),
                                  ])),
                          actions: [
                            TextButton(
                                onPressed: () => Navigator.pop(dialogContext),
                                child: const Text('取消')),
                            TextButton(
                              onPressed: () async {
                                for (int i = 0; i < tagsFromAI.length; i++) {
                                  if (selectedTagsStatus[i]) {
                                    _createOrAddTag(tagsFromAI[i], tagProvider);
                                  } else {
                                    _removeTagByName(
                                        tagsFromAI[i], tagProvider);
                                  }
                                }
                                Navigator.pop(dialogContext);
                                ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(content: Text('标签已更新')));
                              },
                              child: const Text('确定'),
                            ),
                          ],
                        );
                      },
                    ));
          },
          onClose: () => setState(() => _showAIMenu = false),
        ),
      ),
    );
  }

  Widget _buildToolbar() {
    final contentType = widget.note?.contentType ?? _selectedContentType;
    if (contentType == 'markdown') {
      return _buildMarkdownToolbar();
    } else {
      // RichTextEditor builds its own toolbar internally if not readOnly
      return const SizedBox.shrink();
    }
  }

  Widget _buildMarkdownToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          border: Border(
              top:
                  BorderSide(color: Theme.of(context).dividerColor, width: 1))),
      child: Column(children: [
        SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              _buildMarkdownButton(
                  child: const Text('H1',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('# '),
                  tooltip: '标题1'),
              _buildMarkdownButton(
                  child: const Text('H2',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('## '),
                  tooltip: '标题2'),
              _buildMarkdownButton(
                  child: const Text('H3',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('### '),
                  tooltip: '标题3'),
              _buildMarkdownButton(
                  child: const Text('H4',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('#### '),
                  tooltip: '标题4'),
              const VerticalDivider(width: 16, thickness: 1),
              _buildMarkdownButton(
                  child: const Icon(Icons.format_bold, size: 20),
                  onPressed: () => _insertMarkdownSyntax('**', '**'),
                  tooltip: '加粗'),
              _buildMarkdownButton(
                  child: const Icon(Icons.format_italic, size: 20),
                  onPressed: () => _insertMarkdownSyntax('*', '*'),
                  tooltip: '斜体'),
              _buildMarkdownButton(
                  child: const Icon(Icons.format_strikethrough, size: 20),
                  onPressed: () => _insertMarkdownSyntax('~~', '~~'),
                  tooltip: '删除线'),
              const VerticalDivider(width: 16, thickness: 1),
              _buildMarkdownButton(
                  child: const Icon(Icons.format_list_bulleted, size: 20),
                  onPressed: () => _insertList(false),
                  tooltip: '无序列表'),
              _buildMarkdownButton(
                  child: const Icon(Icons.format_list_numbered, size: 20),
                  onPressed: () => _insertList(true),
                  tooltip: '有序列表'),
              const VerticalDivider(width: 16, thickness: 1),
              _buildMarkdownButton(
                  child: const Icon(Icons.code, size: 20),
                  onPressed: () => _insertCodeBlock(),
                  tooltip: '代码块'),
              _buildMarkdownButton(
                  child: const Icon(Icons.format_quote, size: 20),
                  onPressed: () => _insertMarkdownSyntax('> '),
                  tooltip: '引用'),
              _buildMarkdownButton(
                  child: const Icon(Icons.horizontal_rule, size: 20),
                  onPressed: () => _insertMarkdownSyntax('---\n'),
                  tooltip: '分隔线'),
              _buildMarkdownButton(
                  child: const Icon(Icons.image, size: 20),
                  onPressed: () =>
                      _insertMarkdownSyntax('![alt text](image_url)'),
                  tooltip: '插入图片'),
              _buildMarkdownButton(
                  child: const Icon(Icons.link, size: 20),
                  onPressed: () => _insertMarkdownSyntax('[link text](url)'),
                  tooltip: '插入链接'),
              _buildMarkdownButton(
                  child: const Icon(Icons.table_chart, size: 20),
                  onPressed: () => _insertMarkdownSyntax(
                      '| 标题1 | 标题2 | 标题3 |\n| --- | --- | --- |\n| 内容1 | 内容2 | 内容3 |\n'),
                  tooltip: '插入表格'),
            ])),
        if (_aiService.isSmartSuggestionsEnabled)
          Padding(
              padding: const EdgeInsets.only(top: 8),
              child:
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                Icon(Icons.lightbulb_outline,
                    size: 14, color: AppTheme.primaryColor),
                const SizedBox(width: 4),
                Text('输入时按Tab键接受智能预测',
                    style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.primaryColor,
                        fontStyle: FontStyle.italic)),
              ])),
      ]),
    );
  }

  Widget _buildMarkdownButton(
      {required Widget child,
      required VoidCallback onPressed,
      required String tooltip}) {
    return Material(
        color: Colors.transparent,
        child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(4),
            child: Tooltip(
                message: tooltip,
                child: Container(
                    padding: const EdgeInsets.all(8), child: child))));
  }

  void _insertMarkdownSyntax(String prefix, [String suffix = '']) {
    final contentType = widget.note?.contentType ?? _selectedContentType;
    if (contentType == 'markdown') {
      if (_markdownEditorKey.currentState != null) {
        final textController =
            _markdownEditorKey.currentState!.getTextController();
        final selection = textController.selection;
        if (selection.isValid) {
          final text = textController.text;
          final selectedText = selection.textInside(text);
          if (selectedText.isNotEmpty) {
            final newText = prefix + selectedText + suffix;
            final newSelection = TextSelection.collapsed(
                offset: selection.baseOffset + newText.length);
            textController.value = TextEditingValue(
                text:
                    text.replaceRange(selection.start, selection.end, newText),
                selection: newSelection);
          } else {
            final newText = prefix + suffix;
            final newSelection = TextSelection.collapsed(
                offset: selection.baseOffset + prefix.length);
            textController.value = TextEditingValue(
                text: text.replaceRange(
                    selection.start, selection.start, newText),
                selection: newSelection);
          }
          _updateDocumentFromMarkdown();
          setState(() {});
        }
      }
    } else {
      if (_richTextEditorKey.currentState != null) {
        final editorState = _richTextEditorKey.currentState!;
        final selection = editorState.selection;
        if (selection != null) {
          if (selection.baseOffset != selection.extentOffset) {
            final currentText = editorState.currentDocument.toPlainText();
            final selectedText = currentText.substring(
                selection.baseOffset, selection.extentOffset);
            final newText = prefix + selectedText + suffix;
            editorState.replaceText(selection.baseOffset,
                selection.extentOffset - selection.baseOffset, newText,
                selection: TextSelection.collapsed(
                    offset: selection.baseOffset + newText.length));
          } else {
            editorState.replaceText(selection.baseOffset, 0, prefix + suffix,
                selection: TextSelection.collapsed(
                    offset: selection.baseOffset + prefix.length));
          }
        }
      }
    }
  }

  // 智能列表插入方法
  void _insertList(bool isOrdered) {
    final contentType = widget.note?.contentType ?? _selectedContentType;
    if (contentType == 'markdown') {
      if (_markdownEditorKey.currentState != null) {
        final textController =
            _markdownEditorKey.currentState!.getTextController();
        final selection = textController.selection;
        if (selection.isValid) {
          final text = textController.text;
          final selectedText = selection.textInside(text);

          if (selectedText.isNotEmpty) {
            // 处理多行选择
            _insertListForMultipleLines(textController, selection, isOrdered);
          } else {
            // 处理单行插入
            _insertListForSingleLine(textController, selection, isOrdered);
          }
          _updateDocumentFromMarkdown();
          setState(() {});
        }
      }
    } else {
      // 富文本编辑器的列表处理保持原样
      final prefix = isOrdered ? '1. ' : '- ';
      if (_richTextEditorKey.currentState != null) {
        final editorState = _richTextEditorKey.currentState!;
        final selection = editorState.selection;
        if (selection != null) {
          if (selection.baseOffset != selection.extentOffset) {
            final currentText = editorState.currentDocument.toPlainText();
            final selectedText = currentText.substring(
                selection.baseOffset, selection.extentOffset);
            final newText = prefix + selectedText;
            editorState.replaceText(selection.baseOffset,
                selection.extentOffset - selection.baseOffset, newText,
                selection: TextSelection.collapsed(
                    offset: selection.baseOffset + newText.length));
          } else {
            editorState.replaceText(selection.baseOffset, 0, prefix,
                selection: TextSelection.collapsed(
                    offset: selection.baseOffset + prefix.length));
          }
        }
      }
    }
  }

  // 处理多行列表插入
  void _insertListForMultipleLines(TextEditingController textController,
      TextSelection selection, bool isOrdered) {
    final text = textController.text;
    final selectedText = selection.textInside(text);
    final lines = selectedText.split('\n');

    final processedLines = <String>[];
    // 为每个缩进级别维护独立的计数器
    Map<int, int> levelCounters = {};

    for (String line in lines) {
      if (line.trim().isEmpty) {
        processedLines.add(line); // 保留空行
        continue;
      }

      // 检测当前行的缩进级别
      final indentLevel = _getIndentLevel(line);
      final trimmedLine = line.trim();

      // 移除已有的列表标记（如果存在）
      final cleanLine = _removeExistingListMarker(trimmedLine);

      // 根据缩进级别和列表类型生成新的标记
      String marker;
      if (isOrdered) {
        // 为当前级别初始化或递增计数器
        if (!levelCounters.containsKey(indentLevel)) {
          levelCounters[indentLevel] = 1;
          // 清除更深级别的计数器
          levelCounters.removeWhere((level, _) => level > indentLevel);
        } else {
          levelCounters[indentLevel] = levelCounters[indentLevel]! + 1;
        }
        marker =
            _getOrderedListMarker(levelCounters[indentLevel]!, indentLevel);
      } else {
        marker = _getUnorderedListMarker(indentLevel);
      }

      // 重建行：缩进 + 标记 + 内容
      final indent = '  ' * indentLevel; // 每级缩进2个空格
      processedLines.add('$indent$marker $cleanLine');
    }

    final newText = processedLines.join('\n');
    final newSelection =
        TextSelection.collapsed(offset: selection.baseOffset + newText.length);

    textController.value = TextEditingValue(
        text: text.replaceRange(selection.start, selection.end, newText),
        selection: newSelection);
  }

  // 处理单行列表插入
  void _insertListForSingleLine(TextEditingController textController,
      TextSelection selection, bool isOrdered) {
    final text = textController.text;
    final cursorPosition = selection.baseOffset;

    // 找到当前行的开始和结束位置
    final lineStart = text.lastIndexOf('\n', cursorPosition - 1) + 1;
    final lineEnd = text.indexOf('\n', cursorPosition);
    final actualLineEnd = lineEnd == -1 ? text.length : lineEnd;

    final currentLine = text.substring(lineStart, actualLineEnd);
    final indentLevel = _getIndentLevel(currentLine);

    // 检查是否已经是列表项
    final trimmedLine = currentLine.trim();
    if (_isListItem(trimmedLine)) {
      // 如果已经是列表项，切换类型或增加缩进
      _toggleOrIndentListItem(
          textController, lineStart, actualLineEnd, currentLine, isOrdered);
    } else {
      // 如果不是列表项，转换为列表项
      _convertToListItem(textController, lineStart, actualLineEnd, currentLine,
          isOrdered, indentLevel);
    }
  }

  // 获取行的缩进级别
  int _getIndentLevel(String line) {
    int level = 0;
    for (int i = 0; i < line.length; i += 2) {
      if (i + 1 < line.length && line.substring(i, i + 2) == '  ') {
        level++;
      } else {
        break;
      }
    }
    return level;
  }

  // 移除已有的列表标记
  String _removeExistingListMarker(String line) {
    // 移除无序列表标记
    if (RegExp(r'^[-*+]\s+').hasMatch(line)) {
      return line.replaceFirst(RegExp(r'^[-*+]\s+'), '');
    }
    // 移除有序列表标记
    if (RegExp(r'^\d+\.\s+').hasMatch(line)) {
      return line.replaceFirst(RegExp(r'^\d+\.\s+'), '');
    }
    // 移除罗马数字标记
    if (RegExp(r'^[ivxlcdm]+\.\s+', caseSensitive: false).hasMatch(line)) {
      return line.replaceFirst(
          RegExp(r'^[ivxlcdm]+\.\s+', caseSensitive: false), '');
    }
    // 移除字母标记
    if (RegExp(r'^[a-z]\.\s+').hasMatch(line)) {
      return line.replaceFirst(RegExp(r'^[a-z]\.\s+'), '');
    }
    return line;
  }

  // 获取有序列表标记（根据级别）
  String _getOrderedListMarker(int number, int level) {
    switch (level % 3) {
      case 0: // 第一级：数字
        return '$number.';
      case 1: // 第二级：小写罗马数字
        return '${_toRomanNumber(number).toLowerCase()}.';
      case 2: // 第三级：小写字母
        return '${String.fromCharCode(96 + number)}.';
      default:
        return '$number.';
    }
  }

  // 获取无序列表标记（根据级别）
  String _getUnorderedListMarker(int level) {
    switch (level % 3) {
      case 0: // 第一级：圆点
        return '•';
      case 1: // 第二级：中空圆点
        return '○';
      case 2: // 第三级：方块
        return '■';
      default:
        return '•';
    }
  }

  // 转换为罗马数字
  String _toRomanNumber(int number) {
    if (number <= 0) return '';
    final values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
    final symbols = [
      'M',
      'CM',
      'D',
      'CD',
      'C',
      'XC',
      'L',
      'XL',
      'X',
      'IX',
      'V',
      'IV',
      'I'
    ];

    String result = '';
    for (int i = 0; i < values.length; i++) {
      while (number >= values[i]) {
        result += symbols[i];
        number -= values[i];
      }
    }
    return result;
  }

  // 检查是否为列表项
  bool _isListItem(String line) {
    return RegExp(r'^[-*+•○■]\s+').hasMatch(line) ||
        RegExp(r'^\d+\.\s+').hasMatch(line) ||
        RegExp(r'^[ivxlcdm]+\.\s+', caseSensitive: false).hasMatch(line) ||
        RegExp(r'^[a-z]\.\s+').hasMatch(line);
  }

  // 切换或缩进列表项
  void _toggleOrIndentListItem(TextEditingController textController,
      int lineStart, int lineEnd, String currentLine, bool isOrdered) {
    // 简单实现：如果已经是列表项，增加一级缩进
    final newLine = '  $currentLine';
    final newSelection =
        TextSelection.collapsed(offset: lineStart + newLine.length);

    textController.value = TextEditingValue(
        text: textController.text.replaceRange(lineStart, lineEnd, newLine),
        selection: newSelection);
  }

  // 转换为列表项
  void _convertToListItem(TextEditingController textController, int lineStart,
      int lineEnd, String currentLine, bool isOrdered, int indentLevel) {
    final trimmedLine = currentLine.trim();
    if (trimmedLine.isEmpty) return;

    String marker;
    if (isOrdered) {
      marker = _getOrderedListMarker(1, indentLevel);
    } else {
      marker = _getUnorderedListMarker(indentLevel);
    }

    final indent = '  ' * indentLevel;
    final newLine = '$indent$marker $trimmedLine';
    final newSelection =
        TextSelection.collapsed(offset: lineStart + newLine.length);

    textController.value = TextEditingValue(
        text: textController.text.replaceRange(lineStart, lineEnd, newLine),
        selection: newSelection);
  }

  void _insertCodeBlock() {
    final contentType = widget.note?.contentType ?? _selectedContentType;
    showDialog(
      context: context,
      builder: (context) {
        final languages = [
          'python',
          'javascript',
          'java',
          'c',
          'cpp',
          'csharp',
          'go',
          'rust',
          'swift',
          'kotlin',
          'php',
          'ruby',
          'sql',
          'html',
          'css',
          'xml',
          'json',
          'yaml',
          'bash',
          'plaintext'
        ];
        String selectedLanguage = 'python';
        return AlertDialog(
          title: const Text('选择代码语言'),
          content: Container(
              width: double.maxFinite,
              child: SingleChildScrollView(
                  child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: languages
                          .map((lang) => RadioListTile<String>(
                              title: Text(lang),
                              value: lang,
                              groupValue: selectedLanguage,
                              onChanged: (value) {
                                selectedLanguage = value!;
                                Navigator.pop(context, selectedLanguage);
                              }))
                          .toList()))),
          actions: [
            TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消')),
            TextButton(
                onPressed: () => Navigator.pop(context, selectedLanguage),
                child: const Text('确定'))
          ],
        );
      },
    ).then((language) {
      if (language == null) return;
      String codeTemplate;
      switch (language) {
        case 'python':
          codeTemplate =
              "def hello():\n    print(\"Hello, World!\")\n\nhello()";
          break;
        case 'javascript':
          codeTemplate =
              "function hello() {\n    console.log(\"Hello, World!\");\n}\n\nhello();";
          break;
        // ... other cases
        default:
          codeTemplate = "// 在此处输入代码";
          break;
      }
      final codeBlock = "```$language\n$codeTemplate\n```";
      if (contentType == 'markdown') {
        if (_markdownEditorKey.currentState != null) {
          final textController =
              _markdownEditorKey.currentState!.getTextController();
          final selection = textController.selection;
          if (selection.isValid) {
            final text = textController.text;
            String newText = (selection.baseOffset > 0 &&
                    text[selection.baseOffset - 1] != '\n')
                ? "\n$codeBlock\n"
                : "$codeBlock\n";
            textController.value = TextEditingValue(
                text: text.replaceRange(
                    selection.start, selection.start, newText),
                selection: TextSelection.collapsed(
                    offset: selection.baseOffset + newText.length));
            _updateDocumentFromMarkdown();
            setState(() {});
          }
        }
      } else {
        if (_richTextEditorKey.currentState != null) {
          final editorState = _richTextEditorKey.currentState!;
          final selection = editorState.selection;
          if (selection != null) {
            editorState.replaceText(selection.baseOffset, 0, "$codeBlock\n",
                selection: TextSelection.collapsed(
                    offset: selection.baseOffset + codeBlock.length + 1));
          }
        }
      }
    });
  }

  Widget _buildReadingMode({Key? key}) {
    final aiProvider = Provider.of<AIProvider>(context,
        listen: false); // Needed for RichTextEditor prop
    return SafeArea(
        key: key,
        child: Column(children: [
          _buildReadingHeader(),
          Expanded(
              child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AnimatedOpacity(
                                opacity: 1.0,
                                duration: const Duration(milliseconds: 500),
                                child: Text(
                                    _titleController.text.isEmpty
                                        ? '无标题'
                                        : _titleController.text,
                                    style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(context)
                                            .textTheme
                                            .titleLarge
                                            ?.color))),
                            const SizedBox(height: 20),
                            Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8),
                                child: Row(children: [
                                  Icon(Icons.access_time,
                                      size: 16,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color),
                                  const SizedBox(width: 8),
                                  Text(
                                      '最后编辑: ${DateTimeHelper.formatDateTime(widget.note?.updatedAt ?? DateTime.now())}',
                                      style: TextStyle(
                                          fontSize: 14,
                                          color: Theme.of(context)
                                              .textTheme
                                              .bodySmall
                                              ?.color))
                                ])),
                            if (_tags.isNotEmpty)
                              Container(
                                  margin: const EdgeInsets.only(
                                      top: 12, bottom: 20),
                                  child: Wrap(
                                      spacing: 8,
                                      runSpacing: 8,
                                      children: _tags
                                          .map((tagId) =>
                                              _buildReadingTag(tagId))
                                          .toList())),
                            const SizedBox(height: 12),
                            AnimatedOpacity(
                                opacity: 1.0,
                                duration: const Duration(milliseconds: 700),
                                child: _getContentTypeForReading() == 'markdown'
                                    ? _buildMarkdownContent()
                                    : RichTextEditor(
                                        key: ValueKey(
                                            'reading_rte_${widget.note?.id ?? "new"}'),
                                        initialDocument: _document,
                                        readOnly: true,
                                        noteId: widget.note?.id,
                                        showAiSuggestionsHint: aiProvider
                                                .aiAssistantEnabled &&
                                            aiProvider.suggestionFrequency !=
                                                '手动触发',
                                      )),
                          ])))),
          _buildReadingFooter(),
        ]));
  }

  String _getContentTypeForReading() {
    return widget.note?.contentType ?? _selectedContentType;
  }

  Widget _buildMarkdownContent() {
    String markdownContent = _document.toPlainText();
    String processedContent = processMarkdownForRendering(markdownContent);
    return Container(
        width: double.infinity,
        child: MarkdownBody(
          data: processedContent,
          selectable: true,
          softLineBreak: true,
          builders: {
            'table': CustomTableBuilder(),
            'code': CodeBlockBuilder(),
            'blockquote': BlockquoteBuilder()
          },
          extensionSet: md.ExtensionSet.gitHubWeb,
          onTapLink: (text, href, title) {
            if (href != null) launchUrl(Uri.parse(href));
          },
          imageBuilder: (Uri uri, String? title, String? alt) {
            // 定义一个固定的占位符尺寸，以减少布局跳动
            const double placeholderWidth = 200.0;
            const double placeholderHeight = 150.0;

            return Image.network(
              uri.toString(),
              fit: BoxFit.contain, // 图片适应方式
              loadingBuilder: (BuildContext context, Widget child,
                  ImageChunkEvent? loadingProgress) {
                if (loadingProgress == null) {
                  return child; // 图片加载完成
                }
                return Container(
                  width: placeholderWidth,
                  height: placeholderHeight,
                  color: Theme.of(context)
                      .colorScheme
                      .surfaceContainerHighest, // 使用主题颜色作为背景
                  child: Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                );
              },
              errorBuilder:
                  (BuildContext context, Object error, StackTrace? stackTrace) {
                print('Markdown Image Load Error: $error'); // 打印错误信息
                return Container(
                  width: placeholderWidth,
                  height: placeholderHeight,
                  color: Theme.of(context)
                      .colorScheme
                      .errorContainer, // 使用主题错误颜色作为背景
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image,
                        color: Theme.of(context).colorScheme.onErrorContainer,
                        size: 40,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        alt ?? '图片加载失败', // 如果有alt文本则显示，否则显示通用错误
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onErrorContainer,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
          styleSheet: MarkdownStyleSheet(
            h1: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            h2: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            h3: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            h4: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            h5: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            h6: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
            p: const TextStyle(
              fontSize: 16,
              height: 1.5,
            ),
            code: const TextStyle(
              backgroundColor: Color(0xFFf7f7f7),
              fontFamily: 'monospace',
              fontSize: 14,
            ),
            codeblockDecoration: BoxDecoration(
              color: const Color(0xFFf7f7f7),
              borderRadius: BorderRadius.circular(4),
            ),
            blockquote: const TextStyle(
              color: Colors.grey,
              fontStyle: FontStyle.italic,
            ),
            blockquoteDecoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
              border: Border(
                left: BorderSide(
                  color: AppTheme.primaryColor,
                  width: 4,
                ),
              ),
            ),
            tableHead: const TextStyle(fontWeight: FontWeight.bold),
            tableBody: const TextStyle(),
            tableBorder: TableBorder.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
          ),
        ));
  }

  Widget _buildReadingHeader() {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border: Border(
                bottom: BorderSide(
                    color: Theme.of(context).dividerColor, width: 1))),
        child: Row(children: [
          InkWell(
              onTap: () => Navigator.pop(context),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                  width: 40,
                  height: 40,
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(8)),
                  child: Icon(Icons.arrow_back,
                      color: Theme.of(context).iconTheme.color, size: 20))),
          Expanded(
              child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(_titleController.text,
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).textTheme.titleLarge?.color),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis))),
          Row(children: [
            InkWell(
                onTap: _toggleMode,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                    width: 40,
                    height: 40,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(8)),
                    child: Icon(Icons.edit,
                        color: Theme.of(context).iconTheme.color, size: 20))),
            const SizedBox(width: 16),
            InkWell(
                onTap: _toggleFavorite,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                    width: 40,
                    height: 40,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(8)),
                    child: Icon(_isFavorite ? Icons.star : Icons.star_border,
                        color: _isFavorite
                            ? Colors.orange
                            : AppTheme.darkGrayColor,
                        size: 20))),
            const SizedBox(width: 16),
            InkWell(
                onTap: _shareNote,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                    width: 40,
                    height: 40,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(8)),
                    child: Icon(Icons.share,
                        color: AppTheme.darkGrayColor, size: 20))),
            const SizedBox(width: 16),
            InkWell(
                onTap: _showMoreOptions,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                    width: 40,
                    height: 40,
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(8)),
                    child: Icon(Icons.more_vert,
                        color: AppTheme.darkGrayColor, size: 20))),
          ]),
        ]));
  }

  Widget _buildReadingFooter() {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            border:
                Border(top: BorderSide(color: Theme.of(context).dividerColor)),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 1,
                  offset: const Offset(0, -1))
            ]),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Row(children: [
            IconButton(
                onPressed: _toggleFavorite,
                icon: Icon(_isFavorite ? Icons.star : Icons.star_border,
                    color:
                        _isFavorite ? Colors.orange : AppTheme.darkGrayColor),
                tooltip: _isFavorite ? '取消收藏' : '收藏'),
            IconButton(
                onPressed: _shareNote,
                icon: Icon(Icons.share, color: AppTheme.darkGrayColor),
                tooltip: '分享')
          ]),
          ElevatedButton.icon(
              onPressed: () => setState(() => _isEditing = true),
              icon: const Icon(Icons.edit),
              label: const Text('编辑'),
              style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)))),
        ]));
  }

  Widget _buildShareOption(IconData icon, String label, VoidCallback onTap) {
    return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(children: [
              Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                      color: AppTheme.lightGrayColor,
                      borderRadius: BorderRadius.circular(12)),
                  child: Icon(icon, color: AppTheme.primaryColor)),
              const SizedBox(height: 8),
              Text(label)
            ])));
  }

  Widget _buildReadingTag(String tagId) {
    final tag = _getTagById(tagId);
    if (tag == null) return Container();
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
            color: tag.colorValue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16)),
        child: Row(mainAxisSize: MainAxisSize.min, children: [
          Icon(Icons.label, size: 16, color: tag.colorValue),
          const SizedBox(width: 4),
          Text(tag.name, style: TextStyle(color: tag.colorValue, fontSize: 14))
        ]));
  }

  void _shareNote() {
    if (widget.note == null) {
      _saveNote();
      return;
    }
    Navigator.pushNamed(context, AppRoutes.shareNote, arguments: widget.note);
  }

  void _showMoreOptions() {
    showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        builder: (context) {
          return Padding(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: const Text('删除笔记'),
                    onTap: () {
                      Navigator.pop(context);
                      _showDeleteConfirmation();
                    }),
                ListTile(
                    leading: Icon(Icons.copy, color: AppTheme.darkGrayColor),
                    title: const Text('复制内容'),
                    onTap: () {
                      Navigator.pop(context);
                      _copyContent();
                    }),
                ListTile(
                    leading: Icon(Icons.star, color: AppTheme.darkGrayColor),
                    title: Text(_isFavorite ? '取消收藏' : '收藏笔记'),
                    onTap: () {
                      Navigator.pop(context);
                      _toggleFavorite();
                    }),
              ]));
        });
  }

  void _showDeleteConfirmation() {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
              title: const Text('删除笔记'),
              content: const Text('确定要删除这个笔记吗？此操作不可撤销。'),
              actions: [
                TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('取消')),
                TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _deleteNote();
                    },
                    child:
                        const Text('删除', style: TextStyle(color: Colors.red))),
              ]);
        });
  }

  void _toggleMode() {
    if (_selectedContentType == 'markdown') {
      _updateDocumentFromMarkdown();
    }
    setState(() {
      _isEditing = !_isEditing;
      if (!_isEditing) {
        _showAIMenu = false;
        _showAIPrediction = false;
        _showAIHint = false;
      }
    });
  }

  Widget _getEditorWidget() {
    final contentType = widget.note?.contentType ?? _selectedContentType;
    final aiProvider = Provider.of<AIProvider>(context, listen: false);

    if (contentType == 'markdown') {
      return MarkdownEditor(
        key: _markdownEditorKey,
        initialContent: _document.toPlainText(),
        focusNode: _contentFocusNode,
        readOnly: false,
        onChanged: (content) {
          _updateDocumentFromMarkdown();
          _handleAIPredictionTrigger();
        },
      );
    } else {
      return Column(
        children: [
          // 粘贴加载指示器
          if (_isPasting)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                border: Border(
                  bottom: BorderSide(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '正在解析富文本数据...',
                    style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          // 富文本编辑器
          Expanded(
            child: RichTextEditor(
              key: _richTextEditorKey,
              initialDocument: _document,
              focusNode: _contentFocusNode,
              readOnly: false,
              noteId: widget.note?.id,
              onDocumentChanged: _onRichTextDocumentChanged,
              onSelectionChanged: _onRichTextSelectionChanged,
              showAiSuggestionsHint: aiProvider.aiAssistantEnabled &&
                  aiProvider.suggestionFrequency != '手动触发',
              onImageUpload: _handleImageUpload,
              onPaste: _pasteRichTextContent,
              isPasting: _isPasting, // 传递粘贴状态
            ),
          ),
        ],
      );
    }
  }

  Future<String?> _handleImageUpload(dynamic imageFile, String? noteId) async {
    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    try {
      final result =
          await noteProvider.uploadNoteImage(imageFile, noteId: noteId);
      if (result['success'] == true && result['data'] != null) {
        return result['data']['url'];
      } else {
        if (mounted) {
          SnackbarHelper.showError(
              context: context,
              message: result['error']?['message'] ?? '图片上传失败');
        }
        return null;
      }
    } catch (e) {
      if (mounted) {
        SnackbarHelper.showError(context: context, message: '图片上传异常: $e');
      }
      return null;
    }
  }

  void _updateDocumentFromMarkdown() {
    if (_markdownEditorKey.currentState != null) {
      final content = _markdownEditorKey.currentState!.getContent();
      String finalContent = content.endsWith('\n') ? content : '$content\n';
      if (finalContent.trim().isEmpty) finalContent = '\n';
      final delta = Delta()..insert(finalContent);
      _document = ParchmentDocument.fromDelta(delta);
      if (_richTextEditorKey.currentState != null &&
          _selectedContentType != 'markdown') {
        _richTextEditorKey.currentState!.updateDocument(_document);
      }
    }
  }

  Future<ParchmentDocument> _prepareRichTextDocumentForSave(
      ParchmentDocument document, String? noteId) async {
    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    final delta = document.toDelta();
    final updatedDelta = Delta();
    bool imagesProcessed = false;

    for (var op in delta.toList()) {
      if (op.isInsert && op.data is Map) {
        final Map opData = op.data as Map;
        // Check for direct image embed (older format or simple image insert)
        if (opData['source_type'] != null &&
            opData['source'] != null &&
            (opData['isLocal'] == true || opData['uploadPending'] == true)) {
          final sourceType = opData['source_type'] as String?;
          final source = opData['source'] as String?;
          imagesProcessed = true;
          dynamic fileToUpload;
          if ((sourceType == 'local_web' || sourceType == null) &&
              source!.startsWith('data:')) {
            final parts = source.split(',');
            if (parts.length >= 2) fileToUpload = base64Decode(parts[1]);
          } else if (sourceType == 'local_file') {
            if (!kIsWeb)
              fileToUpload = source;
            else {
              updatedDelta.push(op);
              continue;
            }
          }
          if (fileToUpload != null) {
            final uploadResult = await noteProvider
                .uploadNoteImage(fileToUpload, noteId: noteId);
            if (uploadResult['success'] == true &&
                uploadResult['data']?['url'] != null) {
              final newOpData = Map<String, dynamic>.from(opData);
              newOpData['source_type'] = 'url';
              newOpData['source'] = uploadResult['data']['url'];
              newOpData.remove('isLocal');
              newOpData.remove('uploadPending');
              updatedDelta.insert(
                  newOpData['embed'] ?? newOpData,
                  newOpData[
                      'attributes']); // Handle both direct and embed structures
              continue;
            }
          }
        }
        // Check for standard embed structure
        else if (opData.containsKey('embed') && opData['embed'] is Map) {
          final embed = opData['embed'] as Map;
          if (embed['type'] == 'image' && embed['data'] is Map) {
            final imageData = embed['data'] as Map;
            final sourceType = imageData['source_type'] as String?;
            final source = imageData['source'] as String?;
            final bool isLocal = imageData['isLocal'] == true ||
                imageData['uploadPending'] == true;

            if (isLocal && source != null) {
              imagesProcessed = true;
              dynamic fileToUpload;
              if ((sourceType == 'local_web' || sourceType == null) &&
                  source.startsWith('data:')) {
                final parts = source.split(',');
                if (parts.length >= 2) fileToUpload = base64Decode(parts[1]);
              } else if (sourceType == 'local_file') {
                if (!kIsWeb)
                  fileToUpload = source;
                else {
                  updatedDelta.push(op);
                  continue;
                }
              }

              if (fileToUpload != null) {
                final uploadResult = await noteProvider
                    .uploadNoteImage(fileToUpload, noteId: noteId);
                if (uploadResult['success'] == true &&
                    uploadResult['data']?['url'] != null) {
                  final newImageData = Map<String, dynamic>.from(imageData);
                  newImageData['source_type'] = 'url';
                  newImageData['source'] = uploadResult['data']['url'];
                  newImageData.remove('isLocal');
                  newImageData.remove('uploadPending');
                  updatedDelta.insert({
                    'embed': {'type': 'image', 'data': newImageData}
                  }, op.attributes);
                  continue;
                }
              }
            }
          }
        }
      }
      updatedDelta.push(op);
    }
    if (imagesProcessed && mounted) {
      SnackbarHelper.showInfo(context: context, message: "正在处理图片上传...");
    }
    return ParchmentDocument.fromDelta(updatedDelta);
  }

  void _saveNote() async {
    final currentEditorType = widget.note?.contentType ?? _selectedContentType;
    String finalContentJson;
    ParchmentDocument documentToSave = _document;

    if (currentEditorType == 'markdown') {
      _updateDocumentFromMarkdown(); // 确保 _document 是最新的并且符合 Parchment 要求
      documentToSave = _document;
      // 修复：对于 Markdown 类型，应该保存其纯文本内容
      finalContentJson = documentToSave.toPlainText();
      // toPlainText() 应该已经包含了末尾的换行符，因为 _document 是那样构建的。
      // 如果纯文本内容为空（例如，用户只输入了空格然后全部删除），确保至少保存一个换行符。
      if (finalContentJson.trim().isEmpty) {
        finalContentJson = '\n';
      }
      print(
          '[DEBUG_ROO] _saveNote (markdown): finalContentJson (plain text) to be saved = "$finalContentJson"'); // 更新日志 B
    } else {
      if (_richTextEditorKey.currentState != null) {
        documentToSave = _richTextEditorKey.currentState!.currentDocument;
      }
      // 注意：对于新笔记，暂时不处理图片，等创建笔记后再处理
      if (widget.note != null) {
        // 现有笔记：使用真实笔记ID处理图片
        documentToSave = await _prepareRichTextDocumentForSave(
            documentToSave, widget.note!.id);
      }
      finalContentJson = jsonEncode(documentToSave.toJson());
    }

    if (documentToSave.toPlainText().trim().isEmpty) {
      if (mounted) {
        SnackbarHelper.showError(context: context, message: '请输入内容');
      }
      return;
    }

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    if (mounted) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) =>
              const Center(child: CircularProgressIndicator()));
    }

    try {
      Note? savedNote;

      if (widget.note != null) {
        // 更新现有笔记的情况
        if (widget.initialTagId != null &&
            widget.initialTagId!.isNotEmpty &&
            !_tags.contains(widget.initialTagId)) {
          _tags.add(widget.initialTagId!);
        }

        savedNote = await noteProvider.updateNote(
          id: widget.note!.id,
          title: _titleController.text,
          content: finalContentJson,
          contentType: currentEditorType,
          tags: _tags,
        );
      } else {
        // 创建新笔记的情况 - 恢复正确的三步流程
        if (_selectedContentType == 'markdown') {
          // Markdown格式直接创建笔记
          savedNote = await noteProvider.createNote(
            title: _titleController.text,
            content: finalContentJson,
            contentType: _selectedContentType,
            tags: _tags,
          );
        } else {
          // 富文本格式需要三步流程：
          // 1. 先创建一个基本的笔记以获取ID
          final initialContent = documentToSave.toPlainText(); // 先用纯文本内容
          savedNote = await noteProvider.createNote(
            title: _titleController.text,
            content: initialContent,
            contentType: _selectedContentType,
            tags: _tags,
          );

          if (savedNote != null) {
            // 2. 处理内容中的本地图片，使用新创建的笔记ID
            final updatedDocument = await _prepareRichTextDocumentForSave(
                documentToSave, savedNote.id);
            final processedContent = jsonEncode(updatedDocument.toJson());

            // 3. 更新笔记内容，包含处理后的图片，使用 isCompletingInitialSave: true
            savedNote = await noteProvider.updateNote(
              id: savedNote.id,
              title: _titleController.text,
              content: processedContent,
              contentType: _selectedContentType,
              tags: _tags,
              isCompletingInitialSave: true, // 关键修复：恢复此参数
            );
          }
        }
      }

      if (!mounted) return;
      Navigator.pop(context);

      if (savedNote != null) {
        if (mounted) {
          SnackbarHelper.showSuccess(context: context, message: '笔记保存成功');
          Navigator.pop(context);
        }
      } else {
        if (mounted) {
          SnackbarHelper.showError(
              context: context, message: '保存失败: ${noteProvider.error}');
        }
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.pop(context);
      if (mounted) {
        SnackbarHelper.showError(context: context, message: '保存失败: $e');
      }
    }
  }

  Future<bool> _showFormatSelectionDialog() async {
    final result = await showDialog<bool>(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext dialogContext) {
          return StatefulBuilder(builder: (context, setDialogState) {
            return AlertDialog(
                title: const Text('选择笔记格式'),
                content: SingleChildScrollView(
                    child: ListBody(children: <Widget>[
                  const Text('请选择笔记的格式类型，这将决定笔记的存储和显示方式。'),
                  const SizedBox(height: 16),
                  _buildFormatOptionInDialog(
                      title: '富文本格式',
                      description: '支持图片、表格等复杂格式，适合复杂笔记',
                      icon: Icons.text_fields,
                      value: 'rich-text',
                      onSelected: () => setDialogState(
                          () => _selectedContentType = 'rich-text'),
                      isSelected: _selectedContentType == 'rich-text'),
                  const SizedBox(height: 8),
                  _buildFormatOptionInDialog(
                      title: 'Markdown格式',
                      description: '使用Markdown语法，适合纯文本笔记和代码片段',
                      icon: Icons.code,
                      value: 'markdown',
                      onSelected: () => setDialogState(
                          () => _selectedContentType = 'markdown'),
                      isSelected: _selectedContentType == 'markdown'),
                ])),
                actions: <Widget>[
                  TextButton(
                      child: const Text('取消'),
                      onPressed: () => Navigator.of(context).pop(false)),
                  TextButton(
                      child: const Text('确定'),
                      onPressed: () => Navigator.of(context).pop(true))
                ]);
          });
        });
    return result ?? false;
  }

  Widget _buildFormatOptionInDialog(
      {required String title,
      required String description,
      required IconData icon,
      required String value,
      required VoidCallback onSelected,
      required bool isSelected}) {
    return Material(
        color: Colors.transparent,
        child: InkWell(
            onTap: onSelected,
            borderRadius: BorderRadius.circular(8),
            child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                    border: Border.all(
                        color: isSelected
                            ? AppTheme.primaryColor
                            : Colors.grey.shade300,
                        width: isSelected ? 2 : 1),
                    borderRadius: BorderRadius.circular(8),
                    color: isSelected
                        ? AppTheme.primaryColor.withOpacity(0.1)
                        : Colors.transparent),
                child: Row(children: [
                  Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                          color: isSelected
                              ? AppTheme.primaryColor
                              : Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(8)),
                      child: Icon(icon,
                          color:
                              isSelected ? Colors.white : Colors.grey.shade700,
                          size: 24)),
                  const SizedBox(width: 12),
                  Expanded(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                        Text(title,
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: isSelected
                                    ? AppTheme.primaryColor
                                    : Colors.black)),
                        const SizedBox(height: 4),
                        Text(description,
                            style: TextStyle(
                                fontSize: 12, color: Colors.grey.shade700))
                      ])),
                  if (isSelected)
                    Icon(Icons.check_circle,
                        color: AppTheme.primaryColor, size: 24),
                ]))));
  }

  void _addTag() async {
    final tagProvider = Provider.of<TagProvider>(context, listen: false);
    await tagProvider.fetchTags();
    final tagsFromProvider = tagProvider.tags; // Renamed to avoid conflict
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
              title: const Text('添加标签'),
              content: Container(
                  width: double.maxFinite,
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    if (tagsFromProvider.isNotEmpty) ...[
                      const Text('选择已有标签:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Container(
                          height: 150,
                          child: ListView.builder(
                              shrinkWrap: true,
                              itemCount: tagsFromProvider.length,
                              itemBuilder: (context, index) {
                                final tag = tagsFromProvider[index];
                                final isSelected = _tags.contains(tag.id);
                                return ListTile(
                                    title: Text(tag.name),
                                    leading: Icon(Icons.label,
                                        color: Color(int.parse(
                                                tag.color.substring(1),
                                                radix: 16) +
                                            0xFF000000)),
                                    trailing:
                                        isSelected ? Icon(Icons.check) : null,
                                    onTap: () => Navigator.pop(context,
                                        {'action': 'select', 'tag': tag}));
                              })),
                      const Divider(),
                    ],
                    ListTile(
                        title: const Text('创建新标签'),
                        leading: Icon(Icons.add),
                        onTap: () =>
                            Navigator.pop(context, {'action': 'create'})),
                  ])),
              actions: [
                TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('取消'))
              ]);
        }).then((result) async {
      if (result == null) return;
      if (result['action'] == 'select') {
        final selectedTag = result['tag'];
        if (!_tags.contains(selectedTag.id))
          setState(() => _tags.add(selectedTag.id));
      } else if (result['action'] == 'create') {
        _showCreateTagDialog();
      }
    });
  }

  void _showCreateTagDialog() {
    final TextEditingController tagController = TextEditingController();
    String selectedColor = '#5D5FEF';
    final List<String> colorOptions = [
      '#F44336',
      '#E91E63',
      '#9C27B0',
      '#673AB7',
      '#3F51B5',
      '#2196F3',
      '#03A9F4',
      '#00BCD4',
      '#009688',
      '#4CAF50',
      '#8BC34A',
      '#CDDC39',
      '#FFEB3B',
      '#FFC107',
      '#FF9800',
      '#FF5722',
      '#795548',
      '#9E9E9E',
      '#607D8B'
    ];
    showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setStateDialog) {
            return AlertDialog(
                title: const Text('创建新标签'),
                content: SingleChildScrollView(
                    child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                      TextField(
                          controller: tagController,
                          decoration: const InputDecoration(hintText: '输入标签名称'),
                          autofocus: true),
                      const SizedBox(height: 16),
                      const Text('选择标签颜色:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: colorOptions.map((color) {
                            bool isSelected = color == selectedColor;
                            return GestureDetector(
                                onTap: () =>
                                    setStateDialog(() => selectedColor = color),
                                child: Container(
                                    width: 32,
                                    height: 32,
                                    decoration: BoxDecoration(
                                        color: Color(int.parse(color.substring(1),
                                                radix: 16) +
                                            0xFF000000),
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                            color: isSelected
                                                ? Colors.white
                                                : Colors.transparent,
                                            width: 2),
                                        boxShadow: isSelected
                                            ? [
                                                BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.3),
                                                    blurRadius: 4,
                                                    spreadRadius: 1)
                                              ]
                                            : []),
                                    child: isSelected
                                        ? const Icon(Icons.check,
                                            color: Colors.white, size: 18)
                                        : null));
                          }).toList()),
                    ])),
                actions: [
                  TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('取消')),
                  TextButton(
                      onPressed: () async {
                        final newTagName = tagController.text.trim();
                        if (newTagName.isNotEmpty) {
                          final tagProvider =
                              Provider.of<TagProvider>(context, listen: false);
                          showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (context) => const Center(
                                  child: CircularProgressIndicator()));
                          try {
                            final success = await tagProvider
                                .createTag(newTagName, color: selectedColor);
                            Navigator.pop(context);
                            if (success) {
                              await tagProvider.fetchTags();
                              final newTag = tagProvider.tags.firstWhere(
                                  (tag) => tag.name == newTagName,
                                  orElse: () =>
                                      throw Exception('Tag not found'));
                              this.setState(() => _tags.add(newTag.id));
                              Navigator.pop(context);
                              SnackbarHelper.showSuccess(
                                  context: context,
                                  message: '标签"$newTagName"创建成功并添加到笔记');
                            } else {
                              SnackbarHelper.showError(
                                  context: context,
                                  message:
                                      '创建标签失败: ${tagProvider.errorMessage}');
                            }
                          } catch (e) {
                            Navigator.pop(context);
                            SnackbarHelper.showError(
                                context: context, message: '创建标签失败: $e');
                          }
                        } else {
                          Navigator.pop(context);
                        }
                      },
                      child: const Text('创建')),
                ]);
          });
        });
  }

  void _createOrAddTag(String tagName, TagProvider tagProvider) async {
    final existingTag =
        tagProvider.tags.where((tag) => tag.name == tagName).toList();
    if (existingTag.isNotEmpty) {
      if (!_tags.contains(existingTag[0].id)) {
        setState(() {
          _tags.add(existingTag[0].id);
        });
      }
    } else {
      final List<String> colorOptions = [
        '#F44336',
        '#E91E63',
        '#9C27B0',
        '#673AB7',
        '#3F51B5',
        '#2196F3',
        '#03A9F4',
        '#00BCD4',
        '#009688',
        '#4CAF50',
        '#8BC34A',
        '#CDDC39',
        '#FFEB3B',
        '#FFC107',
        '#FF9800',
        '#FF5722',
        '#795548',
        '#9E9E9E',
        '#607D8B'
      ];
      final int colorIndex = tagName.hashCode.abs() % colorOptions.length;
      final String tagColor = colorOptions[colorIndex];
      final success = await tagProvider.createTag(tagName, color: tagColor);
      if (success) {
        await tagProvider.fetchTags();
        final newTag = tagProvider.tags.firstWhere((tag) => tag.name == tagName,
            orElse: () => Tag.empty());
        if (newTag.id.isNotEmpty && !_tags.contains(newTag.id)) {
          setState(() {
            _tags.add(newTag.id);
          });
        }
      }
    }
  }

  void _removeTagByName(String tagName, TagProvider tagProvider) {
    final tagToRemove =
        tagProvider.tags.where((tag) => tag.name == tagName).toList();
    if (tagToRemove.isNotEmpty) {
      if (_tags.contains(tagToRemove[0].id)) {
        setState(() {
          _tags.remove(tagToRemove[0].id);
        });
      }
    }
  }

  void _copyContent() async {
    final currentEditorType = widget.note?.contentType ?? _selectedContentType;

    if (currentEditorType == 'markdown') {
      // Markdown格式复制纯文本
      final content = _document.toPlainText();
      await Clipboard.setData(ClipboardData(text: content));
      if (mounted) {
        SnackbarHelper.showSuccess(context: context, message: '内容已复制到剪贴板');
      }
    } else {
      // 富文本格式复制JSON格式，保留格式信息
      try {
        final richTextDocument =
            _richTextEditorKey.currentState?.currentDocument ?? _document;
        final jsonContent = jsonEncode(richTextDocument.toJson());
        final plainContent = richTextDocument.toPlainText();

        // 同时复制富文本JSON和纯文本，优先使用富文本格式
        await Clipboard.setData(ClipboardData(text: jsonContent));

        // 存储到本地缓存，用于应用内粘贴
        _cachedRichTextContent = jsonContent;
        _cachedPlainTextContent = plainContent;

        if (mounted) {
          SnackbarHelper.showSuccess(context: context, message: '富文本内容已复制到剪贴板');
        }
      } catch (e) {
        // 如果富文本复制失败，回退到纯文本
        final content = _document.toPlainText();
        await Clipboard.setData(ClipboardData(text: content));
        if (mounted) {
          SnackbarHelper.showSuccess(context: context, message: '内容已复制到剪贴板');
        }
      }
    }
  }

  // 缓存的富文本内容，用于应用内粘贴
  static String? _cachedRichTextContent;
  static String? _cachedPlainTextContent;

  // 粘贴状态管理
  bool _isPasting = false;

  // 粘贴富文本内容
  Future<void> _pasteRichTextContent() async {
    // 防止重复调用
    if (_isPasting) {
      return;
    }

    setState(() {
      _isPasting = true;
    });

    try {
      // 首先尝试从剪贴板获取内容
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      String? clipboardText = clipboardData?.text;

      // 如果剪贴板有内容，优先使用剪贴板内容
      String? contentToPaste = clipboardText ?? _cachedRichTextContent;

      if (contentToPaste != null && contentToPaste.isNotEmpty) {
        // 尝试解析为JSON格式的富文本
        try {
          // 检查是否为JSON格式
          if (contentToPaste.trim().startsWith('[') ||
              contentToPaste.trim().startsWith('{')) {
            // 异步解析JSON数据，避免阻塞UI
            await Future.microtask(() async {
              final dynamic jsonData = jsonDecode(contentToPaste);

              // 尝试创建Parchment文档，如果失败会抛出异常
              final newDocument = ParchmentDocument.fromJson(jsonData);

              // 处理图片占位符 - 将图片URL替换为占位符
              final processedDocument =
                  await _processImagesForPasteAsync(newDocument);

              // 获取当前光标位置
              final selection = _richTextEditorKey.currentState?.selection ??
                  TextSelection.collapsed(offset: _document.length - 1);

              // 在光标位置插入富文本内容
              final currentDocument =
                  _richTextEditorKey.currentState?.currentDocument ?? _document;
              final currentDelta = currentDocument.toDelta();
              final insertDelta = processedDocument.toDelta();

              // 创建新的Delta，在光标位置插入内容
              final newDelta = Delta();

              // 添加光标前的内容
              if (selection.baseOffset > 0) {
                newDelta.retain(selection.baseOffset);
              }

              // 插入新内容
              for (final op in insertDelta.toList()) {
                newDelta.push(op);
              }

              // 应用到编辑器
              final finalDocument =
                  ParchmentDocument.fromDelta(currentDelta.compose(newDelta));
              _richTextEditorKey.currentState?.updateDocument(finalDocument);

              // 更新内部文档状态，确保切换到阅读模式时内容不丢失
              setState(() {
                _document = finalDocument;
                _isEditing = true; // 标记为已编辑状态
              });
            });

            if (mounted) {
              SnackbarHelper.showSuccess(context: context, message: '富文本内容已粘贴');
            }
            return;
          }
        } catch (e) {
          // JSON解析失败，继续作为纯文本处理
          debugPrint('JSON解析失败，作为纯文本处理: $e');
        }

        // 作为纯文本粘贴
        _pasteAsPlainText(contentToPaste);
      } else {
        // 如果有缓存的纯文本内容，使用它
        if (_cachedPlainTextContent != null) {
          _pasteAsPlainText(_cachedPlainTextContent!);
        } else {
          if (mounted) {
            SnackbarHelper.showInfo(context: context, message: '剪贴板为空');
          }
        }
      }
    } catch (e) {
      if (mounted) {
        SnackbarHelper.showError(context: context, message: '粘贴失败: $e');
      }
    } finally {
      // 确保在所有情况下都重置状态
      if (mounted) {
        setState(() {
          _isPasting = false;
        });
      }
    }
  }

  // 异步处理粘贴时的图片，将图片替换为占位符
  Future<ParchmentDocument> _processImagesForPasteAsync(
      ParchmentDocument document) async {
    final delta = document.toDelta();
    final newDelta = Delta();

    // 分批处理操作，避免长时间阻塞
    final operations = delta.toList();
    const batchSize = 50; // 每批处理50个操作

    for (int i = 0; i < operations.length; i += batchSize) {
      final endIndex = (i + batchSize < operations.length)
          ? i + batchSize
          : operations.length;
      final batch = operations.sublist(i, endIndex);

      // 处理当前批次
      for (final op in batch) {
        if (op.data is Map) {
          final dataMap = op.data as Map<String, dynamic>;
          if (dataMap['_type'] == 'image') {
            // 替换图片为占位符
            final imageData = Map<String, dynamic>.from(dataMap);
            imageData['source'] = 'placeholder://image';
            imageData['source_type'] = 'placeholder';
            imageData['isLocal'] = true;
            imageData['uploadPending'] = false;
            newDelta.push(Operation.insert(imageData, op.attributes));
          } else {
            newDelta.push(op);
          }
        } else {
          newDelta.push(op);
        }
      }

      // 每处理一批后让出控制权，允许UI更新
      if (i + batchSize < operations.length) {
        await Future.delayed(Duration.zero);
      }
    }

    return ParchmentDocument.fromDelta(newDelta);
  }

  // 粘贴纯文本内容
  void _pasteAsPlainText(String text) {
    final currentEditorType = widget.note?.contentType ?? _selectedContentType;

    if (currentEditorType == 'markdown') {
      // Markdown编辑器粘贴
      _markdownEditorKey.currentState?.insertTextAtCursor(text);
    } else {
      // 富文本编辑器粘贴纯文本
      final selection = _richTextEditorKey.currentState?.selection ??
          TextSelection.collapsed(offset: _document.length - 1);

      _richTextEditorKey.currentState?.replaceText(
        selection.baseOffset,
        selection.extentOffset - selection.baseOffset,
        text,
        selection:
            TextSelection.collapsed(offset: selection.baseOffset + text.length),
      );
    }

    if (mounted) {
      SnackbarHelper.showSuccess(context: context, message: '内容已粘贴');
    }
  }

  void _toggleFavorite() async {
    if (widget.note == null) {
      SnackbarHelper.showError(context: context, message: '请先保存笔记');
      return;
    }
    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    try {
      final success = await noteProvider.toggleFavorite(widget.note!.id);
      if (success) {
        setState(() => _isFavorite = !_isFavorite);
        SnackbarHelper.showSuccess(
            context: context,
            message: _isFavorite ? '已添加到收藏' : '已取消收藏',
            duration: const Duration(seconds: 1));
      } else {
        SnackbarHelper.showError(
            context: context, message: '操作失败: ${noteProvider.error}');
      }
    } catch (e) {
      SnackbarHelper.showError(context: context, message: '操作失败: $e');
    }
  }

  void _deleteNote() async {
    if (widget.note == null) {
      SnackbarHelper.showInfo(context: context, message: '笔记未保存，无需删除');
      Navigator.pop(context);
      return;
    }
    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()));
    try {
      final success = await noteProvider.deleteNote(widget.note!.id);
      Navigator.pop(context);
      if (success) {
        Navigator.pop(context);
        SnackbarHelper.showSuccess(context: context, message: '笔记已删除');
      } else {
        SnackbarHelper.showError(
            context: context, message: '删除失败: ${noteProvider.error}');
      }
    } catch (e) {
      Navigator.pop(context);
      SnackbarHelper.showError(context: context, message: '删除失败: $e');
    }
  }

  void _loadTagData() async {
    final tagProvider = Provider.of<TagProvider>(context, listen: false);
    try {
      await tagProvider.fetchTags();
      if (widget.initialTagId != null && widget.initialTagId!.isNotEmpty) {
        if (!_tags.contains(widget.initialTagId)) {
          setState(() => _tags.add(widget.initialTagId!));
        }
      }
      if (mounted) setState(() {});
    } catch (e) {
      print('DEBUG: 编辑页加载标签数据失败: $e');
    }
  }

  void _showHistoryBottomSheet(BuildContext context) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        builder: (bottomSheetContext) {
          return Consumer<NoteProvider>(
              builder: (context, noteProvider, child) {
            Widget content;
            if (noteProvider.isLoadingHistory) {
              content = const Center(
                  child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator()));
            } else if (noteProvider.historyError.isNotEmpty) {
              content = Center(
                  child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text('加载失败: ${noteProvider.historyError}')));
            } else if (noteProvider.noteHistory.isEmpty) {
              content = const Center(
                  child: Padding(
                      padding: EdgeInsets.all(32.0), child: Text('该笔记暂无历史版本')));
            } else {
              content = ListView.separated(
                shrinkWrap: true,
                itemCount: noteProvider.noteHistory.length,
                itemBuilder: (context, index) {
                  final historyItem = noteProvider.noteHistory[index];
                  final historyTime =
                      historyItem.archivedAt ?? historyItem.updatedAt;
                  final formattedTime = historyTime != null
                      ? DateTimeHelper.formatDateTime(historyTime)
                      : '未知时间';
                  return ListTile(
                    leading: CircleAvatar(
                        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                        foregroundColor: AppTheme.primaryColor,
                        child: Text('${historyItem.version}',
                            style:
                                const TextStyle(fontWeight: FontWeight.bold))),
                    title: Text('版本 ${historyItem.version}',
                        style: const TextStyle(fontWeight: FontWeight.w500)),
                    subtitle: Text('保存于: $formattedTime'),
                    trailing: Row(mainAxisSize: MainAxisSize.min, children: [
                      IconButton(
                          icon: Icon(Icons.visibility_outlined,
                              color: AppTheme.primaryColor),
                          tooltip: '预览此版本',
                          onPressed: () {
                            Navigator.pop(bottomSheetContext);
                            _navigateToPreviewPage(context, historyItem);
                          }),
                      IconButton(
                          icon: Icon(Icons.restore,
                              color: Colors.orange.shade700),
                          tooltip: '恢复到此版本',
                          onPressed: noteProvider.isRestoringVersion
                              ? null
                              : () {
                                  Navigator.pop(bottomSheetContext);
                                  _showRestoreConfirmation(
                                      context, historyItem);
                                }),
                    ]),
                  );
                },
                separatorBuilder: (context, index) =>
                    const Divider(height: 1, indent: 16, endIndent: 16),
              );
            }
            return Padding(
                padding: EdgeInsets.only(
                    bottom:
                        MediaQuery.of(bottomSheetContext).viewInsets.bottom +
                            16,
                    top: 16,
                    left: 16,
                    right: 16),
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('笔记历史版本',
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      const Divider(),
                      const SizedBox(height: 8),
                      LimitedBox(
                          maxHeight: MediaQuery.of(context).size.height * 0.6,
                          child: content),
                    ]));
          });
        });
  }

  void _navigateToPreviewPage(BuildContext context, Note historyNote) {
    if (widget.note == null) {
      SnackbarHelper.showError(context: context, message: '无法预览：找不到原始笔记');
      return;
    }
    Navigator.pushNamed(context, AppRoutes.historyPreview, arguments: {
      'historyNote': historyNote,
      'originalNoteId': widget.note!.id
    });
  }

  void _showRestoreConfirmation(BuildContext context, Note historyNote) {
    final formattedTime = DateTimeHelper.formatDateTime(
        historyNote.archivedAt ?? historyNote.updatedAt);
    showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
              title: const Text('确认恢复'),
              content: Text(
                  '确定要将笔记恢复到 版本 ${historyNote.version} (保存于 $formattedTime) 吗？\n\n此操作会覆盖当前编辑器的内容，但您需要手动保存才能使更改生效。'),
              actions: [
                TextButton(
                    onPressed: () => Navigator.pop(dialogContext),
                    child: const Text('取消')),
                Consumer<NoteProvider>(builder: (context, noteProvider, child) {
                  return TextButton(
                    onPressed: noteProvider.isRestoringVersion
                        ? null
                        : () async {
                            Navigator.pop(dialogContext);
                            final success = await Provider.of<NoteProvider>(
                                    context,
                                    listen: false)
                                .restoreVersion(
                                    widget.note!.id, historyNote.version);
                            if (success) {
                              SnackbarHelper.showSuccess(
                                  context: context,
                                  message:
                                      '已恢复到版本 ${historyNote.version}，请检查内容并保存');
                              _updateEditorWithHistory(historyNote);
                            }
                          },
                    style: TextButton.styleFrom(
                        foregroundColor: Colors.orange.shade700),
                    child: noteProvider.isRestoringVersion
                        ? const SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(strokeWidth: 2))
                        : const Text('恢复'),
                  );
                }),
              ],
            ));
  }

  void _updateEditorWithHistory(Note historyNote) {
    try {
      ParchmentDocument newDocument;
      if (historyNote.contentType == 'markdown') {
        String content = historyNote.content;
        if (!content.endsWith('\n')) content += '\n';
        newDocument = ParchmentDocument.fromJson([
          {"insert": content}
        ]);
      } else {
        if (historyNote.content.trim().startsWith('[') ||
            historyNote.content.trim().startsWith('{')) {
          dynamic deltaJson = jsonDecode(historyNote.content);
          newDocument = ParchmentDocument.fromJson(deltaJson);
        } else {
          String content = historyNote.content;
          if (!content.endsWith('\n')) content += '\n';
          newDocument = ParchmentDocument.fromJson([
            {"insert": content}
          ]);
        }
      }

      setState(() {
        _titleController.text = historyNote.title;
        _tags = List.from(historyNote.tagIds);
        _isEditing = true;
        _selectedContentType = historyNote.contentType;
        _document = newDocument;

        if (_selectedContentType == 'markdown' &&
            _markdownEditorKey.currentState != null) {
          _markdownEditorKey.currentState!.getTextController().text =
              _document.toPlainText();
        } else if (_selectedContentType != 'markdown' &&
            _richTextEditorKey.currentState != null) {
          _richTextEditorKey.currentState!.updateDocument(_document);
        }
      });
      SnackbarHelper.showSuccess(
          context: context, message: '已恢复到版本 ${historyNote.version}，请检查内容并保存');
    } catch (e) {
      SnackbarHelper.showError(context: context, message: '加载历史内容失败: $e');
    }
  }
}
