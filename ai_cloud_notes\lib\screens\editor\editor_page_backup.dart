import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb; // 导入kIsWeb常量
import 'package:flutter/services.dart'; // 导入Clipboard功能和键盘事件
import 'package:provider/provider.dart'; // 导入Provider
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/models/tag_model.dart'; // 导入Tag模型
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:uuid/uuid.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:fleather/fleather.dart';
import 'package:parchment/parchment.dart';
import 'package:parchment_delta/parchment_delta.dart';
import 'package:flutter_markdown/flutter_markdown.dart'; // 导入Markdown渲染包
import 'package:markdown/markdown.dart' as md; // 导入Markdown核心库
import 'package:ai_cloud_notes/providers/note_provider.dart'; // 导入笔记Provider
import 'package:ai_cloud_notes/providers/tag_provider.dart'; // 导入标签Provider
import 'package:ai_cloud_notes/providers/ai_provider.dart'; // 导入AI Provider
import 'package:ai_cloud_notes/providers/ai_function_provider.dart'; // 导入AI功能 Provider
import 'package:ai_cloud_notes/utils/snackbar_helper.dart'; // 导入SnackBar辅助工具
import 'package:ai_cloud_notes/utils/date_time_helper.dart'; // 导入时间处理工具类
import 'package:ai_cloud_notes/screens/editor/ai_menu.dart'; // 导入AI菜单
import 'package:ai_cloud_notes/screens/editor/markdown_editor.dart'; // 导入Markdown编辑器组件
import 'package:ai_cloud_notes/screens/editor/ai_prediction_bar.dart'; // 导入可拖动的AI预测栏
import 'package:shared_preferences/shared_preferences.dart'; // 导入本地存储

import 'package:image_picker/image_picker.dart'; // 导入图片选择器
import 'dart:typed_data'; // 导入Uint8List
import 'dart:convert'; // 导入JSON和Base64支持
import 'dart:io';
import 'dart:math'; // 导入math包，支持min函数
import 'dart:async'; // 导入Timer类
import 'package:http/http.dart' show UriData; // 导入UriData用于处理data URI
import 'package:url_launcher/url_launcher.dart'; // 导入URL启动器

class EditorPage extends StatefulWidget {
  final Note? note;
  final String? initialTagId; // 添加初始标签ID参数
  final String? initialTagName; // 添加初始标签名称参数

  const EditorPage({
    Key? key,
    this.note,
    this.initialTagId,
    this.initialTagName,
  }) : super(key: key);

  @override
  State<EditorPage> createState() => _EditorPageState();
}

class _EditorPageState extends State<EditorPage> {
  final TextEditingController _titleController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();

  // Fleather相关控制器
  late FleatherController _fleatherController;
  late ParchmentDocument _document;

  // Markdown编辑器的Key
  final GlobalKey<MarkdownEditorState> _markdownEditorKey =
      GlobalKey<MarkdownEditorState>();

  bool _isEditing = true;
  bool _showAIHint = false;
  bool _showAIMenu = false;
  List<String> _tags = [];
  String _aiSuggestion = '';
  bool _editingTitle = false;
  bool _isFavorite = false;

  // 光标预测相关
  String _aiPrediction = '';
  bool _showAIPrediction = false;
  Offset _cursorPosition =
      Offset.zero; // _cursorPosition 不再直接用于定位预测栏，但可能仍用于逻辑判断
  bool _isGeneratingPrediction = false;
  Offset _predictionBarPosition =
      Offset(100, 100); // 用于 DraggableAIPredictionBar 的位置

  // AI提示栏尺寸 - 使用动态测量替代硬编码值
  double _aiHintBarWidth = 300.0; // 默认宽度
  double _aiHintBarHeight = 100.0; // 默认高度
  final GlobalKey _aiPredictionBarKey = GlobalKey(); // 用于测量实际尺寸

  // AI助手图标位置相关
  Offset? _aiButtonPosition; // 存储图标的绝对位置（相对于屏幕）

  // 预测栏位置是否已从本地存储加载
  bool _predictionBarPositionLoaded = false;

  // AI功能相关
  late AIFunctionProvider _aiService;

  // 防抖计时器
  Timer? _savePositionDebounceTimer;
  // 最新的预测栏位置
  Offset? _latestPredictionBarPosition;

  Timer? _debouncePredictionTimer; // 新增防抖定时器

  // 保存预测栏位置到本地存储（带防抖功能）
  void _savePredictionBarPosition(Offset position) {
    // 更新最新位置
    _latestPredictionBarPosition = position;
    print('[POSITION_DEBUG] 保存预测栏位置: $position');

    // 获取屏幕尺寸，确保位置在屏幕范围内
    final size = MediaQuery.of(context).size;

    // 使用测量的尺寸或默认值
    final barWidth = _aiHintBarWidth;
    final barHeight = _aiHintBarHeight;
    print('[POSITION_DEBUG] 使用的预测栏尺寸: ${barWidth}x${barHeight}');

    // 确保位置在屏幕范围内
    // 为右侧边界添加额外的安全边距，确保内容完全可见
    const safetyMargin = 20.0; // 额外的安全边距
    double x = position.dx.clamp(0, size.width - barWidth - safetyMargin);
    double y = position.dy.clamp(0, size.height - barHeight);

    // 如果位置被调整，记录日志
    if (x != position.dx || y != position.dy) {
      print('[POSITION_DEBUG] 位置已调整: 从 $position 到 (${x}, ${y})');
    }

    // 更新当前位置（立即更新UI）
    if (mounted) {
      setState(() {
        _predictionBarPosition = Offset(x, y);
        _predictionBarPositionLoaded = true;
      });
    }

    // 取消之前的计时器
    _savePositionDebounceTimer?.cancel();

    // 设置新的计时器，增加到500毫秒，减少写入频率
    _savePositionDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      _persistPredictionBarPosition();
    });
  }

  @override
  void didUpdateWidget(EditorPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 如果预测栏可见，测量其尺寸
    if (_showAIPrediction && _aiPrediction.isNotEmpty) {
      _measureAIPredictionBarSize();
    }
  }

  // 实际执行持久化保存的方法
  Future<void> _persistPredictionBarPosition() async {
    if (_latestPredictionBarPosition == null) return;

    try {
      print('[POSITION_DEBUG] 开始持久化保存预测栏位置: $_latestPredictionBarPosition');
      final prefs = await SharedPreferences.getInstance();

      // 在异步操作后检查组件是否仍然挂载
      if (!mounted) return;

      // 使用JSON格式保存更多信息
      final positionData = {
        'dx': _latestPredictionBarPosition!.dx,
        'dy': _latestPredictionBarPosition!.dy,
        'width': _aiHintBarWidth,
        'height': _aiHintBarHeight,
        'timestamp': DateTime.now().millisecondsSinceEpoch, // 添加时间戳，确保数据更新
      };

      print('[POSITION_DEBUG] 准备保存的数据: $positionData');

      // 保存到本地存储
      await prefs.setString('prediction_bar_data', jsonEncode(positionData));

      // 验证数据是否已保存
      final savedData = prefs.getString('prediction_bar_data');
      print('[POSITION_DEBUG] 验证保存的数据: ${savedData != null ? '有数据' : '无数据'}');

      if (mounted) {
        print('[POSITION_DEBUG] 预测栏位置已成功保存');
      }
    } catch (e) {
      // 保存失败时不影响正常功能
      print('[POSITION_DEBUG] 保存预测栏位置失败: $e');
    }
  }

  // 从本地存储加载预测栏位置
  Future<Offset?> _loadPredictionBarPosition() async {
    try {
      print('[POSITION_DEBUG] 开始从本地存储加载预测栏位置');
      final prefs = await SharedPreferences.getInstance();

      // 尝试加载新格式数据
      final dataStr = prefs.getString('prediction_bar_data');
      print(
          '[POSITION_DEBUG] 从SharedPreferences加载的数据: ${dataStr != null ? '有数据' : '无数据'}');

      if (dataStr != null) {
        final data = jsonDecode(dataStr) as Map<String, dynamic>;
        print('[POSITION_DEBUG] 解析的JSON数据: $data');

        // 加载宽高信息
        if (data.containsKey('width') && data.containsKey('height')) {
          // 安全地转换为double，处理可能存储为int的情况
          _aiHintBarWidth = (data['width'] as num).toDouble();
          _aiHintBarHeight = (data['height'] as num).toDouble();
          print('[POSITION_DEBUG] 加载的宽高: $_aiHintBarWidth x $_aiHintBarHeight');
        }

        // 标记位置已加载
        _predictionBarPositionLoaded = true;
        print('[POSITION_DEBUG] 设置_predictionBarPositionLoaded = true');

        // 安全地转换为double，处理可能存储为int的情况
        final loadedPosition = Offset(
            (data['dx'] as num).toDouble(), (data['dy'] as num).toDouble());

        print(
            '[POSITION_DEBUG] 从本地存储加载预测栏位置: $loadedPosition, 尺寸: $_aiHintBarWidth x $_aiHintBarHeight');
        return loadedPosition;
      }

      // 兼容旧格式数据
      final dx = prefs.getDouble('prediction_bar_x');
      final dy = prefs.getDouble('prediction_bar_y');
      print('[POSITION_DEBUG] 尝试加载旧格式数据: dx=$dx, dy=$dy');

      if (dx != null && dy != null) {
        // 标记位置已加载
        _predictionBarPositionLoaded = true;
        final legacyPosition = Offset(dx, dy);
        print('[POSITION_DEBUG] 从旧格式数据加载预测栏位置: $legacyPosition');
        return legacyPosition;
      }

      print('[POSITION_DEBUG] 没有找到任何保存的位置数据');
    } catch (e) {
      // 加载失败时使用默认位置
      print('[POSITION_DEBUG] 加载预测栏位置失败: $e');
    }
    return null;
  }

  // 测量AI预测栏的实际尺寸
  void _measureAIPredictionBarSize() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      // 使用GlobalKey获取渲染对象
      final RenderBox? renderBox =
          _aiPredictionBarKey.currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null && renderBox.hasSize) {
        // 更新尺寸变量
        setState(() {
          _aiHintBarWidth = renderBox.size.width;
          _aiHintBarHeight = renderBox.size.height;
          print(
              '[POSITION_DEBUG] 测量到的预测栏尺寸: $_aiHintBarWidth x $_aiHintBarHeight');
        });
      }
    });
  }

  // 重置预测栏位置到默认位置
  void _resetPredictionBarPosition() {
    if (!mounted) return;

    final size = MediaQuery.of(context).size;
    final defaultPosition = Offset(size.width - 300, size.height / 2);

    print('[POSITION_DEBUG] 重置预测栏位置到默认位置: $defaultPosition');

    setState(() {
      _predictionBarPosition = defaultPosition;
      _predictionBarPositionLoaded = true;
      _latestPredictionBarPosition = defaultPosition;
    });

    // 保存到本地存储
    _persistPredictionBarPosition();
  }

  @override
  void initState() {
    super.initState();

    // 直接初始AI服务
    _aiService = AIFunctionProvider(context);

    // 修正：优先异步加载本地保存的预测栏位置
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      Offset? savedPosition = await _loadPredictionBarPosition();
      final size = MediaQuery.of(context).size;
      setState(() {
        if (savedPosition != null) {
          _predictionBarPosition = savedPosition;
          print('[POSITION_DEBUG] 加载本地保存的预测栏位置: \\$_predictionBarPosition');
        } else {
          _predictionBarPosition = Offset(size.width - 300, size.height / 2);
          print('[POSITION_DEBUG] 使用默认预测栏位置: \\$_predictionBarPosition');
        }
        _predictionBarPositionLoaded = true;
      });
    });

    // 延迟初始AI助手图标位置，确保在屏幕尺寸可用后设置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      // 获取屏幕尺寸
      final size = MediaQuery.of(context).size;
      // 默认将图标放在右下角
      final aiButtonPos = Offset(size.width - 70, size.height - 200);
      // 更新AI助手图标位置
      setState(() {
        _aiButtonPosition = aiButtonPos;
      });
    });

    // 如果是编辑现有笔记，填充数据
    if (widget.note != null) {
      _titleController.text = widget.note!.title;

      // 设置内容类型
      _selectedContentType = widget.note!.contentType;

      // 根据笔记类型加载内容
      if (widget.note!.contentType == 'markdown') {
        // 如果是Markdown格式，直接作为纯文本加载
        print('DEBUG: [EditorPage] 加载Markdown格式笔记');
        String content = widget.note!.content;
        if (!content.endsWith('\n')) {
          content += '\n';
        }
        _document = ParchmentDocument.fromJson([
          {"insert": content}
        ]);
      } else {
        // 尝试解析富文本内容
        try {
          // 检查内容是否为JSON格式的Delta
          if (widget.note!.content.trim().startsWith('[') ||
              widget.note!.content.trim().startsWith('{')) {
            dynamic deltaJson = jsonDecode(widget.note!.content);
            _document = ParchmentDocument.fromJson(deltaJson);
            print('DEBUG: [EditorPage] 成功加载富文本内容');

            // 确保文档以换行结尾
            String content = _document.toPlainText();
            if (!content.endsWith('\n')) {
              print('DEBUG: [EditorPage] 文档不以换行结尾，添加换行');
              final delta = _document.toDelta()..insert('\n');
              _document = ParchmentDocument.fromDelta(delta);
            }
          } else {
            // 如果是纯文本，转换为Delta格式
            print('DEBUG: [EditorPage] 内容不是Delta格式，作为纯文本处理');
            String content = widget.note!.content;
            if (!content.endsWith('\n')) {
              content += '\n';
            }
            _document = ParchmentDocument.fromJson([
              {"insert": content}
            ]);
          }
        } catch (e) {
          print('ERROR: [EditorPage] 解析笔记内容失败: $e');
          // 解析失败时，使用纯文本格式
          String content = widget.note!.content;
          if (!content.endsWith('\n')) {
            content += '\n';
          }
          _document = ParchmentDocument.fromJson([
            {"insert": content}
          ]);
        }
      }

      // 最后检查文档是否以换行结尾
      print('DEBUG: [EditorPage] 最终文档内容: "${_document.toPlainText()}"');

      // 使用tagIds而不是tags字段
      _tags = List.from(widget.note!.tagIds);
      print('DEBUG: 编辑页面加载标签IDs: $_tags');

      _isFavorite = widget.note!.isFavorite;
    } else {
      // 新笔记，先显示格式选择对话框
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        if (mounted) {
          print('DEBUG: 新笔记，显示格式选择对话框');
          final confirmed = await _showFormatSelectionDialog();
          if (!confirmed) {
            // 用户取消了格式选择，返回上一页
            print('DEBUG: 用户取消了格式选择，返回上一页');
            Navigator.pop(context);
            return;
          }
          print('DEBUG: 用户确认了格式选择，选择的格式是: $_selectedContentType');

          // 如果需要，可以在这里重新构建编辑器
          setState(() {});
        }
      });

      _titleController.text = '无标题';
      // 创建空的ParchmentDocument
      // 确保文档以换行结尾
      final delta = Delta()..insert('\n');
      _document = ParchmentDocument.fromDelta(delta);
      print('DEBUG: [EditorPage] 创建空文档，内容为: "${_document.toPlainText()}"');

      // 如果有初始标签ID，则添加到标签列表中
      if (widget.initialTagId != null && widget.initialTagId!.isNotEmpty) {
        _tags.add(widget.initialTagId!);
        print('DEBUG: 创建新笔记，添加初始标签ID: ${widget.initialTagId}');
      }
    }

    // 使用命名参数初始化FleatherController
    _fleatherController = FleatherController(document: _document);

    // 监听文档变化，在适当的时候提供AI建议
    _fleatherController.document.changes.listen((_) {
      // 如果内容发生变化，且AI功能已启用，则在一定延迟后显示AI提示
      _scheduleAISuggestion();

      // 生成光标预测
      _handleAIPredictionTrigger();
    });

    // 定期检查光标位置变化
    // 注意：FleatherController没有selectionNotifier属性，所以我们使用定时器来检测光标位置变化
    _selectionTimer =
        Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (mounted) {
        _checkSelectionChanged();
      } else {
        timer.cancel();
      }
    });

    // 监听内容焦点变化
    _contentFocusNode.addListener(() {
      if (!_contentFocusNode.hasFocus) {
        // 获取当前笔记类型
        final contentType = widget.note?.contentType ?? _selectedContentType;

        // 在Markdown模式下，不因失去焦点而隐藏预测栏
        if (contentType != 'markdown') {
          // 只在非Markdown模式下，当失去焦点时隐藏预测
          setState(() {
            _showAIPrediction = false;
          });
        }
      }
    });

    // 如果有笔记，预加载标签数据以便显示
    if (widget.note != null && widget.note!.tagIds.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadTagData();
      });
    }
    // 如果是通过标签页面创建的新笔记且有初始标签ID，也需要加载标签数据
    else if (widget.initialTagId != null && widget.initialTagId!.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadTagData();
      });
    }
  }

  /// 根据AI设置，在适当的时候提供AI建议
  void _scheduleAISuggestion() {
    // 不再显示AI提示，改为使用光标预测功能
    // 这个方法保留但不执行任何操作
    return;
  }

  /// 生成AI建议
  void _generateAISuggestion(String content) async {
    // 不再生成AI建议，改为使用光标预测功能
    // 这个方法保留但不执行任何操作
    return;
  }

  // 保存上一次光标位置
  TextSelection? _lastSelection;

  /// 检查光标位置变化
  void _checkSelectionChanged() {
    // 获取当前光标位置
    final selection = _fleatherController.selection;

    // 如果光标位置与上次不同，生成新的预测
    if (selection != null &&
        selection.isCollapsed &&
        (_lastSelection == null ||
            _lastSelection!.baseOffset != selection.baseOffset ||
            _lastSelection!.extentOffset != selection.extentOffset)) {
      // 更新上次光标位置
      _lastSelection = selection;
      // 生成光标预测
      _handleAIPredictionTrigger();
    }
  }

  /// 统一AI预测触发方法，根据频率设置自动/防抖/手动
  void _handleAIPredictionTrigger() {
    _debouncePredictionTimer?.cancel(); // 优化：无条件先取消定时器，防止残留
    final aiProvider = Provider.of<AIProvider>(context, listen: false);
    final freq = aiProvider.suggestionFrequency;
    if (freq == '实时') {
      _generateCursorPrediction();
    } else if (freq == '输入停顿后') {
      _debouncePredictionTimer = Timer(const Duration(milliseconds: 800), () {
        _generateCursorPrediction();
      });
    } else if (freq == '手动触发') {
      setState(() {
        _showAIPrediction = false;
      });
    }
  }

  /// 生成光标预测
  void _generateCursorPrediction() async {
    // 如果AI功能未启用或正在生成预测，则返回
    if (!mounted ||
        !_aiService.isSmartSuggestionsEnabled ||
        _isGeneratingPrediction) {
      return;
    }

    // 获取当前内容和光标位置
    String content;
    int cursorPosition;

    // 根据笔记类型获取内容和光标位置
    final contentType = widget.note?.contentType ?? _selectedContentType;
    if (contentType == 'markdown') {
      // 从Markdown编辑器获取内容和光标位置
      if (_markdownEditorKey.currentState != null) {
        content = _markdownEditorKey.currentState!.getContent();
        cursorPosition = _markdownEditorKey.currentState!.getCursorPosition();

        // 如果无法获取光标位置，不生成预测
        if (cursorPosition < 0) {
          setState(() {
            _showAIPrediction = false;
          });
          return;
        }
      } else {
        // 如果无法获取Markdown编辑器状态，不生成预测
        setState(() {
          _showAIPrediction = false;
        });
        return;
      }
    } else {
      // 从Fleather编辑器获取内容和光标位置
      final selection = _fleatherController.selection;
      if (selection == null || !selection.isCollapsed) {
        setState(() {
          _showAIPrediction = false;
        });
        return;
      }

      content = _document.toPlainText();
      cursorPosition = selection.baseOffset;
    }

    // 如果光标前的内容少于3个字符，不生成预测
    if (cursorPosition < 3) {
      setState(() {
        _showAIPrediction = false;
      });
      return;
    }

    // 标记正在生成预测
    _isGeneratingPrediction = true;

    try {
      // 调用AI服务生成预测
      final result = await _aiService.generateCompletion(
        content: content,
        cursorPosition: cursorPosition,
      );

      // 检查组件是否已经被卸载
      if (!mounted) return;

      if (result['success'] == true &&
          result['data'] != null &&
          result['data']['completion'] != null) {
        final prediction = result['data']['completion'];

        // 检查生成的内容是否为空
        if (prediction.trim().isEmpty) {
          if (mounted) {
            setState(() {
              _showAIPrediction = false;
            });
          }
          return;
        }

        // 更新预测内容
        if (mounted) {
          setState(() {
            _aiPrediction = prediction;
            _showAIPrediction = true;

            // 在编辑器中显示预测内容
            print('DEBUG: [EditorPage] 生成预测内容: "$prediction"');

            // 不再重置预测栏位置，保留用户拖动后的位置
            // 如果是第一次显示预测，才设置初始位置
            if (!_predictionBarPositionLoaded) {
              // 获取屏幕尺寸
              final size = MediaQuery.of(context).size;

              // 设置预测栏的初始位置在标签栏上方
              _predictionBarPosition = Offset(
                20, // 左侧距离
                size.height - 100, // 固定在标签栏上方，稍微上移一点
              );
              _predictionBarPositionLoaded = true;

              // 在下一帧测量预测栏的实际尺寸
              _measureAIPredictionBarSize();
            }
          });
        }
      } else {
        if (mounted) {
          setState(() {
            _showAIPrediction = false;
          });
        }
      }
    } catch (e) {
      print('ERROR: [EditorPage] 生成光标预测失败: $e');
      if (mounted) {
        setState(() {
          _showAIPrediction = false;
        });
      }
    } finally {
      _isGeneratingPrediction = false;
    }
  }

  /// 处理Tab键接受预测
  void _acceptPrediction() {
    if (!_showAIPrediction || _aiPrediction.isEmpty) {
      return;
    }

    final contentType = widget.note?.contentType ?? _selectedContentType;
    bool predictionAccepted = false;

    if (contentType == 'markdown') {
      if (_markdownEditorKey.currentState != null) {
        // 在Markdown模式下，insertTextAtCursor方法已经包含了请求焦点的逻辑
        _markdownEditorKey.currentState!.insertTextAtCursor(_aiPrediction);
        _updateDocumentFromMarkdown();
        predictionAccepted = true;
      }
    } else {
      final selection = _fleatherController.selection;
      if (selection != null && selection.isCollapsed) {
        final String currentPlainText =
            _fleatherController.document.toPlainText();
        final int currentPlainTextLength = currentPlainText.length;
        final int insertPosition =
            min(selection.baseOffset, currentPlainTextLength);

        _fleatherController.replaceText(
          insertPosition,
          0,
          _aiPrediction,
          selection: TextSelection.collapsed(
              offset: insertPosition + _aiPrediction.length),
        );
        predictionAccepted = true;

        if (kIsWeb) {
          // Web 平台，插入后尝试恢复焦点并确保光标可见
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              _contentFocusNode.requestFocus();
              // 确保光标位置正确更新后，FleatherEditor能正确显示
              // 有时可能需要强制刷新或等待下一个事件循环
              setState(() {});
            }
          });
        }
      }
    }

    if (predictionAccepted) {
      setState(() {
        _showAIPrediction = false;
        _aiPrediction = '';
      });
    }
  }

  // 定时器实例
  Timer? _selectionTimer;

  @override
  void dispose() {
    _titleController.dispose();
    // 取消定时器
    _selectionTimer?.cancel();
    // 取消防抖计时器
    _savePositionDebounceTimer?.cancel();
    _debouncePredictionTimer?.cancel(); // 释放防抖定时器
    // 在退出前保存最新的预测栏位置
    if (_latestPredictionBarPosition != null) {
      _persistPredictionBarPosition();
    }
    _fleatherController.dispose(); // 释放Fleather控制器
    _titleFocusNode.dispose();
    _contentFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final aiProvider = context.watch<AIProvider>(); // 监听AIProvider的变化

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: GestureDetector(
        // 点击空白区域关闭AI菜单
        onTap: () {
          if (_showAIMenu || _showAIHint) {
            setState(() {
              _showAIMenu = false;
              _showAIHint = false;
            });
          }
        },
        child: Stack(
          children: [
            // 主要内容
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (Widget child, Animation<double> animation) {
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: _isEditing
                          ? const Offset(1.0, 0.0)
                          : const Offset(-1.0, 0.0),
                      end: Offset.zero,
                    ).animate(CurvedAnimation(
                      parent: animation,
                      curve: Curves.easeOutCubic,
                    )),
                    child: child,
                  ),
                );
              },
              child: _isEditing
                  ? _buildEditor(key: const ValueKey('editor_mode'))
                  : _buildReadingMode(key: const ValueKey('reading_mode')),
            ),

            // AI预测栏 - 只在编辑模式下显示
            if (_showAIPrediction && _isEditing && _aiPrediction.isNotEmpty)
              Positioned(
                left: _predictionBarPosition.dx,
                top: _predictionBarPosition.dy,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 添加重置位置按钮
                    Align(
                      alignment: Alignment.centerRight,
                      child: IconButton(
                        onPressed: _resetPredictionBarPosition,
                        icon: const Icon(
                          Icons.refresh,
                          size: 16,
                          color: AppTheme.primaryColor,
                        ),
                        tooltip: '重置预测栏位置',
                        padding: const EdgeInsets.all(4),
                        constraints: BoxConstraints(),
                        splashRadius: 18,
                      ),
                    ),
                    const SizedBox(height: 4),
                    // 预测栏
                    DraggableAIPredictionBar(
                      key: _aiPredictionBarKey, // 使用GlobalKey测量实际尺寸
                      prediction: _aiPrediction,
                      initialPosition:
                          Offset.zero, // 使用相对位置，因为已经在Positioned中设置了绝对位置
                      preventDismissOnTapOutside: false, // 不再需要此属性，关闭按钮已修改为始终可用
                      onAccept: () {
                        _acceptPrediction();
                      },
                      onDismiss: () {
                        setState(() {
                          _showAIPrediction = false;
                        });
                      },
                      onDrag: (newPosition) {
                        // 保存预测栏位置到本地存储，不再需要setState
                        _savePredictionBarPosition(newPosition);
                      },
                      useLocalPositioning: true, // 启用本地位置管理
                    ),
                  ],
                ),
              ),

            // AI助手图标 - 只在编辑模式且AI助手启用时显示
            if (_aiButtonPosition != null &&
                _isEditing &&
                aiProvider.aiAssistantEnabled)
              Positioned(
                left: _aiButtonPosition!.dx,
                top: _aiButtonPosition!.dy,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _showAIMenu = true;
                    });
                  },
                  // 添加拖动功能
                  onPanUpdate: (details) {
                    setState(() {
                      // 直接更新按钮的绝对位置
                      _aiButtonPosition = Offset(
                        _aiButtonPosition!.dx + details.delta.dx,
                        _aiButtonPosition!.dy + details.delta.dy,
                      );

                      // 限制范围，防止拖出屏幕
                      final size = MediaQuery.of(context).size;
                      _aiButtonPosition = Offset(
                        _aiButtonPosition!.dx.clamp(0.0, size.width - 48),
                        _aiButtonPosition!.dy.clamp(0.0, size.height - 48),
                      );
                    });
                  },
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 8,
                          spreadRadius: 1,
                          offset: const Offset(0, 2),
                        ),
                      ],
                      border: Border.all(
                        color: AppTheme.primaryColor.withOpacity(0.2),
                        width: 1.5,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Image.asset(
                        '/images/AI.png',
                        width: 24,
                        height: 24,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ),
              ),

            // AI菜单 - 只在编辑模式下显示
            if (_showAIMenu && _isEditing) _buildAIMenu(),

            // AI提示 - 只在编辑模式下显示
            if (_showAIHint && _isEditing) _buildAIHint(),

            // 在手动触发模式且处于编辑状态时，显示AI预测按钮
            if (aiProvider.suggestionFrequency == '手动触发' && _isEditing)
              Positioned(
                right: 24,
                bottom: 24 + MediaQuery.of(context).padding.bottom,
                child: FloatingActionButton(
                  onPressed: _generateCursorPrediction,
                  child: Icon(Icons.lightbulb),
                  tooltip: 'AI预测',
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  heroTag: 'aiSuggestionFab',
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditor({Key? key}) {
    return SafeArea(
      key: key,
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildEditorContent(),
            ),
          ),
          _buildToolbar(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧返回按钮
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.arrow_back,
                color: Theme.of(context).iconTheme.color,
                size: 20,
              ),
            ),
          ),

          // 标题（可双击编辑）
          Expanded(
            child: GestureDetector(
              onDoubleTap: () {
                setState(() {
                  _editingTitle = true;
                });
                _titleFocusNode.requestFocus();
              },
              child: _editingTitle
                  ? TextField(
                      controller: _titleController,
                      focusNode: _titleFocusNode,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.titleLarge?.color,
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 16),
                      ),
                      onSubmitted: (_) {
                        setState(() {
                          _editingTitle = false;
                          if (_titleController.text.trim().isEmpty) {
                            _titleController.text = '无标题';
                          }
                        });
                      },
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        _titleController.text,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).textTheme.titleLarge?.color,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
            ),
          ),

          // 右侧按钮
          Row(
            children: [
              // 切换编辑/阅读模式
              InkWell(
                onTap: _toggleMode,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _isEditing ? Icons.visibility : Icons.edit,
                    color: Theme.of(context).iconTheme.color,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // 历史版本按钮
              InkWell(
                onTap: () {
                  // 检查当前是否在编辑一个已存在的笔记
                  if (widget.note != null && widget.note!.id.isNotEmpty) {
                    final noteId = widget.note!.id;
                    print('DEBUG: [EditorPage] 历史按钮点击，笔记ID: $noteId');
                    // 调用 Provider 开始获取历史记录
                    Provider.of<NoteProvider>(context, listen: false)
                        .fetchNoteHistory(noteId);
                    // 显示底部面板来展示历史记录
                    _showHistoryBottomSheet(context);
                  } else {
                    // 如果是新笔记，提示用户先保存
                    SnackbarHelper.showInfo(
                        context: context, message: '请先保存笔记以查看历史版本');
                  }
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.history,
                    color: Theme.of(context).iconTheme.color,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // 分享按钮
              InkWell(
                onTap: _shareNote,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.share,
                    color: Theme.of(context).iconTheme.color,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // 保存按钮
              InkWell(
                onTap: () {
                  _saveNote();
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.save,
                    color: Theme.of(context).iconTheme.color,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    return Stack(
      children: [
        // 内容区域 - 根据笔记类型选择不同的编辑器
        // 为AI预测栏预留空间，调整Padding的bottom值
        Padding(
          padding: const EdgeInsets.only(bottom: 120), // 增加了40以容纳预测栏和标签栏
          child: Focus(
            // 使用 Focus widget 替代 RawKeyboardListener
            focusNode: FocusNode(), // 编辑器也使用此 focusNode
            onKey: (FocusNode node, RawKeyEvent event) {
              if (event is RawKeyDownEvent &&
                  event.logicalKey == LogicalKeyboardKey.tab) {
                if (kIsWeb) {
                  // Web 平台
                  if (_showAIPrediction && _aiPrediction.isNotEmpty) {
                    _acceptPrediction(); // _acceptPrediction 会处理焦点
                    return KeyEventResult.handled; // 阻止 Tab 键的默认行为
                  }
                }
                // 非 Web 平台或没有预测时，Tab 键执行默认行为
                return KeyEventResult.ignored;
              }
              // 其他按键执行默认行为
              return KeyEventResult.ignored;
            },
            child: _getEditorWidget(),
          ),
        ),

        // 标签区域
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 60, // Explicit height for tag area
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  ..._tags.map((tag) => Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: _buildTag(tag),
                      )),
                  _buildAddTagButton(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 根据标签ID获取标签对象
  Tag? _getTagById(String tagId) {
    final tagProvider = Provider.of<TagProvider>(context, listen: false);
    try {
      return tagProvider.tags.firstWhere((tag) => tag.id == tagId);
    } catch (e) {
      // 如果未找到标签，返回null
      return null;
    }
  }

  Widget _buildTag(String tagId) {
    final tag = _getTagById(tagId);
    if (tag == null) return Container(); // 如果标签不存在，返回空容器

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: tag.colorValue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            tag.name,
            style: TextStyle(
              color: tag.colorValue,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 6),
          InkWell(
            onTap: () {
              setState(() {
                _tags.remove(tagId);
              });
            },
            child: Icon(
              Icons.close,
              size: 14,
              color: tag.colorValue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddTagButton() {
    return InkWell(
      onTap: _addTag,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade800
              : Colors.grey.shade200,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              size: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
            const SizedBox(width: 6),
            Text(
              '添加标签',
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAIHint() {
    if (_aiButtonPosition == null) return const SizedBox.shrink();

    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;

    // 计算AI提示的位置
    double left = max(0, _aiButtonPosition!.dx - 150); // 水平居中于AI按钮
    double top = max(0, _aiButtonPosition!.dy + 48); // 在AI按钮下方

    // 确保提示不会超出屏幕边界
    if (left + 300 > screenSize.width) {
      left = screenSize.width - 300;
    }
    if (top + 100 > screenSize.height) {
      top = _aiButtonPosition!.dy - 100; // 如果下方空间不足，显示在上方
    }

    return Positioned(
      left: left,
      top: top,
      child: DraggableAIPredictionBar(
        prediction: _aiSuggestion,
        initialPosition: Offset.zero, // 初始位置由Positioned控制
        preventDismissOnTapOutside: false, // 确保关闭按钮始终可用
        onAccept: () {
          if (_aiSuggestion.trim().isEmpty) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('AI建议内容为空')),
            );
            return;
          }

          setState(() {
            _showAIHint = false;
            // 接受AI建议 - 添加到Fleather编辑器，保留富文本内容
            final currentDelta = _document.toDelta();

            // 创建建议内容的Delta
            final suggestionDelta = Delta()..insert(_aiSuggestion);

            // 确保建议内容以换行结尾
            if (!_aiSuggestion.endsWith('\n')) {
              suggestionDelta.insert('\n');
            }

            // 将建议内容添加到当前文档的末尾
            final newDelta = currentDelta.compose(suggestionDelta);

            // 创建新文档并设置到编辑器
            final newDocument = ParchmentDocument.fromDelta(newDelta);
            _document = newDocument;
            _fleatherController = FleatherController(document: _document);
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('已添加AI建议的内容')),
          );
        },
        onDismiss: () {
          setState(() {
            _showAIHint = false;
          });
        },
      ),
    );
  }

  Widget _buildAIMenu() {
    if (_aiButtonPosition == null) return const SizedBox.shrink();

    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;

    // 获取AI菜单的大小（估计值）
    double menuWidth = 300.0;
    const menuHeight = 250.0;

    // 计算屏幕四个象限的可用空间
    double rightSpace = screenSize.width - _aiButtonPosition!.dx - 48; // 右侧空间
    double leftSpace = _aiButtonPosition!.dx; // 左侧空间
    double bottomSpace = screenSize.height - _aiButtonPosition!.dy - 48; // 下方空间
    double topSpace = _aiButtonPosition!.dy; // 上方空间

    // 根据可用空间决定菜单位置
    double left;
    double top;

    // 如果图标在屏幕右侧，将菜单显示在左侧
    if (_aiButtonPosition!.dx > screenSize.width / 2) {
      left = max(0, _aiButtonPosition!.dx - menuWidth);
      // 如果左侧空间不足，调整菜单宽度
      if (leftSpace < menuWidth) {
        menuWidth = max(200, leftSpace); // 最小宽度为200
        left = max(0, _aiButtonPosition!.dx - menuWidth);
      }
    } else {
      // 如果图标在屏幕左侧，将菜单显示在右侧
      left = _aiButtonPosition!.dx + 48; // 图标宽度为48
      // 如果右侧空间不足，调整菜单宽度
      if (rightSpace < menuWidth) {
        menuWidth = max(200, rightSpace); // 最小宽度为200
      }
    }

    // 如果图标在屏幕下方，将菜单显示在上方
    if (_aiButtonPosition!.dy > screenSize.height / 2) {
      top = max(0, _aiButtonPosition!.dy - menuHeight);
    } else {
      // 如果图标在屏幕上方，将菜单显示在下方
      top = _aiButtonPosition!.dy + 48; // 图标高度为48
      // 如果下方空间不足，将菜单显示在上方
      if (top + menuHeight > screenSize.height) {
        top = max(0, _aiButtonPosition!.dy - menuHeight);
      }
    }

    return Positioned(
      left: left,
      top: top,
      child: Container(
        width: menuWidth,
        child: AIMenu(
          currentContent: _document.toPlainText(),
          parentContext: context, // 传递父级上下文
          onGenerateCompletion: (completion) {
            if (completion.trim().isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('生成的续写内容为空')),
              );
              return;
            }

            // 添加续写内容到编辑器，保留富文本内容
            final currentDelta = _document.toDelta();

            // 创建续写内容的Delta
            final completionDelta = Delta()..insert(completion);

            // 确保续写内容以换行结尾
            if (!completion.endsWith('\n')) {
              completionDelta.insert('\n');
            }

            // 将续写内容添加到当前文档的末尾
            final newDelta = currentDelta.compose(completionDelta);

            // 创建新文档并设置到编辑器
            final newDocument = ParchmentDocument.fromDelta(newDelta);
            setState(() {
              _document = newDocument;
              _fleatherController = FleatherController(document: _document);
            });

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('已添加智能续写内容')),
            );
          },
          onGenerateSummary: (summary) {
            // 显示摘要对话框
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('笔记摘要'),
                content: Text(summary),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('关闭'),
                  ),
                  TextButton(
                    onPressed: () {
                      // 将摘要添加到笔记开头，保留富文本内容
                      final currentDelta = _document.toDelta();
                      final summaryDelta = Delta()..insert('摘要: $summary\n\n');

                      // 创建新的Delta，先插入摘要，再插入原文档内容
                      final newDelta = summaryDelta;
                      for (final op in currentDelta.toList()) {
                        newDelta.push(op);
                      }

                      // 创建新文档并设置到编辑器
                      final newDocument = ParchmentDocument.fromDelta(newDelta);
                      setState(() {
                        _document = newDocument;
                        _fleatherController =
                            FleatherController(document: _document);
                      });

                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('已添加摘要到笔记')),
                      );
                    },
                    child: const Text('添加到笔记'),
                  ),
                ],
              ),
            );
          },
          onGenerateTagSuggestions: (tags) async {
            // 获取标签Provider
            final tagProvider =
                Provider.of<TagProvider>(context, listen: false);

            // 创建一个列表来跟踪选中状态
            List<bool> selectedTags = List.generate(tags.length, (index) {
              // 检查标签是否已经存在于当前标签列表中
              final existingTag = tagProvider.tags
                  .where((tag) => tag.name == tags[index])
                  .toList();
              if (existingTag.isNotEmpty) {
                return _tags.contains(existingTag[0].id);
              }
              return false;
            });

            // 全选状态
            bool allSelected = selectedTags.every((selected) => selected);

            // 显示标签建议对话框
            showDialog(
              context: context,
              builder: (dialogContext) => StatefulBuilder(
                builder: (context, setDialogState) {
                  return AlertDialog(
                    title: const Text('标签建议'),
                    content: SizedBox(
                      width: double.maxFinite,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 全选复选框
                          CheckboxListTile(
                            title: const Text('全选'),
                            value: allSelected,
                            onChanged: (value) {
                              setDialogState(() {
                                allSelected = value ?? false;
                                selectedTags = List.generate(
                                    tags.length, (_) => allSelected);
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                          const Divider(),
                          // 标签列表
                          Expanded(
                            child: ListView.builder(
                              shrinkWrap: true,
                              itemCount: tags.length,
                              itemBuilder: (context, index) {
                                return CheckboxListTile(
                                  title: Text(tags[index]),
                                  value: selectedTags[index],
                                  onChanged: (value) {
                                    setDialogState(() {
                                      selectedTags[index] = value ?? false;
                                      // 更新全选状态
                                      allSelected = selectedTags
                                          .every((selected) => selected);
                                    });
                                  },
                                  controlAffinity:
                                      ListTileControlAffinity.leading,
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(dialogContext),
                        child: const Text('取消'),
                      ),
                      TextButton(
                        onPressed: () async {
                          // 处理选中的标签
                          for (int i = 0; i < tags.length; i++) {
                            if (selectedTags[i]) {
                              // 检查标签是否存在，如果不存在则创建
                              _createOrAddTag(tags[i], tagProvider);
                            } else {
                              // 移除标签
                              _removeTagByName(tags[i], tagProvider);
                            }
                          }
                          Navigator.pop(dialogContext);

                          // 显示成功消息
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('标签已更新')),
                          );
                        },
                        child: const Text('确定'),
                      ),
                    ],
                  );
                },
              ),
            );
          },
          onClose: () {
            setState(() {
              _showAIMenu = false;
            });
          },
        ),
      ),
    );
  }

  Widget _buildToolbar() {
    // 获取当前笔记类型
    final contentType = widget.note?.contentType ?? _selectedContentType;

    // 如果是Markdown格式，显示Markdown工具栏
    if (contentType == 'markdown') {
      return _buildMarkdownToolbar();
    } else {
      // 否则显示富文本工具栏
      return _buildRichTextToolbar();
    }
  }

  // 构建Markdown工具栏
  Widget _buildMarkdownToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Markdown简化工具栏
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 标题按钮
                _buildMarkdownButton(
                  child: const Text('H1',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('# '),
                  tooltip: '标题1',
                ),
                _buildMarkdownButton(
                  child: const Text('H2',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('## '),
                  tooltip: '标题2',
                ),
                _buildMarkdownButton(
                  child: const Text('H3',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('### '),
                  tooltip: '标题3',
                ),
                _buildMarkdownButton(
                  child: const Text('H4',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('#### '),
                  tooltip: '标题4',
                ),
                // 分隔线
                const VerticalDivider(width: 16, thickness: 1),
                // 格式化按钮
                _buildMarkdownButton(
                  child: const Icon(Icons.format_bold, size: 20),
                  onPressed: () => _insertMarkdownSyntax('**', '**'),
                  tooltip: '加粗',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.format_italic, size: 20),
                  onPressed: () => _insertMarkdownSyntax('*', '*'),
                  tooltip: '斜体',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.format_strikethrough, size: 20),
                  onPressed: () => _insertMarkdownSyntax('~~', '~~'),
                  tooltip: '删除线',
                ),
                // 分隔线
                const VerticalDivider(width: 16, thickness: 1),
                // 列表按钮
                _buildMarkdownButton(
                  child: const Icon(Icons.format_list_bulleted, size: 20),
                  onPressed: () => _insertMarkdownSyntax('- '),
                  tooltip: '无序列表',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.format_list_numbered, size: 20),
                  onPressed: () => _insertMarkdownSyntax('1. '),
                  tooltip: '有序列表',
                ),
                // 分隔线
                const VerticalDivider(width: 16, thickness: 1),
                // 其他元素
                _buildMarkdownButton(
                  child: const Icon(Icons.code, size: 20),
                  onPressed: () => _insertCodeBlock(),
                  tooltip: '代码块',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.format_quote, size: 20),
                  onPressed: () => _insertMarkdownSyntax('> '),
                  tooltip: '引用',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.horizontal_rule, size: 20),
                  onPressed: () => _insertMarkdownSyntax('---\n'),
                  tooltip: '分隔线',
                ),
                // 插入图片和链接
                _buildMarkdownButton(
                  child: const Icon(Icons.image, size: 20),
                  onPressed: () =>
                      _insertMarkdownSyntax('![alt text](image_url)'),
                  tooltip: '插入图片',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.link, size: 20),
                  onPressed: () => _insertMarkdownSyntax('[link text](url)'),
                  tooltip: '插入链接',
                ),
                // 表格按钮
                _buildMarkdownButton(
                  child: const Icon(Icons.table_chart, size: 20),
                  onPressed: () => _insertMarkdownSyntax(
                      '| 标题1 | 标题2 | 标题3 |\n| --- | --- | --- |\n| 内容1 | 内容2 | 内容3 |\n'),
                  tooltip: '插入表格',
                ),
              ],
            ),
          ),
          // 显示Tab键提示，当AI功能启用时
          if (_aiService.isSmartSuggestionsEnabled)
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    size: 14,
                    color: AppTheme.primaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '输入时按Tab键接受智能预测',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  // 构建Markdown工具栏按钮
  Widget _buildMarkdownButton({
    required Widget child,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(4),
        child: Tooltip(
          message: tooltip,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: child,
          ),
        ),
      ),
    );
  }

  // 在光标位置插入Markdown语法
  void _insertMarkdownSyntax(String prefix, [String suffix = '']) {
    // 获取当前笔记类型
    final contentType = widget.note?.contentType ?? _selectedContentType;

    if (contentType == 'markdown') {
      // 如果是Markdown编辑器，直接在TextField中插入
      if (_markdownEditorKey.currentState != null) {
        final textController =
            _markdownEditorKey.currentState!.getTextController();
        final selection = textController.selection;

        if (selection.isValid) {
          final text = textController.text;
          final selectedText = selection.textInside(text);

          if (selectedText.isNotEmpty) {
            // 有选中文本，在选中文本前后添加语法
            final newText = prefix + selectedText + suffix;
            final newSelection = TextSelection.collapsed(
                offset: selection.baseOffset + newText.length);

            textController.value = TextEditingValue(
              text: text.replaceRange(selection.start, selection.end, newText),
              selection: newSelection,
            );
          } else {
            // 没有选中文本，在光标位置插入语法
            final newText = prefix + suffix;
            final newSelection = TextSelection.collapsed(
                offset: selection.baseOffset + prefix.length);

            textController.value = TextEditingValue(
              text:
                  text.replaceRange(selection.start, selection.start, newText),
              selection: newSelection,
            );
          }

          // 更新文档
          _updateDocumentFromMarkdown();

          // 强制重建状态，确保更新生效
          setState(() {});

          // 打印调试信息
          print('DEBUG: [EditorPage] 插入Markdown语法: $prefix$suffix');
        }
      }
    } else {
      // 如果是富文本编辑器，使用Fleather编辑器的API
      // 获取当前文本内容
      final content = _document.toPlainText();
      // 获取光标位置
      final selection = _fleatherController.selection;

      if (selection != null) {
        // 如果有选中文本，则在选中文本前后添加语法
        if (selection.baseOffset != selection.extentOffset) {
          final selectedText =
              content.substring(selection.baseOffset, selection.extentOffset);
          final newText = prefix + selectedText + suffix;

          // 替换选中文本
          _fleatherController.replaceText(
            selection.baseOffset,
            selection.extentOffset - selection.baseOffset,
            newText,
            selection: TextSelection.collapsed(
                offset: selection.baseOffset + newText.length),
          );
        } else {
          // 如果没有选中文本，则在光标位置插入语法
          _fleatherController.replaceText(
            selection.baseOffset,
            0,
            prefix + suffix,
            selection: TextSelection.collapsed(
                offset: selection.baseOffset + prefix.length),
          );
        }
      }
    }
  }

  // 插入代码块
  void _insertCodeBlock() {
    // 获取当前笔记类型
    final contentType = widget.note?.contentType ?? _selectedContentType;

    // 弹出语言选择对话框
    showDialog(
      context: context,
      builder: (context) {
        // 预定义的语言选项
        final languages = [
          'python',
          'javascript',
          'java',
          'c',
          'cpp',
          'csharp',
          'go',
          'rust',
          'swift',
          'kotlin',
          'php',
          'ruby',
          'sql',
          'html',
          'css',
          'xml',
          'json',
          'yaml',
          'bash',
          'plaintext',
        ];

        String selectedLanguage = 'python'; // 默认选中 Python

        return AlertDialog(
          title: const Text('选择代码语言'),
          content: Container(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 语言选项
                  ...languages.map((lang) => RadioListTile<String>(
                        title: Text(lang),
                        value: lang,
                        groupValue: selectedLanguage,
                        onChanged: (value) {
                          selectedLanguage = value!;
                          Navigator.pop(context, selectedLanguage);
                        },
                      )),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, selectedLanguage),
              child: const Text('确定'),
            ),
          ],
        );
      },
    ).then((language) {
      if (language == null) return; // 用户取消

      // 根据选择的语言生成代码块模板
      String codeTemplate = '';

      // 根据语言生成示例代码
      switch (language) {
        case 'python':
          codeTemplate =
              "def hello():\n    print(\"Hello, World!\")\n\nhello()";
          break;
        case 'javascript':
          codeTemplate =
              "function hello() {\n    console.log(\"Hello, World!\");\n}\n\nhello();";
          break;
        case 'java':
          codeTemplate =
              "public class Hello {\n    public static void main(String[] args) {\n        System.out.println(\"Hello, World!\");\n    }\n}";
          break;
        case 'c':
          codeTemplate =
              "#include <stdio.h>\n\nint main() {\n    printf(\"Hello, World!\\n\");\n    return 0;\n}";
          break;
        case 'cpp':
          codeTemplate =
              "#include <iostream>\n\nint main() {\n    std::cout << \"Hello, World!\" << std::endl;\n    return 0;\n}";
          break;
        default:
          codeTemplate = "// 在此处输入代码";
          break;
      }

      // 构建完整的代码块
      final codeBlock = "```$language\n$codeTemplate\n```";

      print('DEBUG: [EditorPage] 插入代码块: $language');
      print(
          'DEBUG: [EditorPage] 代码块内容: "${codeBlock.replaceAll('\n', '\\n')}"');

      // 插入代码块
      if (contentType == 'markdown') {
        // 如果是Markdown编辑器
        if (_markdownEditorKey.currentState != null) {
          final textController =
              _markdownEditorKey.currentState!.getTextController();
          final selection = textController.selection;

          if (selection.isValid) {
            final text = textController.text;

            // 确保代码块前后有空行
            String newText;
            if (selection.baseOffset > 0 &&
                text[selection.baseOffset - 1] != '\n') {
              newText = "\n$codeBlock\n";
            } else {
              newText = "$codeBlock\n";
            }

            // 插入代码块
            textController.value = TextEditingValue(
              text:
                  text.replaceRange(selection.start, selection.start, newText),
              selection: TextSelection.collapsed(
                  offset: selection.baseOffset + newText.length),
            );

            // 更新文档
            _updateDocumentFromMarkdown();

            // 强制重建状态
            setState(() {});
          }
        }
      } else {
        // 如果是富文本编辑器
        final selection = _fleatherController.selection;

        if (selection != null) {
          // 确保代码块前后有空行
          _fleatherController.replaceText(
            selection.baseOffset,
            0,
            "$codeBlock\n",
            selection: TextSelection.collapsed(
                offset: selection.baseOffset + codeBlock.length + 1),
          );
        }
      }
    });
  }

  // 构建富文本工具栏
  Widget _buildRichTextToolbar() {
    // 判断是否为Web环境
    if (kIsWeb) {
      // Web环境下使用原有的工具栏实现
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          border: Border(
            top: BorderSide(
              color: Theme.of(context).dividerColor,
              width: 1,
            ),
          ),
        ),
        child: Column(
          children: [
            // 使用基本工具栏
            FleatherToolbar.basic(controller: _fleatherController),
            // 添加自定义按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 自定义按钮：插入图片
                IconButton(
                  icon: const Icon(Icons.image, size: 20),
                  onPressed: _insertImage,
                  tooltip: '插入图片',
                ),
                // 自定义按钮：插入表格
                IconButton(
                  icon: const Icon(Icons.table_chart, size: 20),
                  onPressed: _insertTable,
                  tooltip: '插入表格',
                ),
                // 自定义按钮：AI辅助 - 移除这里的按钮，改为悬浮在右侧
              ],
            ),
            // 显示Tab键提示，当AI功能启用时
            if (_aiService.isSmartSuggestionsEnabled)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      size: 14,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '输入时按Tab键接受智能预测',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.primaryColor,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      );
    } else {
      // 移动设备环境下使用滚动工具栏
      return LayoutBuilder(
        builder: (context, constraints) {
          // 获取屏幕宽度，用于响应式布局
          final screenWidth = MediaQuery.of(context).size.width;
          final isSmallScreen = screenWidth < 600; // 定义小屏幕的阈值

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                // 使用SingleChildScrollView包装工具栏，使其可以在小屏幕上滚动
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    // 在小屏幕上减小内边距
                    padding:
                        EdgeInsets.symmetric(horizontal: isSmallScreen ? 4 : 8),
                    // 使用FleatherToolbar.basic
                    child:
                        FleatherToolbar.basic(controller: _fleatherController),
                  ),
                ),
                // 添加自定义按钮 - 使用Wrap替代Row，实现自动换行
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    spacing: 8, // 水平间距
                    runSpacing: 8, // 垂直间距
                    children: [
                      // 自定义按钮：插入图片
                      IconButton(
                        icon: const Icon(Icons.image, size: 20),
                        onPressed: _insertImage,
                        tooltip: '插入图片',
                        constraints: BoxConstraints.tightFor(
                            width: 40, height: 40), // 固定大小
                      ),
                      // 自定义按钮：插入表格
                      IconButton(
                        icon: const Icon(Icons.table_chart, size: 20),
                        onPressed: _insertTable,
                        tooltip: '插入表格',
                        constraints: BoxConstraints.tightFor(
                            width: 40, height: 40), // 固定大小
                      ),
                      // 自定义按钮：AI辅助 - 移除这里的按钮，改为悬浮在右侧
                    ],
                  ),
                ),
                // 显示Tab键提示，当AI功能启用时
                if (_aiService.isSmartSuggestionsEnabled)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          size: 14,
                          color: AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '输入时按Tab键接受智能预测',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.primaryColor,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        },
      );
    }
  }

  // 阅读模式
  Widget _buildReadingMode({Key? key}) {
    return SafeArea(
      key: key,
      child: Column(
        children: [
          _buildReadingHeader(),
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AnimatedOpacity(
                      opacity: 1.0,
                      duration: const Duration(milliseconds: 500),
                      child: Text(
                        _titleController.text.isEmpty
                            ? '无标题'
                            : _titleController.text,
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).textTheme.titleLarge?.color,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 16,
                            color: Theme.of(context).textTheme.bodySmall?.color,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '最后编辑: ${DateTimeHelper.formatDateTime(widget.note?.updatedAt ?? DateTime.now())}',
                            style: TextStyle(
                              fontSize: 14,
                              color:
                                  Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (_tags.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.only(top: 12, bottom: 20),
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _tags
                              .map((tag) => _buildReadingTag(tag))
                              .toList(),
                        ),
                      ),
                    const SizedBox(height: 12),
                    // 根据笔记类型显示不同的内容
                    AnimatedOpacity(
                      opacity: 1.0,
                      duration: const Duration(milliseconds: 700),
                      child: _getContentTypeForReading() == 'markdown'
                          ? _buildMarkdownContent()
                          : FleatherEditor(
                              controller: _fleatherController,
                              focusNode: FocusNode(),
                              readOnly: true,
                              padding: EdgeInsets.zero,
                              enableInteractiveSelection: true,
                              scrollable: false,
                              expands: false,
                              embedBuilder: _embedBuilder,
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          _buildReadingFooter(),
        ],
      ),
    );
  }

  // 构建Markdown内容显示
  Widget _buildMarkdownContent() {
    // 获取Markdown内容，始终使用文档对象中的内容以确保显示最新编辑
    String markdownContent = _document.toPlainText();
    print(
        'DEBUG: [EditorPage] 使用文档对象内容渲染Markdown: ${markdownContent.length} 字节');

    // 处理Markdown内容以正确渲染
    String processedContent = markdownContent;

    print('DEBUG: [EditorPage] 处理后的Markdown内容长度: ${processedContent.length}');

    // 使用flutter_markdown包渲染Markdown内容
    return Container(
      width: double.infinity,
      child: MarkdownBody(
        data: processedContent,
        selectable: true,
        softLineBreak: true, // 将单个换行视为硬换行
        builders: {
          'table': CustomTableBuilder(),
          'code': CodeBlockBuilder(),
          'blockquote': BlockquoteBuilder(),
        },
        extensionSet: md.ExtensionSet.gitHubWeb,
        onTapLink: (text, href, title) {
          if (href != null) {
            // 处理链接点击
            launchUrl(Uri.parse(href));
          }
        },
        styleSheet: MarkdownStyleSheet(
          h1: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          h2: const TextStyle(
            fontSize: 22,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          h3: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          h4: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          h5: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          h6: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          p: const TextStyle(
            fontSize: 16,
            height: 1.5,
          ),
          code: const TextStyle(
            backgroundColor: Color(0xFFf7f7f7),
            fontFamily: 'monospace',
            fontSize: 14,
          ),
          codeblockDecoration: BoxDecoration(
            color: const Color(0xFFf7f7f7),
            borderRadius: BorderRadius.circular(4),
          ),
          blockquote: const TextStyle(
            color: Colors.grey,
            fontStyle: FontStyle.italic,
          ),
          blockquoteDecoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(4),
            border: Border(
              left: BorderSide(
                color: AppTheme.primaryColor,
                width: 4,
              ),
            ),
          ),
          tableHead: const TextStyle(fontWeight: FontWeight.bold),
          tableBody: const TextStyle(),
          tableBorder: TableBorder.all(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
      ),
    );
  }

  Widget _buildReadingHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.arrow_back,
                color: Theme.of(context).iconTheme.color,
                size: 20,
              ),
            ),
          ),

          // 标题
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                _titleController.text,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).textTheme.titleLarge?.color,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          Row(
            children: [
              InkWell(
                onTap: _toggleMode,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.edit,
                    color: Theme.of(context).iconTheme.color,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              InkWell(
                onTap: _toggleFavorite,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _isFavorite ? Icons.star : Icons.star_border,
                    color: _isFavorite ? Colors.orange : AppTheme.darkGrayColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              InkWell(
                onTap: _shareNote,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.share,
                    color: AppTheme.darkGrayColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              InkWell(
                onTap: _showMoreOptions,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.more_vert,
                    color: AppTheme.darkGrayColor,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // _buildAIPrediction 方法已移除，其内容已整合到 DraggableAIPredictionBar widget 中
  // 并且 DraggableAIPredictionBar 的实例化已移至 _buildEditorContent 方法内

  Widget _buildReadingFooter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          top: BorderSide(color: Theme.of(context).dividerColor),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 1,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: _toggleFavorite,
                icon: Icon(
                  _isFavorite ? Icons.star : Icons.star_border,
                  color: _isFavorite ? Colors.orange : AppTheme.darkGrayColor,
                ),
                tooltip: _isFavorite ? '取消收藏' : '收藏',
              ),
              IconButton(
                onPressed: () {
                  // 直接调用_shareNote方法，与上方分享按钮保持一致
                  _shareNote();
                },
                icon: Icon(Icons.share, color: AppTheme.darkGrayColor),
                tooltip: '分享',
              ),
            ],
          ),
          ElevatedButton.icon(
            onPressed: () {
              setState(() {
                _isEditing = true;
              });
            },
            icon: const Icon(Icons.edit),
            label: const Text('编辑'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareOption(IconData icon, String label, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppTheme.lightGrayColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(label),
          ],
        ),
      ),
    );
  }

  Widget _buildReadingTag(String tagId) {
    final tag = _getTagById(tagId);
    if (tag == null) return Container(); // 如果标签不存在，返回空容器

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: tag.colorValue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.label,
            size: 16,
            color: tag.colorValue,
          ),
          const SizedBox(width: 4),
          Text(
            tag.name,
            style: TextStyle(
              color: tag.colorValue,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // 分享笔记
  void _shareNote() {
    if (widget.note == null) {
      // 如果是新笔记，先保存
      _saveNote();
      return;
    }

    Navigator.pushNamed(
      context,
      AppRoutes.shareNote,
      arguments: widget.note,
    );
  }

  // 显示更多选项
  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(
                  Icons.delete,
                  color: Colors.red,
                ),
                title: const Text('删除笔记'),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmation();
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.copy,
                  color: AppTheme.darkGrayColor,
                ),
                title: const Text('复制内容'),
                onTap: () {
                  Navigator.pop(context);
                  _copyContent();
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.star,
                  color: AppTheme.darkGrayColor,
                ),
                title: Text(_isFavorite ? '取消收藏' : '收藏笔记'),
                onTap: () {
                  Navigator.pop(context);
                  _toggleFavorite();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('删除笔记'),
          content: const Text('确定要删除这个笔记吗？此操作不可撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _deleteNote();
              },
              child: const Text(
                '删除',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  // 切换编辑/阅读模式
  void _toggleMode() {
    // 如果是Markdown格式，确保在切换到阅读模式前更新文档内容
    if (widget.note?.contentType == 'markdown' ||
        _selectedContentType == 'markdown') {
      _updateDocumentFromMarkdown();
    }

    setState(() {
      _isEditing = !_isEditing;

      // 切换到阅读模式时，关闭AI菜单和预测
      if (!_isEditing) {
        _showAIMenu = false;
        _showAIPrediction = false;
        _showAIHint = false;
      }
    });
  }

  // 清除图片缓存
  void _clearImageCache() {
    print('DEBUG: [EditorPage] 清除图片缓存');
    _imageCache.clear();
  }

  // 选择的内容类型，默认为富文本
  String _selectedContentType = 'rich-text';

  // 获取阅读模式下的内容类型
  String _getContentTypeForReading() {
    // 如果是现有笔记，使用笔记的内容类型
    if (widget.note != null) {
      return widget.note!.contentType;
    }
    // 如果是新笔记，使用选择的内容类型
    return _selectedContentType;
  }

  // 根据笔记类型获取相应的编辑器组件
  Widget _getEditorWidget() {
    // 如果是编辑现有笔记，使用笔记的内容类型
    final contentType = widget.note?.contentType ?? _selectedContentType;

    if (contentType == 'markdown') {
      // 使用Markdown编辑器
      return MarkdownEditor(
        key: _markdownEditorKey,
        initialContent: _document.toPlainText(),
        focusNode: _contentFocusNode,
        readOnly: false,
        onChanged: (content) {
          // 更新文档内容
          // 注意：不直接使用content，而是使用getContent方法确保以换行结尾
          _updateDocumentFromMarkdown();

          // 生成光标预测
          _handleAIPredictionTrigger();
        },
      );
    } else {
      // 使用Fleather富文本编辑器
      return FleatherEditor(
        controller: _fleatherController,
        focusNode: _contentFocusNode,
        padding: const EdgeInsets.all(16),
        scrollable: true,
        readOnly: false,
        expands: false,
        showCursor: true,
        enableInteractiveSelection: true,
        embedBuilder: _embedBuilder,
      );
    }
  }

  // 从 Markdown 编辑器更新文档
  void _updateDocumentFromMarkdown() {
    if (_markdownEditorKey.currentState != null) {
      final content = _markdownEditorKey.currentState!.getContent();
      print('DEBUG: [EditorPage] 从 Markdown 编辑器更新文档，原始内容长度: ${content.length}');

      // 确保内容以换行结尾
      String finalContent = content.endsWith('\n') ? content : content + '\n';

      // 保存前处理空行，避免影响排序
      // 将连续的多个空行替换为一个空行，但保留单个空行
      finalContent = finalContent.replaceAll(RegExp(r'\n{3,}'), '\n\n');

      // 确保文档内容不为空
      if (finalContent.trim().isEmpty) {
        finalContent = '\n';
      }

      print('DEBUG: [EditorPage] 处理后的内容长度: ${finalContent.length}');

      // 创建Delta并更新文档
      final delta = Delta()..insert(finalContent);
      _document = ParchmentDocument.fromDelta(delta);

      // 打印调试信息
      print(
          'DEBUG: [EditorPage] 文档更新成功，最终内容长度: ${_document.toPlainText().length}');
    } else {
      print('ERROR: [EditorPage] 无法更新Markdown文档，编辑器实例不可用');
    }
  }

  // 处理Markdown内容以正确渲染
  String _processMarkdownForRendering(String content) {
    // 先保护特殊结构，防止被换行处理破坏
    // 1. 保护表格
    List<String> tables = [];
    final tableRegex = RegExp(
        r'\|[^\n]*\|[\s]*\n[\s]*\|[-:| ]+\|[\s]*\n([\s]*\|[^\n]*\|[\s]*\n)*',
        multiLine: true);

    content = content.replaceAllMapped(tableRegex, (match) {
      tables.add(match.group(0)!);
      return "TABLE_PLACEHOLDER_${tables.length - 1}";
    });

    // 2. 保护代码块
    List<String> codeBlocks = [];
    // 使用更精确的正则表达式来匹配代码块
    // 匹配```开始和```结束的代码块，包括可能的语言标记
    // 使用非贪婪模式匹配，确保正确匹配嵌套的代码块
    final codeBlockRegex =
        RegExp(r'```([a-zA-Z0-9]*)\s*[\s\S]*?```', multiLine: true);

    content = content.replaceAllMapped(codeBlockRegex, (match) {
      String codeBlock = match.group(0)!;
      String? language = match.group(1);
      print('DEBUG: [EditorPage] 找到代码块: ${codeBlock.length} 字节, 语言: $language');
      print(
          'DEBUG: [EditorPage] 代码块内容: "${codeBlock.replaceAll('\n', '\\n')}"');
      codeBlocks.add(codeBlock);
      return "CODE_PLACEHOLDER_${codeBlocks.length - 1}";
    });

    // 3. 保护列表
    List<String> lists = [];
    final listRegex =
        RegExp(r'(^|\n)([ ]*[-*+][ ].+[\s\S]*?)(?=\n[^ -]|$)', multiLine: true);

    content = content.replaceAllMapped(listRegex, (match) {
      lists.add(match.group(0)!);
      return "LIST_PLACEHOLDER_${lists.length - 1}";
    });

    // 4. 保护有序列表
    List<String> orderedLists = [];
    final orderedListRegex =
        RegExp(r'(^|\n)([ ]*\d+\.[ ].+[\s\S]*?)(?=\n[^\d]|$)', multiLine: true);

    content = content.replaceAllMapped(orderedListRegex, (match) {
      orderedLists.add(match.group(0)!);
      return "ORDERED_LIST_PLACEHOLDER_${orderedLists.length - 1}";
    });

    // 5. 保护空行序列
    List<String> emptyLines = [];
    // 匹配连续的空行（两个或更多）
    final emptyLineRegex = RegExp(r'\n{2,}', multiLine: true);

    content = content.replaceAllMapped(emptyLineRegex, (match) {
      emptyLines.add(match.group(0)!);
      return "EMPTY_LINE_PLACEHOLDER_${emptyLines.length - 1}";
    });

    // 处理换行，确保每个段落之间有空行
    // 对于标准 Markdown 渲染，我们需要确保段落之间有空行
    // 将单个换行替换为两个换行，但保留空行占位符
    content = content.replaceAll('\n', '\n\n');

    // 恢复特殊结构
    // 1. 恢复表格
    for (int i = 0; i < tables.length; i++) {
      content = content.replaceAll("TABLE_PLACEHOLDER_$i", tables[i]);
    }

    // 2. 恢复代码块
    for (int i = 0; i < codeBlocks.length; i++) {
      content = content.replaceAll("CODE_PLACEHOLDER_$i", codeBlocks[i]);
    }

    // 3. 恢复列表
    for (int i = 0; i < lists.length; i++) {
      content = content.replaceAll("LIST_PLACEHOLDER_$i", lists[i]);
    }

    // 4. 恢复有序列表
    for (int i = 0; i < orderedLists.length; i++) {
      content =
          content.replaceAll("ORDERED_LIST_PLACEHOLDER_$i", orderedLists[i]);
    }

    // 5. 恢复空行
    for (int i = 0; i < emptyLines.length; i++) {
      // 保留原始的空行数量，不进行压缩
      content = content.replaceAll("EMPTY_LINE_PLACEHOLDER_$i", emptyLines[i]);
    }

    // 确保以换行结尾
    if (!content.endsWith('\n')) {
      content += '\n';
    }

    // 移除可能导致问题的尾部数字0
    if (content.endsWith('0\n')) {
      content = content.substring(0, content.length - 2) + '\n';
    }

    // 移除任何单独的数字0
    content = content.replaceAll(RegExp(r'\n0\n'), '\n\n');

    print('DEBUG: [EditorPage] 处理后的渲染内容长度: ${content.length}');
    return content;
  }

  // 保存笔记
  void _saveNote() async {
    // 如果是Markdown编辑器，先更新文档
    if (_selectedContentType == 'markdown') {
      _updateDocumentFromMarkdown();
    }

    final content = _document.toPlainText().trim();

    if (content.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入内容')),
      );
      return;
    }

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);

    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    print('DEBUG: 开始保存笔记内容');

    try {
      Note? savedNote;

      if (widget.note != null) {
        // 更新现有笔记的情况
        String processedContent;

        // 根据笔记类型处理内容
        if (widget.note!.contentType == 'markdown') {
          // Markdown格式直接使用纯文本内容
          processedContent = _document.toPlainText();
        } else {
          // 富文本格式需要处理Delta JSON
          // 先处理内容中的本地图片
          final updatedDocument =
              await _processLocalImages(_document, noteId: widget.note!.id);
          final jsonData = updatedDocument.toJson();
          processedContent = jsonEncode(jsonData);
        }

        // 确保initialTagId在_tags中
        if (widget.initialTagId != null &&
            widget.initialTagId!.isNotEmpty &&
            !_tags.contains(widget.initialTagId)) {
          _tags.add(widget.initialTagId!);
          print('DEBUG: 保存前添加初始标签ID: ${widget.initialTagId}');
        }

        // 更新笔记
        savedNote = await noteProvider.updateNote(
          id: widget.note!.id,
          title: _titleController.text,
          content: processedContent,
          contentType: widget.note!.contentType, // 保持原有格式
          tags: _tags,
        );
      } else {
        // 创建新笔记的情况
        // 根据选择的格式类型处理内容
        if (_selectedContentType == 'markdown') {
          // Markdown格式直接创建笔记
          final content = _document.toPlainText();
          savedNote = await noteProvider.createNote(
            title: _titleController.text,
            content: content,
            contentType: _selectedContentType,
            tags: _tags,
          );
        } else {
          // 富文本格式需要处理Delta JSON
          // 1. 先创建一个基本的笔记以获取ID
          final initialContent = _document.toPlainText(); // 先用纯文本内容
          savedNote = await noteProvider.createNote(
            title: _titleController.text,
            content: initialContent,
            contentType: _selectedContentType,
            tags: _tags,
          );

          if (savedNote != null) {
            // 2. 处理内容中的本地图片，使用新创建的笔记ID
            final updatedDocument =
                await _processLocalImages(_document, noteId: savedNote.id);
            final jsonData = updatedDocument.toJson();
            final processedContent = jsonEncode(jsonData);

            // 3. 更新笔记内容，包含处理后的图片
            savedNote = await noteProvider.updateNote(
              id: savedNote.id,
              title: _titleController.text,
              content: processedContent,
              contentType: _selectedContentType,
              tags: _tags,
              isCompletingInitialSave: true,
            );
          }
        }
      }

      // 检查组件是否已经被卸载
      if (!mounted) return;

      // 关闭加载对话框
      Navigator.pop(context);

      if (savedNote != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('笔记保存成功')),
        );

        // 返回上一页
        Navigator.pop(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: ${noteProvider.error}')),
        );
      }
    } catch (e) {
      // 检查组件是否已经被卸载
      if (!mounted) return;

      // 关闭加载对话框
      Navigator.pop(context);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('保存失败: $e')),
      );
    }
  }

  // 显示格式选择对话框
  Future<bool> _showFormatSelectionDialog() async {
    print('DEBUG: 显示格式选择对话框，当前选中格式: $_selectedContentType');

    // 使用StatefulBuilder来管理对话框内部状态
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: true, // 允许点击外部关闭对话框
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            print('DEBUG: 对话框重建，当前选中格式: $_selectedContentType');

            return AlertDialog(
              title: const Text('选择笔记格式'),
              content: SingleChildScrollView(
                child: ListBody(
                  children: <Widget>[
                    const Text('请选择笔记的格式类型，这将决定笔记的存储和显示方式。'),
                    const SizedBox(height: 16),
                    _buildFormatOptionInDialog(
                      title: '富文本格式',
                      description: '支持图片、表格等复杂格式，适合复杂笔记',
                      icon: Icons.text_fields,
                      value: 'rich-text',
                      onSelected: () {
                        print('DEBUG: 选择富文本格式');
                        setDialogState(() {
                          _selectedContentType = 'rich-text';
                        });
                      },
                      isSelected: _selectedContentType == 'rich-text',
                    ),
                    const SizedBox(height: 8),
                    _buildFormatOptionInDialog(
                      title: 'Markdown格式',
                      description: '使用Markdown语法，适合纯文本笔记和代码片段',
                      icon: Icons.code,
                      value: 'markdown',
                      onSelected: () {
                        print('DEBUG: 选择Markdown格式');
                        setDialogState(() {
                          _selectedContentType = 'markdown';
                        });
                      },
                      isSelected: _selectedContentType == 'markdown',
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: const Text('取消'),
                  onPressed: () {
                    print('DEBUG: 点击取消按钮');
                    Navigator.of(context).pop(false); // 返回false表示取消
                  },
                ),
                TextButton(
                  child: const Text('确定'),
                  onPressed: () {
                    print('DEBUG: 点击确定按钮，选中格式: $_selectedContentType');
                    Navigator.of(context).pop(true); // 返回true表示确认
                  },
                ),
              ],
            );
          },
        );
      },
    );

    print('DEBUG: 对话框关闭，结果: $result, 选中格式: $_selectedContentType');
    return result ?? false; // 如果用户点击外部关闭，返回false
  }

  // 在对话框中构建格式选项
  Widget _buildFormatOptionInDialog({
    required String title,
    required String description,
    required IconData icon,
    required String value,
    required VoidCallback onSelected,
    required bool isSelected,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onSelected,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected
                ? AppTheme.primaryColor.withOpacity(0.1)
                : Colors.transparent,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      isSelected ? AppTheme.primaryColor : Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: isSelected ? Colors.white : Colors.grey.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color:
                            isSelected ? AppTheme.primaryColor : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建格式选项
  Widget _buildFormatOption({
    required String title,
    required String description,
    required IconData icon,
    required String value,
  }) {
    final bool isSelected = _selectedContentType == value;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedContentType = value;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected
                ? AppTheme.primaryColor.withOpacity(0.1)
                : Colors.transparent,
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      isSelected ? AppTheme.primaryColor : Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: isSelected ? Colors.white : Colors.grey.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color:
                            isSelected ? AppTheme.primaryColor : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理文档中的本地图片，上传并替换引用
  Future<ParchmentDocument> _processLocalImages(ParchmentDocument document,
      {String? noteId}) async {
    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    final delta = document.toDelta();
    bool hasLocalImages = false;

    print('DEBUG: [EditorPage] 开始处理本地图片');
    print('DEBUG: [EditorPage] 笔记内容包含 ${delta.length} 个操作');

    if (noteId != null) {
      print('DEBUG: [EditorPage] 将上传图片到笔记ID子目录: $noteId');
    }

    // 创建一个新的delta用于构建更新后的文档
    final updatedDelta = Delta();

    // 遍历文档中的所有操作
    for (var i = 0; i < delta.length; i++) {
      final operation = delta.elementAt(i);

      if (operation.isInsert && operation.data is Map) {
        final Map data = operation.data as Map;
        print(
            'DEBUG: [EditorPage] 检查操作 #$i: ${data.toString().substring(0, min(50, data.toString().length))}...');

        // 直接检查是否包含source_type和source字段 - 图片数据的存储结构可能多种多样
        if (data.containsKey('source_type') && data.containsKey('source')) {
          final sourceType = data['source_type'] as String?;
          final source = data['source'] as String?;

          print(
              'DEBUG: [EditorPage] 直接找到图片数据 - 类型: $sourceType, 源: ${source?.substring(0, min(50, source?.length ?? 0))}...');

          // 处理本地图片，统一处理不区分平台
          // 添加检查isLocal和uploadPending标记
          final bool isLocalImage =
              (data['isLocal'] == true || data['uploadPending'] == true) ||
                  (source != null &&
                      (sourceType == 'local_file' ||
                          sourceType == 'local_web' ||
                          (sourceType == null && source.startsWith('data:'))));

          if (isLocalImage) {
            hasLocalImages = true;
            print('DEBUG: [EditorPage] 确认发现本地图片: $sourceType');

            try {
              Map<String, dynamic> result;
              dynamic imageFile;

              // 准备上传的文件数据，统一处理
              if ((sourceType == 'local_web' || sourceType == null) &&
                  source!.startsWith('data:')) {
                print('DEBUG: [EditorPage] 处理Web平台base64图片');
                // 从data URI中提取base64图片数据
                final parts = source.split(',');
                if (parts.length < 2) {
                  throw Exception('无效的data URI格式');
                }
                final base64Data = parts[1];
                imageFile = base64Decode(base64Data);
                print(
                    'DEBUG: [EditorPage] Base64图片转换成功，大小: ${imageFile.length} 字节');
              } else if (sourceType == 'local_file') {
                // 移动平台本地文件
                print('DEBUG: [EditorPage] 处理移动平台文件: $source');
                imageFile = File(source!);
              } else if (sourceType == 'url' &&
                  (data['isLocal'] == true || data['uploadPending'] == true)) {
                // 已经是URL但仍然标记为本地或待上传 - 保持URL不变，只移除标记
                print('DEBUG: [EditorPage] 图片已经是URL格式，仅移除临时标记: $source');

                // 创建新数据，移除临时标记
                final updatedData = Map<String, dynamic>.from(data);
                updatedData.remove('isLocal');
                updatedData.remove('uploadPending');

                final updatedOperation = Operation.insert(updatedData);
                updatedDelta.push(updatedOperation);
                continue;
              } else {
                throw Exception('不支持的图片格式: $sourceType - $source');
              }

              // 统一调用NoteProvider上传图片，传递笔记ID
              print(
                  'DEBUG: [EditorPage] 开始上传图片到服务器${noteId != null ? "的笔记子目录 $noteId" : ""}...');
              result =
                  await noteProvider.uploadNoteImage(imageFile, noteId: noteId);

              // 检查组件是否已经被卸载
              if (!mounted) return ParchmentDocument.fromDelta(delta);

              print('DEBUG: [EditorPage] 上传结果: $result');

              if (result['success'] == true && result['data'] != null) {
                final imageUrl = result['data']['url'];
                print('DEBUG: [EditorPage] 图片上传成功: $imageUrl');

                // 更新嵌入数据结构，保留原始结构但修改必要字段
                final updatedData = Map<String, dynamic>.from(data);
                updatedData['source_type'] = 'url';
                updatedData['source'] = imageUrl;

                // 移除临时标记
                updatedData.remove('isLocal');
                updatedData.remove('uploadPending');

                // 创建更新后的操作
                final updatedOperation = Operation.insert(updatedData);

                updatedDelta.push(updatedOperation);
                continue; // 已处理此操作，继续下一个
              } else {
                final errorMessage = result['error']?['message'] ?? '上传失败';
                print('ERROR: [EditorPage] 图片上传失败: $errorMessage');

                // 使用原始操作，保持图片不变
                updatedDelta.push(operation);
              }
            } catch (e) {
              print('ERROR: [EditorPage] 处理图片时出错: $e');

              // 出错时保持原始操作，不丢失图片
              updatedDelta.push(operation);
            }
          } else {
            // 保持原始操作不变
            updatedDelta.push(operation);
          }
        } else if (data.containsKey('embed')) {
          // 检查嵌入式数据中的图片
          final embedData = data['embed'];
          print(
              'DEBUG: [EditorPage] 检查嵌入数据: ${embedData.toString().substring(0, min(50, embedData.toString().length))}...');

          if (embedData is Map &&
              embedData['type'] == 'image' &&
              embedData.containsKey('data')) {
            final imageData = embedData['data'] as Map;
            final sourceType = imageData['source_type'] as String?;
            final source = imageData['source'] as String?;

            print(
                'DEBUG: [EditorPage] 嵌入图片数据 - 类型: $sourceType, 源: ${source?.substring(0, min(50, source?.length ?? 0))}...');

            // 处理本地图片，统一处理不区分平台
            // 检查是否有临时标记
            final bool isLocalEmbedImage = (imageData['isLocal'] == true ||
                    imageData['uploadPending'] == true) ||
                (source != null &&
                    (sourceType == 'local_file' ||
                        sourceType == 'local_web' ||
                        (sourceType == null && source.startsWith('data:'))));

            if (isLocalEmbedImage) {
              hasLocalImages = true;
              print('DEBUG: [EditorPage] 确认发现嵌入式本地图片: $sourceType');

              try {
                Map<String, dynamic> result;
                dynamic imageFile;

                // 准备上传的文件数据，统一处理
                if ((sourceType == 'local_web' || sourceType == null) &&
                    source!.startsWith('data:')) {
                  print('DEBUG: [EditorPage] 处理Web平台base64图片');
                  // 从data URI中提取base64图片数据
                  final parts = source.split(',');
                  if (parts.length < 2) {
                    throw Exception('无效的data URI格式');
                  }
                  final base64Data = parts[1];
                  imageFile = base64Decode(base64Data);
                  print(
                      'DEBUG: [EditorPage] Base64图片转换成功，大小: ${imageFile.length} 字节');
                } else if (sourceType == 'local_file') {
                  // 移动平台本地文件
                  print('DEBUG: [EditorPage] 处理移动平台文件: $source');
                  imageFile = File(source!);
                } else if (sourceType == 'url' &&
                    (imageData['isLocal'] == true ||
                        imageData['uploadPending'] == true)) {
                  // 已经是URL但仍然标记为本地或待上传 - 保持URL不变，只移除标记
                  print('DEBUG: [EditorPage] 嵌入图片已经是URL格式，仅移除临时标记: $source');

                  // 创建新的图片数据，移除临时标记
                  final newImageData = Map<String, dynamic>.from(imageData);
                  newImageData.remove('isLocal');
                  newImageData.remove('uploadPending');

                  // 创建更新后的操作
                  final updatedOperation = Operation.insert({
                    'embed': {'type': 'image', 'data': newImageData}
                  });

                  updatedDelta.push(updatedOperation);
                  continue;
                } else {
                  throw Exception('不支持的图片格式: $sourceType - $source');
                }

                // 统一调用NoteProvider上传图片，传递笔记ID
                print(
                    'DEBUG: [EditorPage] 开始上传图片到服务器${noteId != null ? "的笔记子目录 $noteId" : ""}...');
                result = await noteProvider.uploadNoteImage(imageFile,
                    noteId: noteId);
                print('DEBUG: [EditorPage] 上传结果: $result');

                if (result['success'] == true && result['data'] != null) {
                  final imageUrl = result['data']['url'];
                  print('DEBUG: [EditorPage] 图片上传成功: $imageUrl');

                  // 更新embed数据，将本地图片替换为网络图片
                  final newImageData = Map<String, dynamic>.from(imageData);
                  newImageData['source_type'] = 'url';
                  newImageData['source'] = imageUrl;

                  // 移除临时标记
                  newImageData.remove('isLocal');
                  newImageData.remove('uploadPending');

                  // 创建更新后的操作
                  final updatedOperation = Operation.insert({
                    'embed': {'type': 'image', 'data': newImageData}
                  });

                  updatedDelta.push(updatedOperation);
                  continue; // 已处理此操作，继续下一个
                } else {
                  final errorMessage = result['error']?['message'] ?? '上传失败';
                  print('ERROR: [EditorPage] 图片上传失败: $errorMessage');

                  // 使用原始操作，保持图片不变
                  updatedDelta.push(operation);
                }
              } catch (e) {
                print('ERROR: [EditorPage] 处理图片时出错: $e');

                // 出错时保持原始操作，不丢失图片
                updatedDelta.push(operation);
              }
            } else {
              // 如果是网络图片，保持不变
              updatedDelta.push(operation);
            }
          } else {
            // 非图片嵌入，保持不变
            updatedDelta.push(operation);
          }
        } else {
          // 保持原始操作不变
          updatedDelta.push(operation);
        }
      } else {
        // 非Map类型的Insert操作，保持不变
        updatedDelta.push(operation);
      }
    }

    if (hasLocalImages) {
      print(
          'DEBUG: [EditorPage] 本地图片处理完成，共发现并上传 ${hasLocalImages ? "至少一张" : "0张"} 图片');
    } else {
      print('DEBUG: [EditorPage] 未发现本地图片，内容保持不变');
    }

    // 创建并返回更新后的文档
    return ParchmentDocument.fromDelta(updatedDelta);
  }

  // 添加标签
  void _addTag() async {
    final tagProvider = Provider.of<TagProvider>(context, listen: false);

    // 获取标签列表
    await tagProvider.fetchTags();
    final tags = tagProvider.tags;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('添加标签'),
          content: Container(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 已有标签列表
                if (tags.isNotEmpty) ...[
                  const Text('选择已有标签:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Container(
                    height: 150,
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: tags.length,
                      itemBuilder: (context, index) {
                        final tag = tags[index];
                        final isSelected = _tags.contains(tag.id);

                        return ListTile(
                          title: Text(tag.name),
                          leading: Icon(
                            Icons.label,
                            color: Color(
                                int.parse(tag.color.substring(1), radix: 16) +
                                    0xFF000000),
                          ),
                          trailing: isSelected ? Icon(Icons.check) : null,
                          onTap: () {
                            Navigator.pop(
                                context, {'action': 'select', 'tag': tag});
                          },
                        );
                      },
                    ),
                  ),
                  const Divider(),
                ],

                // 创建新标签选项
                ListTile(
                  title: const Text('创建新标签'),
                  leading: Icon(Icons.add),
                  onTap: () {
                    Navigator.pop(context, {'action': 'create'});
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
          ],
        );
      },
    ).then((result) async {
      if (result == null) return;

      if (result['action'] == 'select') {
        final selectedTag = result['tag'];

        if (!_tags.contains(selectedTag.id)) {
          setState(() {
            _tags.add(selectedTag.id);
          });
        }
      } else if (result['action'] == 'create') {
        _showCreateTagDialog();
      }
    });
  }

  void _showCreateTagDialog() {
    final TextEditingController tagController = TextEditingController();
    // 默认标签颜色为主题色
    String selectedColor = '#5D5FEF';

    // 预定义的颜色选项
    final List<String> colorOptions = [
      '#F44336',
      '#E91E63',
      '#9C27B0',
      '#673AB7',
      '#3F51B5',
      '#2196F3',
      '#03A9F4',
      '#00BCD4',
      '#009688',
      '#4CAF50',
      '#8BC34A',
      '#CDDC39',
      '#FFEB3B',
      '#FFC107',
      '#FF9800',
      '#FF5722',
      '#795548',
      '#9E9E9E',
      '#607D8B'
    ];

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            title: const Text('创建新标签'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: tagController,
                    decoration: const InputDecoration(
                      hintText: '输入标签名称',
                    ),
                    autofocus: true,
                  ),
                  const SizedBox(height: 16),
                  const Text('选择标签颜色:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: colorOptions.map((color) {
                      bool isSelected = color == selectedColor;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedColor = color;
                          });
                        },
                        child: Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: Color(
                                int.parse(color.substring(1), radix: 16) +
                                    0xFF000000),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? Colors.white
                                  : Colors.transparent,
                              width: 2,
                            ),
                            boxShadow: isSelected
                                ? [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.3),
                                      blurRadius: 4,
                                      spreadRadius: 1,
                                    )
                                  ]
                                : [],
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 18,
                                )
                              : null,
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () async {
                  final newTagName = tagController.text.trim();
                  if (newTagName.isNotEmpty) {
                    final tagProvider =
                        Provider.of<TagProvider>(context, listen: false);

                    // 显示加载对话框
                    showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (context) =>
                          const Center(child: CircularProgressIndicator()),
                    );

                    try {
                      // 传递颜色参数
                      final success = await tagProvider.createTag(newTagName,
                          color: selectedColor);

                      // 关闭加载对话框
                      Navigator.pop(context);

                      if (success) {
                        // 获取创建的标签ID并添加到列表
                        await tagProvider.fetchTags();
                        final newTag = tagProvider.tags.firstWhere(
                          (tag) => tag.name == newTagName,
                          orElse: () => throw Exception('Tag not found'),
                        );

                        // 使用外部setState，不是对话框的setState
                        this.setState(() {
                          _tags.add(newTag.id);
                        });

                        // 关闭创建标签对话框
                        Navigator.pop(context);

                        // 显示成功消息
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('标签"$newTagName"创建成功并添加到笔记')),
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                              content:
                                  Text('创建标签失败: ${tagProvider.errorMessage}')),
                        );
                      }
                    } catch (e) {
                      // 关闭加载对话框
                      Navigator.pop(context);

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('创建标签失败: $e')),
                      );
                    }
                  } else {
                    Navigator.pop(context);
                  }
                },
                child: const Text('创建'),
              ),
            ],
          );
        });
      },
    );
  }

  // 复制内容到剪贴板
  void _copyContent() async {
    final content = _document.toPlainText();
    await Clipboard.setData(ClipboardData(text: content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('内容已复制到剪贴板')),
    );
  }

  // 切换收藏状态
  void _toggleFavorite() async {
    if (widget.note == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先保存笔记')),
      );
      return;
    }

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);

    try {
      final success = await noteProvider.toggleFavorite(widget.note!.id);

      if (success) {
        setState(() {
          _isFavorite = !_isFavorite;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isFavorite ? '已添加到收藏' : '已取消收藏'),
            duration: const Duration(seconds: 1),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('操作失败: ${noteProvider.error}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('操作失败: $e')),
      );
    }
  }

  // 删除笔记
  void _deleteNote() async {
    if (widget.note == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('笔记未保存，无需删除')),
      );
      Navigator.pop(context);
      return;
    }

    final noteProvider = Provider.of<NoteProvider>(context, listen: false);

    // 显示加载对话框
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      final success = await noteProvider.deleteNote(widget.note!.id);

      // 关闭加载对话框
      Navigator.pop(context);

      if (success) {
        // 返回上一页
        Navigator.pop(context);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('笔记已删除')),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: ${noteProvider.error}')),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      Navigator.pop(context);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('删除失败: $e')),
      );
    }
  }

  // 添加加载标签数据的方法
  void _loadTagData() async {
    final tagProvider = Provider.of<TagProvider>(context, listen: false);
    try {
      await tagProvider.fetchTags();
      print('DEBUG: 编辑页加载标签数据成功，共 ${tagProvider.tags.length} 个标签');

      // 检查initialTagId是否存在且有效
      if (widget.initialTagId != null && widget.initialTagId!.isNotEmpty) {
        // 如果该标签ID不在当前标签列表中，添加它
        if (!_tags.contains(widget.initialTagId)) {
          setState(() {
            _tags.add(widget.initialTagId!);
          });
          print('DEBUG: 在加载标签数据后添加初始标签ID: ${widget.initialTagId}');
        }
      }

      // 重新刷新UI，确保标签显示正确
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('DEBUG: 编辑页加载标签数据失败: $e');
    }
  }

  /// 显示历史版本底部面板
  void _showHistoryBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // 允许内容滚动且高度可变
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (bottomSheetContext) {
        // 使用独立的 context
        // 使用 Consumer 监听 NoteProvider 的状态变化
        return Consumer<NoteProvider>(
          builder: (context, noteProvider, child) {
            Widget content;
            // 根据加载状态显示不同内容
            if (noteProvider.isLoadingHistory) {
              content = const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: CircularProgressIndicator(),
                ),
              );
            } else if (noteProvider.historyError.isNotEmpty) {
              content = Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text('加载失败: ${noteProvider.historyError}'),
                ),
              );
            } else if (noteProvider.noteHistory.isEmpty) {
              content = const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: Text('该笔记暂无历史版本'),
                ),
              );
            } else {
              // 显示历史版本列表
              content = ListView.separated(
                shrinkWrap: true, // 根据内容自适应高度
                itemCount: noteProvider.noteHistory.length,
                itemBuilder: (context, index) {
                  final historyItem = noteProvider.noteHistory[index];
                  // 历史记录的保存时间 - 使用DateTimeHelper工具类
                  final historyTime = historyItem.archivedAt ??
                      historyItem.updatedAt; // 优先使用archivedAt
                  final formattedTime = historyTime != null
                      ? DateTimeHelper.formatDateTime(historyTime)
                      : '未知时间';

                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                      foregroundColor: AppTheme.primaryColor,
                      child: Text(
                        '${historyItem.version}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                    title: Text(
                      '版本 ${historyItem.version}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    subtitle: Text('保存于: $formattedTime'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min, // 让 Row 紧凑
                      children: [
                        // 预览按钮
                        IconButton(
                          icon: Icon(Icons.visibility_outlined,
                              color: AppTheme.primaryColor),
                          tooltip: '预览此版本',
                          onPressed: () {
                            Navigator.pop(bottomSheetContext); // 关闭底部面板
                            _navigateToPreviewPage(context, historyItem);
                          },
                        ),
                        // 恢复按钮
                        IconButton(
                          icon: Icon(Icons.restore,
                              color: Colors.orange.shade700),
                          tooltip: '恢复到此版本',
                          onPressed: noteProvider
                                  .isRestoringVersion // 恢复过程中禁用按钮
                              ? null
                              : () {
                                  Navigator.pop(bottomSheetContext); // 关闭底部面板
                                  _showRestoreConfirmation(
                                      context, historyItem);
                                },
                        ),
                      ],
                    ),
                  );
                },
                separatorBuilder: (context, index) =>
                    const Divider(height: 1, indent: 16, endIndent: 16),
              );
            }

            // 返回包含标题和内容的 Column
            return Padding(
              // 添加一些内边距，特别是底部，避免紧贴屏幕边缘
              padding: EdgeInsets.only(
                  bottom:
                      MediaQuery.of(bottomSheetContext).viewInsets.bottom + 16,
                  top: 16,
                  left: 16,
                  right: 16),
              child: Column(
                mainAxisSize: MainAxisSize.min, // 高度自适应
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '笔记历史版本',
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Divider(),
                  const SizedBox(height: 8),
                  // 使用 LimitedBox 限制 ListView 的最大高度，避免过长
                  LimitedBox(
                    maxHeight: MediaQuery.of(context).size.height * 0.6,
                    child: content, // 显示加载、错误、空或列表内容
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// 导航到历史版本预览页面
  void _navigateToPreviewPage(BuildContext context, Note historyNote) {
    print('DEBUG: [EditorPage] 导航到历史预览，版本: ${historyNote.version}');

    // 如果当前笔记为null，不应该发生这种情况
    if (widget.note == null) {
      SnackbarHelper.showError(context: context, message: '无法预览：找不到原始笔记');
      return;
    }

    // 使用命名路由导航到历史预览页面
    Navigator.pushNamed(
      context,
      AppRoutes.historyPreview,
      arguments: {
        'historyNote': historyNote,
        'originalNoteId': widget.note!.id,
      },
    );
  }

  /// 显示恢复确认对话框
  void _showRestoreConfirmation(BuildContext context, Note historyNote) {
    // 使用DateTimeHelper工具类格式化时间
    final formattedTime = DateTimeHelper.formatDateTime(
        historyNote.archivedAt ?? historyNote.updatedAt);
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('确认恢复'),
        content: Text(
            '确定要将笔记恢复到 版本 ${historyNote.version} (保存于 $formattedTime) 吗？\n\n此操作会覆盖当前编辑器的内容，但您需要手动保存才能使更改生效。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: const Text('取消'),
          ),
          Consumer<NoteProvider>(// 使用Consumer获取恢复状态
              builder: (context, noteProvider, child) {
            return TextButton(
              onPressed: noteProvider.isRestoringVersion // 恢复过程中禁用
                  ? null
                  : () async {
                      Navigator.pop(dialogContext); // 关闭对话框
                      print(
                          'DEBUG: [EditorPage] 确认恢复到版本 ${historyNote.version}');
                      // 调用 Provider 执行恢复
                      final success = await Provider.of<NoteProvider>(context,
                              listen: false)
                          .restoreVersion(widget.note!.id, historyNote.version);

                      if (success) {
                        SnackbarHelper.showSuccess(
                            context: context,
                            message: '已恢复到版本 ${historyNote.version}，请检查内容并保存');
                        // 恢复成功后，需要更新当前编辑器的内容
                        // 注意：这只是更新本地编辑器的状态，需要用户 **手动保存** 才能持久化
                        _updateEditorWithHistory(historyNote);
                      } else {
                        // Provider 内部会处理错误提示，这里可以不显示
                        // SnackbarHelper.showError(context: context, message: '恢复失败: ${noteProvider.error}');
                      }
                    },
              style:
                  TextButton.styleFrom(foregroundColor: Colors.orange.shade700),
              child: noteProvider.isRestoringVersion
                  ? const SizedBox(
                      width: 18,
                      height: 18,
                      child: CircularProgressIndicator(strokeWidth: 2))
                  : const Text('恢复'),
            );
          }),
        ],
      ),
    );
  }

  /// 使用历史版本数据更新当前编辑器状态 (本地更新，非保存)
  void _updateEditorWithHistory(Note historyNote) {
    print('DEBUG: [EditorPage] 使用历史版本 ${historyNote.version} 更新编辑器内容');

    try {
      setState(() {
        // 更新标题
        _titleController.text = historyNote.title;

        // 更新标签
        _tags = List.from(historyNote.tagIds);

        // 切换到编辑模式，如果当前是阅读模式的话
        _isEditing = true;

        // 更新Fleather编辑器内容
        try {
          // 解析历史笔记内容为Delta JSON
          dynamic deltaJson;
          if (historyNote.content.trim().startsWith('[') ||
              historyNote.content.trim().startsWith('{')) {
            deltaJson = jsonDecode(historyNote.content);
          } else {
            // 如果是纯文本或其他格式，需要转换成Delta
            deltaJson = [
              {"insert": historyNote.content + "\n"}
            ];
          }

          // 创建新的ParchmentDocument
          _document = ParchmentDocument.fromJson(deltaJson);

          // 替换编辑器中的文档
          _fleatherController.dispose(); // 先释放旧的控制器
          _fleatherController =
              FleatherController(document: _document); // 创建新的控制器

          print('DEBUG: [EditorPage] 历史笔记内容已成功加载到编辑器');
        } catch (e) {
          print('ERROR: [EditorPage] 无法解析/加载历史内容到Fleather: $e');

          // 解析失败，加载纯文本
          _document = ParchmentDocument.fromJson([
            {"insert": historyNote.content + "\n"}
          ]);
          _fleatherController.dispose();
          _fleatherController = FleatherController(document: _document);

          SnackbarHelper.showInfo(
              context: context, message: '历史内容格式有问题，已以纯文本方式加载');
        }
      });

      // 显示成功提示
      SnackbarHelper.showSuccess(
          context: context, message: '已恢复到版本 ${historyNote.version}，请检查内容并保存');
    } catch (e) {
      print('ERROR: [EditorPage] 更新编辑器内容失败: $e');
      SnackbarHelper.showError(context: context, message: '加载历史内容失败: $e');
    }
  }

  // 处理嵌入内容的构建器
  Widget _embedBuilder(BuildContext context, EmbedNode node) {
    // 获取当前模式（编辑或阅读）
    final bool isEditingMode = _isEditing;
    // 处理图片嵌入
    if (node.value.type == 'image') {
      final sourceType = node.value.data['source_type'];
      final source = node.value.data['source'];

      // 为图片创建唯一标识符，用于Hero动画
      // 使用source和offset创建稳定的ID，避免重新加载
      final String imageId = 'img-${node.offset}-${source.hashCode}';

      // 创建图片展示组件
      return RepaintBoundary(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
              maxHeight: 300,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildImageBySourceType(sourceType, source, imageId),
            ),
          ),
        ),
      );
    }

    // 处理表格嵌入
    if (node.value.type == 'table') {
      // 提取表格数据
      final Map<String, dynamic> data =
          Map<String, dynamic>.from(node.value.data);
      final int rows = data['rows'] as int? ?? 3;
      final int columns = data['columns'] as int? ?? 3;

      // *** 提取 tableId - 使用递归方法 ***
      String? tableId = _findValueInMap(data, 'tableId');
      if (tableId != null) {
        print('DEBUG: [EditorPage] 在表格节点中找到tableId: $tableId');
      } else {
        print('WARNING: [EditorPage] 表格节点中未找到tableId');
        // 在没有ID的情况下，生成一个新ID并添加到数据中
        tableId = Uuid().v4();
        data['tableId'] = tableId;
        print('DEBUG: [EditorPage] 为表格节点生成新的tableId: $tableId');
      }

      // 获取表格数据
      List<List<String>> cellsData = [];

      // 尝试读取表格数据
      if (data.containsKey('tableData') && data['tableData'] is List) {
        final List tableData = data['tableData'] as List;

        for (int i = 0; i < min(tableData.length, rows); i++) {
          final rowData = tableData[i];
          List<String> row = [];

          if (rowData is List) {
            for (int j = 0; j < min(rowData.length, columns); j++) {
              row.add((rowData[j]?.toString() ?? ''));
            }
          }

          // 确保行长度正确
          while (row.length < columns) {
            row.add('');
          }

          cellsData.add(row);
        }
      }

      // 确保行数正确
      while (cellsData.length < rows) {
        cellsData.add(List.filled(columns, '', growable: true));
      }

      // 获取行高和列宽配置 (如果有的话)
      List<double> rowHeights = List.filled(rows, 40.0);
      List<double> columnWidths = List.filled(columns, 100.0);

      // 获取保存的行高
      if (data.containsKey('rowHeights') && data['rowHeights'] is List) {
        final List savedRowHeights = data['rowHeights'] as List;
        for (int i = 0; i < min(savedRowHeights.length, rows); i++) {
          if (savedRowHeights[i] is num) {
            rowHeights[i] = (savedRowHeights[i] as num).toDouble();
          }
        }
      }

      // 获取保存的列宽
      if (data.containsKey('columnWidths') && data['columnWidths'] is List) {
        final List savedColumnWidths = data['columnWidths'] as List;
        for (int i = 0; i < min(savedColumnWidths.length, columns); i++) {
          if (savedColumnWidths[i] is num) {
            columnWidths[i] = (savedColumnWidths[i] as num).toDouble();
          }
        }
      }

      // 使用静态的表格渲染
      return _buildSimpleTable(
        context,
        rows,
        columns,
        cellsData,
        rowHeights,
        columnWidths,
        node, // 传递节点引用
        tableId, // *** 传递 tableId ***
        isEditingMode: isEditingMode, // 传递当前模式
      );
    }

    // 使用默认的嵌入构建器处理其他类型
    return defaultFleatherEmbedBuilder(context, node);
  }

  // 根据源类型构建不同的图片组件
  // 使用静态缓存来存储已加载的图片，确保在模式切换时保持缓存
  static final Map<String, Widget> _imageCache = {};

  Widget _buildImageBySourceType(
      String? sourceType, dynamic source, String imageId) {
    // 检查缓存中是否已有该图片
    if (_imageCache.containsKey(imageId)) {
      print('DEBUG: [EditorPage] 使用缓存图片: $imageId');
      return _imageCache[imageId]!;
    }

    print('DEBUG: [EditorPage] 创建新图片: $imageId, 类型: $sourceType');
    // 创建占位图片
    Widget placeholder = Container(
      color: AppTheme.lightGrayColor.withOpacity(0.2),
      child: Center(
        child: SizedBox(
          width: 40,
          height: 40,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
      ),
    );

    // 错误占位图片
    Widget errorWidget = Container(
      width: 200,
      height: 150,
      decoration: BoxDecoration(
        color: AppTheme.lightGrayColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.mediumGrayColor),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.broken_image, color: AppTheme.darkGrayColor, size: 40),
          const SizedBox(height: 8),
          Text(
            '图片加载失败',
            style: TextStyle(color: AppTheme.darkGrayColor),
          ),
        ],
      ),
    );

    try {
      if (sourceType == 'assets') {
        // 加载资源图片
        Widget imageWidget = Image(
          image: AssetImage(source),
          fit: BoxFit.contain,
          frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
            if (wasSynchronouslyLoaded || frame != null) {
              return child;
            }
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: frame != null ? child : placeholder,
            );
          },
          errorBuilder: (context, error, stackTrace) {
            print('ERROR: [EditorPage] 资源图片加载失败: $error');
            return errorWidget;
          },
        );

        // 将图片组件添加到缓存中
        _imageCache[imageId] = imageWidget;
        return imageWidget;
      } else if (sourceType == 'file') {
        // 加载本地文件图片
        Widget imageWidget = Image.file(
          File(source),
          fit: BoxFit.contain,
          cacheWidth: 1000, // 缓存宽度，提高性能
          frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
            if (wasSynchronouslyLoaded || frame != null) {
              return child;
            }
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: frame != null ? child : placeholder,
            );
          },
          errorBuilder: (context, error, stackTrace) {
            print('ERROR: [EditorPage] 文件图片加载失败: $error');
            return errorWidget;
          },
        );

        // 将图片组件添加到缓存中
        _imageCache[imageId] = imageWidget;
        return imageWidget;
      } else if (sourceType == 'url') {
        // 加载网络图片
        Widget imageWidget = Image.network(
          source,
          fit: BoxFit.contain,
          cacheWidth: 1000, // 缓存宽度，提高性能
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) {
              return child;
            }
            return placeholder;
          },
          errorBuilder: (context, error, stackTrace) {
            print('ERROR: [EditorPage] 网络图片加载失败: $error');
            return errorWidget;
          },
        );

        // 将图片组件添加到缓存中
        _imageCache[imageId] = imageWidget;
        return imageWidget;
      } else if (sourceType == 'local_file') {
        // 加载本地文件图片 - 移动平台
        Widget imageWidget = Image.file(
          File(source),
          fit: BoxFit.contain,
          cacheWidth: 1000, // 缓存宽度，提高性能
          frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
            if (wasSynchronouslyLoaded || frame != null) {
              return child;
            }
            return AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: frame != null ? child : placeholder,
            );
          },
          errorBuilder: (context, error, stackTrace) {
            print('ERROR: [EditorPage] 本地文件图片加载失败: $error');
            return errorWidget;
          },
        );

        // 将图片组件添加到缓存中
        _imageCache[imageId] = imageWidget;
        return imageWidget;
      } else if (sourceType == 'local_web' &&
          source.toString().startsWith('data:')) {
        // 加载本地Web图片 - 使用data URI
        try {
          final bytes = UriData.parse(source).contentAsBytes();
          Widget imageWidget = Image.memory(
            bytes,
            fit: BoxFit.contain,
            cacheWidth: 1000, // 缓存宽度，提高性能
            frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
              if (wasSynchronouslyLoaded || frame != null) {
                return child;
              }
              return AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: frame != null ? child : placeholder,
              );
            },
            errorBuilder: (context, error, stackTrace) {
              print('ERROR: [EditorPage] data URI图片加载失败: $error');
              return errorWidget;
            },
          );

          // 将图片组件添加到缓存中
          _imageCache[imageId] = imageWidget;
          return imageWidget;
        } catch (e) {
          print('ERROR: [EditorPage] 解析data URI失败: $e');
          return errorWidget;
        }
      }
    } catch (e) {
      print('ERROR: [EditorPage] 图片加载异常: $e');
    }

    // 默认返回错误占位图片
    Widget resultWidget = errorWidget;

    // 将图片组件添加到缓存中
    _imageCache[imageId] = resultWidget;
    return resultWidget;
  }

  // 创建一个简单的、不可编辑的表格视图
  Widget _buildSimpleTable(
      BuildContext context,
      int rows,
      int columns,
      List<List<String>> cells,
      List<double> rowHeights,
      List<double> columnWidths,
      EmbedNode node, // 添加节点引用参数
      String? tableId, // *** 添加 tableId 参数 ***
      {bool isEditingMode = true}) {
    // 添加编辑模式参数，默认为编辑模式
    // 打印表格节点和ID信息用于调试
    print(
        'DEBUG: [EditorPage] 构建表格视图，表格ID: $tableId, 节点值类型: ${node.value.type}');

    final screenWidth = MediaQuery.of(context).size.width;

    // 计算表格总宽度（所有列宽之和）
    double totalTableWidth = columnWidths.fold(0, (sum, width) => sum + width);
    // 限制表格最大宽度
    totalTableWidth = min(totalTableWidth, screenWidth * 0.95);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Align(
        alignment: Alignment.center, // 修改为居中对齐
        child: Container(
          constraints: BoxConstraints(maxWidth: totalTableWidth),
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.lightGrayColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 表格内容
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Table(
                  border: TableBorder.all(
                    color: AppTheme.lightGrayColor,
                    width: 1,
                  ),
                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                  // 应用自定义列宽
                  columnWidths: Map.fromIterable(
                    List.generate(columns, (index) => index),
                    key: (i) => i,
                    value: (i) => FixedColumnWidth(columnWidths[i]),
                  ),
                  children: List.generate(
                    rows,
                    (i) => TableRow(
                      // 应用自定义行高
                      decoration: i == 0
                          ? BoxDecoration(
                              color: AppTheme.lightGrayColor.withOpacity(0.3))
                          : null,
                      children: List.generate(
                        columns,
                        (j) => Container(
                          height: rowHeights[i],
                          constraints: BoxConstraints(
                            minHeight: rowHeights[i],
                            maxHeight: rowHeights[i],
                          ),
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Text(
                              i < cells.length && j < cells[i].length
                                  ? cells[i][j]
                                  : '',
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              style: i == 0
                                  ? const TextStyle(fontWeight: FontWeight.bold)
                                  : null,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // 编辑按钮 - 仅在编辑模式下显示
              if (isEditingMode) // 仅在编辑模式下显示编辑按钮
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton.icon(
                        icon: const Icon(
                          Icons.edit,
                          size: 16,
                        ),
                        label: const Text('编辑表格'),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 4),
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        onPressed: () {
                          // *** 检查 tableId 是否存在 ***
                          if (tableId == null) {
                            print('ERROR: [EditorPage] 表格缺少 tableId，无法编辑');
                            SnackbarHelper.showError(
                                context: context, message: '无法编辑此表格（缺少标识符）');
                            return;
                          }

                          print('DEBUG: [EditorPage] 开始编辑表格，ID: $tableId');

                          // 尝试从节点数据中手动提取tableId作为备用
                          if (tableId == null) {
                            final extractedId =
                                _findValueInMap(node.value.data, 'tableId');
                            if (extractedId != null) {
                              print(
                                  'DEBUG: [EditorPage] 从节点手动提取到tableId: $extractedId');
                              tableId = extractedId;
                            }
                          }

                          _showTableEditDialog(
                              context,
                              rows,
                              columns,
                              cells,
                              rowHeights,
                              columnWidths,
                              node,
                              tableId!); // *** 传递 tableId ***
                        },
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示表格编辑对话框
  void _showTableEditDialog(
    BuildContext context,
    int rows,
    int columns,
    List<List<String>> initialData,
    List<double> initialRowHeights,
    List<double> initialColumnWidths,
    EmbedNode node, // 添加节点引用参数
    String tableId, // *** 添加 tableId 参数 ***
  ) {
    print('DEBUG: [EditorPage] 显示表格编辑对话框, ID: $tableId');

    // 设置行高和列宽
    List<double> rowHeights = List.from(initialRowHeights);
    List<double> columnWidths = List.from(initialColumnWidths);

    // 创建单元格编辑控制器的数组
    List<List<TextEditingController>> controllers = List.generate(
      rows,
      (i) => List.generate(
        columns,
        (j) => TextEditingController(text: initialData[i][j]),
        growable: false,
      ),
      growable: false,
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: const EdgeInsets.all(16),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: min(MediaQuery.of(context).size.width - 32, 800),
              maxHeight: min(MediaQuery.of(context).size.height - 80, 600),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Text('编辑表格',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold)),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                const Divider(),
                // 表格编辑区域
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 行高和列宽控制
                          if (rows > 0 && columns > 0)
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text('行高设置:',
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          const Text('第1行(标题): '),
                                          Expanded(
                                            child: Slider(
                                              value: rowHeights[0],
                                              min: 30,
                                              max: 100,
                                              divisions: 14,
                                              label: rowHeights[0]
                                                  .round()
                                                  .toString(),
                                              onChanged: (value) {
                                                rowHeights[0] = value;
                                                (context as Element)
                                                    .markNeedsBuild();
                                              },
                                            ),
                                          ),
                                          Text('${rowHeights[0].round()}px'),
                                        ],
                                      ),
                                      if (rows > 1)
                                        Row(
                                          children: [
                                            const Text('数据行: '),
                                            Expanded(
                                              child: Slider(
                                                value: rowHeights[1],
                                                min: 30,
                                                max: 100,
                                                divisions: 14,
                                                label: rowHeights[1]
                                                    .round()
                                                    .toString(),
                                                onChanged: (value) {
                                                  // 更新所有数据行的高度
                                                  for (int i = 1;
                                                      i < rows;
                                                      i++) {
                                                    rowHeights[i] = value;
                                                  }
                                                  (context as Element)
                                                      .markNeedsBuild();
                                                },
                                              ),
                                            ),
                                            Text('${rowHeights[1].round()}px'),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text('列宽设置:',
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          const Text('所有列: '),
                                          Expanded(
                                            child: Slider(
                                              value: columnWidths[0],
                                              min: 60,
                                              max: 200,
                                              divisions: 14,
                                              label: columnWidths[0]
                                                  .round()
                                                  .toString(),
                                              onChanged: (value) {
                                                // 更新所有列的宽度
                                                for (int i = 0;
                                                    i < columns;
                                                    i++) {
                                                  columnWidths[i] = value;
                                                }
                                                (context as Element)
                                                    .markNeedsBuild();
                                              },
                                            ),
                                          ),
                                          Text('${columnWidths[0].round()}px'),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),

                          const SizedBox(height: 20),
                          const Text('表格内容:',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          // 表格编辑区域
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: SingleChildScrollView(
                              child: Table(
                                border: TableBorder.all(),
                                defaultVerticalAlignment:
                                    TableCellVerticalAlignment.middle,
                                columnWidths: Map.fromIterable(
                                  List.generate(columns, (index) => index),
                                  key: (i) => i,
                                  value: (i) =>
                                      FixedColumnWidth(columnWidths[i]),
                                ),
                                children: List.generate(
                                  rows,
                                  (i) => TableRow(
                                    decoration: i == 0
                                        ? BoxDecoration(
                                            color: AppTheme.lightGrayColor
                                                .withOpacity(0.3))
                                        : null,
                                    children: List.generate(
                                      columns,
                                      (j) => Container(
                                        height: rowHeights[i],
                                        padding: const EdgeInsets.all(4),
                                        child: Center(
                                          child: TextField(
                                            controller: controllers[i][j],
                                            textAlign: TextAlign.center,
                                            style: i == 0
                                                ? const TextStyle(
                                                    fontWeight: FontWeight.bold)
                                                : null,
                                            decoration: const InputDecoration(
                                              border: InputBorder.none,
                                              contentPadding: EdgeInsets.all(4),
                                              isDense: true,
                                            ),
                                            expands: true,
                                            maxLines: null,
                                            minLines: null,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Divider(),
                // 底部按钮
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('取消'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          print(
                              'DEBUG: [EditorPage] 表格编辑完成，准备更新表格 ID: $tableId');

                          // 收集编辑后的数据
                          List<List<String>> newData = List.generate(
                            rows,
                            (i) => List.generate(
                              columns,
                              (j) => controllers[i][j].text,
                              growable: true,
                            ),
                            growable: true,
                          );

                          // 关闭对话框
                          Navigator.of(context).pop();

                          // 创建新表格 或 更新现有表格
                          _createNewTableAtCursor(
                              newData,
                              rows,
                              columns,
                              rowHeights,
                              columnWidths,
                              tableId); // *** 传递 tableId, 移除 node ***
                        },
                        child: const Text('保存'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 在光标位置创建新表格或替换已有表格
  void _createNewTableAtCursor(
    List<List<String>> tableData,
    int rows,
    int columns,
    List<double> rowHeights,
    List<double> columnWidths,
    String tableId, // *** 添加 tableId 参数, 移除 node ***
  ) {
    try {
      // 查找当前 Delta 中的全部操作
      final deltaOperations = _document.toDelta().toList();

      // 详细打印所有操作的结构，帮助调试
      print('DEBUG: [EditorPage] 开始查找表格 ID: $tableId');
      print('DEBUG: [EditorPage] 文档包含 ${deltaOperations.length} 个操作');

      // 尝试找到文档位置的另一种方法 - 使用node.value.offset
      // 先尝试找到具有相同ID的表格位置
      int tableIndex = -1;
      int currentOffset = 0; // 跟踪当前操作的偏移量

      print('DEBUG: [EditorPage] 使用多种算法查找表格位置...');

      // 使用UUID搜索 - 方法1
      for (int i = 0; i < deltaOperations.length; i++) {
        final op = deltaOperations[i];

        if (op.isInsert && op.data is Map<String, dynamic>) {
          final opData = op.data as Map<String, dynamic>;

          // 打印关键操作的结构，帮助调试
          print(
              'DEBUG: [EditorPage] 检查操作 #$i: ${opData.toString().substring(0, min(100, opData.toString().length))}...');

          // 使用递归查找函数
          String? foundTableId = _findValueInMap(opData, 'tableId');
          if (foundTableId == tableId) {
            print('DEBUG: [EditorPage] 方法1: 找到匹配的表格ID: $foundTableId 在操作 #$i');
            tableIndex = i;
            break;
          }
        }

        // 累加偏移量
        currentOffset += op.length;
      }

      // 如果方法1失败，尝试方法2 - 查找任何表格类型的操作
      if (tableIndex == -1) {
        print('DEBUG: [EditorPage] 方法1未找到表格，尝试方法2: 查找任何表格类型的操作');

        // 记录表格操作的索引
        List<int> tableIndices = [];

        // 首先，找出所有表格操作
        for (int i = 0; i < deltaOperations.length; i++) {
          final op = deltaOperations[i];
          if (op.isInsert && op.data is Map<String, dynamic>) {
            final opData = op.data as Map<String, dynamic>;

            // 检查是否是表格操作 - 使用包含多种可能嵌入方式
            bool isTableOperation = false;

            // 遍历所有键查找与"table"相关的
            for (final key in opData.keys) {
              // 检查键值是否包含table或type字段
              if (key == 'type' && opData[key] == 'table') {
                isTableOperation = true;
                break;
              }
              // 检查嵌入键中的数据
              else if (key == 'embed' && opData[key] is Map) {
                final embedData = opData[key] as Map;
                if (embedData['type'] == 'table') {
                  isTableOperation = true;
                  break;
                }
              }
              // 其他可能的格式...
            }

            if (isTableOperation) {
              tableIndices.add(i);
              print(
                  'DEBUG: [EditorPage] 方法2: 找到表格操作 #$i: ${opData.toString().substring(0, min(100, opData.toString().length))}...');
            }
          }
        }

        print('DEBUG: [EditorPage] 方法2: 共找到 ${tableIndices.length} 个表格操作');

        // 如果只找到一个表格，就假定它是我们要修改的
        if (tableIndices.length == 1) {
          tableIndex = tableIndices[0];
          print('DEBUG: [EditorPage] 方法2: 只找到一个表格操作，将使用索引 $tableIndex');
        }
        // 如果找到多个表格，暂时使用最后一个
        else if (tableIndices.isNotEmpty) {
          tableIndex = tableIndices.last; // 使用最后一个表格，通常是最新插入的
          print(
              'DEBUG: [EditorPage] 方法2: 找到 ${tableIndices.length} 个表格操作，将使用最后一个，索引 $tableIndex');
        }
      }

      // 如果找到了表格位置
      if (tableIndex >= 0) {
        // 重新计算准确的偏移量
        int targetOffset = 0;
        for (int i = 0; i < tableIndex; i++) {
          targetOffset += deltaOperations[i].length;
        }

        print(
            'DEBUG: [EditorPage] 找到表格 ID: $tableId, 位置: $targetOffset (索引 $tableIndex), 开始替换');

        // 创建新的表格嵌入数据，*** 必须包含相同的 tableId ***
        final tableObject = EmbeddableObject('table', inline: false, data: {
          'rows': rows,
          'columns': columns,
          'tableData': tableData,
          'rowHeights':
              rowHeights.map((h) => h.roundToDouble()).toList(), // 保留 Double
          'columnWidths':
              columnWidths.map((w) => w.roundToDouble()).toList(), // 保留 Double
          'tableId': tableId, // *** 保持 ID 不变 ***
        });

        // 替换表格 (嵌入对象长度为 1)
        _fleatherController.replaceText(
          targetOffset, // 使用计算出的偏移量
          1, // 嵌入对象的长度总是 1
          tableObject,
        );

        // 提示用户表格已更新
        SnackbarHelper.showSuccess(context: context, message: '表格已更新');
      } else {
        print('DEBUG: [EditorPage] 所有操作数据检查完毕，未找到表格ID: $tableId');
        print('ERROR: [EditorPage] 无法找到具有 ID $tableId 的表格进行更新。这不应该发生。');

        // 为了安全，暂时还是插入新表格并提示用户
        final currentSelection = _fleatherController.selection;
        final tableObject = EmbeddableObject('table', inline: false, data: {
          'rows': rows,
          'columns': columns,
          'tableData': tableData,
          'rowHeights': rowHeights.map((h) => h.roundToDouble()).toList(),
          'columnWidths': columnWidths.map((w) => w.roundToDouble()).toList(),
          'tableId': tableId, // 仍然给新表格一个ID
        });
        _fleatherController.replaceText(
            currentSelection.baseOffset, 0, tableObject);
        _fleatherController.replaceText(
            currentSelection.baseOffset + 1, 0, '\n');

        SnackbarHelper.showError(
            context: context, message: '无法找到原始表格进行更新，已创建新表格。请手动删除旧表格。');
      }
    } catch (e, stackTrace) {
      // 添加堆栈跟踪
      print('ERROR: [EditorPage] 更新表格失败: $e\n$stackTrace'); // 打印堆栈
      SnackbarHelper.showError(context: context, message: '表格更新失败: $e');
    }
  }

  // 递归查找Map中的值，处理各种嵌套结构
  String? _findValueInMap(Map<String, dynamic> map, String targetKey) {
    // 直接检查当前级别
    if (map.containsKey(targetKey)) {
      final value = map[targetKey];
      if (value is String) {
        return value;
      }
    }

    // 递归检查所有Map类型的值
    for (final entry in map.entries) {
      final value = entry.value;

      // 如果值是Map，递归检查
      if (value is Map<String, dynamic>) {
        final result = _findValueInMap(value, targetKey);
        if (result != null) {
          return result;
        }
      }
      // 如果值是Map但类型不完全匹配，尝试转换
      else if (value is Map) {
        try {
          final convertedMap = Map<String, dynamic>.from(value);
          final result = _findValueInMap(convertedMap, targetKey);
          if (result != null) {
            return result;
          }
        } catch (e) {
          // 转换失败，忽略并继续
        }
      }
      // 如果值是List，检查List中的每个Map
      else if (value is List) {
        for (final item in value) {
          if (item is Map<String, dynamic>) {
            final result = _findValueInMap(item, targetKey);
            if (result != null) {
              return result;
            }
          } else if (item is Map) {
            try {
              final convertedMap = Map<String, dynamic>.from(item);
              final result = _findValueInMap(convertedMap, targetKey);
              if (result != null) {
                return result;
              }
            } catch (e) {
              // 转换失败，忽略并继续
            }
          }
        }
      }
    }

    // 没有找到目标键
    return null;
  }

  // 替换当前表格
  void _replaceTableWithNewData(
      List<List<String>> newData, int rows, int columns) {
    // 由于不再需要，此方法可以移除
  }

  // 插入图片
  Future<void> _insertImage() async {
    try {
      // 显示选择对话框
      await showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: Icon(
                    Icons.photo_camera,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('拍照'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickAndUploadImage(ImageSource.camera);
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.photo_library,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('从相册选择'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickAndUploadImage(ImageSource.gallery);
                  },
                ),
              ],
            ),
          );
        },
      );
    } catch (e) {
      SnackbarHelper.showError(context: context, message: '选择图片失败: $e');
    }
  }

  // 选择并插入本地图片（不立即上传）
  Future<void> _pickAndUploadImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 1000,
        maxHeight: 1000,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        print('DEBUG: [EditorPage] 已选择本地图片: ${pickedFile.path}');

        // 统一处理图片数据，不区分平台
        dynamic imageData;
        String sourceType;

        // 处理图片数据
        if (kIsWeb) {
          // Web平台 - 读取为base64
          final bytes = await pickedFile.readAsBytes();
          final base64String = base64Encode(bytes);

          // 获取MIME类型
          String mimeType = 'image/png';
          if (pickedFile.name.toLowerCase().endsWith('.jpg') ||
              pickedFile.name.toLowerCase().endsWith('.jpeg')) {
            mimeType = 'image/jpeg';
          } else if (pickedFile.name.toLowerCase().endsWith('.gif')) {
            mimeType = 'image/gif';
          }

          // 创建data URI
          imageData = 'data:$mimeType;base64,$base64String';
          sourceType = 'local_web';
          print(
              'DEBUG: [EditorPage] 创建了Web图片数据，MIME类型: $mimeType, 数据长度: ${base64String.length}');
        } else {
          // 移动平台 - 使用文件路径
          imageData = pickedFile.path;
          sourceType = 'local_file';
          print('DEBUG: [EditorPage] 创建了移动平台图片引用: $imageData');
        }

        // 在编辑器中插入本地图片
        final selection = _fleatherController.selection;

        print('DEBUG: [EditorPage] 插入图片，类型: $sourceType');

        // 插入图片，设置更合理的临时标记
        _fleatherController.replaceText(
          selection.baseOffset,
          selection.extentOffset - selection.baseOffset,
          EmbeddableObject('image', inline: false, data: {
            'source_type': sourceType,
            'source': imageData,
            'isLocal': true, // 标记为本地图片
            'uploadPending': true // 标记为需要上传
          }),
          selection: selection,
        );

        // 在图片后添加新行
        _fleatherController.replaceText(
          selection.baseOffset + 1,
          0,
          '\n',
          selection: TextSelection.collapsed(offset: selection.baseOffset + 2),
        );

        SnackbarHelper.showSuccess(
            context: context, message: '已插入图片（保存笔记时将自动上传到服务器的uploads/notes目录）');
      }
    } catch (e) {
      print('ERROR: [EditorPage] 选择或插入图片失败: $e');
      SnackbarHelper.showError(context: context, message: '选择图片失败: $e');
    }
  }

  // 插入表格
  void _insertTable() {
    // 默认行列数
    int rows = 3;
    int columns = 3;

    // 显示对话框让用户输入行列数
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('插入表格'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Text('行数: '),
                  Expanded(
                    child: TextField(
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: '3',
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          int? parsedValue = int.tryParse(value);
                          if (parsedValue != null &&
                              parsedValue > 0 &&
                              parsedValue <= 20) {
                            rows = parsedValue;
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('列数: '),
                  Expanded(
                    child: TextField(
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        hintText: '3',
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          int? parsedValue = int.tryParse(value);
                          if (parsedValue != null &&
                              parsedValue > 0 &&
                              parsedValue <= 10) {
                            columns = parsedValue;
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Text(
                '注意: 行数最大为20，列数最大为10',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _createTableWithEmptyData(rows, columns);
              },
              child: const Text('插入'),
            ),
          ],
        );
      },
    );
  }

  // 创建表格并插入空数据
  void _createTableWithEmptyData(int rows, int columns) {
    // 创建表格数据 - 二维数组
    final List<List<String>> tableData = List.generate(
      rows,
      (i) => List.filled(columns, '', growable: true),
      growable: true,
    );

    // 插入标题行的默认文本
    for (int j = 0; j < columns; j++) {
      tableData[0][j] = '标题 ${j + 1}';
    }

    // *** 添加唯一 ID ***
    final String tableId = Uuid().v4();
    print('DEBUG: [EditorPage] 创建新表格，ID: $tableId');

    // 创建表格对象
    final tableObject = EmbeddableObject('table', inline: false, data: {
      'rows': rows,
      'columns': columns,
      'tableData': tableData,
      'tableId': tableId,
      'rowHeights': List.filled(rows, 40.0),
      'columnWidths': List.filled(columns, 100.0),
    });

    // 在编辑器中插入表格
    final selection = _fleatherController.selection;
    _fleatherController.replaceText(
      selection.baseOffset,
      selection.extentOffset - selection.baseOffset,
      tableObject,
    );

    // 打印调试信息 - 在创建表格后检查文档结构
    Future.microtask(() {
      final deltaOperations = _document.toDelta().toList();
      print('DEBUG: [EditorPage] 创建表格后，文档包含 ${deltaOperations.length} 个操作');

      // 查找并打印表格在文档中的存储结构
      for (int i = 0; i < deltaOperations.length; i++) {
        final op = deltaOperations[i];
        if (op.isInsert && op.data is Map<String, dynamic>) {
          final opData = op.data as Map<String, dynamic>;
          String? foundTableId = _findValueInMap(opData, 'tableId');
          if (foundTableId == tableId) {
            print('DEBUG: [EditorPage] 找到新创建的表格，存储结构：');
            print('DEBUG: [EditorPage] 操作 #$i 数据: $opData');
            break;
          }
        }
      }
    });

    // 在表格后添加新行
    _fleatherController.replaceText(
      selection.baseOffset + 1,
      0,
      '\n',
      selection: TextSelection.collapsed(offset: selection.baseOffset + 2),
    );

    SnackbarHelper.showSuccess(context: context, message: '已插入表格');
  }

  /// 根据标签名称创建或添加标签
  void _createOrAddTag(String tagName, TagProvider tagProvider) async {
    // 检查标签是否已存在
    final existingTag =
        tagProvider.tags.where((tag) => tag.name == tagName).toList();

    if (existingTag.isNotEmpty) {
      // 如果标签已存在，直接添加
      if (!_tags.contains(existingTag[0].id)) {
        setState(() {
          _tags.add(existingTag[0].id);
        });
      }
    } else {
      // 如果标签不存在，创建新标签
      // 预定义的颜色选项
      final List<String> colorOptions = [
        '#F44336',
        '#E91E63',
        '#9C27B0',
        '#673AB7',
        '#3F51B5',
        '#2196F3',
        '#03A9F4',
        '#00BCD4',
        '#009688',
        '#4CAF50',
        '#8BC34A',
        '#CDDC39',
        '#FFEB3B',
        '#FFC107',
        '#FF9800',
        '#FF5722',
        '#795548',
        '#9E9E9E',
        '#607D8B'
      ];

      // 根据标签名称生成一个伪随机颜色
      // 使用标签名称的哈希值来选择颜色，这样相同标签名称会有相同颜色
      final int colorIndex = tagName.hashCode.abs() % colorOptions.length;
      final String tagColor = colorOptions[colorIndex];

      final success = await tagProvider.createTag(tagName, color: tagColor);

      if (success) {
        // 获取新创建的标签
        final newTag = tagProvider.tags.firstWhere(
          (tag) => tag.name == tagName,
          orElse: () => Tag.empty(),
        );

        if (newTag.id.isNotEmpty) {
          setState(() {
            _tags.add(newTag.id);
          });
        }
      }
    }
  }

  /// 根据标签名称移除标签
  void _removeTagByName(String tagName, TagProvider tagProvider) {
    // 查找标签
    final tag = tagProvider.tags.where((tag) => tag.name == tagName).toList();

    if (tag.isNotEmpty) {
      setState(() {
        _tags.remove(tag[0].id);
      });
    }
  }
}
