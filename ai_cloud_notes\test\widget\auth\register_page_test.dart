import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_cloud_notes/screens/auth/register_page.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import '../../mocks/mock_api_service.dart';
import '../../test_config/test_helpers.dart';
import '../../test_config/test_app.dart';

void main() {
  group('RegisterPage 组件测试', () {
    late MockApiService mockApiService;
    late AuthProvider authProvider;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider(mockApiService);
    });

    tearDown(() {
      mockApiService.reset();
    });

    Widget createRegisterPage() {
      return TestApp(
        mockApiService: mockApiService,
        mockAuthProvider: authProvider,
        home: const RegisterPage(),
      );
    }

    group('UI渲染测试', () {
      testWidgets('应该正确渲染注册页面的所有元素', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 验证AppBar标题
        TestHelpers.expectAppBarTitle('注册账号');

        // 验证表单字段
        expect(find.byType(TextFormField), findsNWidgets(4)); // 用户名、邮箱、密码、验证码

        // 验证按钮
        TestHelpers.expectTextExists('注 册');
        TestHelpers.expectTextExists('获取验证码');
        TestHelpers.expectTextExists('已有账号？立即登录');

        // 验证字段标签
        TestHelpers.expectTextExists('用户名');
        TestHelpers.expectTextExists('邮箱');
        TestHelpers.expectTextExists('密码');
        TestHelpers.expectTextExists('验证码');
      });

      testWidgets('应该显示正确的输入提示', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 查找输入字段的提示文本
        expect(find.text('请输入用户名'), findsOneWidget);
        expect(find.text('请输入邮箱地址'), findsOneWidget);
        expect(find.text('请输入密码'), findsOneWidget);
        expect(find.text('请输入验证码'), findsOneWidget);
      });

      testWidgets('密码字段应该是隐藏的', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 查找密码字段
        final passwordField = find.byKey(const Key('password_field'));
        expect(passwordField, findsOneWidget);

        // 验证密码字段是隐藏的
        final textFormField = tester.widget<TextFormField>(passwordField);
        expect(textFormField.obscureText, isTrue);
      });
    });

    group('表单验证测试', () {
      testWidgets('空用户名应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 点击注册按钮而不输入任何内容
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请输入用户名');
      });

      testWidgets('短用户名应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 输入短用户名
        await TestHelpers.enterText(tester, 'username_field', 'ab');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('用户名长度为3-20个字符');
      });

      testWidgets('无效邮箱应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 输入有效用户名和无效邮箱
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'email_field', 'invalid-email');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请输入有效的邮箱地址');
      });

      testWidgets('短密码应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 输入有效用户名、邮箱和短密码
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.enterText(tester, 'password_field', '123');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('密码长度至少为6位');
      });

      testWidgets('空验证码应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 输入所有字段除了验证码
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.enterText(tester, 'password_field', 'password123');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请输入验证码');
      });

      testWidgets('有效输入应该通过验证', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 输入所有有效字段
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.enterText(tester, 'password_field', 'password123');
        await TestHelpers.enterText(tester, 'verification_code_field', '123456');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 验证没有表单验证错误
        expect(find.text('请输入用户名'), findsNothing);
        expect(find.text('请输入有效的邮箱地址'), findsNothing);
        expect(find.text('密码长度至少为6位'), findsNothing);
        expect(find.text('请输入验证码'), findsNothing);
      });
    });

    group('验证码功能测试', () {
      testWidgets('应该能够发送验证码', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createRegisterPage());

        // 输入邮箱
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');

        // 点击获取验证码按钮
        await TestHelpers.tapButton(tester, '获取验证码');
        await TestHelpers.waitForAsync(tester);

        // 验证按钮状态变化（应该开始倒计时）
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });

      testWidgets('发送验证码后应该开始倒计时', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createRegisterPage());

        // 输入邮箱
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');

        // 点击获取验证码按钮
        await TestHelpers.tapButton(tester, '获取验证码');
        await TestHelpers.waitForAsync(tester);

        // 等待状态更新
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 验证倒计时开始（按钮文本应该变化）
        // 注意：具体的倒计时文本可能需要根据实际实现调整
      });

      testWidgets('邮箱为空时不应该发送验证码', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 不输入邮箱，直接点击获取验证码
        await TestHelpers.tapButton(tester, '获取验证码');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请先输入邮箱地址');
      });

      testWidgets('无效邮箱时不应该发送验证码', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 输入无效邮箱
        await TestHelpers.enterText(tester, 'email_field', 'invalid-email');

        // 点击获取验证码
        await TestHelpers.tapButton(tester, '获取验证码');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请输入有效的邮箱地址');
      });
    });

    group('注册流程测试', () {
      testWidgets('注册成功应该显示成功消息并导航', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createRegisterPage());

        // 输入所有有效字段
        await TestHelpers.enterText(tester, 'username_field', 'newuser');
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.enterText(tester, 'password_field', 'password123');
        await TestHelpers.enterText(tester, 'verification_code_field', '123456');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 等待注册完成
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });

      testWidgets('注册失败应该显示错误消息', (WidgetTester tester) async {
        // 设置API返回失败
        mockApiService.shouldSucceed = false;

        await tester.pumpWidget(createRegisterPage());

        // 输入字段（使用已存在的用户名）
        await TestHelpers.enterText(tester, 'username_field', 'existinguser');
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.enterText(tester, 'password_field', 'password123');
        await TestHelpers.enterText(tester, 'verification_code_field', '123456');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 等待错误处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });

      testWidgets('验证码错误应该显示相应错误', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createRegisterPage());

        // 输入字段（使用错误的验证码）
        await TestHelpers.enterText(tester, 'username_field', 'newuser');
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.enterText(tester, 'password_field', 'password123');
        await TestHelpers.enterText(tester, 'verification_code_field', '000000');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester);

        // 等待错误处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });
    });

    group('用户交互测试', () {
      testWidgets('点击登录链接应该导航到登录页面', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 点击登录链接
        await TestHelpers.tapButton(tester, '已有账号？立即登录');
        await TestHelpers.waitForAnimations(tester);

        // 验证导航
      });

      testWidgets('应该能够输入所有字段', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 输入所有字段
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        expect(find.text('testuser'), findsOneWidget);

        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        expect(find.text('<EMAIL>'), findsOneWidget);

        await TestHelpers.enterText(tester, 'password_field', 'password123');
        // 密码字段是隐藏的

        await TestHelpers.enterText(tester, 'verification_code_field', '123456');
        expect(find.text('123456'), findsOneWidget);
      });
    });

    group('状态管理测试', () {
      testWidgets('加载状态应该禁用注册按钮', (WidgetTester tester) async {
        // 设置延迟以观察加载状态
        mockApiService.shouldDelay = true;
        mockApiService.delayMilliseconds = 200;

        await tester.pumpWidget(createRegisterPage());

        // 输入所有字段
        await TestHelpers.enterText(tester, 'username_field', 'newuser');
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.enterText(tester, 'password_field', 'password123');
        await TestHelpers.enterText(tester, 'verification_code_field', '123456');

        // 点击注册按钮
        await TestHelpers.tapButton(tester, '注 册');
        await TestHelpers.waitForAsync(tester, milliseconds: 50);

        // 验证加载状态
        TestHelpers.expectLoadingIndicator();

        // 等待操作完成
        await TestHelpers.waitForAsync(tester, milliseconds: 300);
      });

      testWidgets('应该正确响应AuthProvider状态变化', (WidgetTester tester) async {
        await tester.pumpWidget(createRegisterPage());

        // 验证初始状态
        expect(authProvider.isLoading, isFalse);
        expect(authProvider.error, isNull);

        // 模拟状态变化
        mockApiService.shouldSucceed = true;
        await TestHelpers.enterText(tester, 'username_field', 'newuser');
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.enterText(tester, 'password_field', 'password123');
        await TestHelpers.enterText(tester, 'verification_code_field', '123456');
        await TestHelpers.tapButton(tester, '注 册');

        // 等待状态更新
        await TestHelpers.waitForProviderUpdate(tester);
      });
    });
  });
}
