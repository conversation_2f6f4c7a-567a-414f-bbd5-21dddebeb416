import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:provider/provider.dart';
import 'package:fleather/fleather.dart';
import 'dart:convert';
import 'dart:math' show min, Random;
import 'dart:io';
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';
import 'package:ai_cloud_notes/services/api_service.dart';
import 'package:image_picker/image_picker.dart';
import 'package:http_parser/http_parser.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

/// 分享笔记查看页面
///
/// 用于查看通过分享链接访问的笔记，不需要登录
class SharedNoteViewPage extends StatefulWidget {
  /// 分享令牌
  final String shareToken;

  const SharedNoteViewPage({
    Key? key,
    required this.shareToken,
  }) : super(key: key);

  @override
  State<SharedNoteViewPage> createState() => _SharedNoteViewPageState();
}

class _SharedNoteViewPageState extends State<SharedNoteViewPage> {
  /// Fleather编辑器控制器
  late FleatherController _fleatherController;

  /// 笔记文档
  late ParchmentDocument _document;

  /// 是否正在加载
  bool _isLoading = true;

  /// 笔记数据
  Note? _note;

  /// 错误信息
  String? _errorMessage;

  /// 是否处于编辑模式
  bool _isEditing = true;

  /// 内容焦点节点
  final FocusNode _contentFocusNode = FocusNode();

  /// 标题焦点节点
  final FocusNode _titleFocusNode = FocusNode();

  /// 标题控制器
  final TextEditingController _titleController = TextEditingController();

  /// 是否正在编辑标题
  bool _editingTitle = false;

  /// API服务
  late ApiService _apiService;

  @override
  void initState() {
    super.initState();
    // 初始化空文档
    _document = ParchmentDocument();
    _fleatherController = FleatherController(document: _document);

    // 初始化API服务
    _apiService = ApiService();

    // 加载分享的笔记
    _loadSharedNote();
  }

  /// 密码输入控制器
  final TextEditingController _passwordController = TextEditingController();

  /// 是否需要密码
  bool _needPassword = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _fleatherController.dispose();
    _titleController.dispose();
    _markdownController.dispose(); // 释放Markdown编辑器控制器
    _contentFocusNode.dispose();
    _titleFocusNode.dispose();
    super.dispose();
  }

  /// 切换编辑/阅读模式
  void _toggleMode() {
    // 如果是Markdown格式，确保在切换到阅读模式前更新笔记内容
    if (_note?.contentType == 'markdown' && _isEditing) {
      // 确保笔记内容已更新为最新的编辑内容，并且以换行符结尾
      if (_note != null) {
        String content = _markdownController.text;
        // 确保内容以换行符结尾
        if (!content.endsWith('\n')) {
          content += '\n';
          print('DEBUG: [SharedNoteViewPage] 添加换行符到Markdown内容末尾');
        }

        if (content != _note!.content) {
          _note = _note!.copyWith(content: content);
          print('DEBUG: [SharedNoteViewPage] 更新笔记内容为最新编辑内容');
        }
      }
    }

    setState(() {
      _isEditing = !_isEditing;

      // 如果从编辑模式切换到阅读模式，确保UI刷新以显示最新内容
      if (!_isEditing && _note?.contentType == 'markdown') {
        // 刷新UI以显示更新后的内容
        print('DEBUG: [SharedNoteViewPage] 从编辑模式切换到阅读模式，刷新Markdown内容');
      }
    });
  }

  /// 加载分享的笔记
  Future<void> _loadSharedNote() async {
    // 先设置状态
    _isLoading = true;
    _errorMessage = null;
    _needPassword = false;
    // 安全地更新状态
    if (mounted) setState(() {});

    try {
      print('DEBUG: 开始加载分享笔记，token=${widget.shareToken}');
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      final note = await noteProvider.getSharedNote(
        widget.shareToken,
        password: _passwordController.text.isNotEmpty
            ? _passwordController.text
            : null,
      );

      // 检查组件是否仍然挂载
      if (!mounted) return;

      if (note != null) {
        print('DEBUG: 成功获取分享笔记: ${note.title}');
        // 更新状态
        _note = note;
        _isLoading = false;
        _needPassword = false;

        // 设置标题
        _titleController.text = note.title;

        // 根据分享类型设置编辑模式
        _isEditing = note.shareAccessType == 'editable';

        // 安全地更新UI
        setState(() {});

        // 加载笔记内容到编辑器
        _loadNoteContent(note);
      } else {
        print('DEBUG: 获取分享笔记失败: ${noteProvider.error}');
        // 检查是否需要密码
        if (noteProvider.isPasswordRequired) {
          print('DEBUG: 需要密码才能访问笔记');
          _isLoading = false;
          _needPassword = true;
          _errorMessage = null;
          // 如果密码错误，显示错误提示
          if (_passwordController.text.isNotEmpty) {
            _errorMessage = '密码错误，请重新输入';
          }
          // 安全地更新UI
          setState(() {});
          return;
        }

        // 更新状态
        _isLoading = false;
        _needPassword = false;
        _errorMessage =
            noteProvider.error.isNotEmpty ? noteProvider.error : '无法加载分享的笔记';
        // 安全地更新UI
        setState(() {});
      }
    } catch (e) {
      print('DEBUG: 加载分享笔记异常: $e');
      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 更新状态
      _isLoading = false;
      _needPassword = false;
      _errorMessage = '加载分享笔记时出错: $e';
      // 安全地更新UI
      setState(() {});
    }
  }

  /// 加载笔记内容到编辑器
  Future<void> _loadNoteContent(Note note) async {
    try {
      print('DEBUG: 开始加载笔记内容，笔记ID=${note.id}, 内容类型=${note.contentType}');

      // 创建新的文档对象
      ParchmentDocument newDocument;

      // 设置标题
      _titleController.text = note.title;

      // 根据笔记类型处理内容
      if (note.content.isNotEmpty) {
        if (note.contentType == 'markdown') {
          // 对于Markdown格式，使用简单的文本文档
          print('DEBUG: 加载Markdown内容');
          newDocument = ParchmentDocument.fromJson([
            {"insert": "${note.content}\n"}
          ]);
        } else if (note.contentType == 'rich-text') {
          try {
            // 尝试解析JSON格式的Delta
            final String deltaJson;
            if (note.content.startsWith('[')) {
              deltaJson = note.content;
            } else {
              // 处理纯文本，转换为富文本格式
              final escapedContent = note.content.replaceAll('"', '\\"');
              deltaJson = '[{"insert":"$escapedContent\\n"}]';
            }

            print('DEBUG: 解析Delta JSON: $deltaJson');
            final List<dynamic> delta = json.decode(deltaJson);
            newDocument = ParchmentDocument.fromJson(delta);
            print('DEBUG: 成功解析富文本内容');
          } catch (e) {
            print('ERROR: [SharedNoteViewPage] 无法解析富文本内容: $e');
            // 解析失败，加载纯文本
            newDocument = ParchmentDocument.fromJson([
              {"insert": "${note.content}\n"}
            ]);
          }
        } else {
          // 纯文本内容
          print('DEBUG: 加载纯文本内容');
          newDocument = ParchmentDocument.fromJson([
            {"insert": "${note.content}\n"}
          ]);
        }
      } else {
        // 空内容
        print('DEBUG: 笔记内容为空，创建空文档');
        newDocument = ParchmentDocument();
      }

      // 使用Future.microtask确保在构建周期外更新状态
      await Future.microtask(() {});

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 安全地更新控制器
      setState(() {
        // 先释放旧的控制器
        _fleatherController.dispose();
        // 更新文档
        _document = newDocument;
        // 创建新的控制器
        _fleatherController = FleatherController(document: _document);
        print('DEBUG: 成功更新编辑器控制器');
      });
    } catch (e) {
      print('ERROR: [SharedNoteViewPage] 加载笔记内容失败: $e');

      // 检查组件是否仍然挂载
      if (!mounted) return;

      // 出错时安全地创建空文档
      setState(() {
        _fleatherController.dispose();
        _document = ParchmentDocument();
        _fleatherController = FleatherController(document: _document);
      });

      // 显示错误提示
      SnackbarHelper.showError(context: context, message: '加载笔记内容失败，请稍后再试');
    }
  }

  /// 插入图片
  Future<void> _insertImage() async {
    try {
      // 使用ImagePicker选择图片
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1000,
        maxHeight: 1000,
        imageQuality: 85,
      );

      if (image == null) return;

      // 显示加载对话框
      if (!mounted) return;
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在上传图片...')
            ],
          ),
        ),
      );

      // 统一处理图片数据，不区分平台
      dynamic imageData;
      String sourceType;
      final noteId = _note?.id ?? 'temp';
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      Map<String, dynamic> result;

      // 处理图片数据
      if (kIsWeb) {
        // Web平台 - 读取为base64
        final bytes = await image.readAsBytes();
        result = await noteProvider.uploadNoteImage(bytes,
            noteId: noteId, shareToken: widget.shareToken);
      } else {
        // 移动平台 - 使用文件路径
        final File imageFile = File(image.path);
        result = await noteProvider.uploadNoteImage(imageFile,
            noteId: noteId, shareToken: widget.shareToken);
      }

      // 关闭加载对话框
      if (mounted) Navigator.of(context).pop();

      if (result['success'] == true && result['data'] != null) {
        final imageUrl = result['data']['url'];

        // 在光标位置插入图片
        final selection = _fleatherController.selection;

        // 插入图片
        _fleatherController.replaceText(
          selection.baseOffset,
          selection.extentOffset - selection.baseOffset,
          EmbeddableObject('image', inline: false, data: {
            'source_type': 'url',
            'source': imageUrl,
          }),
          selection: selection,
        );

        // 在图片后添加新行
        _fleatherController.replaceText(
          selection.baseOffset + 1,
          0,
          '\n',
          selection: TextSelection.collapsed(offset: selection.baseOffset + 2),
        );

        // 显示成功提示
        if (mounted) {
          SnackbarHelper.showSuccess(context: context, message: '图片插入成功');
        }
      } else {
        // 显示错误提示
        if (mounted) {
          SnackbarHelper.showError(
              context: context,
              message: '图片上传失败: ${result['error']?['message'] ?? '未知错误'}');
        }
      }
    } catch (e) {
      print('ERROR: [SharedNoteViewPage] 插入图片失败: $e');
      if (mounted) {
        SnackbarHelper.showError(context: context, message: '插入图片失败: $e');
      }
    }
  }

  /// 插入表格
  Future<void> _insertTable() async {
    try {
      // 显示表格设置对话框
      if (!mounted) return;

      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => _buildTableDialog(),
      );

      if (result == null) return;

      final int rows = result['rows'];
      final int columns = result['columns'];

      // 创建表格数据
      List<List<String>> tableData = List.generate(
        rows,
        (i) => List.generate(
          columns,
          (j) => i == 0 ? '标题 ${j + 1}' : '',
        ),
      );

      // 初始化行高和列宽
      List<double> rowHeights = List.filled(rows, 40.0);
      List<double> columnWidths = List.filled(columns, 100.0);

      // 生成唯一的表格 ID
      final tableId =
          'table-${DateTime.now().millisecondsSinceEpoch}-${Random().nextInt(10000)}';

      // 在光标位置插入表格
      final selection = _fleatherController.selection;

      // 插入表格
      _fleatherController.replaceText(
        selection.baseOffset,
        selection.extentOffset - selection.baseOffset,
        EmbeddableObject('table', inline: false, data: {
          'rows': rows,
          'columns': columns,
          'tableData': tableData,
          'rowHeights': rowHeights,
          'columnWidths': columnWidths,
          'tableId': tableId, // 添加唯一ID
        }),
        selection: selection,
      );

      // 在表格后添加新行
      _fleatherController.replaceText(
        selection.baseOffset + 1,
        0,
        '\n',
        selection: TextSelection.collapsed(offset: selection.baseOffset + 2),
      );

      // 显示成功提示
      if (mounted) {
        SnackbarHelper.showSuccess(context: context, message: '表格插入成功');
      }
    } catch (e) {
      print('ERROR: [SharedNoteViewPage] 插入表格失败: $e');
      if (mounted) {
        SnackbarHelper.showError(context: context, message: '插入表格失败: $e');
      }
    }
  }

  /// 构建表格设置对话框
  Widget _buildTableDialog() {
    int rows = 3;
    int columns = 3;

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: const Text('插入表格'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  const Text('行数: '),
                  Expanded(
                    child: Slider(
                      value: rows.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      label: rows.toString(),
                      onChanged: (value) {
                        setState(() {
                          rows = value.toInt();
                        });
                      },
                    ),
                  ),
                  Text(rows.toString()),
                ],
              ),
              Row(
                children: [
                  const Text('列数: '),
                  Expanded(
                    child: Slider(
                      value: columns.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      label: columns.toString(),
                      onChanged: (value) {
                        setState(() {
                          columns = value.toInt();
                        });
                      },
                    ),
                  ),
                  Text(columns.toString()),
                ],
              ),
              const SizedBox(height: 16),
              // 表格预览
              Container(
                height: 100,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text('$rows 行 x $columns 列'),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, {
                'rows': rows,
                'columns': columns,
              }),
              child: const Text('插入'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      // <--- 添加 Theme widget
      data: AppTheme.fixedLightTheme, // <--- 设置固定亮色主题
      child: Scaffold(
        body: _buildBody(),
      ),
    );
  }

  /// 构建嵌入内容处理器
  Widget _embedBuilder(BuildContext context, EmbedNode node) {
    // 获取当前模式（编辑或阅读）
    final bool isEditingMode = _isEditing;

    // 处理图片
    if (node.value.type == 'image') {
      final sourceType = node.value.data['source_type'];
      final source = node.value.data['source'];

      // 创建图片展示组件
      return RepaintBoundary(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
              maxHeight: 300,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                source,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  print('ERROR: [SharedNoteViewPage] 网络图片加载失败: $error');
                  return Container(
                    width: 200,
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade400),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.broken_image,
                            color: Colors.grey.shade700, size: 40),
                        const SizedBox(height: 8),
                        Text(
                          '图片加载失败',
                          style: TextStyle(color: Colors.grey.shade700),
                        ),
                      ],
                    ),
                  );
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    width: double.infinity,
                    height: 150,
                    color: Colors.grey.shade100,
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded /
                                loadingProgress.expectedTotalBytes!
                            : null,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      );
    }
    // 处理表格
    else if (node.value.type == 'table') {
      // 提取表格数据
      final Map<String, dynamic> data =
          Map<String, dynamic>.from(node.value.data);
      final int rows = data['rows'] as int? ?? 3;
      final int columns = data['columns'] as int? ?? 3;

      print('DEBUG: [SharedNoteViewPage] 加载表格: $rows 行 x $columns 列');
      print('DEBUG: [SharedNoteViewPage] 表格数据: ${data.toString()}');

      // 提取表格 ID
      String? tableId = _findValueInMap(data, 'tableId');
      if (tableId == null) {
        // 如果没有ID，生成一个新ID
        tableId =
            'table-${DateTime.now().millisecondsSinceEpoch}-${Random().nextInt(10000)}';
        data['tableId'] = tableId;
        print('DEBUG: [SharedNoteViewPage] 生成新表格 ID: $tableId');
      } else {
        print('DEBUG: [SharedNoteViewPage] 表格 ID: $tableId');
      }

      // 获取表格数据
      List<List<String>> cellsData = [];

      // 尝试读取表格数据
      if (data.containsKey('tableData') && data['tableData'] is List) {
        final List tableData = data['tableData'] as List;
        print('DEBUG: [SharedNoteViewPage] 表格数据行数: ${tableData.length}');

        for (int i = 0; i < min(tableData.length, rows); i++) {
          final rowData = tableData[i];
          List<String> row = [];

          if (rowData is List) {
            for (int j = 0; j < min(rowData.length, columns); j++) {
              row.add((rowData[j]?.toString() ?? ''));
            }
          }

          // 确保行长度正确
          while (row.length < columns) {
            row.add('');
          }

          cellsData.add(row);
        }
      } else {
        print('DEBUG: [SharedNoteViewPage] 表格数据不存在或格式不正确');
      }

      // 确保行数正确
      while (cellsData.length < rows) {
        cellsData.add(List.filled(columns, '', growable: true));
      }

      // 获取行高和列宽配置 (如果有的话)
      List<double> rowHeights = List.filled(rows, 40.0);
      List<double> columnWidths = List.filled(columns, 100.0);

      // 获取保存的行高
      if (data.containsKey('rowHeights') && data['rowHeights'] is List) {
        final List savedRowHeights = data['rowHeights'] as List;
        print('DEBUG: [SharedNoteViewPage] 加载行高数据: $savedRowHeights');

        for (int i = 0; i < min(savedRowHeights.length, rows); i++) {
          if (savedRowHeights[i] is num) {
            rowHeights[i] = (savedRowHeights[i] as num).toDouble();
          }
        }
      } else {
        print('DEBUG: [SharedNoteViewPage] 未找到行高数据，使用默认值');
      }

      // 获取保存的列宽
      if (data.containsKey('columnWidths') && data['columnWidths'] is List) {
        final List savedColumnWidths = data['columnWidths'] as List;
        print('DEBUG: [SharedNoteViewPage] 加载列宽数据: $savedColumnWidths');

        for (int i = 0; i < min(savedColumnWidths.length, columns); i++) {
          if (savedColumnWidths[i] is num) {
            columnWidths[i] = (savedColumnWidths[i] as num).toDouble();
          }
        }
      } else {
        print('DEBUG: [SharedNoteViewPage] 未找到列宽数据，使用默认值');
      }

      print('DEBUG: [SharedNoteViewPage] 最终行高: $rowHeights');
      print('DEBUG: [SharedNoteViewPage] 最终列宽: $columnWidths');

      // 如果在编辑模式下且笔记可编辑，显示可编辑的表格
      if (isEditingMode && _note?.shareAccessType == 'editable') {
        return _buildEditableTable(
          context,
          rows,
          columns,
          cellsData,
          rowHeights,
          columnWidths,
          node,
          tableId,
        );
      }

      // 否则显示只读表格
      final screenWidth = MediaQuery.of(context).size.width;

      // 计算表格总宽度（所有列宽之和）
      double totalTableWidth =
          columnWidths.fold(0, (sum, width) => sum + width);
      // 限制表格最大宽度
      totalTableWidth = min(totalTableWidth, screenWidth * 0.95);

      print('DEBUG: [SharedNoteViewPage] 构建只读表格, ID: $tableId');
      print('DEBUG: [SharedNoteViewPage] 行高: $rowHeights');
      print('DEBUG: [SharedNoteViewPage] 列宽: $columnWidths');
      print('DEBUG: [SharedNoteViewPage] 表格总宽度: $totalTableWidth');

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Align(
          alignment: Alignment.center,
          child: Container(
            constraints: BoxConstraints(maxWidth: totalTableWidth),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Table(
                border: TableBorder.all(
                  color: Colors.grey.shade300,
                  width: 1,
                ),
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                // 应用自定义列宽
                columnWidths: Map.fromIterable(
                  List.generate(columns, (index) => index),
                  key: (i) => i,
                  value: (i) => FixedColumnWidth(columnWidths[i]),
                ),
                children: List.generate(
                  rows,
                  (i) => TableRow(
                    // 应用自定义行高
                    decoration: i == 0
                        ? BoxDecoration(color: Colors.grey.shade200)
                        : null,
                    children: List.generate(
                      columns,
                      (j) => Container(
                        height: rowHeights[i],
                        constraints: BoxConstraints(
                          minHeight: rowHeights[i],
                          maxHeight: rowHeights[i],
                        ),
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: Text(
                            i < cellsData.length && j < cellsData[i].length
                                ? cellsData[i][j]
                                : '',
                            textAlign: TextAlign.center,
                            overflow: TextOverflow.ellipsis,
                            style: i == 0
                                ? const TextStyle(fontWeight: FontWeight.bold)
                                : null,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    }
    // 其他嵌入内容
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: const Center(
        child: Text('不支持的内容类型'),
      ),
    );
  }

  /// 构建页面主体
  Widget _buildBody() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor =
        isDarkMode ? Theme.of(context).cardColor : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black;
    final secondaryTextColor = isDarkMode ? Colors.white70 : Colors.grey;

    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppTheme.primaryColor),
            const SizedBox(height: 16),
            Text('正在加载分享的笔记...', style: TextStyle(color: secondaryTextColor)),
          ],
        ),
      );
    }

    // 需要密码验证
    if (_needPassword) {
      return Center(
        child: Container(
          width: min(400, MediaQuery.of(context).size.width * 0.9),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppTheme.whiteColor, // 使用主题颜色
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.lock_outline,
                size: 64,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(height: 16),
              Text(
                '访问受保护的笔记',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '该笔记需要密码才能访问',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),

              // 密码输入框
              TextField(
                controller: _passwordController,
                decoration: InputDecoration(
                  hintText: '请输入访问密码',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(Icons.vpn_key),
                  errorText: _errorMessage,
                ),
                obscureText: true,
                onSubmitted: (_) => _verifyPassword(),
              ),
              const SizedBox(height: 24),

              // 确认按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _verifyPassword,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('确认'),
                ),
              ),
            ],
          ),
        ), // Closes Container
      ); // Closes Center and return statement
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadSharedNote,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_note == null) {
      return Center(
        child: Text('笔记不存在或已过期', style: TextStyle(color: textColor)),
      );
    }

    // 判断是否可编辑
    final bool canEdit = _note?.shareAccessType == 'editable';

    // 根据模式显示不同的界面
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin:
                  _isEditing ? const Offset(1.0, 0.0) : const Offset(-1.0, 0.0),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          ),
        );
      },
      child: _isEditing && canEdit
          ? _buildEditor(key: const ValueKey('editor_mode'))
          : _buildReadingMode(key: const ValueKey('reading_mode')),
    );
  }

  /// 验证密码
  void _verifyPassword() {
    if (_passwordController.text.isEmpty) {
      setState(() {
        _errorMessage = '请输入密码';
      });
      return;
    }

    // 重新加载笔记，传入密码
    _loadSharedNote();
  }

  /// 构建编辑器模式
  Widget _buildEditor({Key? key}) {
    return SafeArea(
      key: key,
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: _buildEditorContent(),
            ),
          ),
          // 根据笔记类型显示不同的工具栏
          if (_note?.contentType == 'markdown')
            _buildMarkdownToolbar()
          else
            _buildToolbar(),
        ],
      ),
    );
  }

  /// 构建阅读模式
  Widget _buildReadingMode({Key? key}) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final textColor = isDarkMode ? Colors.white : Colors.black;
    final secondaryTextColor = isDarkMode ? Colors.white70 : Colors.grey;

    return SafeArea(
      key: key,
      child: Column(
        children: [
          _buildReadingHeader(),
          Expanded(
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AnimatedOpacity(
                      opacity: 1.0,
                      duration: const Duration(milliseconds: 500),
                      child: Text(
                        _titleController.text.isEmpty
                            ? '无标题'
                            : _titleController.text,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 16,
                            color: AppTheme.darkGrayColor,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '最后编辑: ${_formatDate(_note?.updatedAt ?? DateTime.now())}',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppTheme.darkGrayColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (_note?.tags.isNotEmpty ?? false)
                      Container(
                        margin: const EdgeInsets.only(top: 12, bottom: 20),
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: _note!.tags
                              .map((tag) => _buildReadingTag(tag))
                              .toList(),
                        ),
                      ),
                    const SizedBox(height: 12),
                    // 根据笔记类型选择不同的渲染方式
                    AnimatedOpacity(
                      opacity: 1.0,
                      duration: const Duration(milliseconds: 700),
                      child: _note?.contentType == 'markdown'
                          ? MarkdownBody(
                              data: _note?.content ?? '',
                              selectable: true,
                              softLineBreak: true,
                              styleSheet: MarkdownStyleSheet(
                                p: TextStyle(
                                  fontSize: 16,
                                  color: AppTheme.darkGrayColor,
                                  height: 1.5,
                                ),
                                h1: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                                h2: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                                h3: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                                h4: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                                h5: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                                h6: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                                code: TextStyle(
                                  backgroundColor: Colors.grey.shade200,
                                  fontFamily: 'monospace',
                                ),
                                blockquote: TextStyle(
                                  color: Colors.grey.shade700,
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            )
                          : FleatherEditor(
                              controller: _fleatherController,
                              focusNode: FocusNode(),
                              readOnly: true,
                              padding: EdgeInsets.zero,
                              enableInteractiveSelection: true,
                              scrollable: false,
                              expands: false,
                              embedBuilder: _embedBuilder,
                            ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          _buildReadingFooter(),
        ],
      ),
    );
  }

  /// 构建阅读模式标签
  Widget _buildReadingTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        tag,
        style: TextStyle(
          color: AppTheme.primaryColor,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建编辑器头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.lightGrayColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 左侧返回按钮
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.arrow_back,
                color: AppTheme.darkGrayColor,
                size: 20,
              ),
            ),
          ),

          // 标题（可双击编辑）
          Expanded(
            child: GestureDetector(
              onDoubleTap: () {
                setState(() {
                  _editingTitle = true;
                });
                _titleFocusNode.requestFocus();
              },
              child: _editingTitle
                  ? TextField(
                      controller: _titleController,
                      focusNode: _titleFocusNode,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(horizontal: 16),
                      ),
                      onSubmitted: (_) {
                        setState(() {
                          _editingTitle = false;
                          if (_titleController.text.trim().isEmpty) {
                            _titleController.text = '无标题';
                          }
                        });
                      },
                    )
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        _titleController.text,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
            ),
          ),

          // 右侧按钮
          Row(
            children: [
              InkWell(
                onTap: _toggleMode,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _isEditing ? Icons.visibility : Icons.edit,
                    color: AppTheme.darkGrayColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              InkWell(
                onTap: () {
                  final currentUrl = Uri.base.toString();
                  Clipboard.setData(ClipboardData(text: currentUrl));
                  SnackbarHelper.showSuccess(
                      context: context, message: '链接已复制到剪贴板');
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.copy,
                    color: AppTheme.darkGrayColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              InkWell(
                onTap: _saveChanges,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.save,
                    color: AppTheme.darkGrayColor,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 存储Markdown编辑器的文本控制器
  final TextEditingController _markdownController = TextEditingController();

  /// 构建编辑器内容
  Widget _buildEditorContent() {
    // 为Markdown格式笔记使用普通文本编辑器
    if (_note?.contentType == 'markdown') {
      // 确保控制器内容与笔记内容同步，并且以换行符结尾
      String content = _note?.content ?? '';
      if (!content.endsWith('\n')) {
        content += '\n';
      }

      if (_markdownController.text != content) {
        _markdownController.text = content;
      }

      // 监听文本变化，更新笔记内容
      _markdownController.addListener(() {
        if (_note != null) {
          // 使用copyWith创建新的Note对象
          _note = _note!.copyWith(content: _markdownController.text);
        }
      });

      return Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 80),
            child: TextField(
              controller: _markdownController,
              focusNode: _contentFocusNode,
              maxLines: null,
              expands: true,
              textAlignVertical: TextAlignVertical.top,
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
                hintText: '在此输入Markdown内容...',
              ),
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
                fontFamily: 'monospace',
              ),
            ),
          ),
        ],
      );
    } else {
      // 富文本格式使用Fleather编辑器
      return Stack(
        children: [
          // 内容区域 - 使用Fleather编辑器
          Padding(
            padding: const EdgeInsets.only(bottom: 80),
            child: FleatherEditor(
              controller: _fleatherController,
              focusNode: _contentFocusNode,
              padding: const EdgeInsets.all(16),
              scrollable: true,
              readOnly: false,
              expands: false,
              showCursor: true,
              enableInteractiveSelection: true,
              embedBuilder: _embedBuilder,
            ),
          ),
        ],
      );
    }
  }

  /// 构建Markdown工具栏
  Widget _buildMarkdownToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: AppTheme.lightGrayColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Markdown简化工具栏
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 标题按钮
                _buildMarkdownButton(
                  child: const Text('H1',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('# '),
                  tooltip: '标题1',
                ),
                _buildMarkdownButton(
                  child: const Text('H2',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('## '),
                  tooltip: '标题2',
                ),
                _buildMarkdownButton(
                  child: const Text('H3',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('### '),
                  tooltip: '标题3',
                ),
                _buildMarkdownButton(
                  child: const Text('H4',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onPressed: () => _insertMarkdownSyntax('#### '),
                  tooltip: '标题4',
                ),
                // 分隔线
                const VerticalDivider(width: 16, thickness: 1),
                // 格式化按钮
                _buildMarkdownButton(
                  child: const Icon(Icons.format_bold, size: 20),
                  onPressed: () => _insertMarkdownSyntax('**', '**'),
                  tooltip: '加粗',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.format_italic, size: 20),
                  onPressed: () => _insertMarkdownSyntax('*', '*'),
                  tooltip: '斜体',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.format_strikethrough, size: 20),
                  onPressed: () => _insertMarkdownSyntax('~~', '~~'),
                  tooltip: '删除线',
                ),
                // 分隔线
                const VerticalDivider(width: 16, thickness: 1),
                // 列表按钮
                _buildMarkdownButton(
                  child: const Icon(Icons.format_list_bulleted, size: 20),
                  onPressed: () => _insertMarkdownSyntax('- '),
                  tooltip: '无序列表',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.format_list_numbered, size: 20),
                  onPressed: () => _insertMarkdownSyntax('1. '),
                  tooltip: '有序列表',
                ),
                // 分隔线
                const VerticalDivider(width: 16, thickness: 1),
                // 其他元素
                _buildMarkdownButton(
                  child: const Icon(Icons.code, size: 20),
                  onPressed: () => _insertCodeBlock(),
                  tooltip: '代码块',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.format_quote, size: 20),
                  onPressed: () => _insertMarkdownSyntax('> '),
                  tooltip: '引用',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.horizontal_rule, size: 20),
                  onPressed: () => _insertMarkdownSyntax('---\n'),
                  tooltip: '分隔线',
                ),
                // 插入图片和链接
                _buildMarkdownButton(
                  child: const Icon(Icons.image, size: 20),
                  onPressed: () =>
                      _insertMarkdownSyntax('![alt text](image_url)'),
                  tooltip: '插入图片',
                ),
                _buildMarkdownButton(
                  child: const Icon(Icons.link, size: 20),
                  onPressed: () => _insertMarkdownSyntax('[link text](url)'),
                  tooltip: '插入链接',
                ),
                // 表格按钮
                _buildMarkdownButton(
                  child: const Icon(Icons.table_chart, size: 20),
                  onPressed: () => _insertMarkdownSyntax(
                      '| 标题1 | 标题2 | 标题3 |\n| --- | --- | --- |\n| 内容1 | 内容2 | 内容3 |\n'),
                  tooltip: '插入表格',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建Markdown工具栏按钮
  Widget _buildMarkdownButton({
    required Widget child,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(4),
        child: Tooltip(
          message: tooltip,
          child: Container(
            padding: const EdgeInsets.all(8),
            child: child,
          ),
        ),
      ),
    );
  }

  // 在光标位置插入Markdown语法
  void _insertMarkdownSyntax(String prefix, [String suffix = '']) {
    if (_note?.contentType == 'markdown') {
      // 获取当前文本控制器
      final TextEditingController? markdownController =
          _getMarkdownController();
      if (markdownController != null) {
        final selection = markdownController.selection;

        if (selection.isValid) {
          final text = markdownController.text;
          final selectedText = selection.textInside(text);

          if (selectedText.isNotEmpty) {
            // 有选中文本，在选中文本前后添加语法
            final newText = prefix + selectedText + suffix;
            final newSelection = TextSelection.collapsed(
                offset: selection.baseOffset + newText.length);

            markdownController.value = TextEditingValue(
              text: text.replaceRange(selection.start, selection.end, newText),
              selection: newSelection,
            );
          } else {
            // 没有选中文本，在光标位置插入语法
            final newText = prefix + suffix;
            final newSelection = TextSelection.collapsed(
                offset: selection.baseOffset + prefix.length);

            markdownController.value = TextEditingValue(
              text:
                  text.replaceRange(selection.start, selection.start, newText),
              selection: newSelection,
            );
          }

          // 确保内容以换行符结尾
          String updatedContent = markdownController.text;
          if (!updatedContent.endsWith('\n')) {
            updatedContent += '\n';

            // 更新控制器内容，保持光标位置
            final currentSelection = markdownController.selection;
            markdownController.text = updatedContent;
            if (currentSelection.isValid) {
              markdownController.selection = currentSelection;
            }
          }

          // 更新笔记内容
          if (_note != null) {
            _note = _note!.copyWith(content: updatedContent);
          }
        }
      }
    }
  }

  // 获取Markdown编辑器的文本控制器
  TextEditingController? _getMarkdownController() {
    if (_note?.contentType == 'markdown') {
      // 确保控制器内容以换行符结尾
      if (!_markdownController.text.endsWith('\n')) {
        final text = _markdownController.text;
        final selection = _markdownController.selection;

        // 保存当前选择位置
        _markdownController.text = text + '\n';

        // 恢复选择位置
        if (selection.isValid) {
          _markdownController.selection = selection;
        }
      }

      // 返回类成员变量中的控制器
      return _markdownController;
    }
    return null;
  }

  // 插入代码块
  void _insertCodeBlock() {
    // 弹出语言选择对话框
    showDialog(
      context: context,
      builder: (context) {
        // 预定义的语言选项
        final languages = [
          'python',
          'javascript',
          'java',
          'c',
          'cpp',
          'csharp',
          'go',
          'rust',
          'swift',
          'kotlin',
          'php',
          'ruby',
          'sql',
          'html',
          'css',
          'xml',
          'json',
          'yaml',
          'bash',
          'plaintext',
        ];

        String selectedLanguage = 'python'; // 默认选中 Python

        return AlertDialog(
          title: const Text('选择代码语言'),
          content: Container(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 语言选项
                  ...languages.map((lang) => RadioListTile<String>(
                        title: Text(lang),
                        value: lang,
                        groupValue: selectedLanguage,
                        onChanged: (value) {
                          selectedLanguage = value!;
                          Navigator.pop(context, selectedLanguage);
                        },
                      )),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context, selectedLanguage),
              child: const Text('确定'),
            ),
          ],
        );
      },
    ).then((language) {
      if (language == null) return; // 用户取消

      // 根据选择的语言生成代码块模板
      String codeTemplate = '';

      // 根据语言生成示例代码
      switch (language) {
        case 'python':
          codeTemplate =
              "def hello():\n    print(\"Hello, World!\")\n\nhello()";
          break;
        case 'javascript':
          codeTemplate =
              "function hello() {\n    console.log(\"Hello, World!\");\n}\n\nhello();";
          break;
        case 'java':
          codeTemplate =
              "public class Hello {\n    public static void main(String[] args) {\n        System.out.println(\"Hello, World!\");\n    }\n}";
          break;
        case 'c':
          codeTemplate =
              "#include <stdio.h>\n\nint main() {\n    printf(\"Hello, World!\\n\");\n    return 0;\n}";
          break;
        case 'cpp':
          codeTemplate =
              "#include <iostream>\n\nint main() {\n    std::cout << \"Hello, World!\" << std::endl;\n    return 0;\n}";
          break;
        default:
          codeTemplate = "// 在此处输入代码";
          break;
      }

      // 构建完整的代码块
      final codeBlock = "```$language\n$codeTemplate\n```";

      // 插入代码块
      _insertMarkdownSyntax(codeBlock);
    });
  }

  /// 构建工具栏
  Widget _buildToolbar() {
    // 判断是否为Web环境
    if (kIsWeb) {
      // Web环境下使用原有的工具栏实现
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(
              color: AppTheme.lightGrayColor,
              width: 1,
            ),
          ),
        ),
        child: Column(
          children: [
            // 使用基本工具栏
            FleatherToolbar.basic(controller: _fleatherController),
            // 添加自定义按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 自定义按钮：插入图片
                IconButton(
                  icon: const Icon(Icons.image, size: 20),
                  onPressed: _insertImage,
                  tooltip: '插入图片',
                ),
                // 自定义按钮：插入表格
                IconButton(
                  icon: const Icon(Icons.table_chart, size: 20),
                  onPressed: _insertTable,
                  tooltip: '插入表格',
                ),
              ],
            ),
          ],
        ),
      );
    } else {
      // 移动设备环境下使用滚动工具栏
      return LayoutBuilder(
        builder: (context, constraints) {
          // 获取屏幕宽度，用于响应式布局
          final screenWidth = MediaQuery.of(context).size.width;
          final isSmallScreen = screenWidth < 600; // 定义小屏幕的阈值

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                top: BorderSide(
                  color: AppTheme.lightGrayColor,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              children: [
                // 使用SingleChildScrollView包装工具栏，使其可以在小屏幕上滚动
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    // 在小屏幕上减小内边距
                    padding:
                        EdgeInsets.symmetric(horizontal: isSmallScreen ? 4 : 8),
                    // 使用FleatherToolbar.basic
                    child:
                        FleatherToolbar.basic(controller: _fleatherController),
                  ),
                ),
                // 添加自定义按钮 - 使用Wrap替代Row，实现自动换行
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    spacing: 8, // 水平间距
                    runSpacing: 8, // 垂直间距
                    children: [
                      // 自定义按钮：插入图片
                      IconButton(
                        icon: const Icon(Icons.image, size: 20),
                        onPressed: _insertImage,
                        tooltip: '插入图片',
                        constraints: BoxConstraints.tightFor(
                            width: 40, height: 40), // 固定大小
                      ),
                      // 自定义按钮：插入表格
                      IconButton(
                        icon: const Icon(Icons.table_chart, size: 20),
                        onPressed: _insertTable,
                        tooltip: '插入表格',
                        constraints: BoxConstraints.tightFor(
                            width: 40, height: 40), // 固定大小
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  /// 构建阅读模式头部
  Widget _buildReadingHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.lightGrayColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.arrow_back,
                color: AppTheme.darkGrayColor,
                size: 20,
              ),
            ),
          ),

          // 标题
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                _titleController.text,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),

          Row(
            children: [
              // 切换编辑模式按钮 - 只在可编辑笔记上显示
              if (_note?.shareAccessType == 'editable')
                InkWell(
                  onTap: _toggleMode,
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.edit,
                      color: AppTheme.darkGrayColor,
                      size: 20,
                    ),
                  ),
                ),
              const SizedBox(width: 16),
              // 复制链接按钮
              InkWell(
                onTap: () {
                  final currentUrl = Uri.base.toString();
                  Clipboard.setData(ClipboardData(text: currentUrl));
                  SnackbarHelper.showSuccess(
                      context: context, message: '链接已复制到剪贴板');
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.copy,
                    color: AppTheme.darkGrayColor,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建阅读模式底部
  Widget _buildReadingFooter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: AppTheme.lightGrayColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '分享笔记 | 使用智云笔记创建',
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.darkGrayColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 保存笔记更改
  Future<void> _saveChanges() async {
    if (_note == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // 获取当前笔记内容
      String content;
      final title = _titleController.text;

      // 根据笔记类型处理内容
      if (_note!.contentType == 'markdown') {
        // 对于Markdown格式，确保内容以换行符结尾
        content = _markdownController.text;
        if (!content.endsWith('\n')) {
          content += '\n';
          print('DEBUG: [SharedNoteViewPage] 添加换行符到Markdown内容末尾');
        }
      } else {
        // 对于富文本格式，使用JSON编码
        content = jsonEncode(_document.toDelta().toJson());
      }

      // 调用Provider更新分享笔记
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      final result = await noteProvider.updateSharedNote(
        token: widget.shareToken,
        title: title,
        content: content,
        // 如果笔记需要密码，传入密码
        password: _passwordController.text.isNotEmpty
            ? _passwordController.text
            : null,
      );

      if (result) {
        SnackbarHelper.showSuccess(context: context, message: '笔记已保存');

        // 保存成功后，如果是Markdown格式，刷新阅读模式的内容
        if (_note!.contentType == 'markdown' && !_isEditing) {
          setState(() {
            // 刷新UI以显示更新后的内容
          });
        }
      } else {
        SnackbarHelper.showError(
            context: context,
            message:
                noteProvider.error.isNotEmpty ? noteProvider.error : '保存笔记失败');
      }
    } catch (e) {
      print('ERROR: [SharedNoteViewPage] 保存笔记失败: $e');
      SnackbarHelper.showError(context: context, message: '保存笔记失败: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return '刚刚';
        }
        return '${difference.inMinutes}分钟前';
      }
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    }
  }

  /// 在嵌套的Map中查找值
  String? _findValueInMap(Map<dynamic, dynamic> map, String key) {
    if (map.containsKey(key)) {
      return map[key]?.toString();
    }

    for (var value in map.values) {
      if (value is Map<dynamic, dynamic>) {
        final result = _findValueInMap(value, key);
        if (result != null) {
          return result;
        }
      }
    }

    return null;
  }

  /// 构建可编辑表格
  Widget _buildEditableTable(
    BuildContext context,
    int rows,
    int columns,
    List<List<String>> cellsData,
    List<double> rowHeights,
    List<double> columnWidths,
    EmbedNode node,
    String tableId,
  ) {
    print('DEBUG: [SharedNoteViewPage] 构建可编辑表格, ID: $tableId');
    print('DEBUG: [SharedNoteViewPage] 行高: $rowHeights');
    print('DEBUG: [SharedNoteViewPage] 列宽: $columnWidths');

    // 计算表格总宽度（所有列宽之和）
    double totalTableWidth = columnWidths.fold(0, (sum, width) => sum + width);
    // 限制表格最大宽度
    final screenWidth = MediaQuery.of(context).size.width;
    totalTableWidth = min(totalTableWidth, screenWidth * 0.95);

    print('DEBUG: [SharedNoteViewPage] 表格总宽度: $totalTableWidth');

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Align(
        alignment: Alignment.center,
        child: Container(
          constraints: BoxConstraints(
            maxWidth: totalTableWidth,
          ),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 表格内容
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Table(
                  border: TableBorder.all(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                  // 应用自定义列宽
                  columnWidths: Map.fromIterable(
                    List.generate(columns, (index) => index),
                    key: (i) => i,
                    value: (i) => FixedColumnWidth(columnWidths[i]),
                  ),
                  children: List.generate(
                    rows,
                    (i) => TableRow(
                      // 应用自定义行高
                      decoration: i == 0
                          ? BoxDecoration(color: Colors.grey.shade200)
                          : null,
                      children: List.generate(
                        columns,
                        (j) => Container(
                          height: rowHeights[i],
                          constraints: BoxConstraints(
                            minHeight: rowHeights[i],
                            maxHeight: rowHeights[i],
                          ),
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Text(
                              i < cellsData.length && j < cellsData[i].length
                                  ? cellsData[i][j]
                                  : '',
                              textAlign: TextAlign.center,
                              overflow: TextOverflow.ellipsis,
                              style: i == 0
                                  ? const TextStyle(fontWeight: FontWeight.bold)
                                  : null,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // 编辑按钮
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      icon: const Icon(
                        Icons.edit,
                        size: 16,
                      ),
                      label: const Text('编辑表格'),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                      onPressed: () {
                        print(
                            'DEBUG: [SharedNoteViewPage] 点击编辑表格按钮, ID: $tableId');
                        _showTableEditDialog(
                          context,
                          rows,
                          columns,
                          cellsData,
                          rowHeights,
                          columnWidths,
                          node,
                          tableId,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示表格编辑对话框
  void _showTableEditDialog(
    BuildContext context,
    int rows,
    int columns,
    List<List<String>> initialData,
    List<double> initialRowHeights,
    List<double> initialColumnWidths,
    EmbedNode node,
    String tableId,
  ) {
    print('DEBUG: [SharedNoteViewPage] 显示表格编辑对话框, ID: $tableId');

    // 设置行高和列宽
    List<double> rowHeights = List.from(initialRowHeights);
    List<double> columnWidths = List.from(initialColumnWidths);

    // 创建单元格编辑控制器的数组
    List<List<TextEditingController>> controllers = List.generate(
      rows,
      (i) => List.generate(
        columns,
        (j) => TextEditingController(text: initialData[i][j]),
        growable: false,
      ),
      growable: false,
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: const EdgeInsets.all(16),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: min(MediaQuery.of(context).size.width - 32, 800),
              maxHeight: min(MediaQuery.of(context).size.height - 80, 600),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Text('编辑表格',
                          style: TextStyle(
                              fontSize: 18, fontWeight: FontWeight.bold)),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                const Divider(),
                // 表格编辑区域
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 行高和列宽控制
                          if (rows > 0 && columns > 0)
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text('行高设置:',
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          const Text('第1行(表头): '),
                                          Expanded(
                                            child: Slider(
                                              value: rowHeights[0],
                                              min: 30,
                                              max: 100,
                                              divisions: 14,
                                              label: rowHeights[0]
                                                  .round()
                                                  .toString(),
                                              onChanged: (value) {
                                                rowHeights[0] = value;
                                                (context as Element)
                                                    .markNeedsBuild();
                                              },
                                            ),
                                          ),
                                          Text('${rowHeights[0].round()}px'),
                                        ],
                                      ),
                                      if (rows > 1)
                                        Row(
                                          children: [
                                            const Text('数据行: '),
                                            Expanded(
                                              child: Slider(
                                                value: rowHeights[1],
                                                min: 30,
                                                max: 100,
                                                divisions: 14,
                                                label: rowHeights[1]
                                                    .round()
                                                    .toString(),
                                                onChanged: (value) {
                                                  // 更新所有数据行的高度
                                                  for (int i = 1;
                                                      i < rows;
                                                      i++) {
                                                    rowHeights[i] = value;
                                                  }
                                                  (context as Element)
                                                      .markNeedsBuild();
                                                },
                                              ),
                                            ),
                                            Text('${rowHeights[1].round()}px'),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text('列宽设置:',
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          const Text('所有列: '),
                                          Expanded(
                                            child: Slider(
                                              value: columnWidths[0],
                                              min: 60,
                                              max: 200,
                                              divisions: 14,
                                              label: columnWidths[0]
                                                  .round()
                                                  .toString(),
                                              onChanged: (value) {
                                                // 更新所有列的宽度
                                                for (int i = 0;
                                                    i < columns;
                                                    i++) {
                                                  columnWidths[i] = value;
                                                }
                                                (context as Element)
                                                    .markNeedsBuild();
                                              },
                                            ),
                                          ),
                                          Text('${columnWidths[0].round()}px'),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),

                          const SizedBox(height: 20),
                          const Text('表格内容:',
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8),
                          // 表格编辑区域
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: SingleChildScrollView(
                              child: Table(
                                border: TableBorder.all(),
                                defaultVerticalAlignment:
                                    TableCellVerticalAlignment.middle,
                                columnWidths: Map.fromIterable(
                                  List.generate(columns, (index) => index),
                                  key: (i) => i,
                                  value: (i) =>
                                      FixedColumnWidth(columnWidths[i]),
                                ),
                                children: List.generate(
                                  rows,
                                  (i) => TableRow(
                                    decoration: i == 0
                                        ? BoxDecoration(
                                            color: Colors.grey.shade200)
                                        : null,
                                    children: List.generate(
                                      columns,
                                      (j) => Container(
                                        height: rowHeights[i],
                                        padding: const EdgeInsets.all(4),
                                        child: Center(
                                          child: TextField(
                                            controller: controllers[i][j],
                                            textAlign: TextAlign.center,
                                            style: i == 0
                                                ? const TextStyle(
                                                    fontWeight: FontWeight.bold)
                                                : null,
                                            decoration: const InputDecoration(
                                              border: InputBorder.none,
                                              contentPadding: EdgeInsets.all(4),
                                              isDense: true,
                                            ),
                                            expands: true,
                                            maxLines: null,
                                            minLines: null,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Divider(),
                // 底部按钮
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('取消'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          print(
                              'DEBUG: [SharedNoteViewPage] 表格编辑完成，准备更新表格 ID: $tableId');

                          // 收集编辑后的数据
                          List<List<String>> newData = List.generate(
                            rows,
                            (i) => List.generate(
                              columns,
                              (j) => controllers[i][j].text,
                              growable: true,
                            ),
                            growable: true,
                          );

                          // 关闭对话框
                          Navigator.of(context).pop();

                          // 更新表格
                          _updateTableInDocument(newData, rows, columns,
                              rowHeights, columnWidths, tableId);
                        },
                        child: const Text('保存'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 更新文档中的表格
  void _updateTableInDocument(
    List<List<String>> tableData,
    int rows,
    int columns,
    List<double> rowHeights,
    List<double> columnWidths,
    String tableId,
  ) {
    try {
      // 查找当前 Delta 中的全部操作
      final deltaOperations = _document.toDelta().toList();

      // 详细打印所有操作的结构，帮助调试
      print('DEBUG: [SharedNoteViewPage] 开始查找表格 ID: $tableId');
      print('DEBUG: [SharedNoteViewPage] 文档包含 ${deltaOperations.length} 个操作');

      // 尝试找到文档位置的另一种方法
      // 先尝试找到具有相同ID的表格位置
      int tableIndex = -1;
      int currentOffset = 0; // 跟踪当前操作的偏移量

      print('DEBUG: [SharedNoteViewPage] 使用多种算法查找表格位置...');

      // 使用UUID搜索 - 方法1
      for (int i = 0; i < deltaOperations.length; i++) {
        final op = deltaOperations[i];

        if (op.isInsert && op.data is Map<String, dynamic>) {
          final opData = op.data as Map<String, dynamic>;

          // 打印关键操作的结构，帮助调试
          print(
              'DEBUG: [SharedNoteViewPage] 检查操作 #$i: ${opData.toString().substring(0, min(100, opData.toString().length))}...');

          // 使用递归查找函数
          String? foundTableId = _findValueInMap(opData, 'tableId');
          if (foundTableId == tableId) {
            print(
                'DEBUG: [SharedNoteViewPage] 方法1: 找到匹配的表格 ID: $foundTableId 在操作 #$i');
            tableIndex = i;
            break;
          }
        }

        // 累加偏移量
        currentOffset += op.length;
      }

      // 如果方法1失败，尝试方法2 - 查找任何表格类型的操作
      if (tableIndex == -1) {
        print('DEBUG: [SharedNoteViewPage] 方法1未找到表格，尝试方法2: 查找任何表格类型的操作');

        // 记录表格操作的索引
        List<int> tableIndices = [];

        // 首先，找出所有表格操作
        for (int i = 0; i < deltaOperations.length; i++) {
          final op = deltaOperations[i];
          if (op.isInsert && op.data is Map<String, dynamic>) {
            final opData = op.data as Map<String, dynamic>;

            // 检查是否是表格操作 - 使用包含多种可能嵌入方式
            bool isTableOperation = false;

            // 遍历所有键查找与"table"相关的
            for (final key in opData.keys) {
              // 检查键值是否包含table或type字段
              if (key == 'type' && opData[key] == 'table') {
                isTableOperation = true;
                break;
              }
              // 检查嵌入键中的数据
              else if (key == 'embed' && opData[key] is Map) {
                final embedData = opData[key] as Map;
                if (embedData['type'] == 'table') {
                  isTableOperation = true;
                  break;
                }
              }
              // 其他可能的格式...
            }

            if (isTableOperation) {
              tableIndices.add(i);
              print(
                  'DEBUG: [SharedNoteViewPage] 方法2: 找到表格操作 #$i: ${opData.toString().substring(0, min(100, opData.toString().length))}...');
            }
          }
        }

        print(
            'DEBUG: [SharedNoteViewPage] 方法2: 共找到 ${tableIndices.length} 个表格操作');

        // 如果只找到一个表格，就假定它是我们要修改的
        if (tableIndices.length == 1) {
          tableIndex = tableIndices[0];
          print('DEBUG: [SharedNoteViewPage] 方法2: 只找到一个表格操作，将使用索引 $tableIndex');
        }
        // 如果找到多个表格，暂时使用最后一个
        else if (tableIndices.isNotEmpty) {
          tableIndex = tableIndices.last; // 使用最后一个表格，通常是最新插入的
          print(
              'DEBUG: [SharedNoteViewPage] 方法2: 找到 ${tableIndices.length} 个表格操作，将使用最后一个，索引 $tableIndex');
        }
      }

      // 如果找到了表格位置
      if (tableIndex >= 0) {
        // 重新计算准确的偏移量
        int targetOffset = 0;
        for (int i = 0; i < tableIndex; i++) {
          targetOffset += deltaOperations[i].length;
        }

        print(
            'DEBUG: [SharedNoteViewPage] 找到表格 ID: $tableId, 位置: $targetOffset (索引 $tableIndex), 开始替换');

        // 创建新的表格嵌入数据，*** 必须包含相同的 tableId ***
        final tableObject = EmbeddableObject('table', inline: false, data: {
          'rows': rows,
          'columns': columns,
          'tableData': tableData,
          'rowHeights':
              rowHeights.map((h) => h.roundToDouble()).toList(), // 保留 Double
          'columnWidths':
              columnWidths.map((w) => w.roundToDouble()).toList(), // 保留 Double
          'tableId': tableId, // *** 保持 ID 不变 ***
        });

        // 替换表格 (嵌入对象长度为 1)
        _fleatherController.replaceText(
          targetOffset, // 使用计算出的偏移量
          1, // 嵌入对象的长度总是 1
          tableObject,
        );

        // 提示用户表格已更新
        SnackbarHelper.showSuccess(context: context, message: '表格已更新');
      } else {
        print('DEBUG: [SharedNoteViewPage] 所有操作数据检查完毕，未找到表格 ID: $tableId');
        print('ERROR: [SharedNoteViewPage] 无法找到具有 ID $tableId 的表格进行更新。这不应该发生。');

        // 为了安全，暂时还是插入新表格并提示用户
        final currentSelection = _fleatherController.selection;
        final tableObject = EmbeddableObject('table', inline: false, data: {
          'rows': rows,
          'columns': columns,
          'tableData': tableData,
          'rowHeights': rowHeights.map((h) => h.roundToDouble()).toList(),
          'columnWidths': columnWidths.map((w) => w.roundToDouble()).toList(),
          'tableId': tableId, // 仍然给新表格一个ID
        });
        _fleatherController.replaceText(
            currentSelection.baseOffset, 0, tableObject);
        _fleatherController.replaceText(
            currentSelection.baseOffset + 1, 0, '\n');

        SnackbarHelper.showError(
            context: context, message: '无法找到原始表格进行更新，已创建新表格。请手动删除旧表格。');
      }
    } catch (e, stackTrace) {
      // 添加堆栈跟踪
      print('ERROR: [SharedNoteViewPage] 更新表格失败: $e\n$stackTrace'); // 打印堆栈
      SnackbarHelper.showError(context: context, message: '表格更新失败: $e');
    }
  }
}
