# 智云笔记应用技术规格文档

## 1. 技术架构概述

智云笔记采用前后端分离的架构设计，前端使用Flutter框架开发跨平台客户端，后端使用Node.js + Express框架构建RESTful API服务。数据持久化采用MongoDB数据库，同时结合Redis用于缓存和会话管理。

### 1.1 总体架构图

```
+-------------------+      +-------------------+      +-------------------+
|                   |      |                   |      |                   |
|  Flutter客户端    | <--> |  Express后端服务  | <--> |  MongoDB数据库    |
|  (Web/Android/iOS)|      |  (Node.js)        |      |  + Redis缓存      |
|                   |      |                   |      |                   |
+-------------------+      +-------------------+      +-------------------+
```

### 1.2 核心技术栈

#### 前端技术栈
- **框架**: Flutter 3.10+
- **编程语言**: Dart 3.0+
- **状态管理**: Provider/Riverpod
- **路由**: Flutter Navigator 2.0
- **本地存储**: SQLite (笔记本地缓存)、SharedPreferences (配置和用户状态)
- **网络请求**: Dio
- **依赖注入**: GetIt
- **UI组件**: Material Design 3
- **图片处理**: Flutter Image
- **图标**: Font Awesome Flutter

#### 后端技术栈
- **运行时环境**: Node.js 18+
- **Web框架**: Express 4.18+
- **编程语言**: TypeScript 5+
- **API文档**: Swagger/OpenAPI
- **数据库**: MongoDB 5+ (文档存储)
- **缓存**: Redis 6+ (会话和频繁访问数据)
- **ORM/ODM**: Mongoose (MongoDB操作)
- **认证**: JWT (无状态认证)
- **日志**: Winston (应用日志)、Morgan (HTTP请求日志)
- **测试**: Jest (单元测试)、Supertest (API测试)

## 2. 前端架构设计

### 2.1 应用架构

智云笔记前端采用Clean Architecture架构模式，将应用分为以下几层：

1. **表现层** (Presentation Layer):
   - 包含所有UI组件、页面和视图模型
   - 负责用户界面渲染和用户交互
   - 通过Provider与业务逻辑层通信

2. **业务逻辑层** (Business Logic Layer):
   - 包含用例(Use Cases)和领域模型
   - 实现业务规则和应用逻辑
   - 协调数据流动和转换

3. **数据层** (Data Layer):
   - 包含Repository实现和数据源
   - 负责数据获取和持久化
   - 提供API和本地数据存储接口

### 2.2 目录结构

```
lib/
├── config/                     # 配置文件
│   ├── app_config.dart         # 应用配置
│   └── theme/                  # 主题配置
│       ├── app_theme.dart      # 主题定义
│       └── colors.dart         # 颜色常量
│
├── data/                       # 数据层
│   ├── datasources/            # 数据源
│   │   ├── local/              # 本地数据源
│   │   └── remote/             # 远程API数据源
│   ├── models/                 # 数据模型
│   │   ├── note_model.dart     # 笔记模型
│   │   ├── tag_model.dart      # 标签模型
│   │   └── user_model.dart     # 用户模型
│   └── repositories/           # 仓库实现
│       ├── note_repository.dart # 笔记仓库
│       ├── tag_repository.dart  # 标签仓库
│       └── user_repository.dart # 用户仓库
│
├── domain/                     # 领域层
│   ├── entities/               # 实体对象
│   ├── repositories/           # 仓库接口
│   └── usecases/               # 用例类
│
├── presentation/               # 表现层
│   ├── providers/              # 状态提供者
│   │   ├── auth_provider.dart  # 认证状态
│   │   ├── note_provider.dart  # 笔记状态
│   │   ├── tag_provider.dart   # 标签状态
│   │   ├── theme_service.dart  # 主题服务
│   │   └── ai_provider.dart    # AI功能状态
│   ├── screens/                # 页面
│   │   ├── auth/               # 认证相关页面
│   │   ├── home/               # 主页相关
│   │   ├── editor/             # 编辑器相关
│   │   ├── profile/            # 个人资料相关
│   │   ├── tags/               # 标签管理相关
│   │   ├── settings/           # 设置相关
│   │   ├── search/             # 搜索相关
│   │   └── share/              # 分享相关
│   └── widgets/                # 可复用组件
│       ├── common/             # 通用组件
│       ├── note/               # 笔记相关组件
│       └── tag/                # 标签相关组件
│
├── utils/                      # 工具类
│   ├── extensions/             # Dart扩展方法
│   ├── formatters/             # 格式化工具
│   ├── validators/             # 验证工具
│   ├── snackbar_helper.dart    # 提示消息辅助工具
│   └── services/               # 服务类
│       ├── api_service.dart    # API服务
│       ├── storage_service.dart # 存储服务
│       └── ai_function_service.dart # AI功能服务
│
├── routes/                     # 路由管理
│   ├── app_routes.dart         # 路由定义
│   └── route_generator.dart    # 路由生成器
│
└── main.dart                   # 入口文件
```

### 2.3 状态管理

应用采用Provider/Riverpod进行状态管理，具体实现如下：

#### 2.3.1 主要状态提供者

- **AuthProvider**: 管理用户认证状态，包括登录、注册和会话管理
- **NoteProvider**: 管理笔记数据状态，包括笔记列表、当前编辑笔记等
- **TagProvider**: 管理标签数据状态，包括标签列表、筛选等
- **ThemeProvider**: 管理应用主题和外观设置
- **SettingsProvider**: 管理用户偏好设置
- **AIProvider**: 管理AI功能设置和状态

#### 2.3.2 状态管理流程

1. UI组件通过Provider.of或Consumer访问状态
2. 状态变更通过Provider内的方法触发
3. Provider执行业务逻辑并调用Repository
4. 数据更新后，Provider调用notifyListeners通知UI更新

### 2.4 数据流管理

#### 2.4.1 在线模式数据流

```
UI组件 -> Provider -> UseCase -> Repository -> RemoteDataSource -> API服务器
```

#### 2.4.2 离线模式数据流

```
UI组件 -> Provider -> UseCase -> Repository -> LocalDataSource -> SQLite/SharedPreferences
```

#### 2.4.3 同步机制

- 应用启动时自动同步本地和远程数据
- 用户操作实时保存到本地，并根据网络状态决定是否立即同步到服务器
- 冲突解决策略：默认以服务器数据为准，但保留本地版本供用户选择

### 2.5 路由导航策略

#### 2.5.1 路由管理原则

- 使用命名路由进行所有页面间导航，确保URL正确更新
- 路由名称在`app_routes.dart`中集中定义为常量
- 使用`Navigator.pushNamed`和`Navigator.pushReplacementNamed`进行导航
- 使用`Navigator.pop`返回上一页面
- 动态路由参数通过`arguments`传递，并在`generateRoute`方法中处理

#### 2.5.2 路由实现方式

```dart
// 定义路由
static const String login = '/login';
static const String register = '/register';
static const String editor = '/editor';

// 注册路由
static Map<String, WidgetBuilder> routes = {
  login: (context) => const LoginPage(),
  register: (context) => const RegisterPage(),
};

// 导航示例
Navigator.of(context).pushNamed(AppRoutes.register);

// 带参数导航示例
Navigator.pushNamed(
  context,
  AppRoutes.editor,
  arguments: note,
);

// 动态路由处理
static Route<dynamic> generateRoute(RouteSettings settings) {
  switch (settings.name) {
    case editor:
      final args = settings.arguments;
      return MaterialPageRoute(
        builder: (context) => EditorPage(note: args),
      );
    default:
      return MaterialPageRoute(
        builder: (_) => const NotFoundPage(),
      );
  }
}
```

### 2.6 主要组件设计

#### 2.6.1 富文本编辑器

- 基于Fleather和flutter_markdown构建
- 支持多种文本格式化选项（粗体、斜体、标题等）
- 支持图片插入和处理
- 支持表格和代码块
- 支持Markdown和富文本两种编辑模式切换

#### 2.6.2 笔记列表组件

- 支持网格视图和列表视图切换
- 实现虚拟滚动以优化性能
- 支持笔记项的左右滑动操作
- 支持多选模式进行批量操作

#### 2.6.3 标签管理组件

- 支持拖拽排序
- 集成颜色选择器
- 支持搜索和筛选
- 显示标签使用统计

#### 2.6.4 AI助手组件

- 支持智能预测和自动补全
- 支持标签建议和智能摘要
- 支持笔记问答功能
- 可拖动的悬浮图标

## 3. 后端架构设计

### 3.1 API设计原则

- 遵循RESTful API设计规范
- 使用JSON作为数据交换格式
- 采用JWT进行无状态认证
- 实现适当的速率限制和缓存策略
- 版本控制：在URL中包含API版本号(如 `/api/v1/notes`)

### 3.2 核心数据模型

#### 3.2.1 用户模型(User)

```typescript
interface IUser {
  id: string;               // 用户唯一标识
  username: string;         // 用户名
  email: string;            // 电子邮箱
  passwordHash: string;     // 密码哈希
  avatar?: string;          // 头像URL
  bio?: string;             // 个人简介
  createdAt: Date;          // 创建时间
  updatedAt: Date;          // 更新时间
  lastLoginAt?: Date;       // 最后登录时间
}
```

#### 3.2.2 笔记模型(Note)

```typescript
interface INote {
  id: string;               // 笔记唯一标识
  userId: string;           // 所属用户ID
  title: string;            // 标题
  content: string;          // 内容(富文本格式)
  contentType: string;      // 内容类型(markdown/rich-text)
  tags: string[];           // 标签ID列表
  color?: string;           // 笔记颜色
  isPinned: boolean;        // 是否置顶
  isFavorite: boolean;      // 是否收藏
  isArchived: boolean;      // 是否归档
  isDeleted: boolean;       // 是否删除(软删除)
  deletedAt?: Date;         // 删除时间
  createdAt: Date;          // 创建时间
  updatedAt: Date;          // 更新时间
  version: number;          // 版本号(用于冲突解决)
}
```

#### 3.2.3 标签模型(Tag)

```typescript
interface ITag {
  id: string;               // 标签唯一标识
  userId: string;           // 所属用户ID
  name: string;             // 标签名称
  color: string;            // 标签颜色
  order: number;            // 排序位置
  createdAt: Date;          // 创建时间
  updatedAt: Date;          // 更新时间
}
```

#### 3.2.4 用户设置模型(UserSettings)

```typescript
interface IUserSettings {
  userId: string;           // 用户ID
  theme: string;            // 主题偏好
  fontSize: number;         // 字体大小
  language: string;         // 语言偏好
  syncEnabled: boolean;     // 是否启用同步
  syncFrequency: string;    // 同步频率
  notificationsEnabled: boolean; // 是否启用通知
  createdAt: Date;          // 创建时间
  updatedAt: Date;          // 更新时间
}
```

### 3.3 API端点规范

#### 3.3.1 认证API

```
POST /api/v1/auth/register          # 用户注册
POST /api/v1/auth/login             # 用户登录
POST /api/v1/auth/refresh-token     # 刷新Token
POST /api/v1/auth/logout            # 用户登出
POST /api/v1/auth/forgot-password   # 忘记密码
POST /api/v1/auth/reset-password    # 重置密码
GET  /api/v1/auth/me                # 获取当前用户信息
```

#### 3.3.2 笔记API

```
GET    /api/v1/notes                # 获取笔记列表
POST   /api/v1/notes                # 创建笔记
GET    /api/v1/notes/:id            # 获取单个笔记
PUT    /api/v1/notes/:id            # 更新笔记
DELETE /api/v1/notes/:id            # 删除笔记
PATCH  /api/v1/notes/:id/pin        # 置顶/取消置顶笔记
PATCH  /api/v1/notes/:id/favorite   # 收藏/取消收藏笔记
PATCH  /api/v1/notes/:id/archive    # 归档/取消归档笔记
POST   /api/v1/notes/:id/restore    # 恢复已删除的笔记
GET    /api/v1/notes/trash          # 获取回收站内的笔记
DELETE /api/v1/notes/trash          # 清空回收站
```

#### 3.3.3 标签API

```
GET    /api/v1/tags                 # 获取标签列表
POST   /api/v1/tags                 # 创建标签
GET    /api/v1/tags/:id             # 获取单个标签
PUT    /api/v1/tags/:id             # 更新标签
DELETE /api/v1/tags/:id             # 删除标签
PUT    /api/v1/tags/order           # 更新标签顺序
GET    /api/v1/tags/:id/notes       # 获取标签关联的笔记
```

#### 3.3.4 用户API

```
GET    /api/v1/users/profile        # 获取用户资料
PUT    /api/v1/users/profile        # 更新用户资料
PUT    /api/v1/users/password       # 修改密码
POST   /api/v1/users/avatar         # 上传头像
DELETE /api/v1/users/avatar         # 删除头像
```

#### 3.3.5 设置API

```
GET    /api/v1/settings             # 获取用户设置
PUT    /api/v1/settings             # 更新用户设置
POST   /api/v1/settings/reset       # 重置为默认设置
```

#### 3.3.6 搜索API

```
GET    /api/v1/search               # 搜索笔记
GET    /api/v1/search/suggestions   # 获取搜索建议
```

#### 3.3.7 AI功能API

```
POST   /api/v1/ai/text-prediction   # 文本预测
POST   /api/v1/ai/summarize         # 生成摘要
POST   /api/v1/ai/tag-suggestion    # 标签建议
```

### 3.4 安全实现

#### 3.4.1 认证与授权

- 使用JWT(JSON Web Token)进行用户认证
- Token包含用户ID和角色信息
- 令牌过期机制：访问令牌短期有效，刷新令牌长期有效
- 敏感操作需要二次验证

#### 3.4.2 数据加密

- 用户密码使用bcrypt哈希存储
- API通信通过HTTPS加密
- 敏感数据存储时进行加密

#### 3.4.3 安全防护

- 实现CORS(跨域资源共享)策略
- 防止SQL注入和NoSQL注入
- XSS防护
- CSRF防护
- 请求速率限制防止暴力攻击

## 4. 数据库设计

### 4.1 MongoDB集合设计

#### 4.1.1 users集合

```javascript
{
  _id: ObjectId,            // MongoDB自动生成的ID
  username: String,         // 用户名
  email: String,            // 电子邮箱(唯一索引)
  passwordHash: String,     // 密码哈希
  avatar: String,           // 头像URL
  bio: String,              // 个人简介
  createdAt: Date,          // 创建时间
  updatedAt: Date,          // 更新时间
  lastLoginAt: Date         // 最后登录时间
}
```

#### 4.1.2 notes集合

```javascript
{
  _id: ObjectId,            // MongoDB自动生成的ID
  userId: ObjectId,         // 所属用户ID(外键索引)
  title: String,            // 标题
  content: String,          // 内容(富文本格式)
  contentType: String,      // 内容类型
  tags: [ObjectId],         // 标签ID列表
  color: String,            // 笔记颜色
  isPinned: Boolean,        // 是否置顶
  isFavorite: Boolean,      // 是否收藏
  isArchived: Boolean,      // 是否归档
  isDeleted: Boolean,       // 是否删除(软删除)
  deletedAt: Date,          // 删除时间
  createdAt: Date,          // 创建时间
  updatedAt: Date,          // 更新时间
  version: Number           // 版本号
}
```

#### 4.1.3 tags集合

```javascript
{
  _id: ObjectId,            // MongoDB自动生成的ID
  userId: ObjectId,         // 所属用户ID(外键索引)
  name: String,             // 标签名称
  color: String,            // 标签颜色
  order: Number,            // 排序位置
  createdAt: Date,          // 创建时间
  updatedAt: Date           // 更新时间
}
```

#### 4.1.4 userSettings集合

```javascript
{
  _id: ObjectId,            // MongoDB自动生成的ID
  userId: ObjectId,         // 用户ID(唯一索引)
  theme: String,            // 主题偏好
  fontSize: Number,         // 字体大小
  language: String,         // 语言偏好
  syncEnabled: Boolean,     // 是否启用同步
  syncFrequency: String,    // 同步频率
  notificationsEnabled: Boolean, // 是否启用通知
  createdAt: Date,          // 创建时间
  updatedAt: Date           // 更新时间
}
```

### 4.2 索引设计

- users集合: email(唯一索引)
- notes集合: userId(索引), tags(索引), isDeleted + userId(复合索引), isArchived + userId(复合索引)
- tags集合: userId(索引), userId + name(唯一复合索引)
- userSettings集合: userId(唯一索引)

### 4.3 查询优化

- 使用MongoDB聚合管道优化复杂查询
- 为常见查询创建适当的索引
- 利用投影(Projection)减少返回数据量
- 分页查询限制返回数量

## 5. 离线模式和同步策略

### 5.1 离线数据存储

- 使用SQLite存储本地笔记数据
- 使用SharedPreferences存储用户配置和简单状态

### 5.2 同步算法

1. **双向同步机制**:
   - 服务器到设备：应用启动和定期同步时触发
   - 设备到服务器：用户修改数据后触发

2. **冲突解决策略**:
   - 使用版本号(version)追踪修改
   - 采用"最后修改胜出"策略处理冲突
   - 提供冲突解决界面供用户选择保留哪个版本

3. **同步优化**:
   - 增量同步：只同步有变更的数据
   - 差异对比：只传输变更的字段
   - 批量操作：多个变更打包在一次请求中

## 6. 错误处理策略

### 6.1 前端错误处理

- 使用try-catch捕获异常
- 实现全局错误处理器
- 对不同类型错误提供友好的用户界面反馈
- 离线时提供降级体验

### 6.2 后端错误处理

- 集中式错误处理中间件
- 标准化错误响应格式
- 详细日志记录错误和调用栈
- 错误监控和报警机制

### 6.3 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  }
}
```

## 7. 性能优化策略

### 7.1 前端性能优化

- 懒加载和代码分割
- 图片优化：压缩、缓存和懒加载
- 虚拟滚动列表
- 节流和防抖处理用户输入
- 减少重绘和重排

### 7.2 后端性能优化

- 数据库查询优化
- API响应数据压缩
- 服务端缓存策略
- 负载均衡和水平扩展

### 7.3 网络优化

- HTTP/2支持
- 减少API请求次数
- 请求优先级管理
- 断点续传支持

## 8. 测试策略

### 8.1 前端测试

- 单元测试：测试实体、用例和服务类
- Widget测试：测试UI组件行为
- 集成测试：测试页面和功能流
- 性能测试：测试启动时间和渲染性能

### 8.2 后端测试

- 单元测试：测试控制器、服务和模型
- 集成测试：测试API端点和中间件
- 负载测试：测试并发和响应时间
- 安全测试：测试常见漏洞防护

## 9. 部署策略

### 9.1 CI/CD流程

- 源代码管理：Git
- 持续集成：GitHub Actions
- 测试自动化：每次PR和主分支提交触发
- 自动部署：根据环境和分支策略

### 9.2 环境配置

- 开发环境：本地开发服务器
- 测试环境：测试服务器或云环境
- 生产环境：负载均衡的云服务

### 9.3 监控与日志

- 使用Sentry进行错误监控
- ELK栈(Elasticsearch, Logstash, Kibana)进行日志管理
- Prometheus进行性能指标收集
- Grafana进行可视化和告警

## 10. 扩展性设计

### 10.1 插件系统

- 预留Plugin接口允许第三方功能扩展
- 支持自定义主题和样式
- 支持功能模块的启用/禁用

### 10.2 API扩展性

- 使用RESTful设计原则确保API灵活性
- 支持GraphQL接口满足复杂查询需求
- API版本控制支持兼容性和渐进升级

## 11. 附录

### 11.1 技术术语表

- **JWT (JSON Web Token)**: 一种基于JSON的开放标准，用于在网络应用环境间传递声明
- **ORM (Object-Relational Mapping)**: 对象关系映射，用于在关系型数据库和面向对象编程语言之间转换数据
- **ODM (Object-Document Mapping)**: 对象文档映射，用于在文档型数据库和面向对象编程语言之间转换数据
- **REST (Representational State Transfer)**: 一种软件架构风格，定义了一组用于创建Web服务的约束

### 11.2 第三方库依赖

详细列出前后端主要依赖的第三方库及其版本要求。