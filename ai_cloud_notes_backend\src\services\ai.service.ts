import axios from 'axios';
import { config } from '../config';
import { logger } from '../utils/logger';
import AISettings, { MODEL_MAPPING } from '../models/ai_settings.model';
import mongoose from 'mongoose';

/**
 * AI服务类
 * 负责与SiliconFlow API交互，提供AI功能
 */
class AIService {
  private apiUrl: string;
  private apiKey: string;

  constructor() {
    this.apiUrl = config.ai.serviceUrl;
    this.apiKey = config.ai.serviceKey;
  }

  /**
   * 获取用户选择的模型
   * @param userId 用户ID
   * @returns 模型名称
   */
  private async getUserModel(userId: string): Promise<string> {
    try {
      const userSettings = await AISettings.findOne({
        userId: new mongoose.Types.ObjectId(userId)
      });

      if (!userSettings || !userSettings.aiAssistantEnabled) {
        return MODEL_MAPPING['默认模型'];
      }

      return MODEL_MAPPING[userSettings.selectedModel] || MODEL_MAPPING['默认模型'];
    } catch (error) {
      logger.error(`获取用户模型失败: ${error}`);
      return MODEL_MAPPING['默认模型'];
    }
  }

  /**
   * 调用SiliconFlow API
   * @param userId 用户ID
   * @param messages 消息列表
   * @param options 选项
   * @returns API响应
   */
  private async callSiliconFlowAPI(
    userId: string,
    messages: Array<{role: string, content: string}>,
    options: {
      temperature?: number;
      max_tokens?: number;
      stream?: boolean;
      extra_body?: any; // 添加extra_body选项，用于FIM补全等功能
    } = {}
  ) {
    try {
      const model = await this.getUserModel(userId);

      // 准备请求体
      const requestBody: any = {
        model,
        messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens || 512,
        stream: options.stream || false
      };

      // 如果有extra_body，正确处理FIM参数
      if (options.extra_body) {
        // 根据SiliconFlow文档，FIM参数应该放在extra_body对象中
        requestBody.extra_body = {};

        // 检查是否包含FIM参数
        if (options.extra_body.prefix !== undefined) {
          requestBody.extra_body.prefix = options.extra_body.prefix;
        }
        if (options.extra_body.suffix !== undefined) {
          requestBody.extra_body.suffix = options.extra_body.suffix;
        }
      }

      // 记录请求体，便于调试
      logger.debug(`SiliconFlow API请求体: ${JSON.stringify(requestBody)}`);

      const response = await axios.post(
        `${this.apiUrl}/chat/completions`,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          }
        }
      );

      return response.data;
    } catch (error: any) {
      logger.error(`调用SiliconFlow API失败: ${error.message}`);
      throw new Error(`AI服务调用失败: ${error.message}`);
    }
  }

  /**
   * 智能预测
   * @param userId 用户ID
   * @param content 当前内容
   * @param options 选项
   * @returns 预测内容
   */
  async generateCompletion(userId: string, content: string, options: any = {}) {
    try {
      // 获取光标位置前的内容和光标位置
      const cursorPosition = options.cursorPosition || content.length;
      const textBeforeCursor = content.substring(0, cursorPosition);
      const textAfterCursor = content.substring(cursorPosition);

      // 如果光标前的内容为空，返回空预测
      if (textBeforeCursor.trim().length === 0) {
        return {
          success: true,
          data: {
            completion: ''
          }
        };
      }

      // 获取用户模型，检查是否支持FIM
      const model = await this.getUserModel(userId);
      const supportsFIM = model.includes('Coder') || model.includes('DeepSeek');

      // 判断是否在文本末尾编辑
      const isAtEnd = textAfterCursor.trim().length === 0;

      // 判断使用FIM还是前缀续写
      // 如果在文本末尾，或者后缀很短，优先使用前缀续写
      // 如果在文本中间，且前后文都有内容，使用FIM
      const useFIM = supportsFIM && !isAtEnd && textAfterCursor.trim().length > 2;

      // 根据模型支持情况和编辑位置选择不同的提示词
      let systemPrompt = '';
      let userPrompt = '';

      if (useFIM) {
        // 使用FIM专用提示词，优化以提高预测质量
        systemPrompt = '你是AI云笔记应用中的智能预测功能。你的任务是根据用户当前输入的内容和光标位置，预测最合适的补全内容。\n\n【重要规则】\n1. 只返回预测内容本身，不要添加任何前缀、后缀或标签\n2. 不要输出"前缀："、"后缀："、"预测："等标记\n3. 不要使用引号或其他特殊符号包裹预测内容\n4. 预测应该非常简短（最好不超过5个字）\n5. 预测内容必须能自然连接前缀和后缀\n6. 不要重复前缀或后缀中已有的内容\n7. 直接输出预测内容，不要有任何解释或额外文字';
        userPrompt = `我正在编辑一段文本，光标位置在前缀和后缀之间。请预测最合适的补全内容（不超过5个字）：\n\n前缀：${textBeforeCursor}\n后缀：${textAfterCursor}\n\n你的预测内容应该能自然地连接前缀和后缀，形成连贯的文本。请直接输出预测内容，不要添加任何标记或解释。`;
      } else {
        // 使用前缀续写提示词，优化以提高预测质量
        systemPrompt = '你是AI云笔记应用中的智能预测功能。你的任务是根据用户当前输入的内容，预测下一个最可能的单词或短语。\n\n【重要规则】\n1. 只返回预测内容本身，不要添加任何前缀或标签\n2. 不要重复用户已输入的内容\n3. 不要使用引号或其他特殊符号包裹预测内容\n4. 预测应该非常简短（最好不超过5个字）\n5. 预测内容应该是用户最可能接下来输入的内容\n6. 直接输出预测内容，不要有任何解释或额外文字';
        userPrompt = `我正在编辑一段文本，当前内容如下：\n\n${textBeforeCursor}\n\n请直接预测接下来最可能的内容（不超过5个字），不要重复我已经输入的内容，不要添加任何标记或解释。`;

        // 如果有后缀，添加到提示中
        if (textAfterCursor.trim().length > 0) {
          userPrompt += `\n\n注意：在我的文本后面还有以下内容：\n${textAfterCursor}\n请考虑这些后续内容，确保你的预测与之连贯。`;
        }
      }

      // 记录使用的方法
      logger.debug(`使用${useFIM ? 'FIM补全' : '前缀续写'}功能，模型: ${model}, 光标位置: ${isAtEnd ? '文本末尾' : '文本中间'}`);


      const messages = [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ];

      // 准备请求参数
      let extraBody = {};

      // 根据使用的方法准备参数
      if (useFIM) {
        extraBody = {
          prefix: textBeforeCursor,
          suffix: textAfterCursor
        };
      }

      // 准备API调用参数
      const apiOptions: any = {
        temperature: options.temperature || 0.2, // 使用更低的温度以获得更精确的预测
        max_tokens: options.max_tokens || 10 // 限制生成的token数量，避免过长预测
      };

      // 只有在使用FIM且有extraBody内容时才添加extra_body参数
      if (useFIM && Object.keys(extraBody).length > 0) {
        apiOptions.extra_body = extraBody;
      }

      const response = await this.callSiliconFlowAPI(userId, messages, apiOptions);

      // 处理预测结果
      let prediction = '';

      try {
        // 获取原始预测内容
        const rawPrediction = response.choices[0].message.content.trim();
        logger.debug(`原始预测内容: ${rawPrediction}`);

        // 如果使用了FIM功能（有prefix和suffix参数）
        if (options.extra_body && (options.extra_body.prefix !== undefined || options.extra_body.suffix !== undefined)) {
          // 尝试直接使用模型返回的内容
          prediction = rawPrediction;

          // 如果预测内容包含"前缀："或"后缀："等提示词，尝试提取实际预测内容
          if (rawPrediction.includes('补全内容：')) {
            const match = rawPrediction.match(/补全内容：(.*?)(?:$|(?=\n))/);
            if (match && match[1]) {
              prediction = match[1].trim();
            }
          } else if (rawPrediction.includes('：')) {
            // 尝试提取冒号后的内容作为预测
            const parts = rawPrediction.split('：');
            if (parts.length > 1) {
              prediction = parts[parts.length - 1].trim();
            }
          }

          // 如果预测内容为空，尝试使用前缀续写方式生成
          if (prediction.trim().length === 0 && options.extra_body.prefix) {
            logger.debug('FIM预测内容为空，尝试使用前缀续写方式');

            // 为前缀续写创建专门的消息
            const prefixMessages = [
              {
                role: 'system',
                content: '你是AI云笔记应用中的智能预测功能。你的任务是根据用户当前输入的内容，预测下一个最可能的单词或短语。\n\n【重要规则】\n1. 只返回预测内容本身，不要添加任何前缀或标签\n2. 不要重复用户已输入的内容\n3. 不要使用引号或其他特殊符号包裹预测内容\n4. 预测应该简短精确（一般不超过10个字）\n5. 直接输出预测内容，不要有任何解释或额外文字'
              },
              {
                role: 'user',
                content: `我正在编辑一段文本，当前内容如下：\n\n${options.extra_body.prefix}\n\n请直接预测接下来最可能的内容（不超过10个字），不要重复我已经输入的内容，不要添加任何标记或解释。`
              }
            ];

            const prefixOnlyResponse = await this.callSiliconFlowAPI(userId, prefixMessages, {
              temperature: options.temperature || 0.3,
              max_tokens: options.max_tokens || 20,
              extra_body: { prefix: options.extra_body.prefix }
            });

            let prefixPrediction = prefixOnlyResponse.choices[0].message.content.trim();
            logger.debug(`前缀续写原始结果: ${prefixPrediction}`);

            // 移除前缀部分，只保留新生成的内容
            if (prefixPrediction.startsWith(options.extra_body.prefix)) {
              prefixPrediction = prefixPrediction.substring(options.extra_body.prefix.length).trim();
            }

            // 清理预测内容
            prefixPrediction = this._cleanPrediction(prefixPrediction);
            logger.debug(`前缀续写清理后结果: ${prefixPrediction}`);

            prediction = prefixPrediction;
          }
        } else {
          // 非FIM模式，直接使用返回内容
          prediction = rawPrediction;
        }

        // 清理预测内容，移除特殊字符和冗余符号
        prediction = this._cleanPrediction(prediction);

        // 如果预测内容过长，截取前30个字符
        if (prediction.length > 30) {
          prediction = prediction.substring(0, 30);
        }

        // 如果预测内容仍为空，尝试使用普通方式生成
        if (prediction.trim().length === 0) {
          logger.debug('清理后预测内容为空，尝试使用普通方式生成');
          const fallbackResponse = await this.callSiliconFlowAPI(userId, messages, {
            temperature: options.temperature || 0.3,
            max_tokens: options.max_tokens || 20
          });

          prediction = fallbackResponse.choices[0].message.content.trim();
          prediction = this._cleanPrediction(prediction);

          // 如果预测内容过长，截取前30个字符
          if (prediction.length > 30) {
            prediction = prediction.substring(0, 30);
          }
        }
      } catch (error) {
        logger.error(`处理预测结果失败: ${error}`);
        // 如果处理失败，返回空字符串
        prediction = '';
      }

      logger.debug(`最终预测内容: ${prediction}`);

      return {
        success: true,
        data: {
          completion: prediction
        }
      };
    } catch (error: any) {
      logger.error(`生成预测失败: ${error.message}`);
      return {
        success: false,
        error: {
          message: `生成预测失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 生成内容摘要
   * @param userId 用户ID
   * @param content 笔记内容
   * @param options 选项
   * @returns 内容摘要
   */
  async generateSummary(userId: string, content: string, options: any = {}) {
    try {
      const messages = [
        {
          role: 'system',
          content: '你是AI云笔记应用中的内容摘要功能。你的任务是为用户的笔记生成简洁、准确、有洞察力的摘要。这个摘要应该捕捉笔记的核心观点、关键信息和主要结论，帮助用户快速理解笔记的主要内容。摘要应该有逼真的逻辑结构，便于用户快速复习和回顾笔记内容。'
        },
        {
          role: 'user',
          content: `请为我的云笔记生成一个简洁、准确的摘要（不超过100字），捕捉核心观点和关键信息：\n\n${content}`
        }
      ];

      const response = await this.callSiliconFlowAPI(userId, messages, {
        temperature: options.temperature || 0.3,
        max_tokens: options.max_tokens || 150
      });

      return {
        success: true,
        data: {
          summary: response.choices[0].message.content.trim()
        }
      };
    } catch (error: any) {
      logger.error(`生成摘要失败: ${error.message}`);
      return {
        success: false,
        error: {
          message: `生成摘要失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 生成标签建议
   * @param userId 用户ID
   * @param content 笔记内容
   * @param options 选项
   * @returns 标签建议列表
   */
  async generateTagSuggestions(userId: string, content: string, options: any = {}) {
    try {
      const messages = [
        {
          role: 'system',
          content: '你是AI云笔记应用中的智能标签生成功能。你的任务是为用户的笔记生成准确、相关、有组织性的标签。这些标签应该清晰地反映笔记的主题、领域、关键概念和重要元素，帮助用户更好地组织、分类和检索笔记。生成的标签应该简洁、准确、有价值，并且不重复。请生成真实有意义的标签内容，而不是JSON符号或结构标记。返回的标签应该是一个字符串数组，如["tag1", "tag2", "tag3"]。'
        },
        {
          role: 'user',
          content: `请为我的云笔记生成3-5个准确、相关的标签，每个标签不超过10个字，并且是真实有意义的标签内容（不要返回JSON符号或结构标记）。请以JSON数组格式返回，如["tag1", "tag2", "tag3"]\n\n笔记内容：${content}`
        }
      ];

      const response = await this.callSiliconFlowAPI(userId, messages, {
        temperature: options.temperature || 0.3,
        max_tokens: options.max_tokens || 100
      });

      let tags: string[] = [];

      try {
        // 尝试从响应中提取JSON数组
        const content = response.choices[0].message.content.trim();
        // 查找JSON数组的开始和结束位置
        const startIdx = content.indexOf('[');
        const endIdx = content.lastIndexOf(']');

        if (startIdx !== -1 && endIdx !== -1) {
          const jsonStr = content.substring(startIdx, endIdx + 1);
          try {
            const parsedTags = JSON.parse(jsonStr);

            // 验证解析出的标签是否有效
            if (Array.isArray(parsedTags)) {
              // 过滤掉JSON符号和结构标记
              tags = parsedTags
                .filter((tag: any) => {
                  // 确保是字符串
                  if (typeof tag !== 'string') return false;

                  // 过滤掉只包含JSON符号的标签
                  const jsonSymbols = ['{', '}', '[', ']', '"', '\'', ':', ','];
                  const isJsonSymbol = jsonSymbols.includes(tag) ||
                                      (tag === 'json') ||
                                      (tag.length === 1 && jsonSymbols.includes(tag));

                  return !isJsonSymbol && tag.length > 0 && tag.length <= 10;
                });

              // 如果过滤后没有有效标签，使用备选方法
              if (tags.length === 0) {
                throw new Error('过滤后没有有效标签');
              }
            } else {
              throw new Error('解析结果不是数组');
            }
          } catch (parseError) {
            // JSON解析失败或标签无效，使用备选方法
            throw parseError;
          }
        } else {
          // 如果没有找到JSON数组，则尝试按行分割并清理
          throw new Error('未找到JSON数组');
        }
      } catch (e) {
        logger.warn(`解析标签建议失败，使用备选方法: ${e}`);
        // 备选方法：提取有意义的标签
        const content = response.choices[0].message.content.trim();

        // 尝试使用多种分隔符提取标签
        const extractedTags = content
          .replace(/[\[\]{}"']/g, '') // 移除JSON符号
          .split(/[,，、\n]/)
          .map((tag: string) => tag.trim())
          .filter((tag: string) => {
            // 过滤掉空标签、过长标签和JSON符号
            if (!tag || tag.length > 10) return false;
            if (tag === 'json' || tag === '{' || tag === '}' || tag === '[' || tag === ']') return false;
            return true;
          });

        // 如果提取到有效标签，使用它们
        if (extractedTags.length > 0) {
          tags = extractedTags.slice(0, 5);
        } else {
          // 如果没有提取到有效标签，使用默认标签
          tags = ['笔记', '内容', '文档'];
          logger.warn('无法提取有效标签，使用默认标签');
        }
      }

      return {
        success: true,
        data: {
          tags
        }
      };
    } catch (error: any) {
      logger.error(`生成标签建议失败: ${error.message}`);
      return {
        success: false,
        error: {
          message: `生成标签建议失败: ${error.message}`
        }
      };
    }
  }

  /**
   * 清理预测内容，移除特殊字符和冗余符号
   * @param prediction 原始预测内容
   * @returns 清理后的预测内容
   * @private
   */
  private _cleanPrediction(prediction: string): string {
    if (!prediction) return '';

    // 移除常见的标记和前缀
    let cleaned = prediction;

    // 移除"前缀："、"后缀："、"预测："等标记
    const prefixMarkers = ['前缀：', '后缀：', '预测：', '补全内容：', '补全：'];
    for (const marker of prefixMarkers) {
      if (cleaned.startsWith(marker)) {
        cleaned = cleaned.substring(marker.length);
      }
    }

    // 如果包含这些标记但不在开头，尝试提取冒号后的内容
    if (prefixMarkers.some(marker => cleaned.includes(marker))) {
      const parts = cleaned.split(/[：:]/);
      if (parts.length > 1) {
        // 取最后一个冒号后的内容
        cleaned = parts[parts.length - 1];
      }
    }

    // 移除常见的无意义字符和符号
    cleaned = cleaned
      // 移除常见的拼音标记和特殊符号
      .replace(/d1ji1|d\d+|\[\d+\]|\[\w+\]/g, '')
      // 移除引号和括号
      .replace(/["""''()（）【】\[\]]/g, '')
      // 移除多余的空格和换行符
      .replace(/\s+/g, ' ')
      .trim();

    // 如果清理后的内容为空，返回原始内容的精简版
    if (cleaned.length === 0) {
      return prediction.trim()
        .replace(/\s+/g, ' ') // 至少移除多余空格
        .substring(0, 30); // 限制长度
    }

    return cleaned;
  }

  /**
   * 智能问答
   * @param userId 用户ID
   * @param question 问题
   * @param context 上下文（可选）
   * @param options 选项
   * @returns 回答
   */
  async askQuestion(userId: string, question: string, context: string = '', options: any = {}) {
    try {
      const messages = [
        {
          role: 'system',
          content: '你是AI云笔记应用中的智能问答助手。你的任务是根据用户的笔记内容，回答用户提出的问题。你的回答应该准确、清晰、有洞察力，并且严格基于笔记中提供的信息。如果笔记中没有相关信息，请说明这一点。你的目标是帮助用户更好地理解和利用笔记中的知识，提供有洞察力的见解，并在适当的情况下提出相关的问题或建议。'
        }
      ];

      if (context) {
        messages.push({
          role: 'user',
          content: `以下是我的云笔记内容：\n\n${context}\n\n基于上述笔记内容，请回答我的问题：${question}\n\n请提供准确、清晰、有洞察力的回答，并且严格基于笔记中的信息。如果笔记中没有相关信息，请说明这一点。`
        });
      } else {
        messages.push({
          role: 'user',
          content: `请回答我的问题：${question}\n\n请提供准确、清晰、有洞察力的回答。如果你需要更多信息来回答这个问题，请说明这一点。`
        });
      }

      const response = await this.callSiliconFlowAPI(userId, messages, {
        temperature: options.temperature || 0.7,
        max_tokens: options.max_tokens || 512
      });

      return {
        success: true,
        data: {
          answer: response.choices[0].message.content.trim()
        }
      };
    } catch (error: any) {
      logger.error(`回答问题失败: ${error.message}`);
      return {
        success: false,
        error: {
          message: `回答问题失败: ${error.message}`
        }
      };
    }
  }
}

export default new AIService();
