<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智云笔记 - 编辑器</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common.css">
    <style>
        /* 笔记编辑界面 */
        .editor-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            background-color: white;
        }

        .editor-header {
            padding: 18px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--medium-gray);
        }

        .editor-header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .editor-header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .editor-header i {
            font-size: 1.2rem;
            color: var(--dark-gray);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
            cursor: pointer;
        }

        .editor-header i:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .editor-area {
            flex: 1;
            padding: 24px;
            position: relative;
            overflow-y: auto;
        }

        .editor-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 24px;
            border: none;
            width: 100%;
            outline: none;
            color: var(--black);
            padding: 0;
        }

        .editor-title::placeholder {
            color: var(--dark-gray);
            opacity: 0.7;
        }

        .editor-content {
            font-size: 1rem;
            line-height: 1.8;
            min-height: 200px;
            outline: none;
            color: var(--dark-gray);
            width: 100%;
            border: none;
            resize: none;
            padding: 0;
        }

        .editor-content::placeholder {
            color: var(--dark-gray);
            opacity: 0.7;
        }

        .editor-toolbar {
            display: flex;
            padding: 12px 20px;
            border-top: 1px solid var(--medium-gray);
            justify-content: space-between;
            background-color: white;
            overflow-x: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .editor-toolbar::-webkit-scrollbar {
            display: none;
        }

        .toolbar-group {
            display: flex;
            gap: 8px;
        }

        .toolbar-btn {
            color: var(--dark-gray);
            padding: 8px 10px;
            font-size: 1rem;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
            cursor: pointer;
        }

        .toolbar-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .editor-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid var(--medium-gray);
        }

        .tag {
            padding: 5px 10px;
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: var(--border-radius-sm);
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .tag i {
            font-size: 0.8rem;
            cursor: pointer;
        }

        .add-tag {
            padding: 5px 10px;
            background-color: var(--light-gray);
            color: var(--dark-gray);
            border-radius: var(--border-radius-sm);
            font-size: 0.85rem;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .add-tag:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .prediction-hint {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background-color: var(--white);
            box-shadow: var(--shadow);
            border-radius: var(--border-radius);
            padding: 12px 16px;
            max-width: 80%;
            display: flex;
            align-items: center;
            gap: 12px;
            border: 1px solid var(--primary-light);
        }

        .prediction-hint i {
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .prediction-hint-content {
            flex: 1;
            font-size: 0.9rem;
            color: var(--dark-gray);
        }

        .accept-btn {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border: none;
            border-radius: var(--border-radius-sm);
            padding: 6px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .accept-btn:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* 笔记阅读模式 */
        .reading-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: white;
        }

        .reading-header {
            padding: 18px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--medium-gray);
        }

        .reading-header-actions {
            display: flex;
            gap: 16px;
        }

        .reading-header i {
            font-size: 1.2rem;
            color: var(--dark-gray);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
            cursor: pointer;
        }

        .reading-header i:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .reading-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .reading-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--black);
            margin-bottom: 12px;
        }

        .reading-meta {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .reading-tags {
            display: flex;
            gap: 8px;
            margin-right: 16px;
        }

        .reading-time {
            margin-left: auto;
        }

        .reading-text {
            font-size: 1rem;
            line-height: 1.8;
            color: var(--black);
        }

        .reading-text p {
            margin-bottom: 16px;
        }

        .reading-footer {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid var(--medium-gray);
        }

        .reading-stats {
            display: flex;
            align-items: center;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .reading-stat {
            display: flex;
            align-items: center;
            margin-right: 16px;
        }

        .reading-stat i {
            margin-right: 6px;
        }

        .reading-actions {
            display: flex;
            gap: 16px;
        }

        .reading-action {
            color: var(--dark-gray);
            transition: var(--transition);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
        }

        .reading-action.active {
            color: var(--primary-color);
        }

        .reading-action:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        /* 笔记分享界面 */
        .share-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: white;
        }

        .share-header {
            padding: 18px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--medium-gray);
        }

        .share-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .share-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .share-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--black);
            margin-bottom: 16px;
        }

        .share-options {
            margin-bottom: 32px;
        }

        .share-option-group {
            margin-bottom: 24px;
        }

        .share-option-label {
            font-size: 0.9rem;
            color: var(--dark-gray);
            margin-bottom: 12px;
            font-weight: 500;
        }

        .permission-options {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
        }

        .permission-option {
            flex: 1;
            min-width: 100px;
            background-color: var(--light-gray);
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            padding: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            transition: var(--transition);
            cursor: pointer;
        }

        .permission-option:hover {
            border-color: var(--primary-color);
        }

        .permission-option.active {
            background-color: var(--primary-light);
            border-color: var(--primary-color);
        }

        .permission-option i {
            color: var(--dark-gray);
            font-size: 1.2rem;
        }

        .permission-option.active i {
            color: var(--primary-color);
        }

        .permission-option-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--black);
            text-align: center;
        }

        .permission-option-desc {
            font-size: 0.8rem;
            color: var(--dark-gray);
            text-align: center;
        }

        .expiration-select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-sm);
            background-color: var(--light-gray);
            color: var(--black);
            font-size: 0.95rem;
            outline: none;
            transition: var(--transition);
        }

        .expiration-select:focus {
            border-color: var(--primary-color);
            background-color: var(--white);
        }

        .share-methods {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-bottom: 24px;
        }

        .share-method {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 16px;
            background-color: var(--light-gray);
            border-radius: var(--border-radius);
            transition: var(--transition);
            cursor: pointer;
        }

        .share-method:hover {
            background-color: var(--primary-light);
        }

        .share-method i {
            font-size: 1.5rem;
            color: var(--dark-gray);
        }

        .share-method:hover i {
            color: var(--primary-color);
        }

        .share-method-name {
            font-size: 0.85rem;
            color: var(--dark-gray);
        }

        .share-link-group {
            display: flex;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-sm);
            overflow: hidden;
            margin-bottom: 24px;
        }

        .share-link {
            flex: 1;
            padding: 12px;
            font-size: 0.9rem;
            color: var(--dark-gray);
            background-color: var(--light-gray);
            border: none;
            outline: none;
        }

        .copy-link {
            width: 60px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .copy-link:hover {
            background-color: #4a4cc4;
        }

        .share-confirm {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
        }

        .share-confirm:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(93, 95, 239, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title text-center">智云笔记应用原型设计</h1>
        <p class="page-description text-center">
            笔记编辑器和查看界面，提供丰富的文本编辑功能、AI智能提示和分享选项。
        </p>

        <!-- 笔记编辑页面 -->
        <div>
            <h3 class="screen-title">笔记编辑</h3>
            <p class="screen-description">创建或编辑笔记的界面，包含丰富的编辑工具和AI辅助功能</p>
            <div class="screen">
                <div class="editor-container">
                    <div class="editor-header">
                        <div class="editor-header-left">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="editor-header-right">
                            <i class="fas fa-history"></i>
                            <i class="fas fa-share-alt"></i>
                            <i class="fas fa-save"></i>
                        </div>
                    </div>
                    
                    <div class="editor-area">
                        <input type="text" class="editor-title" placeholder="输入标题..." value="工作计划">
                        <textarea class="editor-content" placeholder="开始输入内容...">本周需要完成的任务：
1. 项目计划书
2. 客户会议
3. 团队协调
4. 进度报告

项目截止日期：2023-08-20
预计所需时间：15小时</textarea>
                        
                        <div class="editor-tags">
                            <div class="tag">工作 <i class="fas fa-times"></i></div>
                            <div class="tag">计划 <i class="fas fa-times"></i></div>
                            <div class="add-tag"><i class="fas fa-plus"></i> 添加标签</div>
                        </div>
                        
                        <div class="prediction-hint">
                            <i class="fas fa-lightbulb"></i>
                            <div class="prediction-hint-content">AI建议：添加"准备演示文稿"到任务列表</div>
                            <button class="accept-btn">接受</button>
                        </div>
                    </div>
                    
                    <div class="editor-toolbar">
                        <div class="toolbar-group">
                            <div class="toolbar-btn"><i class="fas fa-heading"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-bold"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-italic"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-underline"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-strikethrough"></i></div>
                        </div>
                        
                        <div class="toolbar-group">
                            <div class="toolbar-btn"><i class="fas fa-list-ul"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-list-ol"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-tasks"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-quote-right"></i></div>
                        </div>
                        
                        <div class="toolbar-group">
                            <div class="toolbar-btn"><i class="fas fa-link"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-image"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-table"></i></div>
                            <div class="toolbar-btn"><i class="fas fa-code"></i></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 笔记阅读模式 -->
        <div>
            <h3 class="screen-title">笔记阅读模式</h3>
            <p class="screen-description">提供专注阅读体验的模式，减少干扰元素</p>
            <div class="screen">
                <div class="reading-container">
                    <div class="reading-header">
                        <div class="editor-header-left">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="reading-header-actions">
                            <i class="fas fa-edit"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-share-alt"></i>
                            <i class="fas fa-ellipsis-v"></i>
                        </div>
                    </div>
                    
                    <div class="reading-content">
                        <h1 class="reading-title">工作计划</h1>
                        <div class="reading-meta">
                            <div class="reading-tags">
                                <div class="tag">工作</div>
                                <div class="tag">计划</div>
                            </div>
                            <div class="reading-time">最后编辑：2023-08-15</div>
                        </div>
                        
                        <div class="reading-text">
                            <p><strong>本周需要完成的任务：</strong></p>
                            <ol>
                                <li>项目计划书</li>
                                <li>客户会议</li>
                                <li>团队协调</li>
                                <li>进度报告</li>
                            </ol>
                            <p>项目截止日期：2023-08-20</p>
                            <p>预计所需时间：15小时</p>
                        </div>
                    </div>
                    
                    <div class="reading-footer">
                        <div class="reading-stats">
                            <div class="reading-stat">
                                <i class="fas fa-clock"></i>
                                <span>2分钟阅读</span>
                            </div>
                            <div class="reading-stat">
                                <i class="fas fa-eye"></i>
                                <span>5次查看</span>
                            </div>
                        </div>
                        
                        <div class="reading-actions">
                            <div class="reading-action">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="reading-action">
                                <i class="fas fa-print"></i>
                            </div>
                            <div class="reading-action">
                                <i class="fas fa-file-export"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 笔记分享页面 -->
        <div>
            <h3 class="screen-title">笔记分享</h3>
            <p class="screen-description">设置笔记分享权限和方式</p>
            <div class="screen">
                <div class="share-container">
                    <div class="share-header">
                        <div class="editor-header-left">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="share-header-title">分享笔记</div>
                        <div style="width: 40px;"></div>
                    </div>
                    
                    <div class="share-content">
                        <div class="share-options">
                            <div class="share-title">工作计划</div>
                            
                            <div class="share-option-group">
                                <div class="share-option-label">权限设置</div>
                                <div class="permission-options">
                                    <div class="permission-option">
                                        <i class="fas fa-eye"></i>
                                        <div class="permission-option-title">只读</div>
                                        <div class="permission-option-desc">只允许查看</div>
                                    </div>
                                    <div class="permission-option active">
                                        <i class="fas fa-edit"></i>
                                        <div class="permission-option-title">可编辑</div>
                                        <div class="permission-option-desc">允许编辑内容</div>
                                    </div>
                                    <div class="permission-option">
                                        <i class="fas fa-lock"></i>
                                        <div class="permission-option-title">密码保护</div>
                                        <div class="permission-option-desc">需要密码访问</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="share-option-group">
                                <div class="share-option-label">有效期</div>
                                <select class="expiration-select">
                                    <option>永久有效</option>
                                    <option>1天</option>
                                    <option selected>7天</option>
                                    <option>30天</option>
                                    <option>自定义</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="share-option-group">
                            <div class="share-option-label">分享方式</div>
                            <div class="share-methods">
                                <div class="share-method">
                                    <i class="fab fa-weixin"></i>
                                    <div class="share-method-name">微信</div>
                                </div>
                                <div class="share-method">
                                    <i class="fab fa-qq"></i>
                                    <div class="share-method-name">QQ</div>
                                </div>
                                <div class="share-method">
                                    <i class="fas fa-envelope"></i>
                                    <div class="share-method-name">邮件</div>
                                </div>
                                <div class="share-method">
                                    <i class="fab fa-weibo"></i>
                                    <div class="share-method-name">微博</div>
                                </div>
                                <div class="share-method">
                                    <i class="fas fa-link"></i>
                                    <div class="share-method-name">复制链接</div>
                                </div>
                                <div class="share-method">
                                    <i class="fas fa-qrcode"></i>
                                    <div class="share-method-name">二维码</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="share-option-group">
                            <div class="share-option-label">分享链接</div>
                            <div class="share-link-group">
                                <input type="text" class="share-link" value="https://zhiyun.notes/share/workplan123" readonly>
                                <button class="copy-link">复制</button>
                            </div>
                        </div>
                        
                        <button class="share-confirm">确认分享</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>智云笔记应用原型设计 &copy; 2023</p>
            <p>
                <a href="welcome.html">欢迎页面</a> | 
                <a href="auth.html">认证页面</a> | 
                <a href="home.html">主页</a> | 
                <a href="editor.html">编辑器</a> | 
                <a href="settings.html">设置</a> | 
                <a href="tags.html">标签</a>
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 