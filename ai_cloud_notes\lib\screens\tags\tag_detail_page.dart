import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/models/tag_model.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/providers/tag_provider.dart';
import 'package:provider/provider.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/utils/date_time_helper.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/widgets/note_content_preview.dart';

class TagDetailPage extends StatefulWidget {
  final String tagId;

  const TagDetailPage({
    Key? key,
    required this.tagId,
  }) : super(key: key);

  @override
  State<TagDetailPage> createState() => _TagDetailPageState();
}

class _TagDetailPageState extends State<TagDetailPage> {
  bool _isGridView = true;
  bool _isLoading = true;

  // 标签和笔记数据
  Tag? _tag;
  List<Note> _notes = [];
  int _totalNotes = 0;
  int _currentPage = 1;
  final int _pageSize = 20;
  bool _hasMoreNotes = true;

  // 根据ID获取标签对象
  Tag? _getTagById(String tagId) {
    try {
      final tagProvider = Provider.of<TagProvider>(context, listen: false);
      return tagProvider.tags.firstWhere((tag) => tag.id == tagId);
    } catch (e) {
      print('获取标签失败: $e');
      return null;
    }
  }

  @override
  void initState() {
    super.initState();
    // 初始化时加载标签和笔记数据
    _loadTagData();

    // 预加载所有标签，以便显示笔记的标签信息
    final tagProvider = Provider.of<TagProvider>(context, listen: false);
    if (tagProvider.tags.isEmpty) {
      tagProvider.fetchTags();
    }
  }

  // 加载标签详情和关联的笔记
  Future<void> _loadTagData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 使用TagProvider加载标签详情
      final tagProvider = Provider.of<TagProvider>(context, listen: false);
      final tag = await tagProvider.getTagById(widget.tagId);

      if (tag != null) {
        // 加载标签关联的笔记
        await _loadTagNotes(refresh: true);

        setState(() {
          _tag = tag;
          _totalNotes = tag.noteCount ?? 0;
          _isLoading = false;
        });
      } else {
        _showError('加载标签失败');
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      _showError('加载标签详情出错: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 加载标签关联的笔记
  Future<void> _loadTagNotes({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _currentPage = 1;
        _hasMoreNotes = true;
      });
    }

    if (!_hasMoreNotes && !refresh) return;

    try {
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);

      // 使用NoteProvider加载标签相关笔记
      final result = await noteProvider.getNotesByTag(
        tagId: widget.tagId,
        page: _currentPage,
        limit: _pageSize,
      );

      if (result != null) {
        final notes = result['notes'] as List<Note>;
        final totalNotes = result['total'] as int;

        setState(() {
          if (refresh || _currentPage == 1) {
            _notes = notes;
          } else {
            _notes.addAll(notes);
          }

          // 检查是否有更多笔记
          _hasMoreNotes = notes.length == _pageSize;
          _currentPage++;

          // 更新总笔记数
          _totalNotes = totalNotes;
        });
      } else {
        _showError('加载笔记失败');
      }
    } catch (e) {
      _showError('加载笔记出错: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    if (_isLoading) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              _buildHeaderPlaceholder(),
              const Expanded(
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_tag == null) {
      return Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              _buildBackButton(),
              Expanded(
                child: Center(
                  child: Text('标签不存在或已被删除', style: theme.textTheme.bodyLarge),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return WillPopScope(
      onWillPop: () async {
        // 返回时将当前标签ID和笔记数量传回上一页
        // 同时添加一个标志，表示需要刷新所有标签数据
        Navigator.pop(context, {
          'tagId': widget.tagId,
          'noteCount': _totalNotes,
          'refreshAllTags': true // 添加刷新所有标签的标志
        });
        return false; // 返回false，因为我们已经手动处理了返回操作
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: () => _loadTagNotes(refresh: true),
            child: Column(
              children: [
                _buildHeader(),
                _buildNotesSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      alignment: Alignment.centerLeft,
      child: InkWell(
        onTap: () {
          // 返回时将当前标签ID和笔记数量传回上一页
          // 同时添加一个标志，表示需要刷新所有标签数据
          Navigator.pop(context, {
            'tagId': widget.tagId,
            'noteCount': _totalNotes,
            'refreshAllTags': true // 添加刷新所有标签的标志
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.arrow_back,
            color: theme.iconTheme.color,
            size: 20,
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderPlaceholder() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.05),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildBackButton(),
          const SizedBox(width: 16),
          // 标签名占位符
          Container(
            width: 150,
            height: 24,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.05),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              InkWell(
                onTap: () {
                  // 返回时将当前标签ID和笔记数量传回上一页
                  // 同时添加一个标志，表示需要刷新所有标签数据
                  Navigator.pop(context, {
                    'tagId': widget.tagId,
                    'noteCount': _totalNotes,
                    'refreshAllTags': true // 添加刷新所有标签的标志
                  });
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.arrow_back,
                    color: theme.iconTheme.color,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              CircleAvatar(
                backgroundColor: _tag!.colorValue.withOpacity(0.2), // 保留标签颜色
                child: Icon(
                  Icons.tag,
                  color: _tag!.colorValue, // 保留标签颜色
                  size: 16,
                ),
              ),
              const SizedBox(width: 16),
              Text(
                _tag!.name,
                style: theme.textTheme.headlineSmall,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.only(left: 56),
            child: Text(
              '共 $_totalNotes 条笔记',
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '全部笔记',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isGridView = true;
                        });
                      },
                      child: Icon(
                        Icons.grid_view,
                        color: _isGridView
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).iconTheme.color,
                      ),
                    ),
                    const SizedBox(width: 16),
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          _isGridView = false;
                        });
                      },
                      child: Icon(
                        Icons.view_list,
                        color: !_isGridView
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).iconTheme.color,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: _notes.isEmpty
                ? _buildEmptyNotesView()
                : _isGridView
                    ? _buildGridView()
                    : _buildListView(),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
            _hasMoreNotes) {
          _loadTagNotes();
        }
        return true;
      },
      child: GridView.builder(
        padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
        gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: 300,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.1,
        ),
        itemCount: _notes.length + (_hasMoreNotes ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _notes.length) {
            return const Center(child: CircularProgressIndicator());
          }
          final note = _notes[index];
          return _buildGridNoteItem(note);
        },
      ),
    );
  }

  Widget _buildGridNoteItem(Note note) {
    final bool isSelected = false; // 标签详情页面不需要多选功能
    final DateTime createdAt = note.createdAt;
    final dateStr = DateTimeHelper.formatDate(createdAt);

    return InkWell(
      onTap: () => _openNoteEditor(note),
      borderRadius: BorderRadius.circular(12),
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).dividerColor,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withOpacity(0.03),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        note.title.isNotEmpty ? note.title : '无标题',
                        style: Theme.of(context)
                            .textTheme
                            .titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (note.isFavorite)
                      const Icon(
                        Icons.star,
                        color: Colors.orange, // 保留状态色
                        size: 18,
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: NoteContentPreview(
                    note: note,
                    maxLines: 4,
                    fontSize: 14,
                    textColor: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.color
                        ?.withOpacity(0.7),
                  ),
                ),
                // 重新设计底部日期和标签区域，日期靠左，标签区域靠右，支持水平滚动
                Row(
                  children: [
                    // 日期靠左显示
                    Text(
                      dateStr,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),

                    // 标签区域使用Expanded和SingleChildScrollView实现右侧滚动
                    if (note.tagIds.isNotEmpty)
                      Expanded(
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          reverse: true, // 从右向左滚动
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: note.tagIds
                                .map((tagId) {
                                  // 尝试获取标签对象
                                  Tag? tag = _getTagById(tagId);
                                  if (tag == null) return Container();

                                  return Padding(
                                    padding: const EdgeInsets.only(left: 4),
                                    child: Container(
                                      // 使用Chip可能更符合语义，但保持现有结构
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: tag.colorValue
                                            .withOpacity(0.1), // 保留标签颜色
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        tag.name,
                                        style: TextStyle(
                                          // 保留标签颜色
                                          fontSize: 10,
                                          color: tag.colorValue,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  );
                                })
                                .toList()
                                .reversed
                                .toList(), // 反转列表，使最近添加的标签靠右显示
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: InkWell(
              onTap: () => _showNoteOptions(note),
              borderRadius: BorderRadius.circular(100), // For circular ripple
              child: Container(
                decoration: BoxDecoration(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                padding: const EdgeInsets.all(4),
                child: Icon(
                  Icons.more_vert,
                  size: 16,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
            _hasMoreNotes) {
          _loadTagNotes();
        }
        return true;
      },
      child: ListView.builder(
        padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
        itemCount: _notes.length + (_hasMoreNotes ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _notes.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            );
          }
          final note = _notes[index];
          return _buildListNoteItem(note);
        },
      ),
    );
  }

  Widget _buildListNoteItem(Note note) {
    final DateTime createdAt = note.createdAt;
    final dateStr = DateTimeHelper.formatDate(createdAt);

    return Slidable(
      key: ValueKey(note.id),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => _toggleFavorite(note),
            backgroundColor: Colors.orange.withOpacity(0.1), // 状态色
            foregroundColor: Colors.orange, // 状态色
            icon: note.isFavorite ? Icons.star : Icons.star_border,
            label: note.isFavorite ? '取消收藏' : '收藏',
          ),
          SlidableAction(
            onPressed: (_) => _shareNote(note),
            backgroundColor:
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
            foregroundColor: Theme.of(context).colorScheme.primary,
            icon: Icons.share,
            label: '分享',
          ),
          SlidableAction(
            onPressed: (_) => _deleteNote(note),
            backgroundColor: Colors.red.withOpacity(0.1), // 状态色
            foregroundColor: Colors.red, // 状态色
            icon: Icons.delete,
            label: '删除',
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _openNoteEditor(note),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).dividerColor.withOpacity(0.5),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.03),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      note.title.isNotEmpty ? note.title : '无标题',
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium
                          ?.copyWith(fontWeight: FontWeight.w600),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (note.isFavorite)
                    const Icon(
                      Icons.star,
                      color: Colors.orange, // 保留状态色
                      size: 18,
                    ),
                ],
              ),
              const SizedBox(height: 8),
              NoteContentPreview(
                note: note,
                maxLines: 2,
                fontSize: 14,
                textColor: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.color
                    ?.withOpacity(0.7),
              ),
              const SizedBox(height: 8),
              // 重新设计底部日期和标签区域，日期靠左，标签区域靠右，支持水平滚动
              Row(
                children: [
                  // 日期靠左显示
                  Text(
                    dateStr,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),

                  // 标签区域使用Expanded和SingleChildScrollView实现右侧滚动
                  if (note.tagIds.isNotEmpty)
                    Expanded(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        reverse: true, // 从右向左滚动
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: note.tagIds
                              .map((tagId) {
                                // 尝试获取标签对象
                                Tag? tag = _getTagById(tagId);
                                if (tag == null) return Container();

                                return Padding(
                                  padding: const EdgeInsets.only(left: 6),
                                  child: Container(
                                    // 使用Chip可能更符合语义，但保持现有结构
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: tag.colorValue
                                          .withOpacity(0.1), // 保留标签颜色
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.label,
                                          size: 14,
                                          color: tag.colorValue, // 保留标签颜色
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          tag.name,
                                          style: TextStyle(
                                            // 保留标签颜色
                                            fontSize: 12,
                                            color: tag.colorValue,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              })
                              .toList()
                              .reversed
                              .toList(), // 反转列表，使最近添加的标签靠右显示
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyNotesView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.note_alt,
            size: 48,
            color: Theme.of(context).iconTheme.color?.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            '此标签下暂无笔记',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.color
                      ?.withOpacity(0.7),
                ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _createNewNote,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('创建笔记'),
          ),
        ],
      ),
    );
  }

  void _openNoteEditor(Note note) {
    Navigator.pushNamed(
      context,
      AppRoutes.editor,
      arguments: note,
    ).then((_) {
      // 返回时刷新笔记列表
      _loadTagNotes(refresh: true);
    });
  }

  // 创建新笔记方法
  void _createNewNote() {
    if (_tag == null) return;

    // 使用命名路由跳转到编辑页面，传递Map参数
    Navigator.pushNamed(
      context,
      AppRoutes.editor,
      arguments: {
        'note': null, // 传递null表示是新笔记
        'initialTagId': widget.tagId, // 传递当前标签ID
        'initialTagName': _tag!.name, // 传递当前标签名称
      },
    ).then((_) {
      // 返回时刷新笔记列表
      _loadTagNotes(refresh: true);
    });
  }

  // 添加显示笔记选项菜单的方法
  void _showNoteOptions(Note note) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(
                  note.isFavorite ? Icons.star : Icons.star_border,
                  color: note.isFavorite
                      ? Colors.orange
                      : Theme.of(context).iconTheme.color,
                ),
                title: Text(
                  note.isFavorite ? '取消收藏' : '收藏笔记',
                  style: TextStyle(
                      color: note.isFavorite
                          ? Colors.orange
                          : Theme.of(context).textTheme.bodyLarge?.color),
                ),
                onTap: () {
                  Navigator.pop(context);
                  _toggleFavorite(note);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.share,
                  color: Theme.of(context).iconTheme.color,
                ),
                title: const Text('分享笔记'),
                onTap: () {
                  Navigator.pop(context);
                  _shareNote(note);
                },
              ),
              ListTile(
                leading: const Icon(
                  Icons.delete,
                  color: Colors.red, // 保留状态色
                ),
                title: const Text('删除笔记',
                    style: TextStyle(color: Colors.red)), // 匹配图标颜色
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmation(note);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 分享笔记
  void _shareNote(Note note) {
    // 直接跳转到分享页面，与编辑器页面和首页保持一致
    Navigator.pushNamed(
      context,
      AppRoutes.shareNote,
      arguments: note,
    );
  }

  // 收藏/取消收藏笔记
  void _toggleFavorite(Note note) async {
    try {
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      final success = await noteProvider.toggleFavorite(note.id);

      // 检查组件是否已经被卸载
      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(note.isFavorite ? '已取消收藏' : '已加入收藏'),
            duration: const Duration(seconds: 1),
          ),
        );

        // 刷新笔记列表
        _loadTagNotes(refresh: true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('操作失败: ${noteProvider.error}')),
        );
      }
    } catch (e) {
      // 检查组件是否已经被卸载
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('操作失败: $e')),
      );
    }
  }

  // 显示删除确认对话框
  void _showDeleteConfirmation(Note note) {
    showDialog(
      context: context,
      builder: (context) {
        final theme = Theme.of(context);
        return AlertDialog(
          title: Text('删除笔记', style: theme.dialogTheme.titleTextStyle),
          content: Text('确定要删除笔记"${note.title}"吗？此操作不可撤销。',
              style: theme.dialogTheme.contentTextStyle),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('取消',
                  style: TextStyle(color: theme.textTheme.bodySmall?.color)),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _performDeleteNote(note);
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.red, // 保留状态色
              ),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  // 删除笔记 - 始终先显示确认对话框
  void _deleteNote(Note note) {
    // 始终先显示确认对话框，避免直接删除
    _showDeleteConfirmation(note);
  }

  // 执行删除操作
  Future<void> _performDeleteNote(Note note) async {
    try {
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);

      // 显示删除中提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('正在删除笔记...'),
          duration: Duration(seconds: 1),
        ),
      );

      final success = await noteProvider.deleteNote(note.id);

      // 检查组件是否已经被卸载
      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('笔记已删除')),
        );

        // 刷新笔记列表
        _loadTagNotes(refresh: true);

        // 更新所有包含该笔记的标签的笔记数量
        // 由于笔记可能有多个标签，所以需要更新所有相关标签
        final tagProvider = Provider.of<TagProvider>(context, listen: false);
        if (note.tagIds.isNotEmpty) {
          // 对于每个标签，更新其笔记数量
          for (final tagId in note.tagIds) {
            if (tagId != widget.tagId) {
              // 当前标签已经通过_loadTagNotes更新了
              await tagProvider.getTagById(tagId); // 这会更新标签的笔记数量
            }
          }
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('删除失败: ${noteProvider.error}')),
        );
      }
    } catch (e) {
      // 检查组件是否已经被卸载
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('删除失败: $e')),
      );
    }
  }
}
