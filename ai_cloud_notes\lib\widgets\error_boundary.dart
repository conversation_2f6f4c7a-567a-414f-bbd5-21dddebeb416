import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 错误类型枚举
enum ErrorType {
  network,      // 网络错误
  validation,   // 验证错误
  permission,   // 权限错误
  storage,      // 存储错误
  unknown,      // 未知错误
}

/// 错误信息类
class AppError {
  final String message;
  final String? details;
  final ErrorType type;
  final DateTime timestamp;
  final StackTrace? stackTrace;
  final String? code;

  AppError({
    required this.message,
    this.details,
    this.type = ErrorType.unknown,
    DateTime? timestamp,
    this.stackTrace,
    this.code,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'details': details,
      'type': type.toString(),
      'timestamp': timestamp.toIso8601String(),
      'code': code,
      'stackTrace': stackTrace?.toString(),
    };
  }

  @override
  String toString() {
    return 'AppError(message: $message, type: $type, code: $code)';
  }
}

/// 错误处理器接口
abstract class ErrorHandler {
  Future<void> handleError(AppError error);
}

/// 默认错误处理器
class DefaultErrorHandler implements ErrorHandler {
  @override
  Future<void> handleError(AppError error) async {
    // 记录错误日志
    if (kDebugMode) {
      developer.log(
        'Error: ${error.message}',
        name: 'ErrorBoundary',
        error: error,
        stackTrace: error.stackTrace,
      );
    }

    // 在生产环境中可以发送错误报告到服务器
    if (kReleaseMode) {
      // TODO: 发送错误报告到服务器
      // await _sendErrorReport(error);
    }
  }
}

/// 错误边界Widget
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(AppError error)? errorBuilder;
  final ErrorHandler? errorHandler;
  final bool showErrorDetails;
  final VoidCallback? onRetry;

  const ErrorBoundary({
    Key? key,
    required this.child,
    this.errorBuilder,
    this.errorHandler,
    this.showErrorDetails = kDebugMode,
    this.onRetry,
  }) : super(key: key);

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();

  /// 创建一个简单的错误边界
  static Widget simple({
    required Widget child,
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    return ErrorBoundary(
      child: child,
      errorBuilder: (error) => SimpleErrorWidget(
        message: errorMessage ?? '出现了一个错误',
        onRetry: onRetry,
      ),
    );
  }

  /// 创建一个带重试功能的错误边界
  static Widget withRetry({
    required Widget child,
    required VoidCallback onRetry,
    String? errorMessage,
  }) {
    return ErrorBoundary(
      child: child,
      onRetry: onRetry,
      errorBuilder: (error) => RetryErrorWidget(
        message: errorMessage ?? '加载失败',
        onRetry: onRetry,
      ),
    );
  }
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  AppError? _error;
  late ErrorHandler _errorHandler;

  @override
  void initState() {
    super.initState();
    _errorHandler = widget.errorHandler ?? DefaultErrorHandler();
    
    // 设置全局错误处理
    FlutterError.onError = (FlutterErrorDetails details) {
      final error = AppError(
        message: details.exception.toString(),
        details: details.context?.toString(),
        stackTrace: details.stack,
        type: _getErrorType(details.exception),
      );
      
      _handleError(error);
    };

    // 处理异步错误
    PlatformDispatcher.instance.onError = (error, stack) {
      final appError = AppError(
        message: error.toString(),
        stackTrace: stack,
        type: _getErrorType(error),
      );
      
      _handleError(appError);
      return true;
    };
  }

  ErrorType _getErrorType(dynamic exception) {
    if (exception is SocketException || 
        exception is TimeoutException ||
        exception.toString().contains('network') ||
        exception.toString().contains('connection')) {
      return ErrorType.network;
    }
    
    if (exception is FormatException ||
        exception is ArgumentError ||
        exception.toString().contains('validation')) {
      return ErrorType.validation;
    }
    
    if (exception is PlatformException) {
      return ErrorType.permission;
    }
    
    if (exception.toString().contains('storage') ||
        exception.toString().contains('file')) {
      return ErrorType.storage;
    }
    
    return ErrorType.unknown;
  }

  void _handleError(AppError error) {
    if (mounted) {
      setState(() {
        _error = error;
      });
    }
    
    _errorHandler.handleError(error);
  }

  void _retry() {
    setState(() {
      _error = null;
    });
    widget.onRetry?.call();
  }

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!);
      }
      
      return DefaultErrorWidget(
        error: _error!,
        showDetails: widget.showErrorDetails,
        onRetry: widget.onRetry != null ? _retry : null,
      );
    }

    return widget.child;
  }
}

/// 默认错误显示Widget
class DefaultErrorWidget extends StatelessWidget {
  final AppError error;
  final bool showDetails;
  final VoidCallback? onRetry;

  const DefaultErrorWidget({
    Key? key,
    required this.error,
    this.showDetails = false,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getErrorIcon(),
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            _getErrorTitle(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.red[700],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            error.message,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          if (showDetails && error.details != null) ...[
            const SizedBox(height: 16),
            ExpansionTile(
              title: const Text('错误详情'),
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    error.details!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ],
            ),
          ],
          if (onRetry != null) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getErrorIcon() {
    switch (error.type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.validation:
        return Icons.error_outline;
      case ErrorType.permission:
        return Icons.lock;
      case ErrorType.storage:
        return Icons.storage;
      default:
        return Icons.error;
    }
  }

  String _getErrorTitle() {
    switch (error.type) {
      case ErrorType.network:
        return '网络连接错误';
      case ErrorType.validation:
        return '数据验证错误';
      case ErrorType.permission:
        return '权限错误';
      case ErrorType.storage:
        return '存储错误';
      default:
        return '发生错误';
    }
  }
}

/// 简单错误Widget
class SimpleErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const SimpleErrorWidget({
    Key? key,
    required this.message,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }
}

/// 重试错误Widget
class RetryErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback onRetry;

  const RetryErrorWidget({
    Key? key,
    required this.message,
    required this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.refresh,
            size: 64,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('重试'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                horizontal: 32,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 错误边界扩展方法
extension ErrorBoundaryExtension on Widget {
  /// 包装错误边界
  Widget withErrorBoundary({
    Widget Function(AppError error)? errorBuilder,
    ErrorHandler? errorHandler,
    bool showErrorDetails = kDebugMode,
    VoidCallback? onRetry,
  }) {
    return ErrorBoundary(
      errorBuilder: errorBuilder,
      errorHandler: errorHandler,
      showErrorDetails: showErrorDetails,
      onRetry: onRetry,
      child: this,
    );
  }

  /// 包装简单错误边界
  Widget withSimpleErrorBoundary({
    String? errorMessage,
    VoidCallback? onRetry,
  }) {
    return ErrorBoundary.simple(
      child: this,
      errorMessage: errorMessage,
      onRetry: onRetry,
    );
  }
}
