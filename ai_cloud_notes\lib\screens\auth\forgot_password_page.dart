import 'dart:ui'; // Required for ImageFilter
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/utils/validators.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';

class ForgotPasswordPage extends StatefulWidget {
  // 添加令牌和邮箱参数，用于从链接直接打开
  final String? token;
  final String? email;

  const ForgotPasswordPage({
    Key? key,
    this.token,
    this.email,
  }) : super(key: key);

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _tokenController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _isObscure = true;
  bool _isObscureConfirm = true;
  bool _showResetForm = false;

  @override
  void initState() {
    super.initState();

    // 如果从链接打开，预填充邮箱和令牌
    if (widget.email != null) {
      _emailController.text = widget.email!;
    }

    if (widget.token != null) {
      _tokenController.text = widget.token!;
      // 显示重置表单
      _showResetForm = true;
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _tokenController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// 发送重置密码邮件
  void _sendResetEmail() async {
    if (_formKey.currentState!.validate()) {
      // 获取AuthProvider实例
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // 调用忘记密码方法
      final success = await authProvider.forgotPassword(_emailController.text.trim());

      if (mounted) {
        if (success) {
          // 发送成功，显示重置密码表单
          setState(() {
            _showResetForm = true;
          });

          // 显示成功消息
          SnackbarHelper.showSuccess(
            context: context,
            message: '重置密码邮件已发送，请查收邮箱',
          );
        } else {
          // 发送失败，显示错误提示
          SnackbarHelper.showError(
            context: context,
            message: authProvider.error ?? '发送重置密码邮件失败，请稍后再试',
          );
        }
      }
    }
  }

  /// 重置密码
  void _resetPassword() async {
    if (_formKey.currentState!.validate()) {
      // 获取AuthProvider实例
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // 调用重置密码方法
      final success = await authProvider.resetPassword(
        email: _emailController.text.trim(),
        token: _tokenController.text.trim(),
        newPassword: _newPasswordController.text,
      );

      if (mounted) {
        if (success) {
          // 重置成功，显示成功消息并跳转到登录页
          SnackbarHelper.showSuccess(
            context: context,
            message: '密码重置成功，请使用新密码登录',
          );

          // 延迟跳转，让用户看到成功消息
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted) {
              Navigator.of(context).pushReplacementNamed(AppRoutes.login);
            }
          });
        } else {
          // 重置失败，显示错误提示
          SnackbarHelper.showError(
            context: context,
            message: authProvider.error ?? '密码重置失败，请稍后再试',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取AuthProvider，监听状态变化
    final authProvider = Provider.of<AuthProvider>(context);
    final isLoading = authProvider.isLoading;

    return Theme(
      data: AppTheme.fixedLightTheme,
      child: Scaffold(
        appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '找回密码',
          style: TextStyle(
            color: AppTheme.blackColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true, // 标题居中，与其他页面统一
        // 添加毛玻璃效果
        flexibleSpace: ClipRect( // ClipRect 用于裁剪 BackdropFilter 的区域
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0), // 应用模糊效果
            child: Container(
              color: Colors.white.withOpacity(0.3), // 毛玻璃上方的半透明层
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),

                // 邮箱输入框
                Text(
                  '邮箱',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.darkGrayColor,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _emailController,
                  enabled: !isLoading && !_showResetForm,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: '请输入注册邮箱',
                    prefixIcon: Icon(Icons.email_outlined),
                    // 使用下划线边框
                    border: UnderlineInputBorder(
                      borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                    ),
                    focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                      borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                    ),
                    // 调整内边距
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入邮箱';
                    }
                    if (!Validators.isValidEmail(value)) {
                      return '请输入有效的邮箱地址';
                    }
                    return null;
                  },
                ),

                if (!_showResetForm) ...[
                  const SizedBox(height: 32),

                  // 发送重置邮件按钮
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: isLoading ? null : _sendResetEmail,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          // 调整圆角
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0, // 保持无阴影
                        // 可以考虑微调 padding 或 textStyle
                        // padding: EdgeInsets.symmetric(horizontal: 32, vertical: 14),
                        // textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                      child: isLoading
                          ? SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              '发送重置邮件',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                            ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // 提示文字
                  Text(
                    '我们会向您的邮箱发送一封重置密码邮件，请按照邮件中的指引完成密码重置。',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.darkGrayColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],

                if (_showResetForm) ...[
                  const SizedBox(height: 20),

                  // 重置令牌输入框
                  Text(
                    '重置令牌',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.darkGrayColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _tokenController,
                    enabled: !isLoading,
                    decoration: InputDecoration(
                      hintText: '请输入邮件中的重置令牌',
                      prefixIcon: Icon(Icons.token),
                      // 使用下划线边框
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                      ),
                      focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                        borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                      ),
                      // 调整内边距
                      contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入重置令牌';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // 新密码输入框
                  Text(
                    '新密码',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.darkGrayColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _newPasswordController,
                    enabled: !isLoading,
                    obscureText: _isObscure,
                    decoration: InputDecoration(
                      hintText: '请输入新密码',
                      prefixIcon: Icon(Icons.lock_outline),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isObscure ? Icons.visibility_off : Icons.visibility,
                        ),
                        onPressed: () {
                          setState(() {
                            _isObscure = !_isObscure;
                          });
                        },
                      ),
                      // 使用下划线边框
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                      ),
                      focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                        borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                      ),
                      // 调整内边距
                      contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入新密码';
                      }
                      if (value.length < 8) {
                        return '密码长度不能少于8个字符';
                      }
                      if (!Validators.isValidPassword(value)) {
                        return '密码必须包含大小写字母、数字和特殊字符';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),

                  // 确认密码输入框
                  Text(
                    '确认密码',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.darkGrayColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _confirmPasswordController,
                    enabled: !isLoading,
                    obscureText: _isObscureConfirm,
                    decoration: InputDecoration(
                      hintText: '请再次输入新密码',
                      prefixIcon: Icon(Icons.lock_outline),
                      suffixIcon: IconButton(
                        icon: Icon(
                          _isObscureConfirm ? Icons.visibility_off : Icons.visibility,
                        ),
                        onPressed: () {
                          setState(() {
                            _isObscureConfirm = !_isObscureConfirm;
                          });
                        },
                      ),
                      // 使用下划线边框
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                      ),
                      focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                        borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                      ),
                      // 调整内边距
                      contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请再次输入新密码';
                      }
                      if (value != _newPasswordController.text) {
                        return '两次输入的密码不一致';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),

                  // 重置密码按钮
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: isLoading ? null : _resetPassword,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          // 调整圆角
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0, // 保持无阴影
                        // 可以考虑微调 padding 或 textStyle
                        // padding: EdgeInsets.symmetric(horizontal: 32, vertical: 14),
                        // textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                      child: isLoading
                          ? SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              '重置密码',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                            ),
                    ),
                  ),
                ],

                const SizedBox(height: 20),

                // 返回登录链接
                Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '记起密码？',
                        style: TextStyle(
                          color: AppTheme.darkGrayColor,
                          fontSize: 14,
                        ),
                      ),
                      TextButton(
                        onPressed: isLoading
                            ? null
                            : () {
                                // 使用命名路由返回登录页
                                Navigator.of(context).pushReplacementNamed(AppRoutes.login);
                              },
                        child: Text(
                          '返回登录',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      ),
    );
  }
}