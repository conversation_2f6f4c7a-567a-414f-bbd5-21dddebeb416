/// 测试数据类
/// 提供各种测试场景所需的Mock数据
class MockData {
  // ==================== 用户认证相关数据 ====================
  
  /// 测试用户数据
  static const Map<String, dynamic> testUser = {
    'id': 'test_user_id_123',
    'username': 'testuser',
    'email': '<EMAIL>',
    'avatar': 'https://example.com/avatar.jpg',
    'bio': '这是一个测试用户',
    'createdAt': '2024-01-01T00:00:00.000Z',
    'updatedAt': '2024-01-01T00:00:00.000Z',
  };

  /// 测试用户2数据
  static const Map<String, dynamic> testUser2 = {
    'id': 'test_user_id_456',
    'username': 'testuser2',
    'email': '<EMAIL>',
    'avatar': null,
    'bio': '',
    'createdAt': '2024-01-02T00:00:00.000Z',
    'updatedAt': '2024-01-02T00:00:00.000Z',
  };

  /// 登录成功响应
  static const Map<String, dynamic> loginSuccessResponse = {
    'success': true,
    'message': '登录成功',
    'data': {
      'token': 'mock_jwt_token_12345',
      'user': testUser,
    },
  };

  /// 登录失败响应
  static const Map<String, dynamic> loginFailureResponse = {
    'success': false,
    'error': {
      'message': '用户名或密码错误',
      'code': 'INVALID_CREDENTIALS',
    },
  };

  /// 注册成功响应
  static const Map<String, dynamic> registerSuccessResponse = {
    'success': true,
    'message': '注册成功',
    'data': {
      'user': testUser,
    },
  };

  /// 注册失败响应
  static const Map<String, dynamic> registerFailureResponse = {
    'success': false,
    'error': {
      'message': '用户名已存在',
      'code': 'USERNAME_EXISTS',
    },
  };

  /// 发送验证码成功响应
  static const Map<String, dynamic> sendCodeSuccessResponse = {
    'success': true,
    'message': '验证码已发送',
    'data': {
      'expiresIn': 300, // 5分钟
    },
  };

  /// 忘记密码成功响应
  static const Map<String, dynamic> forgotPasswordSuccessResponse = {
    'success': true,
    'message': '重置密码邮件已发送',
    'data': {
      'expiresIn': 1800, // 30分钟
    },
  };

  /// 重置密码成功响应
  static const Map<String, dynamic> resetPasswordSuccessResponse = {
    'success': true,
    'message': '密码重置成功',
  };

  // ==================== 笔记相关数据 ====================

  /// 测试笔记数据
  static const Map<String, dynamic> testNote = {
    'id': 'note_id_123',
    'title': '测试笔记标题',
    'content': '这是一个测试笔记的内容',
    'contentType': 'rich-text',
    'tags': ['tag_id_1', 'tag_id_2'],
    'owner': 'test_user_id_123',
    'isFavorite': false,
    'isArchived': false,
    'createdAt': '2024-01-01T00:00:00.000Z',
    'updatedAt': '2024-01-01T00:00:00.000Z',
    'lastSyncedAt': '2024-01-01T00:00:00.000Z',
  };

  /// Markdown笔记数据
  static const Map<String, dynamic> markdownNote = {
    'id': 'note_id_456',
    'title': 'Markdown测试笔记',
    'content': '# 标题\n\n这是**粗体**文本和*斜体*文本。\n\n- 列表项1\n- 列表项2',
    'contentType': 'markdown',
    'tags': ['tag_id_3'],
    'owner': 'test_user_id_123',
    'isFavorite': true,
    'isArchived': false,
    'createdAt': '2024-01-02T00:00:00.000Z',
    'updatedAt': '2024-01-02T00:00:00.000Z',
    'lastSyncedAt': '2024-01-02T00:00:00.000Z',
  };

  /// 笔记列表数据
  static const List<Map<String, dynamic>> notesList = [
    testNote,
    markdownNote,
  ];

  // ==================== 标签相关数据 ====================

  /// 测试标签数据
  static const Map<String, dynamic> testTag = {
    'id': 'tag_id_1',
    'name': '工作',
    'color': '#FF5722',
    'owner': 'test_user_id_123',
    'noteCount': 5,
    'createdAt': '2024-01-01T00:00:00.000Z',
    'updatedAt': '2024-01-01T00:00:00.000Z',
  };

  /// 标签列表数据
  static const List<Map<String, dynamic>> tagsList = [
    {
      'id': 'tag_id_1',
      'name': '工作',
      'color': '#FF5722',
      'owner': 'test_user_id_123',
      'noteCount': 5,
      'createdAt': '2024-01-01T00:00:00.000Z',
      'updatedAt': '2024-01-01T00:00:00.000Z',
    },
    {
      'id': 'tag_id_2',
      'name': '学习',
      'color': '#2196F3',
      'owner': 'test_user_id_123',
      'noteCount': 3,
      'createdAt': '2024-01-01T00:00:00.000Z',
      'updatedAt': '2024-01-01T00:00:00.000Z',
    },
    {
      'id': 'tag_id_3',
      'name': '生活',
      'color': '#4CAF50',
      'owner': 'test_user_id_123',
      'noteCount': 2,
      'createdAt': '2024-01-01T00:00:00.000Z',
      'updatedAt': '2024-01-01T00:00:00.000Z',
    },
  ];

  // ==================== AI设置相关数据 ====================

  /// AI设置数据
  static const Map<String, dynamic> aiSettings = {
    'aiAssistantEnabled': true,
    'smartSuggestionsEnabled': true,
    'autoTaggingEnabled': false,
    'contentSummaryEnabled': true,
    'selectedModel': '默认模型',
    'suggestionFrequency': '输入停顿后',
    'localProcessingEnabled': false,
    'allowDataCollection': true,
  };

  // ==================== 主题设置相关数据 ====================

  /// 主题设置数据
  static const Map<String, dynamic> themeSettings = {
    'themeMode': 'system',
    'fontColor': '#000000',
    'textSize': 16.0,
  };

  // ==================== 错误响应数据 ====================

  /// 网络错误响应
  static const Map<String, dynamic> networkErrorResponse = {
    'success': false,
    'error': {
      'message': '网络连接失败',
      'code': 'NETWORK_ERROR',
    },
  };

  /// 服务器错误响应
  static const Map<String, dynamic> serverErrorResponse = {
    'success': false,
    'error': {
      'message': '服务器内部错误',
      'code': 'INTERNAL_SERVER_ERROR',
    },
  };

  /// 未授权错误响应
  static const Map<String, dynamic> unauthorizedErrorResponse = {
    'success': false,
    'error': {
      'message': '未授权访问',
      'code': 'UNAUTHORIZED',
    },
  };

  // ==================== 测试表单数据 ====================

  /// 有效的登录表单数据
  static const Map<String, String> validLoginForm = {
    'usernameOrEmail': 'testuser',
    'password': 'password123',
  };

  /// 无效的登录表单数据
  static const Map<String, String> invalidLoginForm = {
    'usernameOrEmail': '',
    'password': '123',
  };

  /// 有效的注册表单数据
  static const Map<String, String> validRegisterForm = {
    'username': 'newuser',
    'email': '<EMAIL>',
    'password': 'password123',
    'verificationCode': '123456',
  };

  /// 无效的注册表单数据
  static const Map<String, String> invalidRegisterForm = {
    'username': 'ab',
    'email': 'invalid-email',
    'password': '123',
    'verificationCode': '12345',
  };
}
