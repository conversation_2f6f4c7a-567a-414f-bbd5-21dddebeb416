# 智云笔记应用

<p align="center">
  <img src="../assets/images/app_logo.png" alt="智云笔记Logo" width="120"/>
</p>

<p align="center">
  <b>简洁高效的跨平台云笔记应用</b>
</p>

<p align="center">
  <a href="#功能特点">功能特点</a> •
  <a href="#技术架构">技术架构</a> •
  <a href="#项目结构">项目结构</a> •
  <a href="#快速开始">快速开始</a> •
  <a href="#开发指南">开发指南</a> •
  <a href="#贡献指南">贡献指南</a> •
  <a href="#开发进度">开发进度</a>
</p>

## 项目介绍

智云笔记是一款基于Flutter开发的跨平台云笔记应用，支持Android、iOS和Web平台。应用提供直观的用户界面和丰富的功能，帮助用户随时随地记录灵感和管理笔记。智云笔记采用Material Design 3设计规范，结合AI智能辅助功能，为用户提供高效、流畅的笔记体验。

### 核心理念

- **简洁易用**：注重直观的用户体验和简洁的界面设计
- **高效协作**：多设备同步和云存储确保数据安全和可访问性
- **智能辅助**：集成SiliconFlow API提供智能写作建议和内容分析
- **隐私保护**：严格的数据安全和隐私保护措施

## 功能特点

### 用户体验

- 🌙 **明/暗主题切换**：支持浅色/深色/跟随系统模式切换
- 🎨 **字体设置**：支持字体颜色和大小调整
- 📱 **响应式布局**：在各种尺寸设备上提供最佳体验
- 🔄 **列表/网格视图**：支持笔记列表和网格两种展示方式

### 笔记管理

- 📝 **富文本编辑**：基于Fleather的富文本编辑器，支持多种格式化选项
- 📄 **Markdown支持**：完整的Markdown编辑和预览功能
- 📊 **表格支持**：插入和编辑表格
- 📂 **分类标签**：支持创建、编辑和删除标签，自定义标签颜色
- ⭐ **收藏功能**：收藏常用笔记便于快速访问
- 📦 **归档功能**：归档不常用笔记
- 🔍 **全文搜索**：支持按关键词、日期范围和内容类型搜索
- 🗣️ **语音搜索**：支持语音输入进行搜索
- 📤 **导出功能**：支持导出为Markdown和JSON格式
- 📥 **导入功能**：支持导入Markdown和JSON格式笔记
- 🔗 **分享功能**：生成分享链接，支持未登录用户查看

### 用户认证

- 👤 **用户注册**：支持邮箱+验证码注册
- 🔑 **用户登录**：支持邮箱/用户名登录
- 🔄 **密码找回**：支持通过邮箱验证找回密码
- 🔒 **密码修改**：支持修改密码
- 📤 **退出登录**：支持安全退出登录

### AI 辅助功能

- ✍️ **智能补全**：Tab键自动补全功能，根据上下文预测下一个单词或语句
- 🏷️ **标签推荐**：根据笔记内容自动推荐合适标签
- 📝 **自动摘要**：一键生成笔记内容摘要
- 💬 **笔记问答**：基于笔记内容进行智能问答对话

## 技术架构

智云笔记采用前后端分离架构，前端使用Flutter框架开发跨平台客户端，后端使用Node.js + Express构建RESTful API服务。

### 前端技术栈

- **框架**: Flutter 3.10+
- **状态管理**: Provider
- **本地存储**: SharedPreferences
- **网络请求**: Dio
- **富文本编辑器**: Fleather
- **Markdown编辑器**: flutter_markdown
- **UI组件**: Material Design 3

### 后端技术栈

- **运行环境**: Node.js 18+
- **框架**: Express 4.18+
- **语言**: TypeScript 5+
- **数据库**: MongoDB 5+
- **认证**: JWT
- **缓存**: Redis
- **AI服务**: SiliconFlow API

## 项目结构

```
ai_cloud_notes/                # 前端Flutter应用
├── lib/                       # Flutter源代码
│   ├── config/                # 配置文件
│   ├── models/                # 数据模型
│   │   ├── note_model.dart    # 笔记模型
│   │   ├── tag_model.dart     # 标签模型
│   │   ├── user_model.dart    # 用户模型
│   │   ├── theme_settings_model.dart # 主题设置模型
│   │   └── ai_settings_model.dart # AI设置模型
│   ├── providers/             # 状态管理提供者
│   │   ├── auth_provider.dart # 认证状态提供者
│   │   ├── note_provider.dart # 笔记状态提供者
│   │   ├── tag_provider.dart  # 标签状态提供者
│   │   ├── theme_service.dart # 主题服务提供者
│   │   ├── user_provider.dart # 用户信息提供者
│   │   ├── search_provider.dart # 搜索提供者
│   │   └── ai_provider.dart   # AI功能提供者
│   ├── routes/                # 路由管理
│   │   └── app_routes.dart    # 应用路由定义
│   ├── screens/               # 页面
│   │   ├── auth/              # 认证相关页面
│   │   ├── editor/            # 编辑器相关页面
│   │   ├── home/              # 主页相关页面
│   │   ├── profile/           # 个人资料相关页面
│   │   ├── search/            # 搜索相关页面
│   │   ├── settings/          # 设置相关页面
│   │   ├── share/             # 分享相关页面
│   │   ├── tags/              # 标签相关页面
│   │   └── welcome/           # 欢迎页面
│   ├── services/              # 服务类
│   │   ├── api_service.dart   # API服务
│   │   ├── export_service.dart # 导出服务
│   │   ├── import_service.dart # 导入服务
│   │   ├── ai_function_service.dart # AI功能服务
│   │   └── import_export_manager.dart # 导入导出管理器
│   ├── themes/                # 主题相关
│   │   └── app_theme.dart     # 应用主题定义
│   ├── utils/                 # 工具类
│   │   ├── date_time_helper.dart # 日期时间辅助工具
│   │   ├── snackbar_helper.dart  # 消息提示辅助工具
│   │   ├── validators.dart       # 表单验证工具
│   │   └── file_helper.dart      # 文件处理辅助工具
│   ├── widgets/               # 可复用组件
│   │   ├── main_layout.dart   # 主布局组件
│   │   ├── note_content_preview.dart # 笔记内容预览组件
│   │   ├── theme_wrapper.dart # 主题包装器组件
│   │   ├── markdown_editor.dart # Markdown编辑器组件
│   │   └── ai_assistant_widget.dart # AI助手组件
│   └── main.dart              # 入口文件
├── assets/                    # 静态资源
│   ├── fonts/                 # 字体文件
│   └── images/                # 图片资源
└── test/                      # 测试代码
    ├── auth/                  # 认证模块测试
    ├── editor/                # 编辑器模块测试
    ├── home/                  # 主页模块测试
    └── logs/                  # 测试日志目录

ai_cloud_notes_backend/       # 后端Node.js应用
├── src/                       # TypeScript源代码
│   ├── config/                # 配置文件
│   ├── controllers/           # 控制器
│   │   ├── auth.controller.ts # 认证控制器
│   │   ├── notes.controller.ts # 笔记控制器
│   │   ├── tags.controller.ts # 标签控制器
│   │   ├── theme_settings.controller.ts # 主题设置控制器
│   │   └── ai_settings.controller.ts # AI设置控制器
│   ├── middlewares/           # 中间件
│   │   ├── auth.middleware.ts # 认证中间件
│   │   └── error.middleware.ts # 错误处理中间件
│   ├── models/                # 数据模型
│   │   ├── note.model.ts      # 笔记模型
│   │   ├── note_history.model.ts # 笔记历史模型
│   │   ├── tag.model.ts       # 标签模型
│   │   ├── theme_settings.model.ts # 主题设置模型
│   │   ├── ai_settings.model.ts # AI设置模型
│   │   └── user.model.ts      # 用户模型
│   ├── routes/                # 路由
│   │   ├── auth.routes.ts     # 认证路由
│   │   ├── notes.routes.ts    # 笔记路由
│   │   ├── tags.routes.ts     # 标签路由
│   │   ├── theme_settings.routes.ts # 主题设置路由
│   │   ├── ai_settings.routes.ts # AI设置路由
│   │   └── ai.routes.ts       # AI功能路由
│   ├── services/              # 服务类
│   │   ├── auth.service.ts    # 认证服务
│   │   ├── notes.service.ts   # 笔记服务
│   │   ├── tags.service.ts    # 标签服务
│   │   ├── theme_settings.service.ts # 主题设置服务
│   │   ├── ai_settings.service.ts # AI设置服务
│   │   └── ai.service.ts      # AI功能服务
│   ├── utils/                 # 工具函数
│   │   ├── logger.ts          # 日志工具
│   │   ├── validators.ts      # 验证工具
│   │   └── auth.ts            # 认证工具
│   └── app.ts                 # 应用入口
├── dist/                      # 编译后的JavaScript代码
└── tests/                     # 测试文件
```

## 快速开始

### 环境要求

- Flutter SDK 3.10+
- Dart SDK 3.0+
- Node.js 18+
- MongoDB 5+
- Redis 6+（用于缓存）

### 前端项目设置

1. 克隆仓库
```bash
git clone https://github.com/your-username/ai_cloud_notes.git
cd ai_cloud_notes
```

2. 安装依赖
```bash
flutter pub get
```

3. 配置环境
创建`.env`文件，包含必要的环境变量：
```
API_BASE_URL=http://localhost:3000/api
SILICON_FLOW_API_KEY=your_silicon_flow_api_key
```

4. 启动应用
```bash
# Web版本
flutter run -d chrome
# Android版本
flutter run -d android
# iOS版本
flutter run -d ios
```

### 后端项目设置

1. 克隆仓库
```bash
git clone https://github.com/your-username/ai_cloud_notes_backend.git
cd ai_cloud_notes_backend
```

2. 安装依赖
```bash
npm install
```

3. 配置环境变量
创建`.env`文件，包含必要的环境变量：
```
PORT=3000
MONGODB_URI=mongodb://localhost:27017/ai_cloud_notes
REDIS_URI=redis://localhost:6379
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d
SILICON_FLOW_API_KEY=your_silicon_flow_api_key
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
NODE_ENV=development
```

4. 启动服务器
```bash
# 开发模式
npm run dev
# 生产模式
npm start
```

## 开发指南

### 编码规范

项目采用以下编码规范：

- **Dart**: 遵循[Effective Dart](https://dart.dev/guides/language/effective-dart)规范
- **TypeScript**: 遵循[Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- **注释**: 使用中文注释，确保代码可读性
- **Git提交**: 使用[约定式提交](https://www.conventionalcommits.org/zh-hans/)规范

### 应用架构

前端采用Clean Architecture架构模式：

- **表现层**：包含UI组件、页面和视图模型
- **领域层**：包含业务逻辑和用例
- **数据层**：处理数据获取和持久化

### 状态管理

使用Provider进行状态管理：

```dart
// 在main.dart中注册Provider
MultiProvider(
  providers: [
    ChangeNotifierProvider(create: (_) => AuthProvider()..init()),
    ChangeNotifierProvider(create: (_) => NoteProvider()),
    ChangeNotifierProvider(create: (_) => TagProvider()),
    ChangeNotifierProvider(create: (_) => ThemeService()),
  ],
  child: MyApp(),
)

// 在UI中使用
Consumer<NoteProvider>(
  builder: (context, noteProvider, child) {
    final notes = noteProvider.notes;
    return ListView.builder(
      itemCount: notes.length,
      itemBuilder: (context, index) => NoteItem(note: notes[index]),
    );
  },
)
```

### API服务

使用Dio进行网络请求：

```dart
// API服务示例
class ApiService {
  final Dio _dio = Dio();
  final String _baseUrl = 'http://localhost:3000/api';
  String? _token;

  // 设置认证令牌
  void setToken(String token) {
    _token = token;
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  // 获取笔记列表
  Future<Map<String, dynamic>> getNotes({int page = 1, int limit = 20}) async {
    try {
      final response = await _dio.get(
        '$_baseUrl/notes',
        queryParameters: {'page': page, 'limit': limit},
      );
      return response.data;
    } catch (e) {
      return {'success': false, 'error': {'message': e.toString()}};
    }
  }
}
```

## 贡献指南

我们欢迎各种形式的贡献，无论是功能请求、bug报告还是代码贡献。

### 贡献流程

1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'feat: add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

### 开发流程

1. 挑选一个任务或issue
2. 在本地开发和测试功能
3. 确保代码通过所有测试
4. 提交Pull Request
5. 代码审核和合并

## 开发进度

项目当前处于积极开发阶段，主要功能模块的开发进度如下：

### 已完成功能

- ✅ **用户认证模块**：注册、登录、找回密码、修改密码、退出登录
- ✅ **笔记管理模块**：创建、编辑、删除、收藏、归档、批量操作
- ✅ **编辑器模块**：富文本编辑器、Markdown编辑器、表格支持
- ✅ **标签管理模块**：创建、编辑、删除、颜色自定义、笔记关联
- ✅ **搜索模块**：基本搜索、高级搜索、语音搜索、搜索历史
- ✅ **设置模块**：主题设置、AI功能设置
- ✅ **AI辅助功能**：智能补全、标签推荐、自动摘要、笔记问答
- ✅ **数据同步**：基本云同步、笔记历史版本管理
- ✅ **导入导出**：Markdown和JSON格式导入导出
- ✅ **分享功能**：生成分享链接，支持未登录用户查看

### 进行中功能

- 🔄 **图片处理**：图片插入和存储优化
- 🔄 **搜索优化**：搜索结果排序和相关性提升
- 🔄 **同步冲突处理**：改进多设备同步冲突解决机制
- 🔄 **性能优化**：大型笔记加载优化、缓存策略优化

### 待开发功能

- 📝 **离线模式支持**：所有模块的离线功能支持
- 📝 **笔记加密**：敏感笔记的加密保护
- 📝 **协作编辑**：多用户同时编辑笔记
- 📝 **插件系统**：支持第三方插件扩展功能

详细的开发进度请参考 [development_progress.md](docs/development_progress.md) 文件。

## 许可证

本项目采用MIT许可证 - 详情请参见 [LICENSE](LICENSE) 文件。