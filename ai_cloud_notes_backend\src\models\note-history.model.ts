import mongoose, { Document, Schema } from 'mongoose';

/**
 * 笔记历史文档接口
 */
export interface INoteHistoryDocument extends Document {
  noteId: mongoose.Types.ObjectId;    // 关联的原笔记ID
  version: number;                    // 历史版本号
  title: string;                      // 标题
  content: string;                    // 内容
  contentType: string;                // 内容类型
  tags: mongoose.Types.ObjectId[];    // 标签IDs
  owner: mongoose.Types.ObjectId;     // 所有者ID
  archivedAt: Date;                   // 存档时间
}

/**
 * 笔记历史模式定义
 */
const NoteHistorySchema = new Schema({
  // 关联的原笔记ID
  noteId: {
    type: Schema.Types.ObjectId,
    ref: 'Note',
    required: true,
    index: true,
  },
  
  // 版本号
  version: {
    type: Number,
    required: true,
  },
  
  // 标题
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  
  // 内容
  content: {
    type: String,
    default: '',
  },
  
  // 内容类型
  contentType: {
    type: String,
    enum: ['plain-text', 'rich-text', 'markdown'],
    default: 'rich-text',
  },
  
  // 标签列表
  tags: [{
    type: Schema.Types.ObjectId,
    ref: 'Tag',
  }],
  
  // 所有者
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true,
  },
  
  // 归档时间(创建时间)
  archivedAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
});

// 创建复合索引
NoteHistorySchema.index({ noteId: 1, version: 1 }, { unique: true });

/**
 * 定义笔记历史模型
 */
const NoteHistory = mongoose.model<INoteHistoryDocument>('NoteHistory', NoteHistorySchema);

export default NoteHistory; 