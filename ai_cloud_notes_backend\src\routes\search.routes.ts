import express from 'express';
import { body } from 'express-validator';
import * as searchController from '../controllers/search.controller';
import { authenticate } from '../middlewares/auth.middleware';

const router = express.Router();

// 所有搜索路由都需要认证，除了获取热门搜索
router.use((req, res, next) => {
  // 获取热门搜索的路由不需要认证
  if (req.path === '/popular') {
    next();
  } else {
    authenticate(req, res, next);
  }
});

/**
 * 高级搜索
 * GET /api/search/advanced
 */
router.get('/advanced', searchController.advancedSearch);

/**
 * 获取搜索历史
 * GET /api/search/history
 */
router.get('/history', searchController.getSearchHistory);

/**
 * 添加搜索历史
 * POST /api/search/history
 */
router.post(
  '/history',
  [
    body('query')
      .notEmpty()
      .withMessage('搜索关键词不能为空')
      .isString()
      .withMessage('搜索关键词必须是字符串')
      .isLength({ max: 100 })
      .withMessage('搜索关键词最多100个字符'),
  ],
  searchController.addSearchHistory
);

/**
 * 清除搜索历史
 * DELETE /api/search/history
 */
router.delete('/history', searchController.clearSearchHistory);

/**
 * 获取热门搜索
 * GET /api/search/popular
 */
router.get('/popular', searchController.getPopularSearches);

export default router; 