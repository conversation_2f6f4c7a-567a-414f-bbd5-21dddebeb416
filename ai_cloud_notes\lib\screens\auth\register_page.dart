import 'dart:ui'; // Required for ImageFilter
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/utils/validators.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({Key? key}) : super(key: key);

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _verificationCodeController = TextEditingController();

  bool _isObscure = true;
  bool _isObscureConfirm = true;
  bool _codeSent = false;
  bool _isSendingCode = false;
  int _countDown = 60;

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }

  /// 发送验证码
  void _sendVerificationCode() async {
    // 验证邮箱格式
    if (!Validators.isValidEmail(_emailController.text)) {
      SnackbarHelper.showError(
        context: context,
        message: '请输入有效的邮箱地址',
      );
      return;
    }

    setState(() {
      _isSendingCode = true;
    });

    // 调用发送验证码接口
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final response = await authProvider.sendVerificationCode(_emailController.text);

    if (mounted) {
      setState(() {
        _isSendingCode = false;
      });

      if (response['success'] == true) {
        // 发送成功，开始倒计时
        setState(() {
          _codeSent = true;
          _countDown = 60;
        });

        // 显示成功消息
        SnackbarHelper.showSuccess(
          context: context,
          message: response['message'] ?? '验证码已发送到您的邮箱',
        );

        // 启动倒计时
        _startCountDown();
      } else {
        // 发送失败，显示错误消息
        SnackbarHelper.showError(
          context: context,
          message: response['error']?['message'] ?? '验证码发送失败，请稍后再试',
        );
      }
    }
  }

  /// 开始倒计时
  void _startCountDown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          if (_countDown > 0) {
            _countDown--;
            _startCountDown();
          } else {
            _codeSent = false;
          }
        });
      }
    });
  }

  /// 执行注册操作
  void _register() async {
    if (_formKey.currentState!.validate()) {
      // 获取AuthProvider实例
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // 调用注册方法
      final success = await authProvider.register(
        username: _usernameController.text.trim(),
        email: _emailController.text.trim(),
        password: _passwordController.text,
        verificationCode: _verificationCodeController.text.trim(),
      );

      if (mounted) {
        if (success) {
          // 注册成功，显示成功消息并跳转到登录页
          SnackbarHelper.showSuccess(
            context: context,
            message: '注册成功，请登录',
          );

          // 延迟跳转，让用户看到成功消息
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted) {
              Navigator.of(context).pushReplacementNamed(AppRoutes.login);
            }
          });
        } else {
          // 注册失败，显示错误提示
          SnackbarHelper.showError(
            context: context,
            message: authProvider.error ?? '注册失败，请稍后再试',
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取AuthProvider，监听状态变化
    final authProvider = Provider.of<AuthProvider>(context);
    final isLoading = authProvider.isLoading;

    return Theme(
      data: AppTheme.fixedLightTheme,
      child: Scaffold(
        appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '注册账号',
          style: TextStyle(
            color: AppTheme.blackColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true, // 标题居中，与其他页面统一
        // 添加毛玻璃效果
        flexibleSpace: ClipRect( // ClipRect 用于裁剪 BackdropFilter 的区域
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0), // 应用模糊效果
            child: Container(
              color: Colors.white.withOpacity(0.3), // 毛玻璃上方的半透明层
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),

                // 用户名输入框
                Text(
                  '用户名',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.darkGrayColor,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _usernameController,
                  enabled: !isLoading,
                  decoration: InputDecoration(
                    hintText: '请输入用户名',
                    prefixIcon: Icon(Icons.person_outline),
                    // 使用下划线边框
                    border: UnderlineInputBorder(
                      borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                    ),
                    focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                      borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                    ),
                    // 调整内边距
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入用户名';
                    }
                    if (value.length < 3 || value.length > 20) {
                      return '用户名长度应在3-20个字符之间';
                    }
                    if (!Validators.isValidUsername(value)) {
                      return '用户名只能包含字母、数字和下划线';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // 邮箱输入框
                Text(
                  '邮箱',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.darkGrayColor,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _emailController,
                  enabled: !isLoading && !_codeSent,
                  keyboardType: TextInputType.emailAddress,
                  decoration: InputDecoration(
                    hintText: '请输入邮箱',
                    prefixIcon: Icon(Icons.email_outlined),
                    // 使用下划线边框
                    border: UnderlineInputBorder(
                      borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                    ),
                    focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                      borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                    ),
                    // 调整内边距
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入邮箱';
                    }
                    if (!Validators.isValidEmail(value)) {
                      return '请输入有效的邮箱地址';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // 验证码输入框
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '验证码',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: AppTheme.darkGrayColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextFormField(
                            controller: _verificationCodeController,
                            enabled: !isLoading,
                            keyboardType: TextInputType.number,
                            maxLength: 6,
                            decoration: InputDecoration(
                              hintText: '请输入验证码',
                              prefixIcon: Icon(Icons.verified_outlined),
                              // 使用下划线边框
                              border: UnderlineInputBorder(
                                borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                              ),
                              focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                                borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                              ),
                              // 调整内边距
                              contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                              counterText: '', // 保持 counterText 为空
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return '请输入验证码';
                              }
                              if (value.length != 6) {
                                return '请输入6位验证码';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 10),
                    Padding(
                      padding: const EdgeInsets.only(top: 28),
                      child: SizedBox(
                        height: 50,
                        child: ElevatedButton(
                          onPressed: (isLoading || _codeSent || _isSendingCode)
                            ? null
                            : _sendVerificationCode,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              // 调整圆角
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 0, // 保持无阴影
                            // 可以考虑微调 padding 或 textStyle
                            // padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                            // textStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                          ),
                          child: _isSendingCode
                              ? SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(
                                  _codeSent ? '$_countDown秒后重试' : '获取验证码',
                                  style: TextStyle(fontSize: 14),
                                ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // 密码输入框
                Text(
                  '密码',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.darkGrayColor,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _passwordController,
                  enabled: !isLoading,
                  obscureText: _isObscure,
                  decoration: InputDecoration(
                    hintText: '请输入密码',
                    prefixIcon: Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isObscure ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _isObscure = !_isObscure;
                        });
                      },
                    ),
                    // 使用下划线边框
                    border: UnderlineInputBorder(
                      borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                    ),
                    focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                      borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                    ),
                    // 调整内边距
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入密码';
                    }
                    if (value.length < 8) {
                      return '密码长度不能少于8个字符';
                    }
                    if (!Validators.isValidPassword(value)) {
                      return '密码须包含大小写字母、数字和特殊字符';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // 确认密码输入框
                Text(
                  '确认密码',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.darkGrayColor,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _confirmPasswordController,
                  enabled: !isLoading,
                  obscureText: _isObscureConfirm,
                  decoration: InputDecoration(
                    hintText: '请再次输入密码',
                    prefixIcon: Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _isObscureConfirm ? Icons.visibility_off : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _isObscureConfirm = !_isObscureConfirm;
                        });
                      },
                    ),
                    // 使用下划线边框
                    border: UnderlineInputBorder(
                      borderSide: BorderSide(color: AppTheme.mediumGrayColor),
                    ),
                    focusedBorder: UnderlineInputBorder( // 获得焦点时的边框
                      borderSide: BorderSide(color: AppTheme.primaryColor, width: 2.0),
                    ),
                    // 调整内边距
                    contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 0),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请再次输入密码';
                    }
                    if (value != _passwordController.text) {
                      return '两次输入的密码不一致';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 30),

                // 注册按钮
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: isLoading ? null : _register,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        // 调整圆角
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0, // 保持无阴影
                      // 可以考虑微调 padding 或 textStyle
                      // padding: EdgeInsets.symmetric(horizontal: 32, vertical: 14),
                      // textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                    child: isLoading
                        ? SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Text(
                            '注 册',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                          ),
                  ),
                ),

                const SizedBox(height: 20),

                // 返回登录链接
                Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '已有账号？',
                        style: TextStyle(
                          color: AppTheme.darkGrayColor,
                          fontSize: 14,
                        ),
                      ),
                      TextButton(
                        onPressed: isLoading
                            ? null
                            : () {
                                // 使用命名路由返回登录页
                                Navigator.of(context).pushReplacementNamed(AppRoutes.login);
                              },
                        child: Text(
                          '返回登录',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ) // Closes SingleChildScrollView
    )   // Closes Scaffold
  );    // Closes Theme and return statement
  }
}