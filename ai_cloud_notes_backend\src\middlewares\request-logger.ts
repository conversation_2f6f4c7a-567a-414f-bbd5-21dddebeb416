import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';

/**
 * 请求日志中间件
 * 记录请求的详细信息
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  
  // 请求结束后记录响应时间
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logObject = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      statusCode: res.statusCode,
      userAgent: req.get('user-agent'),
      duration: `${duration}ms`
    };
    
    // 根据状态码选择日志级别
    if (res.statusCode >= 500) {
      logger.error('请求处理失败', logObject);
    } else if (res.statusCode >= 400) {
      logger.warn('请求错误', logObject);
    } else {
      logger.debug('请求处理完成', logObject);
    }
  });
  
  next();
}; 