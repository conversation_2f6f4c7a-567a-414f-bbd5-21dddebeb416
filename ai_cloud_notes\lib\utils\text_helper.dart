import 'dart:convert';

/// 文本处理工具类
class TextHelper {
  /// 从Delta JSON字符串提取纯文本内容
  /// 
  /// 参数：
  /// - [jsonText] : Delta格式的JSON字符串
  /// 
  /// 返回：提取的纯文本内容
  static String extractTextFromDelta(String jsonText) {
    if (jsonText.isEmpty) return '';
    
    try {
      // 尝试解析JSON
      dynamic jsonData;
      if (jsonText.startsWith('[') || jsonText.startsWith('{')) {
        jsonData = json.decode(jsonText);
      } else {
        // 如果不是JSON格式，直接返回原文本
        return jsonText;
      }
      
      // 处理不同格式的Delta
      String result = '';
      
      if (jsonData is List) {
        // 处理标准Delta格式: [{insert: "text"}, ...]
        for (var op in jsonData) {
          if (op is Map && op.containsKey('insert')) {
            var insert = op['insert'];
            if (insert is String) {
              result += insert;
            } else if (insert is Map) {
              // 如果是嵌入对象（如图片等），添加占位符
              if (insert.containsKey('image')) {
                result += '[图片] ';
              } else {
                result += '[嵌入内容] ';
              }
            }
          }
        }
      } else if (jsonData is Map && jsonData.containsKey('ops')) {
        // 处理完整Delta格式: {ops: [{insert: "text"}, ...]}
        List ops = jsonData['ops'];
        for (var op in ops) {
          if (op is Map && op.containsKey('insert')) {
            var insert = op['insert'];
            if (insert is String) {
              result += insert;
            } else if (insert is Map) {
              // 处理嵌入对象
              if (insert.containsKey('image')) {
                result += '[图片] ';
              } else {
                result += '[嵌入内容] ';
              }
            }
          }
        }
      }
      
      return result.trim();
    } catch (e) {
      print('ERROR: 解析Delta JSON失败: $e');
      // 解析失败时返回原文本
      return jsonText;
    }
  }
} 