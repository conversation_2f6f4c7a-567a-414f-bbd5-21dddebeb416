import { Router } from 'express';
import AISettingsController from '../controllers/ai_settings.controller';
import { authenticate } from '../middlewares/auth.middleware';

const router = Router();

/**
 * @swagger
 * /api/ai-settings:
 *   get:
 *     summary: 获取用户AI设置
 *     tags: [AI设置]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 成功获取AI设置
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.get('/', authenticate, AISettingsController.getUserAISettings);

/**
 * @swagger
 * /api/ai-settings:
 *   post:
 *     summary: 更新用户AI设置
 *     tags: [AI设置]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               aiAssistantEnabled:
 *                 type: boolean
 *                 description: AI助手开关
 *               smartSuggestionsEnabled:
 *                 type: boolean
 *                 description: 智能建议开关
 *               autoTaggingEnabled:
 *                 type: boolean
 *                 description: 自动标签开关
 *               contentSummaryEnabled:
 *                 type: boolean
 *                 description: 内容摘要开关
 *               selectedModel:
 *                 type: string
 *                 description: 选择的AI模型
 *               suggestionFrequency:
 *                 type: string
 *                 enum: ['低', '中', '高']
 *                 description: 建议频率
 *               localProcessingEnabled:
 *                 type: boolean
 *                 description: 本地处理开关
 *               allowDataCollection:
 *                 type: boolean
 *                 description: 允许数据收集开关
 *     responses:
 *       200:
 *         description: 成功更新AI设置
 *       400:
 *         description: 请求数据无效
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器错误
 */
router.post('/', authenticate, AISettingsController.updateUserAISettings);

export default router;
