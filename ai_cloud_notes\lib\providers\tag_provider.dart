import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/models/tag_model.dart';
import 'package:ai_cloud_notes/services/api_service.dart';
import 'package:ai_cloud_notes/utils/logger.dart'; // 假设你有一个日志工具

/// 标签状态管理类
///
/// 负责管理标签数据，包括获取、创建、更新、删除标签，
/// 以及管理相关的加载状态和错误信息。
class TagProvider with ChangeNotifier {
  final ApiService _apiService = ApiService(); // API服务实例

  List<Tag> _tags = []; // 标签列表
  List<Tag> _popularTags = []; // 热门标签列表
  bool _isLoading = false; // 加载状态
  bool _isPopularLoading = false; // 加载热门标签状态
  String? _errorMessage; // 错误信息
  String? _popularErrorMessage; // 热门标签错误信息

  // --- Getters ---
  /// 获取当前标签列表
  List<Tag> get tags => _tags;
  /// 获取热门标签列表
  List<Tag> get popularTags => _popularTags;
  /// 获取加载状态
  bool get isLoading => _isLoading;
  /// 获取加载热门标签状态
  bool get isPopularLoading => _isPopularLoading;
  /// 获取错误信息
  String? get errorMessage => _errorMessage;
  /// 获取热门标签错误信息
  String? get popularErrorMessage => _popularErrorMessage;

  // --- 私有状态更新方法 ---
  /// 设置加载状态
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  /// 设置热门标签加载状态
  void _setPopularLoading(bool value) {
    _isPopularLoading = value;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String? message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// 设置热门标签错误信息
  void _setPopularError(String? message) {
    _popularErrorMessage = message;
    notifyListeners();
  }

  // --- 公开方法 ---

  /// 获取用户的所有标签
  Future<void> fetchTags() async {
    _setLoading(true);
    _setError(null);
    try {
      Log.info('开始获取标签数据');
      final response = await _apiService.getTags();
      if (response['success'] == true && response['data']?['tags'] != null) {
        final List<dynamic> tagsData = response['data']['tags'];
        Log.info('成功获取标签数据，共 ${tagsData.length} 个标签');

        // 先创建标签对象列表，暂时使用默认笔记数量0
        List<Tag> tempTags = tagsData.map((json) {
          // 处理笔记数量字段，后端可能返回count而不是noteCount
          if (json['count'] != null && json['noteCount'] == null) {
            json['noteCount'] = json['count'];
          } else if (json['noteCount'] == null && json['count'] == null) {
            json['noteCount'] = 0; // 临时设置为0
          }
          return Tag.fromJson(json);
        }).toList();

        // 排序标签
        tempTags.sort((a, b) => a.name.compareTo(b.name));

        // 更新标签列表
        _tags = tempTags;

        // 通知UI先显示标签列表（可能笔记数量为0）
        notifyListeners();
        Log.info('标签列表已更新，准备获取每个标签的笔记数量');

        // 立即更新标签的笔记数量，不使用异步
        await _updateTagsNoteCounts();
        Log.info('标签笔记数量更新完成');

        // 再次通知UI更新，显示正确的笔记数量
        notifyListeners();
      } else {
        _setError(response['error']?['message'] ?? '获取标签失败');
        Log.error('获取标签失败: ${response['error']}');
      }
    } catch (e) {
      _setError('获取标签时发生错误: $e');
      Log.error('获取标签异常: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 更新所有标签的笔记数量
  ///
  /// 返回一个Future，可以等待所有标签的笔记数量更新完成
  Future<void> _updateTagsNoteCounts() async {
    try {
      Log.info('开始更新标签笔记数量，共 ${_tags.length} 个标签');

      // 创建一个临时列表存储更新后的标签
      List<Tag> updatedTags = [];

      // 为每个标签获取准确的笔记数量
      for (var tag in _tags) {
        try {
          // 获取标签详情，包括准确的笔记数量
          final response = await _apiService.getTagById(tag.id);
          if (response['success'] == true && response['data'] != null) {
            final noteCount = response['data']['noteCount'] ?? 0;
            // 使用API返回的准确笔记数量更新标签
            updatedTags.add(tag.copyWith(noteCount: noteCount));
            Log.info('标签 ${tag.name} 的笔记数量更新为 $noteCount');
          } else {
            // 如果获取失败，保留原标签
            updatedTags.add(tag);
            Log.error('获取标签笔记数量失败: ${response['error']}');
          }
        } catch (e) {
          // 如果发生异常，保留原标签
          updatedTags.add(tag);
          Log.error('获取标签笔记数量异常: $e');
        }
      }

      // 更新标签列表
      _tags = updatedTags;

      // 打印日志，方便调试
      Log.info('标签笔记数量更新完成，共 ${_tags.length} 个标签');
    } catch (e) {
      Log.error('更新标签笔记数量异常: $e');
    }
  }

  /// 获取热门标签
  Future<void> fetchPopularTags({int limit = 10}) async {
    _setPopularLoading(true);
    _setPopularError(null);
    try {
      final response = await _apiService.getPopularTags(limit: limit);
      if (response['success'] == true && response['data']?['tags'] != null) {
        final List<dynamic> tagsData = response['data']['tags'];
        _popularTags = tagsData.map((json) => Tag.fromJson(json..['noteCount'] = json['count'])).toList(); // 后端返回的是 count
      } else {
        _setPopularError(response['error']?['message'] ?? '获取热门标签失败');
        Log.error('获取热门标签失败: ${response['error']}');
      }
    } catch (e) {
      _setPopularError('获取热门标签时发生错误: $e');
      Log.error('获取热门标签异常: $e');
    } finally {
      _setPopularLoading(false);
    }
  }

  /// 创建新标签
  ///
  /// [name] 标签名称
  /// [color] 标签颜色 (十六进制)
  Future<bool> createTag(String name, {String? color}) async {
    _setLoading(true); // 可以考虑为单个操作设置独立loading状态
    _setError(null);
    try {
      print('DEBUG: [TagProvider] 创建标签: $name, 颜色: $color');

      // 检查是否已存在同名标签
      final existingTag = _tags.where((tag) => tag.name.toLowerCase() == name.toLowerCase()).toList();
      if (existingTag.isNotEmpty) {
        print('DEBUG: [TagProvider] 已存在同名标签: ${existingTag.first.id} - ${existingTag.first.name}');
        return true; // 已存在同名标签，视为创建成功
      }

      final response = await _apiService.createTag(name: name, color: color);
      print('DEBUG: [TagProvider] 创建标签响应: $response');

      if (response['success'] == true && response['data']?['tag'] != null) {
        final newTag = Tag.fromJson(response['data']['tag']);
        print('DEBUG: [TagProvider] 创建的新标签: ${newTag.id} - ${newTag.name}');
        _tags.add(newTag);
        _tags.sort((a, b) => a.name.compareTo(b.name)); // 保持排序
        notifyListeners();
        return true;
      } else {
        _setError(response['error']?['message'] ?? '创建标签失败');
        Log.error('创建标签失败: ${response['error']}');
        print('DEBUG: [TagProvider] 创建标签失败: ${response['error']}');
        return false;
      }
    } catch (e) {
      _setError('创建标签时发生错误: $e');
      Log.error('创建标签异常: $e');
      print('DEBUG: [TagProvider] 创建标签异常: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 更新标签
  ///
  /// [tagId] 要更新的标签ID
  /// [name] 新名称
  /// [color] 新颜色
  Future<bool> updateTag(String tagId, {String? name, String? color}) async {
    _setError(null);
    try {
      final response = await _apiService.updateTag(id: tagId, name: name, color: color);
      if (response['success'] == true && response['data']?['tag'] != null) {
        final updatedTag = Tag.fromJson(response['data']['tag']);
        final index = _tags.indexWhere((tag) => tag.id == tagId);
        if (index != -1) {
          _tags[index] = updatedTag;
          _tags.sort((a, b) => a.name.compareTo(b.name)); // 保持排序
          notifyListeners();
          return true;
        }
        return false; // 未找到要更新的标签
      } else {
        _setError(response['error']?['message'] ?? '更新标签失败');
        Log.error('更新标签失败: ${response['error']}');
        return false;
      }
    } catch (e) {
      _setError('更新标签时发生错误: $e');
      Log.error('更新标签异常: $e');
      return false;
    }
  }

  /// 删除标签
  ///
  /// [tagId] 要删除的标签ID
  Future<bool> deleteTag(String tagId) async {
    _setError(null);
    try {
      final response = await _apiService.deleteTag(tagId);
      if (response['success'] == true) {
        _tags.removeWhere((tag) => tag.id == tagId);
        notifyListeners();
        return true;
      } else {
        _setError(response['error']?['message'] ?? '删除标签失败');
        Log.error('删除标签失败: ${response['error']}');
        return false;
      }
    } catch (e) {
      _setError('删除标签时发生错误: $e');
      Log.error('删除标签异常: $e');
      return false;
    }
  }

  /// 获取标签详情
  ///
  /// [tagId] 标签ID
  /// 返回标签对象，获取失败返回null
  Future<Tag?> getTagById(String tagId) async {
    _setError(null);
    try {
      // 先尝试从本地缓存获取
      final cachedTag = _tags.firstWhere((tag) => tag.id == tagId, orElse: () => Tag.empty());
      if (cachedTag.id.isNotEmpty) {
        // 有缓存但还是请求一次更新笔记数量等信息
        final response = await _apiService.getTagById(tagId);
        if (response['success'] == true && response['data'] != null) {
          final tagData = response['data']['tag'];
          final noteCount = response['data']['noteCount'] ?? 0;

          // 更新本地缓存
          final updatedTag = Tag.fromJson(tagData)
            .copyWith(noteCount: noteCount);

          // 更新列表中的标签
          final index = _tags.indexWhere((tag) => tag.id == tagId);
          if (index != -1) {
            _tags[index] = updatedTag;
            notifyListeners();
          }

          return updatedTag;
        }
        // 如果请求失败，返回缓存的标签
        return cachedTag;
      }

      // 本地没有缓存，从API获取
      final response = await _apiService.getTagById(tagId);
      if (response['success'] == true && response['data'] != null) {
        final tagData = response['data']['tag'];
        final noteCount = response['data']['noteCount'] ?? 0;

        // 创建标签对象
        final tag = Tag.fromJson(tagData)
          .copyWith(noteCount: noteCount);

        // 检查是否已经在列表中
        final existingIndex = _tags.indexWhere((t) => t.id == tagId);
        if (existingIndex == -1) {
          // 不在列表中，添加到列表
          _tags.add(tag);
          _tags.sort((a, b) => a.name.compareTo(b.name)); // 保持排序
          notifyListeners();
        } else {
          // 已在列表中，更新
          _tags[existingIndex] = tag;
          notifyListeners();
        }

        return tag;
      } else {
        _setError(response['error']?['message'] ?? '获取标签详情失败');
        Log.error('获取标签详情失败: ${response['error']}');
        return null;
      }
    } catch (e) {
      _setError('获取标签详情时发生错误: $e');
      Log.error('获取标签详情异常: $e');
      return null;
    }
  }

  /// 批量删除标签
  ///
  /// [tagIds] 要删除的标签ID列表
  /// 返回一个Map，键为标签ID，值为删除是否成功
  Future<Map<String, bool>> batchDeleteTags(List<String> tagIds) async {
    _setError(null);

    final results = <String, bool>{};

    for (final tagId in tagIds) {
      try {
        final response = await _apiService.deleteTag(tagId);
        if (response['success'] == true) {
          _tags.removeWhere((tag) => tag.id == tagId);
          results[tagId] = true;
        } else {
          Log.error('删除标签失败: ${response['error']}');
          results[tagId] = false;
        }
      } catch (e) {
        Log.error('删除标签异常: $e');
        results[tagId] = false;
      }
    }

    // 只需要调用一次通知
    notifyListeners();

    // 如果有任何删除失败，设置错误消息
    if (results.containsValue(false)) {
      final failCount = results.values.where((success) => !success).length;
      _setError('$failCount 个标签删除失败');
    }

    return results;
  }

  /// 清空无笔记标签
  ///
  /// 删除所有没有关联笔记的标签
  /// 返回一个Map，包含成功和失败的数量
  Future<Map<String, dynamic>> cleanEmptyTags() async {
    _setLoading(true);
    _setError(null);

    final results = {
      'success': 0,
      'failed': 0,
      'total': 0,
    };

    try {
      // 先确保标签数据是最新的，并且每个标签都有准确的笔记数量
      await fetchTags();

      // 等待笔记数量更新完成
      await _updateTagsNoteCounts();

      // 筛选出笔记数量确实为0的标签
      final emptyTags = _tags.where((tag) => tag.noteCount == 0).toList();
      results['total'] = emptyTags.length;

      if (emptyTags.isEmpty) {
        _setLoading(false);
        return results;
      }

      // 获取所有无笔记标签的ID
      final emptyTagIds = emptyTags.map((tag) => tag.id).toList();

      // 批量删除这些标签
      final deleteResults = await batchDeleteTags(emptyTagIds);

      // 统计结果
      results['success'] = deleteResults.values.where((success) => success).length;
      results['failed'] = deleteResults.values.where((success) => !success).length;

      // 刷新标签列表
      await fetchTags();

      return results;
    } catch (e) {
      _setError('清空无笔记标签时发生错误: $e');
      Log.error('清空无笔记标签异常: $e');
      return results;
    } finally {
      _setLoading(false);
    }
  }

  /// 更新标签顺序 (仅前端排序，如果后端支持需要调用API)
  ///
  /// [oldIndex] 旧索引
  /// [newIndex] 新索引
  void reorderTags(int oldIndex, int newIndex) {
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    final item = _tags.removeAt(oldIndex);
    _tags.insert(newIndex, item);
    notifyListeners();
    // 注意：如果后端需要保存标签顺序，这里需要调用API更新
  }

  /// 更新标签列表中的特定标签
  ///
  /// [index] 列表中的索引位置
  /// [tag] 更新后的标签对象
  void updateTagInList(int index, Tag tag) {
    if (index >= 0 && index < _tags.length) {
      _tags[index] = tag;
      notifyListeners();
    }
  }
}
