declare module 'bcryptjs' {
  /**
   * 生成盐值
   * @param rounds 加密轮数
   * @param callback 回调函数
   */
  export function genSalt(rounds?: number, callback?: (err: Error | null, salt: string) => void): Promise<string>;

  /**
   * 使用盐值对密码进行哈希
   * @param data 要哈希的数据（密码）
   * @param salt 盐值或加密轮数
   * @param callback 回调函数
   */
  export function hash(data: string, salt: string | number, callback?: (err: Error | null, hash: string) => void): Promise<string>;

  /**
   * 比较密码与哈希值是否匹配
   * @param data 要比较的数据（密码）
   * @param encrypted 哈希值
   * @param callback 回调函数
   */
  export function compare(data: string, encrypted: string, callback?: (err: Error | null, same: boolean) => void): Promise<boolean>;

  /**
   * 同步生成盐值
   * @param rounds 加密轮数
   */
  export function genSaltSync(rounds?: number): string;
  
  /**
   * 同步哈希密码
   * @param data 要哈希的数据（密码）
   * @param salt 盐值或加密轮数
   */
  export function hashSync(data: string, salt: string | number): string;
  
  /**
   * 同步比较密码与哈希值
   * @param data 要比较的数据（密码）
   * @param encrypted 哈希值
   */
  export function compareSync(data: string, encrypted: string): boolean;
} 