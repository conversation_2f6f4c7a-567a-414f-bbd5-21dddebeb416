import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import mongoose from 'mongoose';
import bcrypt from 'bcryptjs'; // 导入 bcryptjs
import Note, { INoteDocument } from '../models/note.model';
import NoteHistory, { INoteHistoryDocument } from '../models/note-history.model';
import Tag from '../models/tag.model';
import { logger } from '../utils/logger';
import { config } from '../config';
import { v4 as uuidv4 } from 'uuid';

/**
 * 创建笔记
 * POST /api/notes
 */
export const createNote = async (req: Request, res: Response) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { title, content, contentType, tags } = req.body;

    // 创建新笔记
    const note = new Note({
      title,
      content,
      contentType: contentType || 'rich-text',
      tags: tags || [],
      owner: userId
    });

    // 保存笔记
    await note.save();

    // 如果有标签，需要填充标签信息
    let savedNote;
    if (tags && tags.length > 0) {
      savedNote = await Note.findById(note._id).populate('tags', 'name color');
    } else {
      savedNote = note;
    }

    return res.status(201).json({
      success: true,
      message: '笔记创建成功',
      data: {
        note: savedNote
      }
    });
  } catch (error: any) {
    logger.error(`创建笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '创建笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取用户的所有笔记
 * GET /api/notes
 */
export const getNotes = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 提取查询参数
    const {
      limit = 20,
      page = 1,
      sortBy = 'updatedAt',
      order = 'desc',
      tag,
      favorite,
      archived,
      search
    } = req.query;

    // 构建查询条件
    const query: any = { owner: userId };

    // 处理标签筛选
    if (tag) {
      query.tags = tag;
    }

    // 处理收藏筛选
    if (favorite === 'true') {
      query.isFavorite = true;
    }

    // 处理归档筛选
    if (archived === 'true') {
      query.isArchived = true;
    } else if (archived === 'false') {
      query.isArchived = false;
    }

    // 处理搜索
    if (search && typeof search === 'string') {
      query.$text = { $search: search };
    }

    // 计算分页参数
    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const skip = (pageNumber - 1) * limitNumber;

    // 构建排序对象
    const sort: any = {};
    sort[sortBy as string] = order === 'desc' ? -1 : 1;

    // 查询符合条件的笔记数量
    const total = await Note.countDocuments(query);

    // 查询笔记列表
    const notes = await Note.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limitNumber)
      .populate('tags', 'name color');

    return res.status(200).json({
      success: true,
      data: {
        notes,
        pagination: {
          total,
          page: pageNumber,
          limit: limitNumber,
          pages: Math.ceil(total / limitNumber)
        }
      }
    });
  } catch (error: any) {
    logger.error(`获取笔记列表失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取笔记列表失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取单个笔记
 * GET /api/notes/:id
 */
export const getNoteById = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID'
        }
      });
    }

    // 查找笔记
    const note = await Note.findOne({
      _id: id,
      owner: userId
    }).populate('tags', 'name color');

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在'
        }
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        note
      }
    });
  } catch (error: any) {
    logger.error(`获取笔记详情失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取笔记详情失败，请稍后再试'
      }
    });
  }
};

/**
 * 更新笔记
 * PUT /api/notes/:id
 */
export const updateNote = async (req: Request, res: Response) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;
    const { title, content, contentType, tags, isCompletingInitialSave = false } = req.body;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID'
        }
      });
    }

    // 查找并检查笔记所有权
    const note = await Note.findOne({
      _id: id,
      owner: userId
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在或无权限'
        }
      });
    }

    // --- 历史版本保存逻辑 ---
    if (!isCompletingInitialSave) {
      try {
        // 创建历史记录
        const history = new NoteHistory({
          noteId: note._id,
          version: note.version || 1, // 使用当前版本号，如果不存在则为1 (首次创建历史)
          title: note.title,
          content: note.content,
          contentType: note.contentType,
          tags: [...note.tags], // 使用展开运算符创建标签ID的副本
          owner: note.owner,
          archivedAt: new Date() // 记录归档时间
        });

        // 保存历史记录
        await history.save();
        logger.info(`为笔记 ${note._id} 创建了历史版本 ${history.version}`);
      } catch (historyError: any) {
        // 记录错误但不中断流程
        logger.error(`创建历史记录失败: ${historyError.message}`, {
          stack: historyError.stack,
          noteId: note._id
        });
        // 不返回错误，继续执行更新操作
      }

      // 更新笔记版本号 (只有在创建历史版本时才增加版本号)
      note.version = (note.version || 1) + 1;
    }
    // --- 结束: 历史版本保存逻辑 ---

    // 更新笔记字段
    if (title !== undefined) note.title = title;
    if (content !== undefined) note.content = content;
    if (contentType !== undefined) note.contentType = contentType;
    if (tags !== undefined) note.tags = tags;

    // 更新最后同步时间
    note.lastSyncedAt = new Date();

    // 保存更新后的笔记
    await note.save();

    // 获取更新后的笔记(包含标签信息)
    const updatedNote = await Note.findById(id).populate('tags', 'name color');

    return res.status(200).json({
      success: true,
      message: '笔记更新成功',
      data: {
        note: updatedNote
      }
    });
  } catch (error: any) {
    logger.error(`更新笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '更新笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 删除笔记
 * DELETE /api/notes/:id
 */
export const deleteNote = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID'
        }
      });
    }

    // 先查找笔记以获取内容
    const note = await Note.findOne({
      _id: id,
      owner: userId
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在或无权限'
        }
      });
    }

    // 导入文件操作库
    const fs = require('fs');
    const path = require('path');

    // 先尝试删除笔记目录中的所有图片文件
    try {
      // 笔记图片目录路径
      const noteImageDir = path.join(__dirname, '../../uploads/notes', id);

      // 检查目录是否存在
      if (fs.existsSync(noteImageDir)) {
        logger.info(`删除笔记 ${id} 的图片目录: ${noteImageDir}`);

        // 读取目录内容
        fs.readdir(noteImageDir, (err, files) => {
          if (err) {
            logger.error(`读取笔记图片目录失败: ${err.message}`);
            return;
          }

          // 删除目录中的所有文件
          for (const file of files) {
            try {
              fs.unlinkSync(path.join(noteImageDir, file));
              logger.info(`已删除笔记图片: ${file}`);
            } catch (e) {
              logger.error(`删除笔记图片文件失败: ${file}, 错误: ${e.message}`);
            }
          }

          // 删除目录本身
          try {
            fs.rmdirSync(noteImageDir);
            logger.info(`已删除笔记图片目录: ${noteImageDir}`);
          } catch (e) {
            logger.error(`删除笔记图片目录失败: ${e.message}`);
          }
        });
      } else {
        logger.info(`笔记 ${id} 没有专用图片目录，检查是否有使用旧方式存储的图片`);
      }
    } catch (e) {
      // 记录错误但继续执行笔记删除
      logger.error(`删除笔记图片目录时出错: ${e.message}`, { stack: e.stack });
    }

    // 同时，查找并删除笔记内容中引用的单独图片文件
    // (为了兼容性，处理旧的图片存储方式)
    try {
      // 提取笔记内容中的所有图片URL
      const imageUrls: string[] = [];

      // 尝试解析笔记内容
      if (note.content) {
        // 如果内容是JSON格式（富文本）
        if (note.contentType === 'rich-text' && (note.content.startsWith('[') || note.content.startsWith('{'))) {
          const content = JSON.parse(note.content);

          // 递归查找所有可能的图片URL字段
          const findImageUrls = (obj: any) => {
            if (!obj) return;

            if (Array.isArray(obj)) {
              obj.forEach(item => findImageUrls(item));
            } else if (typeof obj === 'object') {
              // 检查已知的图片URL字段模式
              if (obj.source_type === 'url' && obj.source && typeof obj.source === 'string') {
                imageUrls.push(obj.source);
              }
              // 检查嵌入图片
              if (obj.embed && obj.embed.type === 'image' && obj.embed.data) {
                if (obj.embed.data.source_type === 'url' && obj.embed.data.source) {
                  imageUrls.push(obj.embed.data.source);
                }
              }

              // 继续检查其他子对象
              Object.values(obj).forEach(value => {
                if (value && typeof value === 'object') {
                  findImageUrls(value);
                }
              });
            }
          };

          findImageUrls(content);
        }
      }

      // 过滤只保留本地上传的图片URL，但排除子目录中的图片
      const uploadedImages = imageUrls.filter(url => {
        return url.includes('/uploads/notes/') && !url.includes(`/uploads/notes/${id}/`);
      });

      logger.info(`从笔记 ${id} 中找到 ${uploadedImages.length} 张非子目录存储的图片`);

      // 删除每个图片文件
      const promises = uploadedImages.map(url => {
        try {
          // 从URL中提取文件路径
          const urlObj = new URL(url);
          const filePath = path.join(__dirname, '../..', urlObj.pathname);

          // 使用 fs.promises API 删除文件
          return fs.promises.unlink(filePath)
            .then(() => logger.info(`已删除笔记图片: ${filePath}`))
            .catch(err => logger.error(`删除笔记图片失败: ${filePath}, 错误: ${err.message}`));
        } catch (e) {
          logger.error(`处理图片URL失败: ${url}, 错误: ${e.message}`);
          return Promise.resolve(); // 忽略错误继续处理
        }
      });

      // 等待所有图片删除完成
      await Promise.allSettled(promises);
    } catch (e) {
      // 记录错误但继续执行笔记删除
      logger.error(`删除单独笔记图片时出错: ${e.message}`, { stack: e.stack });
    }

    // 删除笔记历史版本
    try {
      const { deletedCount } = await NoteHistory.deleteMany({ noteId: id });
      logger.info(`已删除笔记 ${id} 的 ${deletedCount} 条历史记录`);
    } catch (e) {
      // 记录错误但继续执行笔记删除
      logger.error(`删除笔记历史版本时出错: ${e.message}`, { stack: e.stack });
    }

    // 最后删除笔记本身
    await Note.findOneAndDelete({
      _id: id,
      owner: userId
    });

    return res.status(200).json({
      success: true,
      message: '笔记删除成功'
    });
  } catch (error: any) {
    logger.error(`删除笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '删除笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 切换笔记收藏状态
 * PATCH /api/notes/:id/favorite
 */
export const toggleFavorite = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // 检查ID格式是否正确
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID格式'
        }
      });
    }

    // 查询笔记
    const note = await Note.findOne({ _id: id, owner: userId, isDeleted: false });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在或无权访问'
        }
      });
    }

    // 切换收藏状态
    note.isFavorite = !note.isFavorite;
    await note.save();

    return res.status(200).json({
      success: true,
      data: note
    });
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    return res.status(500).json({
      success: false,
      error: {
        message: '服务器错误，切换收藏状态失败'
      }
    });
  }
};

/**
 * 切换笔记归档状态
 * PATCH /api/notes/:id/archive
 */
export const toggleArchive = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    // 检查ID格式是否正确
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID格式'
        }
      });
    }

    // 查询笔记
    const note = await Note.findOne({ _id: id, owner: userId, isDeleted: false });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在或无权访问'
        }
      });
    }

    // 切换归档状态
    note.isArchived = !note.isArchived;
    await note.save();

    return res.status(200).json({
      success: true,
      data: note
    });
  } catch (error) {
    console.error('切换归档状态失败:', error);
    return res.status(500).json({
      success: false,
      error: {
        message: '服务器错误，切换归档状态失败'
      }
    });
  }
};

/**
 * 获取笔记统计信息
 * GET /api/notes/stats
 */
export const getNoteStats = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 查询不同状态的笔记数量
    const totalNotes = await Note.countDocuments({ owner: userId });
    const favoriteNotes = await Note.countDocuments({ owner: userId, isFavorite: true });
    const archivedNotes = await Note.countDocuments({ owner: userId, isArchived: true });
    const activeNotes = await Note.countDocuments({ owner: userId, isArchived: false });

    // 获取按月统计的创建数量
    const currentDate = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(currentDate.getMonth() - 5); // 获取过去6个月的数据

    const monthlyStats = await Note.aggregate([
      {
        $match: {
          owner: new mongoose.Types.ObjectId(userId),
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    return res.status(200).json({
      success: true,
      data: {
        total: totalNotes,
        favorites: favoriteNotes,
        archived: archivedNotes,
        active: activeNotes,
        monthly: monthlyStats.map(item => ({
          year: item._id.year,
          month: item._id.month,
          count: item.count
        }))
      }
    });
  } catch (error: any) {
    logger.error(`获取笔记统计信息失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取笔记统计信息失败，请稍后再试'
      }
    });
  }
};

/**
 * 创建笔记分享链接
 * POST /api/notes/:id/share
 */
export const createShareLink = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;
    const { expireHours, isPublic, accessType, password } = req.body;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID'
        }
      });
    }

    // 查找笔记
    const note = await Note.findOne({
      _id: id,
      owner: userId
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在或无权限'
        }
      });
    }

    // 生成分享令牌
    const shareToken = uuidv4();

    // 设置过期时间
    let shareExpireAt: Date | undefined = undefined;
    if (expireHours && expireHours > 0) {
      shareExpireAt = new Date();
      shareExpireAt.setHours(shareExpireAt.getHours() + expireHours);
    }

    // 设置访问类型
    const shareAccessType = accessType === 'editable' ? 'editable' : 'readonly';

    // 更新笔记分享信息
    note.shareToken = shareToken;
    note.shareExpireAt = shareExpireAt;
    note.isPublicShared = !!isPublic;
    note.shareAccessType = shareAccessType;

    // 如果提供了密码，则设置密码
    if (password && password.trim()) {
      // 哈希密码
      const saltRounds = 10; // 或者从配置中读取
      note.sharePassword = await bcrypt.hash(password.trim(), saltRounds);
    } else {
      // 如果没有提供密码，则清除密码
      note.sharePassword = undefined;
    }

    await note.save();

    // 构建分享链接
    const shareLink = `${config.frontend.url}/share/${shareToken}`;

    return res.status(200).json({
      success: true,
      message: '分享链接创建成功',
      data: {
        shareToken,
        shareLink,
        expireAt: note.shareExpireAt,
        isPublic: note.isPublicShared,
        accessType: note.shareAccessType,
        hasPassword: !!note.sharePassword
      }
    });
  } catch (error: any) {
    logger.error(`创建分享链接失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '创建分享链接失败，请稍后再试'
      }
    });
  }
};

/**
 * 取消笔记分享
 * DELETE /api/notes/:id/share
 */
export const cancelShare = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { id } = req.params;

    // 检查ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID'
        }
      });
    }

    // 更新笔记，清除分享信息
    const note = await Note.findOneAndUpdate(
      { _id: id, owner: userId },
      {
        $unset: {
          shareToken: 1,
          shareExpireAt: 1
        },
        $set: {
          isPublicShared: false
        }
      },
      { new: true }
    );

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在或无权限'
        }
      });
    }

    return res.status(200).json({
      success: true,
      message: '已取消分享'
    });
  } catch (error: any) {
    logger.error(`取消分享失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '取消分享失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取分享的笔记
 * GET /api/notes/shared/:token
 */
export const getSharedNote = async (req: Request, res: Response) => {
  try {
    const { token } = req.params;
    const { password } = req.query;

    if (!token) {
      return res.status(400).json({
        success: false,
        error: {
          message: '分享令牌不能为空'
        }
      });
    }

    // 查找分享的笔记，包含密码字段
    const note = await Note.findOne({
      shareToken: token,
      $or: [
        { shareExpireAt: { $gt: new Date() } },
        { shareExpireAt: null }
      ]
    }).select('+sharePassword').populate('tags', 'name color');

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '分享的笔记不存在或已过期'
        }
      });
    }

    // 检查是否需要密码
    if (note.sharePassword) {
      // 如果笔记设置了密码但没有提供密码
      if (!password) {
        return res.status(403).json({
          success: false,
          error: {
            message: '访问此笔记需要密码',
            requirePassword: true
          }
        });
      }

      // 如果提供的密码不正确
      // 确保 password 是字符串类型
      const providedPassword = typeof password === 'string' ? password : '';
      const passwordsMatch = await bcrypt.compare(providedPassword, note.sharePassword);
      if (!passwordsMatch) {
        return res.status(403).json({
          success: false,
          error: {
            message: '密码错误',
            requirePassword: true
          }
        });
      }
    }

    // 返回笔记数据，但不包含敏感字段
    const sharedNote = {
      _id: note._id,
      title: note.title,
      content: note.content,
      contentType: note.contentType,
      tags: note.tags,
      createdAt: note.createdAt,
      updatedAt: note.updatedAt,
      isPublicShared: note.isPublicShared,
      shareAccessType: note.shareAccessType || 'readonly', // 默认为只读
      hasPassword: !!note.sharePassword
    };

    return res.status(200).json({
      success: true,
      data: {
        note: sharedNote
      }
    });
  } catch (error: any) {
    logger.error(`获取分享笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取分享笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 更新分享的笔记
 * PUT /api/notes/shared/:token
 */
export const updateSharedNote = async (req: Request, res: Response) => {
  try {
    const { token } = req.params;
    const { title, content } = req.body;
    const { password } = req.query;

    if (!token) {
      return res.status(400).json({
        success: false,
        error: {
          message: '分享令牌不能为空'
        }
      });
    }

    // 查找分享的笔记，包含密码字段
    const note = await Note.findOne({
      shareToken: token,
      $or: [
        { shareExpireAt: { $gt: new Date() } },
        { shareExpireAt: null }
      ]
    }).select('+sharePassword');

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '分享的笔记不存在或已过期'
        }
      });
    }

    // 检查是否需要密码
    if (note.sharePassword) {
      // 如果笔记设置了密码但没有提供密码
      if (!password) {
        return res.status(403).json({
          success: false,
          error: {
            message: '访问此笔记需要密码',
            requirePassword: true
          }
        });
      }

      // 如果提供的密码不正确
      // 确保 password 是字符串类型
      const providedPassword = typeof password === 'string' ? password : '';
      const passwordsMatch = await bcrypt.compare(providedPassword, note.sharePassword);
      if (!passwordsMatch) {
        return res.status(403).json({
          success: false,
          error: {
            message: '密码错误',
            requirePassword: true
          }
        });
      }
    }

    // 检查笔记是否可编辑
    if (note.shareAccessType !== 'editable') {
      return res.status(403).json({
        success: false,
        error: {
          message: '此笔记为只读模式，不能编辑'
        }
      });
    }

    // --- 在更新前保存历史版本 ---
    try {
      // 创建历史记录
      const history = new NoteHistory({
        noteId: note._id,
        version: note.version || 1,  // 使用当前版本号
        title: note.title,
        content: note.content,
        contentType: note.contentType,
        tags: [...note.tags], // 使用展开运算符创建标签ID的副本
        owner: note.owner,
        archivedAt: new Date()
      });

      // 保存历史记录
      await history.save();

      // 更新笔记版本号
      note.version = (note.version || 1) + 1;

      logger.info(`为分享笔记 ${note._id} 创建了历史版本 ${history.version}`);
    } catch (historyError: any) {
      // 记录错误但不中断流程
      logger.error(`创建历史记录失败: ${historyError.message}`, {
        stack: historyError.stack,
        noteId: note._id
      });
      // 不返回错误，继续执行更新操作
    }

    // 更新笔记字段
    if (title !== undefined) note.title = title;
    if (content !== undefined) note.content = content;

    // 更新最后修改时间
    note.updatedAt = new Date();

    // 保存更新后的笔记
    await note.save();

    // 获取更新后的笔记(包含标签信息)
    const updatedNote = await Note.findById(note._id).populate('tags', 'name color');

    if (!updatedNote) {
      return res.status(404).json({
        success: false,
        error: {
          message: '更新后的笔记不存在'
        }
      });
    }

    // 返回笔记数据，但不包含敏感字段
    const sharedNote = {
      _id: updatedNote._id,
      title: updatedNote.title,
      content: updatedNote.content,
      contentType: updatedNote.contentType,
      tags: updatedNote.tags,
      createdAt: updatedNote.createdAt,
      updatedAt: updatedNote.updatedAt,
      isPublicShared: updatedNote.isPublicShared,
      shareAccessType: updatedNote.shareAccessType || 'readonly',
      hasPassword: !!updatedNote.sharePassword
    };

    return res.status(200).json({
      success: true,
      message: '笔记更新成功',
      data: {
        note: sharedNote
      }
    });
  } catch (error: any) {
    logger.error(`更新分享笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '更新分享笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 批量操作笔记(删除/归档/收藏)
 * POST /api/notes/batch
 */
export const batchOperation = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { noteIds, operation } = req.body;

    if (!Array.isArray(noteIds) || noteIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: '请提供笔记ID数组'
        }
      });
    }

    // 确保所有ID都有效
    if (noteIds.some(id => !mongoose.Types.ObjectId.isValid(id))) {
      return res.status(400).json({
        success: false,
        error: {
          message: '包含无效的笔记ID'
        }
      });
    }

    let updateData: any = {};
    let successMessage = '';

    // 根据操作类型设置更新数据
    switch (operation) {
      case 'delete':
        // 在批量删除笔记前，先处理每个笔记的图片文件夹和历史版本
        for (const noteId of noteIds) {
          try {
            // 导入文件操作库
            const fs = require('fs');
            const path = require('path');

            // 1. 删除笔记图片目录
            try {
              // 笔记图片目录路径
              const noteImageDir = path.join(__dirname, '../../uploads/notes', noteId);

              // 检查目录是否存在
              if (fs.existsSync(noteImageDir)) {
                logger.info(`删除笔记 ${noteId} 的图片目录: ${noteImageDir}`);

                // 读取目录内容
                const files = fs.readdirSync(noteImageDir);

                // 删除目录中的所有文件
                for (const file of files) {
                  try {
                    fs.unlinkSync(path.join(noteImageDir, file));
                    logger.info(`已删除笔记图片: ${file}`);
                  } catch (e) {
                    logger.error(`删除笔记图片文件失败: ${file}, 错误: ${e.message}`);
                  }
                }

                // 删除目录本身
                try {
                  fs.rmdirSync(noteImageDir);
                  logger.info(`已删除笔记图片目录: ${noteImageDir}`);
                } catch (e) {
                  logger.error(`删除笔记图片目录失败: ${e.message}`);
                }
              }
            } catch (e) {
              // 记录错误但继续执行
              logger.error(`删除笔记 ${noteId} 图片目录时出错: ${e.message}`, { stack: e.stack });
            }

            // 2. 删除笔记历史版本
            try {
              const { deletedCount } = await NoteHistory.deleteMany({ noteId });
              logger.info(`已删除笔记 ${noteId} 的 ${deletedCount} 条历史记录`);
            } catch (e) {
              // 记录错误但继续执行
              logger.error(`删除笔记 ${noteId} 历史版本时出错: ${e.message}`, { stack: e.stack });
            }
          } catch (e) {
            logger.error(`处理笔记 ${noteId} 相关资源时出错: ${e.message}`, { stack: e.stack });
            // 继续处理下一个笔记
          }
        }

        // 执行批量删除笔记
        const deleteResult = await Note.deleteMany({
          _id: { $in: noteIds },
          owner: userId
        });

        return res.status(200).json({
          success: true,
          message: `成功删除${deleteResult.deletedCount}个笔记`,
          data: {
            deletedCount: deleteResult.deletedCount
          }
        });

      case 'archive':
        updateData = { isArchived: true };
        successMessage = '笔记已归档';
        break;

      case 'unarchive':
        updateData = { isArchived: false };
        successMessage = '笔记已取消归档';
        break;

      case 'favorite':
        updateData = { isFavorite: true };
        successMessage = '笔记已收藏';
        break;

      case 'unfavorite':
        updateData = { isFavorite: false };
        successMessage = '笔记已取消收藏';
        break;

      default:
        return res.status(400).json({
          success: false,
          error: {
            message: '不支持的操作类型'
          }
        });
    }

    // 执行批量更新
    const result = await Note.updateMany(
      { _id: { $in: noteIds }, owner: userId },
      { $set: { ...updateData, lastSyncedAt: new Date() } }
    );

    return res.status(200).json({
      success: true,
      message: successMessage,
      data: {
        modifiedCount: result.modifiedCount
      }
    });
  } catch (error: any) {
    logger.error(`批量操作笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '批量操作笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取上次同步后的变更
 * GET /api/notes/sync
 */
export const getChanges = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { lastSyncTime } = req.query;

    const lastSync = lastSyncTime ? new Date(lastSyncTime as string) : new Date(0);

    // 获取所有更新的笔记和已删除但标记为删除的笔记
    const notes = await Note.find({
      owner: userId,
      $or: [
        { updatedAt: { $gt: lastSync } },
        { isDeleted: true, lastSyncedAt: { $gt: lastSync } }
      ]
    }).populate('tags', 'name color');

    return res.status(200).json({
      success: true,
      data: {
        notes,
        syncTime: new Date()
      }
    });
  } catch (error: any) {
    logger.error(`获取同步变更失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取同步变更失败，请稍后再试'
      }
    });
  }
};

/**
 * 上传本地变更
 * POST /api/notes/sync
 */
export const pushChanges = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { notes, deletedNoteIds } = req.body;

    // 修复冲突类型错误
    const results: {
      success: any[];
      conflicts: Array<{
        clientNote: any;
        serverNote: mongoose.Document<unknown, {}, INoteDocument> & INoteDocument & { _id: mongoose.Types.ObjectId; };
      }>;
      errors: any[];
      deleted: number;
    } = {
      success: [],
      conflicts: [],
      errors: [],
      deleted: 0
    };

    // 处理笔记更新和创建
    if (Array.isArray(notes) && notes.length > 0) {
      for (const note of notes) {
        if (note._id) {
          // 更新已有笔记
          const existingNote = await Note.findOne({
            _id: note._id,
            owner: userId
          });

          if (!existingNote) {
            // 创建新笔记，保留客户端提供的ID
            const newNote = new Note({
              ...note,
              owner: userId,
              lastSyncedAt: new Date(),
              version: 1
            });
            await newNote.save();
            results.success.push(newNote._id);
            continue;
          }

          // 修复版本检查
          if (existingNote && existingNote.version !== undefined &&
              existingNote.version > (note.version || 0)) {
            results.conflicts.push({
              clientNote: note,
              serverNote: existingNote
            });
            continue;
          }

          // 没有冲突，更新笔记
          existingNote.title = note.title;
          existingNote.content = note.content;
          existingNote.contentType = note.contentType;
          existingNote.tags = note.tags || [];
          existingNote.isFavorite = note.isFavorite || false;
          existingNote.isArchived = note.isArchived || false;
          existingNote.lastSyncedAt = new Date();
          existingNote.version = (existingNote.version || 0) + 1;

          await existingNote.save();
          results.success.push(existingNote._id);
        } else {
          // 创建新笔记
          const newNote = new Note({
            ...note,
            owner: userId,
            lastSyncedAt: new Date(),
            version: 1
          });
          await newNote.save();
          results.success.push(newNote._id);
        }
      }
    }

    // 处理笔记删除
    if (Array.isArray(deletedNoteIds) && deletedNoteIds.length > 0) {
      // 在删除笔记前，先处理每个笔记的图片文件夹和历史版本
      for (const noteId of deletedNoteIds) {
        try {
          // 导入文件操作库
          const fs = require('fs');
          const path = require('path');

          // 1. 删除笔记图片目录
          try {
            // 笔记图片目录路径
            const noteImageDir = path.join(__dirname, '../../uploads/notes', noteId);

            // 检查目录是否存在
            if (fs.existsSync(noteImageDir)) {
              logger.info(`同步删除笔记 ${noteId} 的图片目录: ${noteImageDir}`);

              // 读取目录内容
              const files = fs.readdirSync(noteImageDir);

              // 删除目录中的所有文件
              for (const file of files) {
                try {
                  fs.unlinkSync(path.join(noteImageDir, file));
                  logger.info(`已删除笔记图片: ${file}`);
                } catch (e) {
                  logger.error(`删除笔记图片文件失败: ${file}, 错误: ${e.message}`);
                }
              }

              // 删除目录本身
              try {
                fs.rmdirSync(noteImageDir);
                logger.info(`已删除笔记图片目录: ${noteImageDir}`);
              } catch (e) {
                logger.error(`删除笔记图片目录失败: ${e.message}`);
              }
            }
          } catch (e) {
            // 记录错误但继续执行
            logger.error(`同步删除笔记 ${noteId} 图片目录时出错: ${e.message}`, { stack: e.stack });
          }

          // 2. 删除笔记历史版本
          try {
            const { deletedCount } = await NoteHistory.deleteMany({ noteId });
            logger.info(`已删除笔记 ${noteId} 的 ${deletedCount} 条历史记录`);
          } catch (e) {
            // 记录错误但继续执行
            logger.error(`同步删除笔记 ${noteId} 历史版本时出错: ${e.message}`, { stack: e.stack });
          }
        } catch (e) {
          logger.error(`同步处理笔记 ${noteId} 相关资源时出错: ${e.message}`, { stack: e.stack });
          // 继续处理下一个笔记
        }
      }

      // 物理删除笔记
      const deleteResult = await Note.deleteMany({
        _id: { $in: deletedNoteIds },
        owner: userId
      });
      results.deleted = deleteResult.deletedCount;
    }

    return res.status(200).json({
      success: true,
      message: `同步成功：创建${results.success.length}个，更新${results.conflicts.length}个，删除${results.deleted}个，冲突${results.conflicts.length}个`,
      data: {
        results,
        syncTime: new Date(),
        conflicts: results.conflicts
      }
    });
  } catch (error: any) {
    logger.error(`推送同步变更失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '同步失败，请稍后再试'
      }
    });
  }
};

/**
 * 笔记导入
 * POST /api/notes/import
 */
export const importNotes = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { notes } = req.body;

    if (!Array.isArray(notes) || notes.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          message: '请提供有效的笔记数据'
        }
      });
    }

    const results = {
      imported: 0,
      failed: 0,
      importedIds: [] as string[]
    };

    // 导入笔记
    for (const noteData of notes) {
      try {
        // 创建新笔记
        const newNote = new Note({
          title: noteData.title || '未命名笔记',
          content: noteData.content || '',
          contentType: noteData.contentType || 'rich-text',
          tags: noteData.tags || [],
          owner: userId,
          isFavorite: noteData.isFavorite || false,
          isArchived: noteData.isArchived || false,
          lastSyncedAt: new Date(),
          version: 1
        });

        const savedNote = await newNote.save();
        results.imported++;
        results.importedIds.push(savedNote._id.toString());
      } catch (error) {
        results.failed++;
      }
    }

    return res.status(200).json({
      success: true,
      message: `成功导入${results.imported}个笔记，失败${results.failed}个`,
      data: {
        results
      }
    });
  } catch (error: any) {
    logger.error(`导入笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '导入笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 笔记导出
 * GET /api/notes/export
 */
export const exportNotes = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 过滤条件
    const { filter } = req.query;
    const query: any = { owner: userId };

    // 根据过滤条件调整查询
    if (filter === 'favorite') {
      query.isFavorite = true;
    } else if (filter === 'archived') {
      query.isArchived = true;
    } else if (filter === 'active') {
      query.isArchived = false;
    }

    // 获取用户笔记
    const notes = await Note.find(query)
      .populate('tags', 'name color')
      .select('-shareToken -shareExpireAt'); // 排除分享相关字段

    // 格式化导出数据
    const exportData = {
      notes,
      exportTime: new Date(),
      totalNotes: notes.length
    };

    return res.status(200).json({
      success: true,
      data: exportData
    });
  } catch (error: any) {
    logger.error(`导出笔记失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '导出笔记失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取笔记历史版本列表
 * GET /api/notes/:id/history
 */
export const getNoteHistory = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 检查笔记ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID'
        }
      });
    }

    // 首先确认用户有权访问此笔记
    const note = await Note.findOne({
      _id: id,
      owner: userId
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在或无权限'
        }
      });
    }

    // 获取笔记的历史版本记录，按版本号降序排列
    const history = await NoteHistory.find({ noteId: id })
      .sort({ version: -1 })
      .populate('tags', 'name color') // 添加标签信息的填充
      .lean(); // 转换为普通JS对象以提高性能

    return res.status(200).json({
      success: true,
      data: {
        history
      }
    });
  } catch (error: any) {
    logger.error(`获取笔记历史失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取笔记历史失败，请稍后再试'
      }
    });
  }
};

/**
 * 恢复笔记到特定历史版本
 * POST /api/notes/:id/restore/:version
 */
export const restoreNoteVersion = async (req: Request, res: Response) => {
  try {
    const { id, version } = req.params;
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 检查笔记ID是否有效
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的笔记ID'
        }
      });
    }

    // 检查版本号是否为数字
    const versionNumber = parseInt(version, 10);
    if (isNaN(versionNumber)) {
      return res.status(400).json({
        success: false,
        error: {
          message: '无效的版本号'
        }
      });
    }

    // 首先确认用户有权访问此笔记
    const note = await Note.findOne({
      _id: id,
      owner: userId
    });

    if (!note) {
      return res.status(404).json({
        success: false,
        error: {
          message: '笔记不存在或无权限'
        }
      });
    }

    // 查找指定版本的历史记录
    const historyVersion = await NoteHistory.findOne({
      noteId: id,
      version: versionNumber
    }).populate('tags', 'name color'); // 填充标签信息

    if (!historyVersion) {
      return res.status(404).json({
        success: false,
        error: {
          message: '未找到指定版本的历史记录'
        }
      });
    }

    // --- 在恢复前，先为当前版本创建一个新的历史记录 ---
    try {
      const newHistory = new NoteHistory({
        noteId: note._id,
        version: note.version || 1, // 当前版本
        title: note.title,
        content: note.content,
        contentType: note.contentType,
        tags: [...note.tags], // 复制标签数组
        owner: note.owner,
        archivedAt: new Date()
      });

      await newHistory.save();
      logger.info(`在恢复操作前，为笔记 ${note._id} 创建了当前版本的历史记录 ${newHistory.version}`);
    } catch (historyError: any) {
      // 记录错误但不中断流程
      logger.error(`恢复前创建历史记录失败: ${historyError.message}`, {
        stack: historyError.stack,
        noteId: note._id
      });
      // 不返回错误，继续执行恢复操作
    }

    // 使用历史版本数据更新当前笔记
    note.title = historyVersion.title;
    note.content = historyVersion.content;
    note.contentType = historyVersion.contentType;
    note.tags = [...historyVersion.tags]; // 复制历史版本的标签
    note.version = (note.version || 1) + 1; // 增加版本号
    note.updatedAt = new Date(); // 更新修改时间
    note.lastSyncedAt = new Date(); // 更新最后同步时间

    // 保存更新后的笔记
    await note.save();

    // 获取完整的笔记数据（包含填充的标签信息）
    const updatedNote = await Note.findById(id).populate('tags', 'name color');

    return res.status(200).json({
      success: true,
      message: `笔记已恢复到版本 ${versionNumber}`,
      data: {
        note: updatedNote
      }
    });
  } catch (error: any) {
    logger.error(`恢复笔记版本失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '恢复笔记版本失败，请稍后再试'
      }
    });
  }
};

/**
 * 上传笔记图片
 * POST /api/notes/upload-image
 */
export const uploadImage = async (req: Request, res: Response) => {
  try {
    // 1. 认证判断
    // @ts-ignore - Express类型扩展
    const userId = req.user?.id;
    const shareToken = req.query.shareToken as string | undefined;
    const noteId = req.query.noteId as string || 'temp';

    let allow = false;
    let ownerId: string | undefined;

    if (userId) {
      // 登录用户，允许上传
      allow = true;
      ownerId = userId;
    } else if (shareToken) {
      // 分享令牌校验
      // 查找分享的笔记
      const note = await Note.findOne({
        shareToken: shareToken,
        $or: [
          { shareExpireAt: { $gt: new Date() } },
          { shareExpireAt: null }
        ]
      });

      if (
        note &&
        note.shareAccessType === 'editable' &&
        note._id.toString() === noteId
      ) {
        allow = true;
        ownerId = note.owner?.toString();
      }
    }

    if (!allow) {
      return res.status(401).json({
        success: false,
        error: { message: '未授权操作' }
      });
    }

    // 检查是否有文件上传
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: { message: '未提供图片文件' }
      });
    }

    // 文件存储逻辑保持不变
    const fs = require('fs');
    const path = require('path');
    const originalFilePath = path.join(__dirname, '../../uploads/notes', req.file.filename);
    const noteImagesDir = path.join(__dirname, '../../uploads/notes', noteId);
    if (!fs.existsSync(noteImagesDir)) {
      fs.mkdirSync(noteImagesDir, { recursive: true });
    }
    const newFilePath = path.join(noteImagesDir, req.file.filename);

    try {
      fs.renameSync(originalFilePath, newFilePath);
      logger.info(`已将上传图片移动到笔记 ${noteId} 的子目录`);
    } catch (moveError) {
      logger.error(`移动图片文件失败: ${moveError}`);
    }

    const imagePath = `/uploads/notes/${noteId}/${req.file.filename}`;
    const baseUrl = `${req.protocol}://${req.get('host')}`;
    const imageUrl = `${baseUrl}${imagePath}`;

    logger.info(`用户/分享上传了笔记图片: ${imagePath}`);

    return res.status(200).json({
      success: true,
      message: '图片上传成功',
      data: {
        url: imageUrl,
        path: imagePath,
        filename: req.file.filename,
        noteId: noteId,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });
  } catch (error: any) {
    logger.error(`上传笔记图片失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: { message: '上传图片失败，请稍后再试' }
    });
  }
};