# 重构计划

**目标：** 将大型 `editor_page.dart` 文件拆分为多个更小、更易于维护的组件，并确保每个阶段的功能正确性。

**核心原则：** 分阶段进行，每个阶段完成后进行充分测试，确保功能无损，再进入下一阶段。

---

**阶段 0: 准备工作**

*   **目标：** 备份现有代码，创建新的工作分支，并确保开发环境正常。
*   **涉及文件：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (将此文件复制一份作为备份，例如 `editor_page_backup.dart`)
*   **测试方法：**
    *   确认代码仓库已备份。
    *   确认已创建新的Git分支。
    *   **确认 `editor_page.dart` 已手动备份到 `editor_page_backup.dart`。**
    *   运行现有应用，确保所有功能在重构前正常工作。

---

**阶段 1: 提取工具函数 (`editor_utils.dart`)**

*   **目标：** 将 `editor_page.dart` 中所有通用的辅助函数、格式转换函数、递归查找函数等提取到 `editor_utils.dart` 中。
*   **涉及文件：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (移除已提取的函数并导入新文件)
    *   [`ai_cloud_notes/lib/utils/editor_utils.dart`](ai_cloud_notes/lib/utils/editor_utils.dart) (新建文件，存放提取的工具函数)
*   **测试方法：**
    *   **单元测试：** 为 `editor_utils.dart` 中的每个函数编写单元测试，确保其独立功能正确。
    *   **集成测试：** 运行 `editor_page.dart`，验证所有依赖这些工具函数的功能（如日期时间格式化、AI预测栏位置保存/加载等）是否正常工作。特别关注那些在 `_EditorPageState` 中直接调用的辅助方法。

---

**阶段 2: 提取富文本编辑器 (`rich_text_editor.dart`)**

*   **目标：** 将 `editor_page.dart` 中与 Fleather 富文本编辑器相关的所有UI、控制器管理、嵌入内容处理（表格、图片等）和文档内容处理方法封装到 `rich_text_editor.dart` 组件中。
*   **涉及文件：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (替换为 `rich_text_editor.dart` 组件实例)
    *   [`ai_cloud_notes/lib/screens/editor/rich_text_editor.dart`](ai_cloud_notes/lib/screens/editor/rich_text_editor.dart) (新建文件，包含富文本编辑器逻辑和UI)
*   **测试方法：**
    *   **单元测试：** 如果 `rich_text_editor.dart` 内部有独立逻辑，为其编写单元测试。
    *   **组件测试：** 独立渲染 `rich_text_editor.dart`，测试其富文本编辑、插入图片/表格等核心功能。
    *   **集成测试：** 运行 `editor_page.dart`，验证富文本编辑器的所有功能（输入、格式化、图片/表格插入、AI预测栏交互等）是否正常。

---

**阶段 3: 提取头部和工具栏组件 (`editor_header.dart`, `editor_toolbar.dart`)**

*   **目标：** 将 `editor_page.dart` 中的页面头部（返回按钮、标题编辑、模式切换、历史版本、分享、保存按钮）和编辑器工具栏（富文本工具栏、Markdown工具栏、工具栏按钮处理函数、响应式布局适配）分别提取到 `editor_header.dart` 和 `editor_toolbar.dart` 中。
*   **涉及文件：：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (替换为 `editor_header.dart` 和 `editor_toolbar.dart` 组件实例)
    *   [`ai_cloud_notes/lib/screens/editor/editor_header.dart`](ai_cloud_notes/lib/screens/editor/editor_header.dart) (新建文件，包含头部UI和逻辑)
    *   [`ai_cloud_notes/lib/screens/editor/editor_toolbar.dart`](ai_cloud_notes/lib/screens/editor/editor_toolbar.dart) (新建文件，包含工具栏UI和逻辑)
*   **测试方法：**
    *   **组件测试：** 独立渲染 `editor_header.dart` 和 `editor_toolbar.dart`，测试所有按钮的点击事件和UI状态变化。
    *   **集成测试：** 运行 `editor_page.dart`，验证头部和工具栏的所有功能是否正常，包括标题编辑、模式切换、保存、分享、以及富文本/Markdown工具栏的各项操作。

---

**阶段 4: 提取标签管理组件 (`editor_tag_bar.dart`)**

*   **目标：** 将 `editor_page.dart` 中与标签管理相关的功能（标签显示、添加/删除、标签选择对话框）提取到 `editor_tag_bar.dart` 组件中。
*   **涉及文件：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (替换为 `editor_tag_bar.dart` 组件实例)
    *   [`ai_cloud_notes/lib/screens/editor/editor_tag_bar.dart`](ai_cloud_notes/lib/screens/editor/editor_tag_bar.dart) (新建文件，包含标签管理UI和逻辑)
*   **测试方法：**
    *   **组件测试：** 独立渲染 `editor_tag_bar.dart`，测试标签的添加、删除、显示以及标签选择对话框的交互。
    *   **集成测试：** 运行 `editor_page.dart`，验证笔记的标签管理功能是否正常，包括新笔记添加标签、现有笔记修改标签、标签的持久化等。

---

**阶段 5: 提取阅读模式视图 (`reading_mode_view.dart`)**

*   **目标：** 将 `editor_page.dart` 中与阅读模式相关的UI和内容渲染逻辑提取到 `reading_mode_view.dart` 组件中。该组件将负责阅读模式的整体布局、头部和底部，并**复用 `ai_cloud_notes/lib/widgets/note_content_preview.dart` 来渲染笔记内容**。
*   **涉及文件：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (替换为 `reading_mode_view.dart` 组件实例)
    *   [`ai_cloud_notes/lib/screens/editor/reading_mode_view.dart`](ai_cloud_notes/lib/screens/editor/reading_mode_view.dart) (新建文件，包含阅读模式UI和渲染逻辑)
    *   [`ai_cloud_notes/lib/widgets/note_content_preview.dart`](ai_cloud_notes/lib/widgets/note_content_preview.dart) (被 `reading_mode_view.dart` 引用)
*   **测试方法：**
    *   **组件测试：** 独立渲染 `reading_mode_view.dart`，测试不同内容类型（富文本、Markdown）的渲染效果，以及阅读模式下的UI布局。特别验证 `NoteContentPreview` 在其中的正确集成。
    *   **集成测试：** 运行 `editor_page.dart`，在编辑模式和阅读模式之间切换，验证内容显示是否正确，且阅读模式下的交互（如链接点击）是否正常。

---

**阶段 6: 提取表格和图片处理逻辑 (`table_editor.dart`, `image_handler.dart`)**

*   **目标：** 将 `editor_page.dart` 中与表格编辑（表格编辑对话框、表格数据处理、表格渲染）和图片处理（图片插入、图片上传、图片缓存管理）相关的逻辑分别提取到 `table_editor.dart` 和 `image_handler.dart` 中。
*   **涉及文件：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (移除相关逻辑，通过 `rich_text_editor.dart` 或 `editor_controller.dart` 调用)
    *   [`ai_cloud_notes/lib/screens/editor/table_editor.dart`](ai_cloud_notes/lib/screens/editor/table_editor.dart) (新建文件，包含表格编辑UI和逻辑)
    *   [`ai_cloud_notes/lib/services/image_handler.dart`](ai_cloud_notes/lib/services/image_handler.dart) (新建文件，包含图片处理逻辑)
*   **测试方法：**
    *   **组件测试：** 独立测试 `table_editor.dart` 的表格编辑功能，以及 `image_handler.dart` 的图片选择、上传和显示功能。
    *   **集成测试：** 运行 `editor_page.dart`，在富文本编辑器中插入表格和图片，验证其编辑、显示和保存功能是否正常。

---

**阶段 7: 提取编辑器控制器 (`editor_controller.dart`)**

*   **目标：** 将 `editor_page.dart` 中主要的业务逻辑（笔记保存、历史版本管理、AI功能集成、内容类型处理）提取到 `editor_controller.dart` 类中。这个类将作为 `editor_page.dart` 的状态管理和业务逻辑层。**该控制器将协调现有AI相关组件 (`ai_menu.dart`, `ai_prediction_bar.dart`, `note_qa_dialog.dart`) 和历史版本页面 (`history_preview_page.dart`) 的逻辑。**
*   **涉及文件：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (将大部分业务逻辑委托给 `editor_controller.dart` 实例)
    *   [`ai_cloud_notes/lib/controllers/editor_controller.dart`](ai_cloud_notes/lib/controllers/editor_controller.dart) (新建文件，包含核心业务逻辑)
    *   [`ai_cloud_notes/lib/screens/editor/ai_menu.dart`](ai_cloud_notes/lib/screens/editor/ai_menu.dart) (被 `editor_controller.dart` 或 `editor_page.dart` 协调)
    *   [`ai_cloud_notes/lib/screens/editor/ai_prediction_bar.dart`](ai_cloud_notes/lib/screens/editor/ai_prediction_bar.dart) (被 `editor_controller.dart` 或 `editor_page.dart` 协调)
    *   [`ai_cloud_notes/lib/screens/editor/history_preview_page.dart`](ai_cloud_notes/lib/screens/editor/history_preview_page.dart) (被 `editor_controller.dart` 协调)
    *   [`ai_cloud_notes/lib/screens/editor/note_qa_dialog.dart`](ai_cloud_notes/lib/screens/editor/note_qa_dialog.dart) (被 `editor_controller.dart` 协调)
*   **测试方法：**
    *   **单元测试：** 为 `editor_controller.dart` 中的每个业务逻辑方法编写单元测试，确保其独立功能正确（例如，笔记保存逻辑、AI调用逻辑）。
    *   **集成测试：** 运行 `editor_page.dart`，验证笔记的保存、历史版本、AI功能（预测、菜单、问答对话框）等核心业务流程是否正常。

---

**阶段 8: 创建新的主文件并整合 (`editor_page_new.dart` -> `editor_page.dart`)**

*   **目标：** 创建一个新的 `editor_page_new.dart` 文件，作为重构后的主文件。该文件将主要负责组件的组织和编排、状态管理（通过 `editor_controller`）、生命周期方法和页面级事件处理。然后，重命名原文件，并将新文件重命名为 `editor_page.dart`。
*   **涉及文件：**
    *   [`ai_cloud_notes/lib/screens/editor/editor_page.dart`](ai_cloud_notes/lib/screens/editor/editor_page.dart) (重命名为 `editor_page_old.dart`)
    *   [`ai_cloud_notes/lib/screens/editor/editor_page_new.dart`](ai_cloud_notes/lib/screens/editor/editor_page_new.dart) (新建文件，作为重构后的主文件)
    *   所有新创建的组件和控制器文件。
*   **测试方法：**
    *   **端到端测试：** 运行整个应用，执行所有关键用户流程（创建新笔记、编辑现有笔记、切换模式、使用AI功能、标签管理、保存、分享等），确保所有功能端到端正常。
    *   **性能测试：** 检查重构后页面的加载速度和响应性是否受到影响。
    *   **回归测试：** 运行所有已有的自动化测试（如果有的话），确保没有引入新的bug。

---

**后续任务：统一编辑器组件目录**

*   **目标：** 将所有编辑器相关的组件（包括新创建的 `rich_text_editor.dart`, `editor_header.dart`, `editor_toolbar.dart`, `editor_tag_bar.dart`, `reading_mode_view.dart`, `table_editor.dart` 以及现有的 `ai_menu.dart`, `ai_prediction_bar.dart`, `markdown_editor.dart`, `history_preview_page.dart`, `note_qa_dialog.dart` 等）统一移动到 `ai_cloud_notes/lib/screens/editor/components` 或类似的子目录下，以提高管理便利性。
*   **涉及文件：** 移动上述组件文件，并更新所有引用这些文件的导入路径。
*   **测试方法：**
    *   **编译检查：** 确保所有文件路径更新正确，没有编译错误。
    *   **冒烟测试：** 运行应用，快速检查主要功能是否正常，确保文件移动没有破坏引用。