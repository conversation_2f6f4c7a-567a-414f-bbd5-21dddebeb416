import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/screens/auth/login_page.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import '../../mocks/mock_api_service.dart';
import '../../test_config/test_helpers.dart';
import '../../test_config/test_app.dart';

void main() {
  group('LoginPage 组件测试', () {
    late MockApiService mockApiService;
    late AuthProvider authProvider;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider(mockApiService);
    });

    tearDown(() {
      mockApiService.reset();
    });

    Widget createLoginPage() {
      return TestApp(
        mockApiService: mockApiService,
        mockAuthProvider: authProvider,
        home: const LoginPage(),
      );
    }

    group('UI渲染测试', () {
      testWidgets('应该正确渲染登录页面的所有元素', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 验证AppBar标题
        TestHelpers.expectAppBarTitle('账号登录');

        // 验证表单字段
        expect(find.byType(TextFormField), findsNWidgets(2)); // 用户名和密码字段

        // 验证按钮
        TestHelpers.expectTextExists('登 录');
        TestHelpers.expectTextExists('注册账号');
        TestHelpers.expectTextExists('忘记密码？');

        // 验证其他UI元素
        TestHelpers.expectTextExists('用户名/邮箱');
        TestHelpers.expectTextExists('密码');
      });

      testWidgets('应该显示正确的输入提示', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 查找输入字段的提示文本
        expect(find.text('请输入用户名或邮箱'), findsOneWidget);
        expect(find.text('请输入密码'), findsOneWidget);
      });

      testWidgets('密码字段应该是隐藏的', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 查找密码字段
        final passwordField = find.byKey(const Key('password_field'));
        expect(passwordField, findsOneWidget);

        // 验证密码字段是隐藏的
        final textFormField = tester.widget<TextFormField>(passwordField);
        expect(textFormField.obscureText, isTrue);
      });
    });

    group('表单验证测试', () {
      testWidgets('空用户名应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 点击登录按钮而不输入任何内容
        await TestHelpers.tapButton(tester, '登 录');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请输入用户名或邮箱');
      });

      testWidgets('空密码应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 只输入用户名
        await TestHelpers.enterText(tester, 'username_field', 'testuser');

        // 点击登录按钮
        await TestHelpers.tapButton(tester, '登 录');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请输入密码');
      });

      testWidgets('短密码应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 输入用户名和短密码
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'password_field', '123');

        // 点击登录按钮
        await TestHelpers.tapButton(tester, '登 录');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('密码长度至少为6位');
      });

      testWidgets('有效输入应该通过验证', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 输入有效的用户名和密码
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'password_field', 'password123');

        // 点击登录按钮
        await TestHelpers.tapButton(tester, '登 录');
        await TestHelpers.waitForAsync(tester);

        // 验证没有表单验证错误
        expect(find.text('请输入用户名或邮箱'), findsNothing);
        expect(find.text('请输入密码'), findsNothing);
        expect(find.text('密码长度至少为6位'), findsNothing);
      });
    });

    group('用户交互测试', () {
      testWidgets('应该能够输入用户名和密码', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 输入用户名
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        expect(find.text('testuser'), findsOneWidget);

        // 输入密码
        await TestHelpers.enterText(tester, 'password_field', 'password123');
        // 注意：密码字段是隐藏的，所以我们不能直接查找密码文本
      });

      testWidgets('点击注册链接应该导航到注册页面', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 点击注册链接
        await TestHelpers.tapButton(tester, '注册账号');
        await TestHelpers.waitForAnimations(tester);

        // 验证导航（这里我们检查是否调用了正确的路由）
        // 在实际应用中，我们会检查当前页面是否为注册页面
      });

      testWidgets('点击忘记密码链接应该导航到忘记密码页面', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 点击忘记密码链接
        await TestHelpers.tapButton(tester, '忘记密码？');
        await TestHelpers.waitForAnimations(tester);

        // 验证导航
      });
    });

    group('登录流程测试', () {
      testWidgets('登录成功应该显示加载状态然后导航', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;
        mockApiService.shouldDelay = true;
        mockApiService.delayMilliseconds = 100;

        await tester.pumpWidget(createLoginPage());

        // 输入有效凭据
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'password_field', 'password123');

        // 点击登录按钮
        await TestHelpers.tapButton(tester, '登 录');
        await TestHelpers.waitForAsync(tester, milliseconds: 50);

        // 验证加载状态
        TestHelpers.expectLoadingIndicator();

        // 等待登录完成
        await TestHelpers.waitForAsync(tester, milliseconds: 200);

        // 验证加载状态消失
        TestHelpers.expectNoLoadingIndicator();
      });

      testWidgets('登录失败应该显示错误消息', (WidgetTester tester) async {
        // 设置API返回失败
        mockApiService.shouldSucceed = false;

        await tester.pumpWidget(createLoginPage());

        // 输入无效凭据
        await TestHelpers.enterText(tester, 'username_field', 'wronguser');
        await TestHelpers.enterText(tester, 'password_field', 'wrongpassword');

        // 点击登录按钮
        await TestHelpers.tapButton(tester, '登 录');
        await TestHelpers.waitForAsync(tester);

        // 等待错误消息显示
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 验证错误消息（通过SnackBar显示）
        // 注意：SnackBar可能需要额外的时间来显示
        await tester.pump(const Duration(milliseconds: 100));
      });

      testWidgets('网络错误应该显示相应的错误消息', (WidgetTester tester) async {
        // 设置网络错误
        mockApiService.shouldSucceed = false;
        mockApiService.forceErrorType = 'network';

        await tester.pumpWidget(createLoginPage());

        // 输入凭据
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'password_field', 'password123');

        // 点击登录按钮
        await TestHelpers.tapButton(tester, '登 录');
        await TestHelpers.waitForAsync(tester);

        // 等待错误处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });
    });

    group('状态管理测试', () {
      testWidgets('加载状态应该禁用登录按钮', (WidgetTester tester) async {
        // 设置延迟以观察加载状态
        mockApiService.shouldDelay = true;
        mockApiService.delayMilliseconds = 200;

        await tester.pumpWidget(createLoginPage());

        // 输入凭据
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'password_field', 'password123');

        // 点击登录按钮
        await TestHelpers.tapButton(tester, '登 录');
        await TestHelpers.waitForAsync(tester, milliseconds: 50);

        // 验证按钮被禁用（通过查找加载指示器）
        TestHelpers.expectLoadingIndicator();

        // 尝试再次点击按钮应该无效
        final loginButton = find.text('登 录');
        final button = tester.widget<ElevatedButton>(
          find.ancestor(
            of: loginButton,
            matching: find.byType(ElevatedButton),
          ),
        );
        expect(button.onPressed, isNull); // 按钮应该被禁用

        // 等待操作完成
        await TestHelpers.waitForAsync(tester, milliseconds: 300);
      });

      testWidgets('应该正确响应AuthProvider状态变化', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 验证初始状态
        expect(authProvider.isLoading, isFalse);
        expect(authProvider.error, isNull);

        // 模拟状态变化
        mockApiService.shouldSucceed = true;
        await TestHelpers.enterText(tester, 'username_field', 'testuser');
        await TestHelpers.enterText(tester, 'password_field', 'password123');
        await TestHelpers.tapButton(tester, '登 录');

        // 等待状态更新
        await TestHelpers.waitForProviderUpdate(tester);

        // 验证状态已更新
        expect(authProvider.isAuthenticated, isTrue);
      });
    });

    group('无障碍性测试', () {
      testWidgets('应该有正确的语义标签', (WidgetTester tester) async {
        await tester.pumpWidget(createLoginPage());

        // 验证表单字段有正确的标签
        expect(find.bySemanticsLabel('用户名或邮箱输入框'), findsOneWidget);
        expect(find.bySemanticsLabel('密码输入框'), findsOneWidget);

        // 验证按钮有正确的标签
        expect(find.bySemanticsLabel('登录按钮'), findsOneWidget);
      });
    });
  });
}
