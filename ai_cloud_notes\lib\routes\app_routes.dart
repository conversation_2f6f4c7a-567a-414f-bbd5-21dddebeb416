import 'package:flutter/material.dart';

// 导入所有页面
import 'package:ai_cloud_notes/screens/welcome/welcome_page.dart';
import 'package:ai_cloud_notes/screens/welcome/onboarding_page.dart';
import 'package:ai_cloud_notes/screens/auth/login_page.dart';
import 'package:ai_cloud_notes/screens/auth/register_page.dart';
import 'package:ai_cloud_notes/screens/auth/forgot_password_page.dart';
import 'package:ai_cloud_notes/screens/home/<USER>';
import 'package:ai_cloud_notes/screens/home/<USER>';
import 'package:ai_cloud_notes/screens/editor/editor_page.dart';
import 'package:ai_cloud_notes/screens/tags/tags_page.dart';
import 'package:ai_cloud_notes/screens/profile/profile_edit_page.dart';
import 'package:ai_cloud_notes/screens/settings/settings_page.dart';
import 'package:ai_cloud_notes/screens/settings/theme_settings_page.dart';
import 'package:ai_cloud_notes/screens/settings/ai_settings_page.dart';
import 'package:ai_cloud_notes/screens/settings/change_password_page.dart';
import 'package:ai_cloud_notes/screens/search/search_page.dart';
import 'package:ai_cloud_notes/screens/share/share_note_page.dart';
import 'package:ai_cloud_notes/screens/share/shared_note_view_page.dart';
import 'package:ai_cloud_notes/screens/tags/tag_detail_page.dart'; // 导入标签详情页面
import 'package:ai_cloud_notes/screens/editor/history_preview_page.dart'; // 导入历史预览页面
import 'package:ai_cloud_notes/models/note_model.dart'; // 导入 Note 类

/// 应用路由
/// 集中管理应用的所有页面路由
class AppRoutes {
  /// 定义路由名称常量
  static const String welcome = '/welcome';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String home = '/home';
  static const String favorites = '/favorites';
  static const String editor = '/editor';
  static const String shareNote = '/share-note';
  static const String sharedNoteView = '/share'; // 共享笔记的基础路径
  static const String tags = '/tags';
  static const String tagDetail = '/tags/detail'; // 标签详情页面
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String themeSettings = '/settings/theme';
  static const String aiSettings = '/settings/ai';
  static const String changePassword = '/settings/change-password';
  static const String search = '/search';
  static const String historyPreview = '/editor/history-preview'; // 历史版本预览页面

  /// 创建路由表
  static Map<String, WidgetBuilder> routes = {
    welcome: (context) => const WelcomePage(),
    onboarding: (context) => const OnboardingPage(),
    login: (context) => const LoginPage(),
    register: (context) => const RegisterPage(),
    forgotPassword: (context) => const ForgotPasswordPage(),
    home: (context) => const HomePage(),
    favorites: (context) => const FavoritesPage(),
    tags: (context) => const TagsPage(),
    profile: (context) => const ProfileEditPage(),
    settings: (context) => const SettingsPage(),
    themeSettings: (context) => const ThemeSettingsPage(),
    aiSettings: (context) => const AISettingsPage(),
    changePassword: (context) => const ChangePasswordPage(),
    search: (context) => const SearchPage(),
  };

  /// 处理动态路由，如传递参数的路由
  static Route<dynamic> generateRoute(RouteSettings settings) {
    if (settings.name != null && settings.name!.startsWith('$sharedNoteView/')) {
      final uri = Uri.parse(settings.name!);
      final pathSegments = uri.pathSegments;
      if (pathSegments.length >= 2 && pathSegments[1].isNotEmpty) {
        final token = pathSegments[1];
        return MaterialPageRoute(
          builder: (context) => SharedNoteViewPage(shareToken: token),
        );
      } else {
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('无效链接')),
            body: const Center(
              child: Text('您访问的分享链接无效或已过期。'),
            ),
          ),
        );
      }
    }

    if (settings.name != null &&
        (settings.name!.startsWith(forgotPassword) ||
         settings.name!.startsWith('/reset-password'))) {
      final uri = Uri.parse(settings.name!);
      final token = uri.queryParameters['token'];
      final email = uri.queryParameters['email'];
      return MaterialPageRoute(
        builder: (context) => ForgotPasswordPage(token: token, email: email),
      );
    }

    switch (settings.name) {
      case editor:
        final dynamic args = settings.arguments;
        Note? note;
        String? initialTagId;
        String? initialTagName;

        // 处理不同类型的参数
        if (args is Note) {
          // 直接传递Note对象
          note = args;
        } else if (args is Map<String, dynamic>) {
          // 传递包含多个参数的Map
          note = args['note'] as Note?;
          initialTagId = args['initialTagId'] as String?;
          initialTagName = args['initialTagName'] as String?;
        }

        return MaterialPageRoute(
          builder: (context) => EditorPage(
            note: note,
            initialTagId: initialTagId,
            initialTagName: initialTagName,
          ),
          settings: settings, // 保留路由设置以便正确显示URL
        );

      case shareNote:
        final dynamic args = settings.arguments;
        if (args is Note) { // 使用正确的类名 Note
          return MaterialPageRoute(
            builder: (context) => ShareNotePage(note: args),
          );
        } else {
          return MaterialPageRoute(
            builder: (_) => Scaffold(
              appBar: AppBar(title: const Text('错误')),
              body: const Center(
                child: Text('无法分享笔记：参数无效或笔记不存在。'),
              ),
            ),
          );
        }

      case search:
        final dynamic args = settings.arguments;
        final String? initialQuery;
        if (args is String) {
          initialQuery = args;
        } else if (args == null) {
          initialQuery = null;
        } else {
          initialQuery = null;
        }
        return MaterialPageRoute(
          builder: (context) => SearchPage(initialQuery: initialQuery),
        );

      case tagDetail:
        final dynamic args = settings.arguments;
        if (args is String) {
          return MaterialPageRoute(
            builder: (context) => TagDetailPage(tagId: args),
            settings: settings, // 保留路由设置
          );
        } else {
          return MaterialPageRoute(
            builder: (_) => Scaffold(
              appBar: AppBar(title: const Text('错误')),
              body: const Center(
                child: Text('无法打开标签详情：参数无效或标签不存在。'),
              ),
            ),
          );
        }

      case historyPreview:
        final dynamic args = settings.arguments;
        if (args is Map<String, dynamic> &&
            args.containsKey('historyNote') &&
            args.containsKey('originalNoteId')) {
          return MaterialPageRoute(
            builder: (context) => HistoryPreviewPage(
              historyNote: args['historyNote'],
              originalNoteId: args['originalNoteId'],
            ),
            settings: settings, // 保留路由设置
          );
        } else {
          return MaterialPageRoute(
            builder: (_) => Scaffold(
              appBar: AppBar(title: const Text('错误')),
              body: const Center(
                child: Text('无法打开历史预览：参数无效或历史记录不存在。'),
              ),
            ),
          );
        }

      case sharedNoteView:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('无效链接')),
            body: const Center(
              child: Text('请使用完整的分享链接访问笔记。'),
            ),
          ),
        );

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('页面未找到')),
            body: Center(
              child: Text('找不到路由: ${settings.name}'),
            ),
          ),
        );
    }
  }
}