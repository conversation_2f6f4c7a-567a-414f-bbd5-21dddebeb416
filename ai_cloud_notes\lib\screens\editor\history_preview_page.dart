import 'dart:convert'; // 用于 JSON 解码
import 'package:flutter/material.dart';
import 'package:provider/provider.dart'; // 添加Provider导入
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/utils/date_time_helper.dart'; // 导入时间处理工具类
import 'package:ai_cloud_notes/providers/note_provider.dart'; // 导入NoteProvider
import 'package:ai_cloud_notes/utils/snackbar_helper.dart'; // 导入SnackBar辅助工具
import 'package:fleather/fleather.dart';
import 'package:parchment/parchment.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'dart:math'; // For min function
import 'package:uuid/uuid.dart'; // For tableId if needed

/// 历史版本预览页面 - 简化版
class HistoryPreviewPage extends StatefulWidget {
  /// 要预览的历史笔记对象
  final Note historyNote;

  /// 原始笔记ID，用于恢复操作
  final String originalNoteId;

  const HistoryPreviewPage({
    Key? key,
    required this.historyNote,
    required this.originalNoteId,
  }) : super(key: key);

  @override
  State<HistoryPreviewPage> createState() => _HistoryPreviewPageState();
}

class _HistoryPreviewPageState extends State<HistoryPreviewPage> {
  late FleatherController _fleatherController;
  late ParchmentDocument _document;
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeDocument(); // 初始化文档
  }

  /// 初始化 Fleather 文档 - 支持Markdown和富文本格式
  void _initializeDocument() {
    print('DEBUG: [HistoryPreviewPage] 初始化预览版本 ${widget.historyNote.version}');
    try {
      // 根据笔记类型处理内容
      print(
          'DEBUG: [HistoryPreviewPage] ContentType: ${widget.historyNote.contentType}');
      print(
          'DEBUG: [HistoryPreviewPage] Raw Content: ${widget.historyNote.content}');
      if (widget.historyNote.contentType == 'markdown') {
        // 对于Markdown格式，使用简单的文本文档
        print('DEBUG: [HistoryPreviewPage] Handling as Markdown.');
        String displayContent = widget.historyNote.content.trim();

        // 确保内容以换行符结束
        if (!displayContent.endsWith('\n')) {
          displayContent = displayContent + '\n';
        }

        // 创建文档对象
        _document = ParchmentDocument.fromJson([
          {"insert": displayContent}
        ]);
      } else {
        // 对于富文本格式，尝试解析Delta JSON
        print('DEBUG: [HistoryPreviewPage] Handling as Rich Text (Delta).');
        try {
          String content = widget.historyNote.content.trim();
          if (content.startsWith('[') || content.startsWith('{')) {
            dynamic deltaJson = jsonDecode(content);
            print(
                'DEBUG: [HistoryPreviewPage] Decoded Delta JSON: ${jsonEncode(deltaJson)}'); // Log decoded JSON
            _document = ParchmentDocument.fromJson(deltaJson);
          } else {
            // 如果不是有效的JSON，作为纯文本处理
            print(
                'DEBUG: [HistoryPreviewPage] Content is not valid JSON, treating as plain text.');
            String displayContent = widget.historyNote.plainTextContent.trim();
            if (displayContent.isEmpty) {
              displayContent = content;
            }

            // 确保内容以换行符结束
            if (!displayContent.endsWith('\n')) {
              displayContent = displayContent + '\n';
            }

            _document = ParchmentDocument.fromJson([
              {"insert": displayContent}
            ]);
          }
        } catch (e) {
          // 解析失败，使用纯文本
          print(
              'ERROR: [HistoryPreviewPage] Delta JSON parsing failed, falling back to plain text. Error: $e');
          String displayContent = widget.historyNote.plainTextContent.trim();
          if (displayContent.isEmpty) {
            displayContent = widget.historyNote.content.trim();
          }

          // 确保内容以换行符结束
          if (!displayContent.endsWith('\n')) {
            displayContent = displayContent + '\n';
          }

          _document = ParchmentDocument.fromJson([
            {"insert": displayContent}
          ]);
        }
      }

      print('DEBUG: [HistoryPreviewPage] 文档初始化成功');
      print(
          'DEBUG: [HistoryPreviewPage] Initialized Document JSON: ${jsonEncode(_document.toJson())}');
    } catch (e) {
      // 解析失败，显示错误信息
      print('ERROR: [HistoryPreviewPage] 文档初始化失败: $e');
      _errorMessage = '无法加载历史内容: ${e.toString()}';
      _document = ParchmentDocument.fromJson([
        {"insert": "无法加载历史内容。\n"},
        {"insert": "版本: ${widget.historyNote.version}\n"},
        {"insert": "错误信息: ${e.toString()}\n"},
      ]);
    } finally {
      // 创建控制器并完成加载
      _fleatherController = FleatherController(document: _document);
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 恢复笔记到此历史版本
  Future<void> _restoreVersion() async {
    // 确认是否要恢复
    final confirmed = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('确认恢复'),
            content: Text(
                '确定要将笔记恢复到版本 ${widget.historyNote.version} 吗？\n\n此操作将覆盖当前笔记内容。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(
                    foregroundColor: Colors.orange.shade700),
                child: const Text('恢复'),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirmed) return;

    // 显示加载指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    try {
      // 获取NoteProvider并调用恢复方法
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      final success = await noteProvider.restoreVersion(
        widget.originalNoteId,
        widget.historyNote.version,
      );

      // 关闭加载对话框
      Navigator.pop(context);

      if (success) {
        // 显示成功消息
        if (!mounted) return;
        SnackbarHelper.showSuccess(
          context: context,
          message: '已恢复到版本 ${widget.historyNote.version}',
        );

        // 关闭预览页面，返回编辑器
        if (mounted) {
          Navigator.pop(context);
          // 返回第二层（如果来自历史面板，关闭历史面板）
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }
        }
      } else {
        // 显示错误信息
        if (!mounted) return;
        SnackbarHelper.showError(
          context: context,
          message: '恢复失败: ${noteProvider.error}',
        );
      }
    } catch (e) {
      // 关闭加载对话框
      Navigator.pop(context);

      // 显示错误信息
      if (!mounted) return;
      SnackbarHelper.showError(
        context: context,
        message: '恢复失败: $e',
      );
    }
  }

  @override
  void dispose() {
    _fleatherController.dispose(); // 释放控制器资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 使用archivedAt字段作为历史记录的保存时间
    final historyTime = widget.historyNote.archivedAt ??
        widget.historyNote.updatedAt; // 优先使用archivedAt
    final formattedTime = historyTime != null
        ? DateTimeHelper.formatDateTime(historyTime)
        : '未知时间';

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('预览: 版本 ${widget.historyNote.version}'),
        centerTitle: true,
        elevation: 1,
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          // 添加恢复按钮
          IconButton(
            icon: Icon(Icons.restore, color: Colors.orange.shade700),
            tooltip: '恢复到此版本',
            onPressed: _restoreVersion,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      _errorMessage,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 显示标题和时间信息
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.historyNote.title,
                            style: Theme.of(context)
                                .textTheme
                                .headlineSmall
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context)
                                      .textTheme
                                      .titleLarge
                                      ?.color,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '保存于: $formattedTime',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color
                                          ?.withOpacity(0.7),
                                    ),
                          ),
                          // 如果需要显示历史标签
                          if (widget.historyNote.tagIds.isNotEmpty) ...[
                            const SizedBox(height: 8),
                            // 使用Wrap组件显示标签
                            Text(
                              '标签:',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.color
                                        ?.withOpacity(0.7),
                                  ),
                            ),
                            const SizedBox(height: 4),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                for (int i = 0;
                                    i < widget.historyNote.tagIds.length;
                                    i++)
                                  Chip(
                                    label: Text(
                                      i < widget.historyNote.tags.length
                                          ? widget.historyNote.tags[i]
                                          : '标签${i + 1}',
                                      style: const TextStyle(fontSize: 12),
                                    ),
                                    backgroundColor:
                                        Theme.of(context).brightness ==
                                                Brightness.dark
                                            ? Colors.grey.shade800
                                            : AppTheme.lightGrayColor,
                                    padding: const EdgeInsets.all(2),
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    const Divider(height: 1),
                    // 根据笔记类型选择不同的渲染方式
                    Expanded(
                      child: Padding(
                        // 添加一些内边距，使内容不紧贴边缘
                        padding: const EdgeInsets.all(16.0),
                        child: widget.historyNote.contentType == 'markdown'
                            ? SingleChildScrollView(
                                child: MarkdownBody(
                                  data: widget.historyNote.content,
                                  selectable: true,
                                  softLineBreak: true,
                                  styleSheet: MarkdownStyleSheet(
                                    p: TextStyle(
                                      fontSize: 16,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                      height: 1.5,
                                    ),
                                    h1: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .headlineMedium
                                          ?.color,
                                    ),
                                    h2: TextStyle(
                                      fontSize: 22,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .headlineMedium
                                          ?.color,
                                    ),
                                    h3: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .headlineMedium
                                          ?.color,
                                    ),
                                    h4: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .headlineMedium
                                          ?.color,
                                    ),
                                    h5: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .headlineMedium
                                          ?.color,
                                    ),
                                    h6: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .headlineMedium
                                          ?.color,
                                    ),
                                    code: TextStyle(
                                      backgroundColor:
                                          Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.grey.shade800
                                              : Colors.grey.shade200,
                                      fontFamily: 'monospace',
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? Colors.orange.shade300
                                          : Colors.red.shade800,
                                    ),
                                    blockquote: TextStyle(
                                      color: Theme.of(context).brightness ==
                                              Brightness.dark
                                          ? Colors.grey.shade400
                                          : Colors.grey.shade700,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ),
                              )
                            : FleatherEditor(
                                controller: _fleatherController,
                                readOnly: true, // 设置为只读模式
                                showCursor: false, // 不显示光标
                                enableInteractiveSelection: true, // 允许选择文本
                                padding: EdgeInsets.zero, // 内部无额外边距
                                focusNode: FocusNode(), // 提供FocusNode
                                scrollController:
                                    ScrollController(), // 提供ScrollController
                                scrollable: true, // 确保可滚动
                                expands: true, // 填充可用空间
                                embedBuilder: _embedBuilder, // 添加 embedBuilder
                              ),
                      ),
                    ),
                  ],
                ),
    );
  }

  // Helper function to find a value in a nested map (copied from editor_page.dart)
  String? _findValueInMap(Map<String, dynamic> map, String targetKey) {
    if (map.containsKey(targetKey)) {
      final value = map[targetKey];
      if (value is String) {
        return value;
      }
    }
    for (final entry in map.entries) {
      final value = entry.value;
      if (value is Map<String, dynamic>) {
        final result = _findValueInMap(value, targetKey);
        if (result != null) {
          return result;
        }
      } else if (value is Map) {
        try {
          final convertedMap = Map<String, dynamic>.from(value);
          final result = _findValueInMap(convertedMap, targetKey);
          if (result != null) {
            return result;
          }
        } catch (e) {
          // Conversion failed, ignore
        }
      } else if (value is List) {
        for (final item in value) {
          if (item is Map<String, dynamic>) {
            final result = _findValueInMap(item, targetKey);
            if (result != null) {
              return result;
            }
          } else if (item is Map) {
            try {
              final convertedMap = Map<String, dynamic>.from(item);
              final result = _findValueInMap(convertedMap, targetKey);
              if (result != null) {
                return result;
              }
            } catch (e) {
              // Conversion failed, ignore
            }
          }
        }
      }
    }
    return null;
  }

  // Build image widget based on source type (adapted for read-only preview)
  Widget _buildImageBySourceType(
      String? sourceType, dynamic source, String imageId) {
    print(
        'DEBUG: [HistoryPreviewPage] Building image: $imageId, Type: $sourceType, Source: $source');
    Widget placeholder = Container(
      color: AppTheme.lightGrayColor.withOpacity(0.2),
      child: const Center(
        child: SizedBox(
          width: 40,
          height: 40,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
      ),
    );

    Widget errorWidget = Container(
      width: 200,
      height: 150,
      decoration: BoxDecoration(
        color: AppTheme.lightGrayColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.mediumGrayColor),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.broken_image, color: AppTheme.darkGrayColor, size: 40),
          const SizedBox(height: 8),
          Text(
            '图片加载失败',
            style: TextStyle(color: AppTheme.darkGrayColor),
          ),
        ],
      ),
    );

    try {
      if (sourceType == 'url' && source is String) {
        return Image.network(
          source,
          fit: BoxFit.contain,
          cacheWidth: 1000,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return placeholder;
          },
          errorBuilder: (context, error, stackTrace) {
            print(
                'ERROR: [HistoryPreviewPage] Network image load failed: $error, Source: $source');
            return errorWidget;
          },
        );
      } else if (sourceType == 'local_web' &&
          source is String &&
          source.startsWith('data:')) {
        // This case might not be common for history if images are uploaded
        try {
          final bytes = UriData.parse(source).contentAsBytes();
          return Image.memory(
            bytes,
            fit: BoxFit.contain,
            cacheWidth: 1000,
            frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
              if (wasSynchronouslyLoaded || frame != null) return child;
              return AnimatedSwitcher(
                duration: const Duration(milliseconds: 300),
                child: frame != null ? child : placeholder,
              );
            },
            errorBuilder: (context, error, stackTrace) {
              print(
                  'ERROR: [HistoryPreviewPage] Data URI image load failed: $error');
              return errorWidget;
            },
          );
        } catch (e) {
          print('ERROR: [HistoryPreviewPage] Parsing data URI failed: $e');
          return errorWidget;
        }
      }
      // Add other source types if necessary for history (e.g., 'file' if paths are stored)
    } catch (e) {
      print('ERROR: [HistoryPreviewPage] Image loading exception: $e');
    }
    print(
        'WARN: [HistoryPreviewPage] Unsupported image sourceType: $sourceType or source: $source');
    return errorWidget;
  }

  // Build a simple, non-editable table view (adapted for read-only preview)
  Widget _buildSimpleTable(
    BuildContext context,
    int rows,
    int columns,
    List<List<String>> cells,
    List<double> rowHeights,
    List<double> columnWidths,
    EmbedNode node,
    String? tableId,
  ) {
    print('DEBUG: [HistoryPreviewPage] Building table view, ID: $tableId');
    final screenWidth = MediaQuery.of(context).size.width;
    double totalTableWidth = columnWidths.fold(0, (sum, width) => sum + width);
    totalTableWidth = min(totalTableWidth, screenWidth * 0.95);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Align(
        alignment: Alignment.center,
        child: Container(
          constraints: BoxConstraints(maxWidth: totalTableWidth),
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.lightGrayColor),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Table(
              border: TableBorder.all(color: AppTheme.lightGrayColor, width: 1),
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              columnWidths: Map.fromIterable(
                List.generate(columns, (index) => index),
                key: (i) => i,
                value: (i) => FixedColumnWidth(columnWidths.length > i
                    ? columnWidths[i]
                    : 100.0), // Safety check
              ),
              children: List.generate(
                rows,
                (i) => TableRow(
                  decoration: i == 0
                      ? BoxDecoration(
                          color: AppTheme.lightGrayColor.withOpacity(0.3))
                      : null,
                  children: List.generate(
                    columns,
                    (j) => Container(
                      height: rowHeights.length > i
                          ? rowHeights[i]
                          : 40.0, // Safety check
                      constraints: BoxConstraints(
                        minHeight: rowHeights.length > i ? rowHeights[i] : 40.0,
                        maxHeight: rowHeights.length > i ? rowHeights[i] : 40.0,
                      ),
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Text(
                          i < cells.length && j < cells[i].length
                              ? cells[i][j]
                              : '',
                          textAlign: TextAlign.center,
                          overflow: TextOverflow.ellipsis,
                          style: i == 0
                              ? const TextStyle(fontWeight: FontWeight.bold)
                              : null,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Embed builder for FleatherEditor (adapted for read-only preview)
  Widget _embedBuilder(BuildContext context, EmbedNode node) {
    final nodeValue = node.value; // Alias for easier access

    if (nodeValue.type == 'image') {
      final data = nodeValue.data;
      final sourceType = data['source_type'] as String?;
      final source = data['source'] as String?;
      final String imageId = 'hist-img-${node.offset}-${source.hashCode}';

      return RepaintBoundary(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
              maxHeight: 300,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildImageBySourceType(sourceType, source, imageId),
            ),
          ),
        ),
      );
    }

    if (nodeValue.type == 'table') {
      final Map<String, dynamic> data =
          Map<String, dynamic>.from(nodeValue.data);
      final int rows = data['rows'] as int? ?? 3;
      final int columns = data['columns'] as int? ?? 3;
      String? tableId = _findValueInMap(data, 'tableId');

      if (tableId == null) {
        // For preview, if ID is missing, we might not need to generate one,
        // but editor's _embedBuilder does. For consistency or if any logic relies on it:
        tableId = const Uuid().v4();
        print(
            'WARN: [HistoryPreviewPage] Table node missing tableId, generated temp: $tableId');
      }

      List<List<String>> cellsData = [];
      if (data.containsKey('tableData') && data['tableData'] is List) {
        final List tableDataList = data['tableData'] as List;
        for (int i = 0; i < min(tableDataList.length, rows); i++) {
          final rowData = tableDataList[i];
          List<String> row = [];
          if (rowData is List) {
            for (int j = 0; j < min(rowData.length, columns); j++) {
              row.add((rowData[j]?.toString() ?? ''));
            }
          }
          while (row.length < columns) row.add('');
          cellsData.add(row);
        }
      }
      while (cellsData.length < rows) {
        cellsData.add(List.filled(columns, '', growable: true));
      }

      List<double> rowHeights = List.filled(rows, 40.0);
      if (data.containsKey('rowHeights') && data['rowHeights'] is List) {
        final List savedRowHeights = data['rowHeights'] as List;
        for (int i = 0; i < min(savedRowHeights.length, rows); i++) {
          if (savedRowHeights[i] is num) {
            rowHeights[i] = (savedRowHeights[i] as num).toDouble();
          }
        }
      }

      List<double> columnWidths = List.filled(columns, 100.0);
      if (data.containsKey('columnWidths') && data['columnWidths'] is List) {
        final List savedColumnWidths = data['columnWidths'] as List;
        for (int i = 0; i < min(savedColumnWidths.length, columns); i++) {
          if (savedColumnWidths[i] is num) {
            columnWidths[i] = (savedColumnWidths[i] as num).toDouble();
          }
        }
      }

      return _buildSimpleTable(context, rows, columns, cellsData, rowHeights,
          columnWidths, node, tableId);
    }

    // Fallback for other embed types
    // You might want to log unknown embed types
    print('WARN: [HistoryPreviewPage] Unknown embed type: ${nodeValue.type}');
    return defaultFleatherEmbedBuilder(
        context, node); // Or a custom placeholder
  }
}
