import { Response } from 'express';
import { logger } from '../utils/logger';
import AISettingsService from '../services/ai_settings.service';
import { AuthenticatedRequest } from '../types/express';

/**
 * AI设置控制器类
 */
class AISettingsController {
  /**
   * 获取用户的AI设置
   * @param req 请求对象
   * @param res 响应对象
   * @returns AI设置响应
   */
  async getUserAISettings(req: AuthenticatedRequest, res: Response) {
    try {
      // 获取请求中的用户ID
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未授权，无法获取AI设置'
        });
      }
      
      // 调用服务获取AI设置
      const aiSettings = await AISettingsService.getUserAISettings(userId);
      
      return res.status(200).json({
        success: true,
        data: aiSettings
      });
    } catch (error) {
      logger.error(`获取用户AI设置出错: ${error}`);
      return res.status(500).json({
        success: false,
        message: '获取AI设置失败'
      });
    }
  }

  /**
   * 更新用户的AI设置
   * @param req 请求对象
   * @param res 响应对象
   * @returns 更新响应
   */
  async updateUserAISettings(req: AuthenticatedRequest, res: Response) {
    try {
      // 获取请求中的用户ID
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未授权，无法更新AI设置'
        });
      }
      
      // 获取请求体中的设置
      const { 
        aiAssistantEnabled,
        smartSuggestionsEnabled,
        autoTaggingEnabled,
        contentSummaryEnabled,
        selectedModel,
        suggestionFrequency,
        localProcessingEnabled,
        allowDataCollection
      } = req.body;
      
      // 数据验证
      if (suggestionFrequency && !['实时', '输入停顿后', '手动触发'].includes(suggestionFrequency)) {
        return res.status(400).json({
          success: false,
          message: '无效的建议频率值'
        });
      }
      
      // 调用服务更新AI设置
      const updatedSettings = await AISettingsService.updateUserAISettings(userId, {
        aiAssistantEnabled,
        smartSuggestionsEnabled,
        autoTaggingEnabled,
        contentSummaryEnabled,
        selectedModel,
        suggestionFrequency,
        localProcessingEnabled,
        allowDataCollection
      });
      
      return res.status(200).json({
        success: true,
        message: 'AI设置已更新',
        data: updatedSettings
      });
    } catch (error) {
      logger.error(`更新用户AI设置出错: ${error}`);
      return res.status(500).json({
        success: false,
        message: '更新AI设置失败'
      });
    }
  }
}

export default new AISettingsController();
