import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/utils/url_helper.dart';

/// 用户资料模型类
/// 
/// 用于表示用户的个人资料信息，包含基本信息和统计数据
class UserProfile {
  /// 用户ID
  final String id;
  
  /// 用户名
  final String username;
  
  /// 邮箱
  final String email;
  
  /// 个人签名
  final String? bio;
  
  /// 头像URL
  final String? avatar;
  
  /// 是否已验证
  final bool isVerified;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 更新时间
  final DateTime updatedAt;
  
  /// 用户统计信息
  final UserStats stats;

  /// 构造函数
  UserProfile({
    required this.id,
    required this.username,
    required this.email,
    this.bio,
    this.avatar,
    this.isVerified = false,
    required this.createdAt,
    required this.updatedAt,
    required this.stats,
  });

  /// 从JSON映射创建UserProfile对象
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    // 处理头像URL，确保是完整的URL
    String? avatarUrl = json['avatar'];
    if (avatarUrl != null) {
      avatarUrl = UrlHelper.getFullAvatarUrl(avatarUrl);
    }
    
    return UserProfile(
      id: json['_id'] ?? '',
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      bio: json['bio'],
      avatar: avatarUrl,
      isVerified: json['isVerified'] ?? false,
      createdAt: json['createdAt'] != null 
        ? DateTime.parse(json['createdAt']) 
        : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
        ? DateTime.parse(json['updatedAt']) 
        : DateTime.now(),
      stats: UserStats.fromJson(json['stats'] ?? {}),
    );
  }

  /// 将UserProfile对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'username': username,
      'email': email,
      'bio': bio,
      'avatar': avatar,
      'isVerified': isVerified,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'stats': stats.toJson(),
    };
  }

  /// 创建具有更新属性的新UserProfile实例
  UserProfile copyWith({
    String? id,
    String? username,
    String? email,
    String? bio,
    String? avatar,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserStats? stats,
  }) {
    return UserProfile(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      bio: bio ?? this.bio,
      avatar: avatar ?? this.avatar,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      stats: stats ?? this.stats,
    );
  }

  /// 重写相等运算符
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile &&
      other.id == id &&
      other.username == username &&
      other.email == email &&
      other.bio == bio &&
      other.avatar == avatar &&
      other.isVerified == isVerified;
  }

  /// 重写哈希码
  @override
  int get hashCode => 
    id.hashCode ^
    username.hashCode ^
    email.hashCode ^
    bio.hashCode ^
    avatar.hashCode ^
    isVerified.hashCode;
}

/// 用户统计信息模型类
class UserStats {
  /// 笔记总数
  final int totalNotes;
  
  /// 收藏笔记数
  final int favoriteNotes;
  
  /// 归档笔记数
  final int archivedNotes;
  
  /// 活跃笔记数
  final int activeNotes;
  
  /// 标签总数
  final int totalTags;
  
  /// 月度统计数据
  final List<MonthlyStats> monthly;

  /// 构造函数
  UserStats({
    this.totalNotes = 0,
    this.favoriteNotes = 0,
    this.archivedNotes = 0,
    this.activeNotes = 0,
    this.totalTags = 0,
    this.monthly = const [],
  });

  /// 从JSON映射创建UserStats对象
  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalNotes: json['totalNotes'] ?? 0,
      favoriteNotes: json['favoriteNotes'] ?? 0,
      archivedNotes: json['archivedNotes'] ?? 0,
      activeNotes: json['activeNotes'] ?? 0,
      totalTags: json['totalTags'] ?? 0,
      monthly: (json['monthly'] as List<dynamic>?)
          ?.map((item) => MonthlyStats.fromJson(item))
          .toList() ?? [],
    );
  }

  /// 将UserStats对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'totalNotes': totalNotes,
      'favoriteNotes': favoriteNotes,
      'archivedNotes': archivedNotes,
      'activeNotes': activeNotes,
      'totalTags': totalTags,
      'monthly': monthly.map((item) => item.toJson()).toList(),
    };
  }
}

/// 月度统计数据模型类
class MonthlyStats {
  /// 年份
  final int year;
  
  /// 月份
  final int month;
  
  /// 笔记数量
  final int count;

  /// 构造函数
  MonthlyStats({
    required this.year,
    required this.month,
    required this.count,
  });

  /// 从JSON映射创建MonthlyStats对象
  factory MonthlyStats.fromJson(Map<String, dynamic> json) {
    return MonthlyStats(
      year: json['year'] ?? 0,
      month: json['month'] ?? 0,
      count: json['count'] ?? 0,
    );
  }

  /// 将MonthlyStats对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'year': year,
      'month': month,
      'count': count,
    };
  }
} 