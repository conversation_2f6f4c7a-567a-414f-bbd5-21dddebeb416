/* 全局样式 */
:root {
    --primary-color: #5D5FEF;
    --primary-light: #EEEEFF;
    --secondary-color: #06B6D4;
    --accent-color: #F59E0B;
    --light-gray: #F9FAFB;
    --medium-gray: #E5E7EB;
    --dark-gray: #6B7280;
    --white: #ffffff;
    --black: #1F2937;
    --success: #10B981;
    --danger: #EF4444;
    --border-radius-sm: 8px;
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --border-radius-xl: 32px;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow: 0 4px 12px rgba(0,0,0,0.08);
    --shadow-lg: 0 10px 25px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
    color: var(--black);
    background-color: #F5F7FA;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* 通用布局 */
.container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 30px;
}

.screen {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    width: 360px;
    height: 720px;
    position: relative;
    margin: 40px auto;
    transition: var(--transition);
}

.screen:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 30px rgba(0, 0, 0, 0.12);
}

.screen-title {
    text-align: center;
    font-weight: 600;
    margin-top: 24px;
    margin-bottom: 20px;
    color: var(--black);
    font-size: 1.2rem;
}

.screen-description {
    text-align: center;
    color: var(--dark-gray);
    font-size: 0.9rem;
    margin-top: -10px;
    margin-bottom: 20px;
    max-width: 340px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
}

/* 底部导航栏样式 */
.bottom-nav {
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--white);
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
    border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
    padding: 12px 0 25px;
    z-index: 100;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--dark-gray);
    font-size: 0.8rem;
    gap: 4px;
    cursor: pointer;
    transition: color 0.2s ease;
}

.nav-item.active {
    color: var(--primary-color);
}

.nav-item i {
    font-size: 1.2rem;
}

.nav-item.new-note {
    margin-top: -20px;
}

.new-note-btn {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 10px rgba(93, 95, 239, 0.3);
    color: white;
    font-size: 1.2rem;
    transition: transform 0.2s;
}

.new-note-btn:hover {
    transform: scale(1.05);
}

/* 按钮和输入框样式 */
.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 0.9rem;
    color: var(--dark-gray);
}

.input-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--medium-gray);
    border-radius: var(--border-radius-sm);
    background-color: var(--light-gray);
    font-size: 0.95rem;
    transition: var(--transition);
    box-sizing: border-box;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--white);
    box-shadow: 0 0 0 3px rgba(93, 95, 239, 0.1);
}

.btn-primary {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(93, 95, 239, 0.3);
}

.btn-secondary {
    background-color: var(--primary-light);
    color: var(--primary-color);
    border: none;
    padding: 8px 16px;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    font-size: 0.9rem;
    transition: var(--transition);
    cursor: pointer;
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* 标题和文本样式 */
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    color: var(--black);
}

.page-title {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--black);
    margin-bottom: 16px;
    letter-spacing: -0.5px;
}

.page-description {
    font-size: 1.1rem;
    color: var(--dark-gray);
    max-width: 800px;
    margin: 0 auto 30px;
    line-height: 1.6;
}

/* 卡片样式 */
.card {
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: 20px;
    margin-bottom: 16px;
    border: 1px solid var(--medium-gray);
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
    border-color: var(--primary-light);
}

/* 标签样式 */
.tag {
    padding: 3px 8px;
    background-color: var(--primary-light);
    color: var(--primary-color);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    font-size: 0.85rem;
    display: inline-block;
}

/* 实用工具类 */
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.text-center { text-align: center; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.align-center { align-items: center; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* 响应式设计辅助类 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
}

/* 版权信息样式 */
.footer {
    text-align: center;
    margin-top: 40px;
    color: var(--dark-gray);
    font-size: 0.9rem;
}

.footer a {
    color: var(--primary-color);
    text-decoration: none;
} 