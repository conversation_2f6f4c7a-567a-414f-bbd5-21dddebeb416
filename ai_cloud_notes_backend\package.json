{"name": "ai_cloud_notes_backend", "version": "1.0.0", "description": "智云笔记应用后端API服务", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node --transpile-only src/server.ts", "build": "tsc", "test": "jest"}, "author": "智云笔记团队", "license": "ISC", "dependencies": {"@types/multer": "^1.4.12", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.0.0", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.1", "mongoose": "^7.4.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nodemailer": "^6.9.4", "redis": "^4.7.0", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@faker-js/faker": "^9.7.0", "@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/nock": "^11.1.0", "@types/nodemailer": "^6.4.9", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.2", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nock": "^14.0.4", "supertest": "^7.1.0", "ts-jest": "^29.3.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}