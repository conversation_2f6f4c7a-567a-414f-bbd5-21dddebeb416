import jwt from 'jsonwebtoken';
import { config } from '../config';
import { IUser } from '../models/user.model';

/**
 * 生成JWT令牌
 * @param user 用户对象
 * @returns 包含令牌的对象
 */
export const generateToken = (user: IUser) => {
  const payload = {
    id: user._id,
    username: user.username,
    email: user.email
  };

  // 生成访问令牌
  const token = jwt.sign(
    payload,
    config.jwt.secret,
    { expiresIn: config.jwt.expiresIn }
  ) as string;

  return {
    token,
    expiresIn: config.jwt.expiresIn
  };
};

/**
 * 验证JWT令牌
 * @param token JWT令牌
 * @returns 解析后的令牌载荷
 */
export const verifyToken = (token: string) => {
  try {
    const decoded = jwt.verify(token, config.jwt.secret);
    return { valid: true, expired: false, decoded };
  } catch (error: any) {
    return {
      valid: false,
      expired: error.name === 'TokenExpiredError',
      decoded: null
    };
  }
};

/**
 * 从请求头中提取JWT令牌
 * @param authHeader 认证头
 * @returns 提取的令牌
 */
export const extractTokenFromHeader = (authHeader: string) => {
  // 检查认证头是否包含Bearer令牌
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  // 从Bearer令牌中提取实际的JWT
  const token = authHeader.slice(7);
  return token || null;
}; 