import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/screens/tags/tag_detail_page.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:ai_cloud_notes/models/tag_model.dart';
import 'package:ai_cloud_notes/providers/tag_provider.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/widgets/main_layout.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';

class TagsPage extends StatefulWidget {
  const TagsPage({Key? key}) : super(key: key);

  @override
  State<TagsPage> createState() => _TagsPageState();
}

class _TagsPageState extends State<TagsPage> {
  // 标签列表和筛选列表
  List<Tag> _filteredTags = [];

  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  // 标签编辑状态
  bool _isEditMode = false;

  // 批量选择模式
  bool _isMultiSelectMode = false;
  Set<String> _selectedTagIds = {};

  // 可选标签颜色
  final List<Color> _availableColors = [
    Colors.red,
    Colors.pink,
    Colors.purple,
    Colors.deepPurple,
    Colors.indigo,
    Colors.blue,
    Colors.lightBlue,
    Colors.cyan,
    Colors.teal,
    Colors.green,
    Colors.lightGreen,
    Colors.lime,
    Colors.yellow,
    Colors.amber,
    Colors.orange,
    Colors.deepOrange,
    Colors.brown,
    Colors.grey,
    Colors.blueGrey,
  ];

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      _filterTags();
    });

    // 在页面创建后加载标签数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTags();
    });
  }

  // 记录上一次路由
  String? _previousRoute;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 获取当前路由
    final currentRoute = ModalRoute.of(context)?.settings.name;

    // 只有当从其他页面返回标签页时才重新加载数据
    if (currentRoute == AppRoutes.tags &&
        _previousRoute != null &&
        _previousRoute != AppRoutes.tags) {
      print('DEBUG: 从${_previousRoute}返回标签页，重新加载标签数据');
      // 使用Future.microtask确保不在build过程中调用setState
      Future.microtask(() {
        if (mounted) {
          _loadTags();
        }
      });
    }

    // 更新上一次路由
    _previousRoute = currentRoute;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // 加载标签数据
  Future<void> _loadTags() async {
    if (!mounted) return;

    final tagProvider = Provider.of<TagProvider>(context, listen: false);

    try {
      print('DEBUG: 开始加载标签数据');
      await tagProvider.fetchTags();
      print('DEBUG: 标签数据加载完成，共 ${tagProvider.tags.length} 个标签');

      // 确保UI更新
      if (mounted) {
        _filterTags();
      }
    } catch (e) {
      print('DEBUG: 加载标签数据出错: $e');
    }
  }

  void _filterTags() {
    // 检查组件是否已经被卸载
    if (!mounted) return;

    final query = _searchController.text.toLowerCase();
    final tagProvider = Provider.of<TagProvider>(context, listen: false);

    setState(() {
      _isSearching = query.isNotEmpty;
      if (_isSearching) {
        _filteredTags = tagProvider.tags
            .where((tag) => tag.name.toLowerCase().contains(query))
            .toList();
      } else {
        _filteredTags = List.from(tagProvider.tags);
      }
    });
  }

  void _enterMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = true;
      _selectedTagIds.clear();
    });
  }

  void _exitMultiSelectMode() {
    setState(() {
      _isMultiSelectMode = false;
      _selectedTagIds.clear();
    });
  }

  void _toggleTagSelection(String tagId) {
    setState(() {
      if (_selectedTagIds.contains(tagId)) {
        _selectedTagIds.remove(tagId);
      } else {
        _selectedTagIds.add(tagId);
      }

      // 如果没有选中的标签，退出多选模式
      if (_selectedTagIds.isEmpty && _isMultiSelectMode) {
        _isMultiSelectMode = false;
      }
    });
  }

  void _selectAllTags() {
    setState(() {
      if (_selectedTagIds.length == _filteredTags.length) {
        // 如果已经全选，则取消全选
        _selectedTagIds.clear();
      } else {
        // 否则全选
        _selectedTagIds = _filteredTags.map((tag) => tag.id).toSet();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 检查当前路由，判断是否是作为独立页面打开的
    final currentRoute = ModalRoute.of(context)?.settings.name;
    final isStandalonePage = currentRoute == AppRoutes.tags;

    return Consumer<TagProvider>(
      builder: (context, tagProvider, child) {
        // 如果正在加载，显示加载指示器
        if (tagProvider.isLoading) {
          if (isStandalonePage) {
            return MainLayout(
              currentIndex: 2, // 标签页索引为2
              body: const Center(child: CircularProgressIndicator()),
              showFab: true,
            );
          } else {
            return const Center(child: CircularProgressIndicator());
          }
        }

        final content = RefreshIndicator(
          onRefresh: _loadTags,
          child: SafeArea(
            child: Column(
              children: [
                _buildHeader(tagProvider),
                _buildSearchBox(),
                Expanded(
                  child: _isSearching && _filteredTags.isEmpty
                      ? _buildEmptySearchResult()
                      : tagProvider.tags.isEmpty
                          ? _buildEmptyTagsView()
                          : _buildTagsList(tagProvider),
                ),
                _buildAddTagButton(),
              ],
            ),
          ),
        );

        // 如果是作为独立页面打开的，则需要包装在MainLayout中
        return isStandalonePage
            ? MainLayout(
                currentIndex: 2, // 标签页索引为2
                body: content,
                showFab: true,
              )
            : content;
      },
    );
  }

  Widget _buildHeader(TagProvider tagProvider) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.cardColor,
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.05),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _isMultiSelectMode
                    ? '已选择 ${_selectedTagIds.length} 个标签'
                    : '标签管理',
                style: theme.textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                '共 ${tagProvider.tags.length} 个标签',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
          // 编辑模式开关或多选模式操作按钮
          _isMultiSelectMode
              ? Row(
                  children: [
                    TextButton.icon(
                      onPressed: _selectAllTags,
                      icon: Icon(
                        _selectedTagIds.length == _filteredTags.length
                            ? Icons.deselect
                            : Icons.select_all,
                        size: 20,
                        color: theme.colorScheme.primary, // 保留品牌色
                      ),
                      label: Text(
                        _selectedTagIds.length == _filteredTags.length
                            ? '取消全选'
                            : '全选',
                        style: TextStyle(
                          color: theme.colorScheme.primary, // 保留品牌色
                        ),
                      ),
                    ),
                    TextButton.icon(
                      onPressed: _exitMultiSelectMode,
                      icon: Icon(
                        Icons.close,
                        size: 20,
                        color: theme.textTheme.labelLarge?.color,
                      ),
                      label: Text(
                        '取消',
                        style: TextStyle(
                          color: theme.textTheme.labelLarge?.color,
                        ),
                      ),
                    ),
                  ],
                )
              : Row(
                  children: [
                    // 清空无笔记标签按钮
                    TextButton.icon(
                      onPressed: _showCleanEmptyTagsConfirmation,
                      icon: Icon(
                        Icons.cleaning_services,
                        size: 20,
                        color: theme.iconTheme.color,
                      ),
                      label: Text(
                        '清空',
                        style: TextStyle(
                          color: theme.textTheme.labelLarge?.color,
                        ),
                      ),
                    ),
                    TextButton.icon(
                      onPressed: _enterMultiSelectMode,
                      icon: Icon(
                        Icons.checklist,
                        size: 20,
                        color: theme.iconTheme.color,
                      ),
                      label: Text(
                        '批量',
                        style: TextStyle(
                          color: theme.textTheme.labelLarge?.color,
                        ),
                      ),
                    ),
                    TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _isEditMode = !_isEditMode;
                        });
                      },
                      icon: Icon(
                        _isEditMode ? Icons.done : Icons.edit,
                        size: 20,
                        color: _isEditMode
                            ? theme.colorScheme.primary
                            : theme.iconTheme.color,
                      ),
                      label: Text(
                        _isEditMode ? '完成' : '编辑',
                        style: TextStyle(
                          color: _isEditMode
                              ? theme.colorScheme.primary
                              : theme.textTheme.labelLarge?.color,
                        ),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildSearchBox() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 0, 20, 16),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.dividerColor),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: TextField(
          controller: _searchController,
          style: theme.textTheme.bodyLarge,
          decoration: InputDecoration(
            hintText: '搜索标签...',
            hintStyle: theme.inputDecorationTheme.hintStyle,
            border: InputBorder.none,
            icon: Icon(
              Icons.search,
              color: theme.inputDecorationTheme.iconColor ??
                  theme.iconTheme.color?.withOpacity(0.6),
              size: 20,
            ),
            suffixIcon: _isSearching
                ? IconButton(
                    icon: Icon(
                      Icons.clear,
                      color: theme.inputDecorationTheme.iconColor ??
                          theme.iconTheme.color?.withOpacity(0.6),
                      size: 20,
                    ),
                    onPressed: () {
                      _searchController.clear();
                      FocusScope.of(context).unfocus();
                    },
                  )
                : null,
          ),
        ),
      ),
    );
  }

  Widget _buildTagsList(TagProvider tagProvider) {
    return _isEditMode
        ? ReorderableListView.builder(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
            itemCount: _filteredTags.length,
            onReorder: (oldIndex, newIndex) {
              setState(() {
                if (newIndex > oldIndex) {
                  newIndex -= 1;
                }
                final tag = _filteredTags.removeAt(oldIndex);
                _filteredTags.insert(newIndex, tag);

                // 更新状态管理中的顺序
                tagProvider.reorderTags(oldIndex, newIndex);
              });
            },
            itemBuilder: (context, index) {
              return _buildEditableTagItem(_filteredTags[index], index);
            },
          )
        : ListView.builder(
            padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
            itemCount: _filteredTags.length,
            itemBuilder: (context, index) {
              return _buildTagItem(_filteredTags[index]);
            },
          );
  }

  Widget _buildEmptyTagsView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.tag,
            size: 48,
            color: Theme.of(context).iconTheme.color?.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无标签',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.color
                      ?.withOpacity(0.7),
                ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击下方按钮创建新标签',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withOpacity(0.7),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptySearchResult() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 48,
            color: Theme.of(context).iconTheme.color?.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            '未找到匹配的标签',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.color
                      ?.withOpacity(0.7),
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagItem(Tag tag) {
    return Slidable(
      key: ValueKey(tag.id),
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        children: [
          SlidableAction(
            onPressed: (_) => _editTag(tag),
            backgroundColor:
                Theme.of(context).colorScheme.primary.withOpacity(0.1),
            foregroundColor: Theme.of(context).colorScheme.primary,
            icon: Icons.edit,
            label: '编辑',
          ),
          SlidableAction(
            onPressed: (_) => _showDeleteConfirmation(tag),
            backgroundColor: Colors.red.withOpacity(0.1), // 状态色背景，降低透明度
            foregroundColor: Colors.red, // 状态色前景
            icon: Icons.delete,
            label: '删除',
          ),
        ],
      ),
      child: GestureDetector(
        onLongPress: () {
          // 长按进入多选模式并选中当前标签
          if (!_isMultiSelectMode) {
            _enterMultiSelectMode();
          }
          _toggleTagSelection(tag.id);
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _isMultiSelectMode && _selectedTagIds.contains(tag.id)
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).dividerColor.withOpacity(0.5),
              width: _isMultiSelectMode && _selectedTagIds.contains(tag.id)
                  ? 2
                  : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.03),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: ListTile(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            leading: _isMultiSelectMode
                ? Checkbox(
                    value: _selectedTagIds.contains(tag.id),
                    onChanged: (selected) {
                      _toggleTagSelection(tag.id);
                    },
                    activeColor: Theme.of(context).colorScheme.primary, // 保留品牌色
                  )
                : CircleAvatar(
                    backgroundColor: tag.colorValue.withOpacity(0.2), // 标签颜色保留
                    child: Icon(
                      Icons.tag,
                      color: tag.colorValue, // 标签颜色保留
                      size: 20,
                    ),
                  ),
            title: Text(
              tag.name,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            subtitle: Text(
              '${tag.noteCount ?? 0} 条笔记',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            trailing: _isMultiSelectMode
                ? null
                : Icon(
                    Icons.chevron_right,
                    color: Theme.of(context).iconTheme.color?.withOpacity(0.6),
                  ),
            onTap: () {
              if (_isMultiSelectMode) {
                _toggleTagSelection(tag.id);
              } else {
                _navigateToTagDetail(tag);
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildEditableTagItem(Tag tag, int index) {
    final theme = Theme.of(context);
    return Dismissible(
      key: ValueKey(tag.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        color: Colors.red, // 状态色背景
        child: Icon(
          Icons.delete,
          color: ThemeData.estimateBrightnessForColor(Colors.red) ==
                  Brightness.dark
              ? Colors.white
              : Colors.black, // 智能判断前景色
        ),
      ),
      confirmDismiss: (direction) async {
        return await showDialog(
          context: context,
          builder: (context) => _buildDeleteDialog(tag),
        );
      },
      onDismissed: (direction) {
        _deleteTag(tag);
        setState(() {
          _filteredTags.removeAt(index);
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: theme.cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: theme.dividerColor.withOpacity(0.5)),
          boxShadow: [
            BoxShadow(
              color: theme.shadowColor.withOpacity(0.03),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: ListTile(
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          leading: CircleAvatar(
            backgroundColor: tag.colorValue.withOpacity(0.2), // 标签颜色保留
            child: Icon(
              Icons.tag,
              color: tag.colorValue, // 标签颜色保留
              size: 20,
            ),
          ),
          title: Text(
            tag.name,
            style: theme.textTheme.titleMedium,
          ),
          subtitle: Text(
            '${tag.noteCount ?? 0} 条笔记',
            style: theme.textTheme.bodySmall,
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: Icon(
                  Icons.edit,
                  color: theme.iconTheme.color,
                  size: 20,
                ),
                onPressed: () => _editTag(tag),
              ),
              IconButton(
                icon: const Icon(
                  Icons.delete,
                  color: Colors.red, // 状态色
                  size: 20,
                ),
                onPressed: () => _showDeleteConfirmation(tag),
              ),
              ReorderableDragStartListener(
                // 确保 ReorderableDragStartListener 在这里
                index: index,
                child: Icon(
                  Icons.drag_handle,
                  color: theme.iconTheme.color?.withOpacity(0.6),
                ),
              ),
            ],
          ),
          onTap: () {
            _editTag(tag);
          },
        ),
      ),
    );
  }

  Widget _buildAddTagButton() {
    final theme = Theme.of(context);
    return Column(
      children: [
        if (_isMultiSelectMode && _selectedTagIds.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: ElevatedButton(
              onPressed: _showBatchDeleteConfirmation,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red, // 状态色
                foregroundColor:
                    ThemeData.estimateBrightnessForColor(Colors.red) ==
                            Brightness.dark
                        ? Colors.white
                        : Colors.black, // 智能判断前景色
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.delete_forever),
                  const SizedBox(width: 8),
                  Text(
                    '删除所选 (${_selectedTagIds.length})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        if (!_isMultiSelectMode)
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: ElevatedButton(
              onPressed: _showAddTagDialog,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                  Icon(Icons.add),
                  SizedBox(width: 8),
                  Text(
                    '创建新标签',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  void _showAddTagDialog() {
    final TextEditingController controller = TextEditingController();
    Color selectedColor = _availableColors[0];

    showDialog(
      context: context,
      builder: (context) {
        final theme = Theme.of(context);
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            title: Text('添加新标签', style: theme.dialogTheme.titleTextStyle),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: controller,
                    autofocus: true,
                    style: theme.textTheme.bodyLarge,
                    decoration: InputDecoration(
                      hintText: '请输入标签名称',
                      hintStyle: theme.inputDecorationTheme.hintStyle,
                      border: const OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: theme.colorScheme.primary),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text('选择标签颜色:', style: theme.textTheme.titleSmall),
                  const SizedBox(height: 8),
                  // 颜色选择器
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _availableColors.map((color) {
                      final bool isSelected = selectedColor == color;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedColor = color;
                          });
                        },
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? theme.colorScheme.primary
                                  : theme.dividerColor.withOpacity(0.5),
                              width: 2,
                            ),
                            boxShadow: [
                              if (isSelected)
                                BoxShadow(
                                  color: theme.shadowColor.withOpacity(0.3),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                ),
                            ],
                          ),
                          child: isSelected
                              ? Icon(
                                  Icons.check,
                                  color: ThemeData.estimateBrightnessForColor(
                                              selectedColor) ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                                  size: 20,
                                )
                              : null,
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('取消',
                    style: TextStyle(color: theme.textTheme.bodySmall?.color)),
              ),
              ElevatedButton(
                onPressed: () {
                  final newTagName = controller.text.trim();
                  if (newTagName.isNotEmpty) {
                    _addNewTag(newTagName, selectedColor);
                    Navigator.pop(context);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                ),
                child: const Text('确定'),
              ),
            ],
          );
        });
      },
    );
  }

  void _editTag(Tag tag) {
    final TextEditingController controller =
        TextEditingController(text: tag.name);
    Color selectedColor = tag.colorValue;

    showDialog(
      context: context,
      builder: (context) {
        final theme = Theme.of(context);
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            title: Text('编辑标签', style: theme.dialogTheme.titleTextStyle),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextField(
                    controller: controller,
                    autofocus: true,
                    style: theme.textTheme.bodyLarge,
                    decoration: InputDecoration(
                      hintText: '请输入标签名称',
                      hintStyle: theme.inputDecorationTheme.hintStyle,
                      border: const OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide:
                            BorderSide(color: theme.colorScheme.primary),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text('选择标签颜色:', style: theme.textTheme.titleSmall),
                  const SizedBox(height: 8),
                  // 颜色选择器
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _availableColors.map((color) {
                      final bool isSelected = selectedColor == color;
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedColor = color;
                          });
                        },
                        child: Container(
                          width: 36,
                          height: 36,
                          decoration: BoxDecoration(
                            color: color,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? theme.colorScheme.primary
                                  : theme.dividerColor.withOpacity(0.5),
                              width: 2,
                            ),
                            boxShadow: [
                              if (isSelected)
                                BoxShadow(
                                  color: theme.shadowColor.withOpacity(0.3),
                                  blurRadius: 4,
                                  spreadRadius: 1,
                                ),
                            ],
                          ),
                          child: isSelected
                              ? Icon(
                                  Icons.check,
                                  color: ThemeData.estimateBrightnessForColor(
                                              selectedColor) ==
                                          Brightness.dark
                                      ? Colors.white
                                      : Colors.black,
                                  size: 20,
                                )
                              : null,
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('取消',
                    style: TextStyle(color: theme.textTheme.bodySmall?.color)),
              ),
              ElevatedButton(
                onPressed: () {
                  final newName = controller.text.trim();
                  if (newName.isNotEmpty) {
                    _updateTag(tag, newName, selectedColor);
                    Navigator.pop(context);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                ),
                child: const Text('保存'),
              ),
            ],
          );
        });
      },
    );
  }

  Widget _buildDeleteDialog(Tag tag) {
    final theme = Theme.of(context);
    return AlertDialog(
      title: Text('删除标签', style: theme.dialogTheme.titleTextStyle),
      content: Text('确定要删除"${tag.name}"标签吗？与该标签关联的笔记将保留，但标签会被移除。',
          style: theme.dialogTheme.contentTextStyle),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text('取消',
              style: TextStyle(color: theme.textTheme.bodySmall?.color)),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(true);
          },
          style: TextButton.styleFrom(
            foregroundColor: Colors.red, // 状态色
          ),
          child: const Text('删除'),
        ),
      ],
    );
  }

  void _showDeleteConfirmation(Tag tag) {
    showDialog(
      context: context,
      builder: (context) => _buildDeleteDialog(tag),
    ).then((confirmed) {
      if (confirmed == true) {
        _deleteTag(tag);
      }
    });
  }

  void _navigateToTagDetail(Tag tag) {
    Navigator.pushNamed(
      context,
      AppRoutes.tagDetail,
      arguments: tag.id,
    ).then((result) {
      // 检查组件是否已经被卸载
      if (!mounted) return;

      // 检查返回的结果并更新笔记数量
      if (result != null && result is Map<String, dynamic>) {
        final tagId = result['tagId'] as String?;
        final noteCount = result['noteCount'] as int?;
        final refreshAllTags = result['refreshAllTags'] as bool?;

        // 如果需要刷新所有标签，则重新加载标签数据
        if (refreshAllTags == true) {
          print('DEBUG: 从标签详情页返回，刷新所有标签数据');
          _loadTags(); // 重新加载所有标签数据
        }
        // 如果不需要刷新所有标签，但有特定标签的笔记数量更新
        else if (tagId != null && noteCount != null) {
          // 更新特定标签的笔记数量
          final tagProvider = Provider.of<TagProvider>(context, listen: false);
          final index = tagProvider.tags.indexWhere((t) => t.id == tagId);

          if (index != -1) {
            final updatedTag =
                tagProvider.tags[index].copyWith(noteCount: noteCount);
            tagProvider.updateTagInList(index, updatedTag);
            _filterTags(); // 刷新过滤后的标签列表显示
          }
        }
      }
    });
  }

  Future<void> _addNewTag(String name, Color color) async {
    // 将Flutter的Color转换为十六进制字符串
    String colorHex = '#${color.value.toRadixString(16).substring(2)}';

    final success = await Provider.of<TagProvider>(context, listen: false)
        .createTag(name, color: colorHex);

    // 检查组件是否已经被卸载
    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('标签"$name"已创建')),
      );
      // 刷新过滤后的标签列表
      _filterTags();
    } else {
      final errorMsg =
          Provider.of<TagProvider>(context, listen: false).errorMessage;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMsg ?? '创建标签失败')),
      );
    }
  }

  Future<void> _updateTag(Tag tag, String newName, Color newColor) async {
    // 将Flutter的Color转换为十六进制字符串
    String colorHex = '#${newColor.value.toRadixString(16).substring(2)}';

    final success = await Provider.of<TagProvider>(context, listen: false)
        .updateTag(tag.id, name: newName, color: colorHex);

    // 检查组件是否已经被卸载
    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('标签"$newName"已更新')),
      );
      // 刷新过滤后的标签列表
      _filterTags();
    } else {
      final errorMsg =
          Provider.of<TagProvider>(context, listen: false).errorMessage;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMsg ?? '更新标签失败')),
      );
    }
  }

  Future<void> _deleteTag(Tag tag) async {
    final success = await Provider.of<TagProvider>(context, listen: false)
        .deleteTag(tag.id);

    // 检查组件是否已经被卸载
    if (!mounted) return;

    if (success) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('标签"${tag.name}"已删除')),
      );
      // 刷新过滤后的标签列表
      _filterTags();
    } else {
      final errorMsg =
          Provider.of<TagProvider>(context, listen: false).errorMessage;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMsg ?? '删除标签失败')),
      );
    }
  }

  void _showBatchDeleteConfirmation() {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('批量删除标签', style: theme.dialogTheme.titleTextStyle),
        content: Text(
            '确定要删除选中的 ${_selectedTagIds.length} 个标签吗？与这些标签关联的笔记将保留，但标签会被移除。',
            style: theme.dialogTheme.contentTextStyle),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消',
                style: TextStyle(color: theme.textTheme.bodySmall?.color)),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _batchDeleteTags();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red, // 状态色
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showCleanEmptyTagsConfirmation() {
    final theme = Theme.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('清空无笔记标签', style: theme.dialogTheme.titleTextStyle),
        content: Text('系统将检查每个标签的笔记数量，并只删除确实没有关联任何笔记的标签。此操作不可撤销，但不会影响有笔记的标签。',
            style: theme.dialogTheme.contentTextStyle),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('取消',
                style: TextStyle(color: theme.textTheme.bodySmall?.color)),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _cleanEmptyTags();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red, // 状态色
            ),
            child: const Text('清空'),
          ),
        ],
      ),
    );
  }

  Future<void> _batchDeleteTags() async {
    // 检查组件是否已经被卸载
    if (!mounted) return;

    // 显示加载指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    final tagProvider = Provider.of<TagProvider>(context, listen: false);

    // 批量删除标签
    final results = await tagProvider.batchDeleteTags(_selectedTagIds.toList());

    // 检查组件是否已经被卸载
    if (!mounted) return;

    // 关闭加载指示器
    Navigator.of(context).pop();

    // 计算成功和失败的数量
    final successCount = results.values.where((success) => success).length;
    final failureCount = results.values.where((success) => !success).length;

    // 显示结果
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(failureCount > 0
            ? '已删除 $successCount 个标签，$failureCount 个标签删除失败'
            : '已成功删除 $successCount 个标签'),
        duration: const Duration(seconds: 3),
      ),
    );

    // 刷新标签列表并退出多选模式
    _exitMultiSelectMode();
    _filterTags();
  }

  Future<void> _cleanEmptyTags() async {
    // 检查组件是否已经被卸载
    if (!mounted) return;

    // 显示加载指示器，并提示用户这可能需要一些时间
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('正在处理'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: const [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在检查每个标签的笔记数量并清理无笔记标签，这可能需要一些时间...'),
          ],
        ),
      ),
    );

    final tagProvider = Provider.of<TagProvider>(context, listen: false);

    // 清空无笔记标签
    final results = await tagProvider.cleanEmptyTags();

    // 检查组件是否已经被卸载
    if (!mounted) return;

    // 关闭加载指示器
    Navigator.of(context).pop();

    // 获取结果数据
    final int total = results['total'] ?? 0;
    final int success = results['success'] ?? 0;
    final int failed = results['failed'] ?? 0;

    // 显示结果
    if (total == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有找到无笔记的标签'),
          duration: Duration(seconds: 3),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(failed > 0
              ? '已清空 $success 个无笔记标签，$failed 个标签清空失败'
              : '已成功清空 $success 个无笔记标签'),
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // 刷新标签列表
    _filterTags();
  }
}
