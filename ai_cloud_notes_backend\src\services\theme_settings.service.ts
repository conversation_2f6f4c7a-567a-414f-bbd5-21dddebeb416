import mongoose from 'mongoose';
import ThemeSettings, { IThemeSettings, ThemeMode } from '../models/theme_settings.model';
import { logger } from '../utils/logger';

/**
 * 主题设置服务类
 */
class ThemeSettingsService {
  /**
   * 获取用户的主题设置
   * @param userId 用户ID
   * @returns 用户主题设置
   */
  async getUserThemeSettings(userId: string): Promise<IThemeSettings | null> {
    try {
      // 查找用户的主题设置
      const userThemeSettings = await ThemeSettings.findOne({ userId: new mongoose.Types.ObjectId(userId) });
      
      // 如果不存在，则创建默认主题设置
      if (!userThemeSettings) {
        const defaultSettings = await this.createDefaultThemeSettings(userId);
        return defaultSettings;
      }
      
      return userThemeSettings;
    } catch (error) {
      logger.error(`获取用户主题设置失败: ${error}`);
      throw error;
    }
  }

  /**
   * 更新用户主题设置
   * @param userId 用户ID
   * @param settings 主题设置数据
   * @returns 更新后的主题设置
   */
  async updateUserThemeSettings(userId: string, settings: Partial<IThemeSettings>): Promise<IThemeSettings | null> {
    try {
      // 更新用户主题设置，如果不存在则创建
      const updatedSettings = await ThemeSettings.findOneAndUpdate(
        { userId: new mongoose.Types.ObjectId(userId) },
        { 
          $set: {
            ...settings,
            updatedAt: new Date()
          } 
        },
        { new: true, upsert: true }
      );
      
      return updatedSettings;
    } catch (error) {
      logger.error(`更新用户主题设置失败: ${error}`);
      throw error;
    }
  }

  /**
   * 创建默认主题设置
   * @param userId 用户ID
   * @returns 默认主题设置
   */
  private async createDefaultThemeSettings(userId: string): Promise<IThemeSettings> {
    try {
      const defaultSettings = new ThemeSettings({
        userId: new mongoose.Types.ObjectId(userId),
        themeMode: ThemeMode.SYSTEM,
        fontColor: '#000000',
        textSize: 16.0,
        titleSize: 18.0,
        updatedAt: new Date()
      });
      
      await defaultSettings.save();
      return defaultSettings;
    } catch (error) {
      logger.error(`创建默认主题设置失败: ${error}`);
      throw error;
    }
  }
}

export default new ThemeSettingsService(); 