import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/screens/profile/profile_edit_page.dart';
import 'package:ai_cloud_notes/screens/settings/theme_settings_page.dart';
import 'package:ai_cloud_notes/screens/settings/ai_settings_page.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/widgets/main_layout.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    debugPrint('[SettingsPage] build called. Theme - Brightness: ${Theme.of(context).brightness}, ScaffoldBG: ${Theme.of(context).scaffoldBackgroundColor}');
    // 检查当前路由，判断是否是作为独立页面打开的
    final currentRoute = ModalRoute.of(context)?.settings.name;
    final isStandalonePage = currentRoute == AppRoutes.settings;

    final content = SafeArea(
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildAccountSection(context),
                    const SizedBox(height: 16),
                    _buildAppSection(context),
                    const SizedBox(height: 16),
                    _buildOtherSection(context),
                    const SizedBox(height: 24),
                    _buildLogoutButton(context),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );

    // 如果是作为独立页面打开的，则需要包装在MainLayout中
    if (isStandalonePage) {
      return MainLayout(
        currentIndex: 3, // 设置页索引为3
        body: content,
        showFab: false, // 设置页不需要显示新建笔记按钮
      );
    } else {
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: content,
      );
    }
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).appBarTheme.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '设置',
            style: TextStyle(
              fontSize: Theme.of(context).appBarTheme.titleTextStyle?.fontSize ?? 18,
              fontWeight: Theme.of(context).appBarTheme.titleTextStyle?.fontWeight ?? FontWeight.bold,
              color: Theme.of(context).appBarTheme.titleTextStyle?.color,
            ),
          ),
          const Spacer(),
          IconButton(
            icon: Icon(
              Icons.help_outline,
              color: Theme.of(context).iconTheme.color,
            ),
            onPressed: () {
              // 显示帮助信息
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context) {
    return _buildSection(
      title: '账户',
      context: context,
      children: [
        _buildSettingsItem(
          context: context,
          icon: Icons.person,
          iconBackground: AppTheme.primaryColor.withOpacity(0.1),
          iconColor: AppTheme.primaryColor,
          title: '个人资料',
          subtitle: '编辑您的个人信息',
          onTap: () {
            Navigator.pushNamed(context, AppRoutes.profile);
          },
        ),
        _buildSettingsItem(
          context: context,
          icon: Icons.key,
          iconBackground: const Color(0xFFFFFBEB),
          iconColor: const Color(0xFFF59E0B),
          title: '修改密码',
          subtitle: '更新您的账户密码',
          onTap: () {
            Navigator.pushNamed(context, AppRoutes.changePassword);
          },
          showDivider: false,
        ),
      ],
    );
  }

  Widget _buildAppSection(BuildContext context) {
    return _buildSection(
      title: '应用',
      context: context,
      children: [
        _buildSettingsItem(
          context: context,
          icon: Icons.palette,
          iconBackground: const Color(0xFFF0F9FF),
          iconColor: const Color(0xFF0EA5E9),
          title: '主题与外观',
          subtitle: '自定义应用界面',
          onTap: () {
            Navigator.pushNamed(context, AppRoutes.themeSettings);
          },
        ),
        _buildSettingsItem(
          context: context,
          icon: Icons.smart_toy,
          iconBackground: const Color(0xFFF5F3FF),
          iconColor: const Color(0xFF8B5CF6),
          title: 'AI设置',
          subtitle: '配置智能预测功能',
          onTap: () {
            Navigator.pushNamed(context, AppRoutes.aiSettings);
          },
        ),
        _buildSettingsItem(
          context: context,
          icon: Icons.cloud,
          iconBackground: const Color(0xFFEFF6FF),
          iconColor: const Color(0xFF3B82F6),
          title: '同步设置',
          subtitle: '配置云同步选项',
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('同步设置功能暂未实现')),
            );
          },
        ),
        _buildSettingsItem(
          context: context,
          icon: Icons.language,
          iconBackground: const Color(0xFFF0FDFA),
          iconColor: const Color(0xFF14B8A6),
          title: '语言',
          subtitle: '选择应用界面语言',
          trailing: Text(
            '简体中文',
            style: TextStyle(
              color: Theme.of(context).textTheme.bodySmall?.color ?? Colors.black54,
              fontSize: Theme.of(context).textTheme.bodySmall?.fontSize ?? 14,
            ),
          ),
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('语言设置功能暂未实现')),
            );
          },
          showDivider: false,
        ),
      ],
    );
  }

  Widget _buildOtherSection(BuildContext context) {
    return _buildSection(
      title: '其他',
      context: context,
      children: [
        _buildSettingsItem(
          context: context,
          icon: Icons.help,
          iconBackground: const Color(0xFFF3F4F6),
          iconColor: const Color(0xFF6B7280),
          title: '帮助与反馈',
          subtitle: '获取支持和提交问题',
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('帮助与反馈功能暂未实现')),
            );
          },
        ),
        _buildSettingsItem(
          context: context,
          icon: Icons.info,
          iconBackground: const Color(0xFFF3F4F6),
          iconColor: const Color(0xFF6B7280),
          title: '关于',
          subtitle: '版本信息与开源协议',
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('关于页面暂未实现')),
            );
          },
          showDivider: false,
        ),
      ],
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return Card(
      color: Theme.of(context).cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(12),
        child: InkWell(
          onTap: () => _showLogoutDialog(context),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppTheme.dangerColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.logout,
                    color: AppTheme.dangerColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '退出登录',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.dangerColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '安全退出当前账号',
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
    required BuildContext context,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 8),
          child: Text(
            title,
            style: TextStyle(
              fontSize: Theme.of(context).textTheme.titleSmall?.fontSize ?? 14,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.titleSmall?.color,
            ),
          ),
        ),
        Card(
          color: Theme.of(context).cardColor,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem({
    required BuildContext context,
    required IconData icon,
    required Color iconBackground,
    required Color iconColor,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    Widget? trailing,
    bool showDivider = true,
  }) {
    return Column(
      children: [
        Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: iconBackground,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: iconColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: Theme.of(context).textTheme.titleMedium?.fontSize ?? 16,
                            fontWeight: FontWeight.w600,
                            color: Theme.of(context).textTheme.titleMedium?.color,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: Theme.of(context).textTheme.bodySmall?.fontSize ?? 14,
                            color: Theme.of(context).textTheme.bodySmall?.color,
                          ),
                        ),
                      ],
                    ),
                  ),
                  trailing ??
                      Icon(
                        Icons.chevron_right,
                        color: Theme.of(context).iconTheme.color?.withOpacity(0.6) ?? AppTheme.mediumGrayColor,
                        size: 20,
                      ),
                ],
              ),
            ),
          ),
        ),
        if (showDivider)
          Divider(
            color: Theme.of(context).dividerColor,
            height: 1,
            indent: 72,
          ),
      ],
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('退出登录'),
          content: const Text('确定要退出当前账号吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                _logout(context);
              },
              child: Text(
                '退出',
                style: TextStyle(color: AppTheme.dangerColor),
              ),
            ),
          ],
        );
      },
    );
  }

  void _logout(BuildContext context) async {
    try {
      // 显示加载提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('正在退出登录...')),
      );

      // 获取AuthProvider实例
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // 调用登出方法
      await authProvider.logout();

      // 导航到登录页面
      if (context.mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          AppRoutes.login,
          (route) => false,
        );
      }
    } catch (e) {
      // 显示错误提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('退出登录失败: $e')),
        );
      }
    }
  }
}