import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Widget Sample Test', () {
    testWidgets('should display a simple text widget', (WidgetTester tester) async {
      // 构建一个简单的Widget
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Center(
              child: Text('Hello, Widget Test!'),
            ),
          ),
        ),
      );

      // 查找文本Widget并验证其内容
      expect(find.text('Hello, Widget Test!'), findsOneWidget);
    });
  });
}