import 'package:flutter/foundation.dart';

/// URL辅助工具类
class UrlHelper {
  /// 后端API基础URL
  static const String apiBaseUrl = 'http://localhost:8080/api';

  /// 后端服务器基础URL
  static const String serverBaseUrl = 'http://localhost:8080';

  /// 获取完整的头像URL
  ///
  /// 如果是相对路径，会自动添加服务器基础URL
  /// 如果已经是完整URL，则直接返回
  static String getFullAvatarUrl(String? avatarUrl) {
    if (avatarUrl == null || avatarUrl.isEmpty) {
      return '';
    }

    // 如果已经是完整URL，则直接返回
    if (avatarUrl.startsWith('http')) {
      return avatarUrl;
    }

    // 处理上传目录路径，确保使用后端服务器地址
    if (avatarUrl.startsWith('/uploads/')) {
      return '$serverBaseUrl$avatarUrl';
    }

    // 处理其他相对路径
    if (avatarUrl.startsWith('/')) {
      return '$serverBaseUrl$avatarUrl';
    } else {
      return '$serverBaseUrl/$avatarUrl';
    }
  }

  /// 获取完整的图片URL
  ///
  /// 如果是相对路径，会自动添加服务器基础URL
  /// 如果已经是完整URL，则直接返回
  static String getFullImageUrl(String? imageUrl) {
    if (imageUrl == null || imageUrl.isEmpty) {
      return '';
    }

    // 如果已经是完整URL，则直接返回
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }

    // 处理上传目录路径，确保使用后端服务器地址
    if (imageUrl.startsWith('/uploads/')) {
      return '$serverBaseUrl$imageUrl';
    }

    // 处理其他相对路径
    if (imageUrl.startsWith('/')) {
      return '$serverBaseUrl$imageUrl';
    } else {
      return '$serverBaseUrl/$imageUrl';
    }
  }

  /// 检查URL是否有效
  static bool isValidUrl(String? url) {
    if (url == null || url.isEmpty) {
      return false;
    }

    // 简单检查URL格式是否有效
    try {
      final uri = Uri.parse(url);
      return uri.scheme.isNotEmpty && uri.host.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('URL无效: $url, 错误: $e');
      }
      return false;
    }
  }
}