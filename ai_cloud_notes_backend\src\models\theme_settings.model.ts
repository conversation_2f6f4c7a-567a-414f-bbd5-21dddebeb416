import mongoose, { Document, Schema } from 'mongoose';

/**
 * 主题模式枚举
 */
export enum ThemeMode {
  SYSTEM = 0,
  LIGHT = 1,
  DARK = 2
}

/**
 * 主题设置接口
 */
export interface IThemeSettings extends Document {
  userId: mongoose.Types.ObjectId;
  themeMode: ThemeMode;
  fontColor: string;
  textSize: number;
  titleSize: number;
  updatedAt: Date;
}

/**
 * 主题设置模式
 */
const ThemeSettingsSchema: Schema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  themeMode: {
    type: Number,
    enum: [0, 1, 2],
    default: 0,
    required: true
  },
  fontColor: {
    type: String,
    default: '#000000',
    required: true
  },
  textSize: {
    type: Number,
    default: 16.0,
    required: true
  },
  titleSize: {
    type: Number,
    default: 18.0,
    required: true
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// 导出模型
export default mongoose.model<IThemeSettings>('ThemeSettings', ThemeSettingsSchema); 