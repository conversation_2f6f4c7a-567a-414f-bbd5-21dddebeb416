import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/providers/tag_provider.dart';
import 'package:ai_cloud_notes/providers/user_provider.dart';
import 'package:ai_cloud_notes/providers/search_provider.dart';
import 'package:ai_cloud_notes/providers/theme_service.dart';
import 'package:ai_cloud_notes/providers/ai_provider.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import '../mocks/mock_api_service.dart';

/// 测试应用包装器
/// 提供所有必要的Provider和Mock服务，用于测试环境
class TestApp extends StatelessWidget {
  final Widget? home;
  final String? initialRoute;
  final Map<String, WidgetBuilder>? routes;
  final MockApiService? mockApiService;
  final AuthProvider? mockAuthProvider;
  final NoteProvider? mockNoteProvider;
  final TagProvider? mockTagProvider;
  final UserProvider? mockUserProvider;
  final SearchProvider? mockSearchProvider;
  final ThemeService? mockThemeService;
  final AIProvider? mockAIProvider;

  const TestApp({
    Key? key,
    this.home,
    this.initialRoute,
    this.routes,
    this.mockApiService,
    this.mockAuthProvider,
    this.mockNoteProvider,
    this.mockTagProvider,
    this.mockUserProvider,
    this.mockSearchProvider,
    this.mockThemeService,
    this.mockAIProvider,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final apiService = mockApiService ?? MockApiService();
    final authProvider = mockAuthProvider ?? AuthProvider(apiService);
    final noteProvider = mockNoteProvider ?? NoteProvider();
    final tagProvider = mockTagProvider ?? TagProvider();
    final userProvider = mockUserProvider ?? UserProvider();
    final searchProvider = mockSearchProvider ?? SearchProvider();
    final themeService = mockThemeService ?? ThemeService();
    final aiProvider = mockAIProvider ?? AIProvider();

    return MultiProvider(
      providers: [
        Provider<MockApiService>.value(value: apiService),
        ChangeNotifierProvider<AuthProvider>.value(value: authProvider),
        ChangeNotifierProvider<NoteProvider>.value(value: noteProvider),
        ChangeNotifierProvider<TagProvider>.value(value: tagProvider),
        ChangeNotifierProvider<UserProvider>.value(value: userProvider),
        ChangeNotifierProvider<SearchProvider>.value(value: searchProvider),
        ChangeNotifierProvider<ThemeService>.value(value: themeService),
        ChangeNotifierProvider<AIProvider>.value(value: aiProvider),
      ],
      child: Consumer<ThemeService>(
        builder: (context, themeService, _) {
          return MaterialApp(
            title: '智云笔记测试',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.getDynamicThemeData(themeService, Brightness.light),
            darkTheme: AppTheme.getDynamicThemeData(themeService, Brightness.dark),
            themeMode: themeService.themeMode,
            home: home,
            initialRoute: initialRoute,
            routes: routes ?? AppRoutes.routes,
            onGenerateRoute: AppRoutes.generateRoute,
          );
        },
      ),
    );
  }
}

/// 简化的测试应用包装器，用于单个Widget测试
class SimpleTestApp extends StatelessWidget {
  final Widget child;
  final ThemeData? theme;

  const SimpleTestApp({
    Key? key,
    required this.child,
    this.theme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '简单测试应用',
      debugShowCheckedModeBanner: false,
      theme: theme ?? AppTheme.fixedLightTheme,
      home: Scaffold(body: child),
    );
  }
}

/// 带Provider的简单测试应用包装器
class ProviderTestApp extends StatelessWidget {
  final Widget child;
  final List<ChangeNotifierProvider> providers;
  final ThemeData? theme;

  const ProviderTestApp({
    Key? key,
    required this.child,
    required this.providers,
    this.theme,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: providers,
      child: MaterialApp(
        title: 'Provider测试应用',
        debugShowCheckedModeBanner: false,
        theme: theme ?? AppTheme.fixedLightTheme,
        home: Scaffold(body: child),
      ),
    );
  }
}
