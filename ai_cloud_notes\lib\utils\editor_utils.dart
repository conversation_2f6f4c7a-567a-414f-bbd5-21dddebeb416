// editor_utils.dart - 用于存放从 editor_page.dart 提取的通用工具函数

import 'package:flutter/material.dart'; // 导入必要的Flutter包
import 'dart:math'; // 导入math包，支持min函数

// 处理Markdown内容以正确渲染
String processMarkdownForRendering(String content) {
  print('DEBUG_UTILS: [Step 1] 原始内容长度: ${content.length}');
  print(
      'DEBUG_UTILS: [Step 1] 原始内容前200字符: "${content.length > 200 ? content.substring(0, 200) : content}"');

  // 先保护特殊结构，防止被换行处理破坏
  // 1. 保护表格
  List<String> tables = [];
  final tableRegex = RegExp(
      r'\|[^\n]*\|[\s]*\n[\s]*\|[-:| ]+\|[\s]*\n([\s]*\|[^\n]*\|[\s]*\n)*',
      multiLine: true);

  content = content.replaceAllMapped(tableRegex, (match) {
    tables.add(match.group(0)!);
    print(
        'DEBUG_UTILS: [Step 2] 找到表格 ${tables.length - 1}: "${match.group(0)!.replaceAll('\n', '\\n')}"');
    return "TABLE_PLACEHOLDER_${tables.length - 1}";
  });
  print('DEBUG_UTILS: [Step 2] 表格保护后内容: "${content.replaceAll('\n', '\\n')}"');

  // 2. 保护代码块
  List<String> codeBlocks = [];
  // 使用更精确的正则表达式来匹配代码块
  // 匹配```开始和```结束的代码块，包括可能的语言标记
  // 使用非贪婪模式匹配，确保正确匹配嵌套的代码块
  final codeBlockRegex =
      RegExp(r'```([a-zA-Z0-9]*)\s*[\s\S]*?```', multiLine: true);

  content = content.replaceAllMapped(codeBlockRegex, (match) {
    String codeBlock = match.group(0)!;
    String? language = match.group(1);
    print(
        'DEBUG_UTILS: [Step 3] 找到代码块 ${codeBlocks.length}: ${codeBlock.length} 字节, 语言: $language');
    print(
        'DEBUG_UTILS: [Step 3] 代码块内容: "${codeBlock.replaceAll('\n', '\\n')}"');
    codeBlocks.add(codeBlock);
    return "CODE_PLACEHOLDER_${codeBlocks.length - 1}";
  });
  print('DEBUG_UTILS: [Step 3] 代码块保护后内容: "${content.replaceAll('\n', '\\n')}"');

  // 3. 保护列表
  List<String> lists = [];
  final listRegex =
      RegExp(r'(^|\n)([ ]*[-*+][ ].+[\s\S]*?)(?=\n[^ -]|$)', multiLine: true);

  content = content.replaceAllMapped(listRegex, (match) {
    lists.add(match.group(0)!);
    return "LIST_PLACEHOLDER_${lists.length - 1}";
  });

  // 4. 保护有序列表
  List<String> orderedLists = [];
  final orderedListRegex =
      RegExp(r'(^|\n)([ ]*\d+\.[ ].+[\s\S]*?)(?=\n[^\d]|$)', multiLine: true);

  content = content.replaceAllMapped(orderedListRegex, (match) {
    orderedLists.add(match.group(0)!);
    return "ORDERED_LIST_PLACEHOLDER_${orderedLists.length - 1}";
  });

  // 5. 保护空行序列 - 暂时禁用，因为与其他处理步骤冲突
  List<String> emptyLines = [];
  // 注释掉空行占位符处理，避免与其他步骤冲突导致占位符恢复失败
  // final emptyLineRegex = RegExp(r'\n{2,}', multiLine: true);
  // content = content.replaceAllMapped(emptyLineRegex, (match) {
  //   emptyLines.add(match.group(0)!);
  //   return "EMPTY_LINE_PLACEHOLDER_${emptyLines.length - 1}";
  // });
  print('DEBUG_UTILS: [Step 5] 跳过空行占位符处理（已禁用）');

  // 处理换行，确保每个段落之间有空行
  // 对于标准 Markdown 渲染，我们需要确保段落之间有空行
  // 将单个换行替换为两个换行，但保留空行占位符
  // 注意：这一步现在移到所有占位符恢复之后，或者根据 MarkdownBody 的 softLineBreak 行为决定是否需要。
  // content = content.replaceAll('\n', '\n\n'); // 暂时注释掉，后续评估

  // 恢复特殊结构
  print(
      'DEBUG_UTILS: [Step 6] 开始恢复占位符，当前内容: "${content.replaceAll('\n', '\\n')}"');

  // 5. 恢复空行
  print('DEBUG_UTILS: [Step 6.5] 恢复 ${emptyLines.length} 个空行序列');
  for (int i = 0; i < emptyLines.length; i++) {
    String placeholder = "EMPTY_LINE_PLACEHOLDER_$i";
    if (content.contains(placeholder)) {
      content = content.replaceAll(placeholder, emptyLines[i]);
      print('DEBUG_UTILS: [Step 6.5] 成功恢复空行序列 $i');
    } else {
      print('DEBUG_UTILS: [Step 6.5] 警告：找不到空行占位符 $placeholder');
    }
  }

  // 4. 恢复有序列表
  print('DEBUG_UTILS: [Step 6.4] 恢复 ${orderedLists.length} 个有序列表');
  for (int i = 0; i < orderedLists.length; i++) {
    String placeholder = "ORDERED_LIST_PLACEHOLDER_$i";
    if (content.contains(placeholder)) {
      content = content.replaceAll(placeholder, orderedLists[i]);
      print('DEBUG_UTILS: [Step 6.4] 成功恢复有序列表 $i');
    } else {
      print('DEBUG_UTILS: [Step 6.4] 警告：找不到有序列表占位符 $placeholder');
    }
  }

  // 3. 恢复列表
  print('DEBUG_UTILS: [Step 6.3] 恢复 ${lists.length} 个列表');
  for (int i = 0; i < lists.length; i++) {
    String placeholder = "LIST_PLACEHOLDER_$i";
    if (content.contains(placeholder)) {
      content = content.replaceAll(placeholder, lists[i]);
      print('DEBUG_UTILS: [Step 6.3] 成功恢复列表 $i');
    } else {
      print('DEBUG_UTILS: [Step 6.3] 警告：找不到列表占位符 $placeholder');
    }
  }

  // 2. 恢复代码块
  print('DEBUG_UTILS: [Step 6.2] 恢复 ${codeBlocks.length} 个代码块');
  for (int i = 0; i < codeBlocks.length; i++) {
    String placeholder = "CODE_PLACEHOLDER_$i";
    if (content.contains(placeholder)) {
      content = content.replaceAll(placeholder, codeBlocks[i]);
      print('DEBUG_UTILS: [Step 6.2] 成功恢复代码块 $i');
    } else {
      print('DEBUG_UTILS: [Step 6.2] 警告：找不到代码块占位符 $placeholder');
    }
  }

  // 1. 恢复表格
  print('DEBUG_UTILS: [Step 6.1] 恢复 ${tables.length} 个表格');
  for (int i = 0; i < tables.length; i++) {
    String placeholder = "TABLE_PLACEHOLDER_$i";
    if (content.contains(placeholder)) {
      content = content.replaceAll(placeholder, tables[i]);
      print('DEBUG_UTILS: [Step 6.1] 成功恢复表格 $i');
    } else {
      print('DEBUG_UTILS: [Step 6.1] 警告：找不到表格占位符 $placeholder');
    }
  }

  print(
      'DEBUG_UTILS: [Step 6] 占位符恢复完成，当前内容: "${content.replaceAll('\n', '\\n')}"');

  // 确保以换行结尾
  if (!content.endsWith('\n')) {
    content += '\n';
  }

  // 移除可能导致问题的尾部数字（0-9）
  for (int i = 0; i <= 9; i++) {
    if (content.endsWith('$i\n')) {
      content = content.substring(0, content.length - 2) + '\n';
      break;
    }
  }

  // 移除任何单独的数字（0-9）- 这些可能是占位符恢复失败后的残留
  for (int i = 0; i <= 9; i++) {
    content = content.replaceAll(RegExp(r'\n$i\n'), '\n\n');
    content = content.replaceAll(RegExp(r'^$i\n'), '\n'); // 处理开头的数字
  }

  print('DEBUG: [EditorPage] 处理后的渲染内容长度: ${content.length}');
  return content;
}

// 递归查找Map中的值，处理各种嵌套结构
String? findValueInMap(Map<String, dynamic> map, String targetKey) {
  // 直接检查当前级别
  if (map.containsKey(targetKey)) {
    final value = map[targetKey];
    if (value is String) {
      return value;
    }
  }

  // 递归检查所有Map类型的值
  for (final entry in map.entries) {
    final value = entry.value;

    // 如果值是Map，递归检查
    if (value is Map<String, dynamic>) {
      final result = findValueInMap(value, targetKey);
      if (result != null) {
        return result;
      }
    }
    // 如果值是Map但类型不完全匹配，尝试转换
    else if (value is Map) {
      try {
        final convertedMap = Map<String, dynamic>.from(value);
        final result = findValueInMap(convertedMap, targetKey);
        if (result != null) {
          return result;
        }
      } catch (e) {
        // 转换失败，忽略并继续
      }
    }
    // 如果值是List，检查List中的每个Map
    else if (value is List) {
      for (final item in value) {
        if (item is Map<String, dynamic>) {
          final result = findValueInMap(item, targetKey);
          if (result != null) {
            return result;
          }
        } else if (item is Map) {
          try {
            final convertedMap = Map<String, dynamic>.from(item);
            final result = findValueInMap(convertedMap, targetKey);
            if (result != null) {
              return result;
            }
          } catch (e) {
            // 转换失败，忽略并继续
          }
        }
      }
    }
  }

  // 没有找到目标键
  return null;
}
