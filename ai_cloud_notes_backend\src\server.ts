import http from 'http';
import app from './app';
import { config } from './config';
import { logger } from './utils/logger';

/**
 * 创建HTTP服务器
 */
const server = http.createServer(app);

/**
 * 服务器启动函数
 */
const startServer = () => {
  const PORT = config.server.port;
  
  server.listen(PORT, () => {
    logger.info(`服务器已启动`);
    logger.info(`环境: ${config.server.nodeEnv}`);
    logger.info(`监听端口: ${PORT}`);
    logger.info(`API地址: http://localhost:${PORT}/api`);
  });
  
  server.on('error', (error: NodeJS.ErrnoException) => {
    if (error.code === 'EADDRINUSE') {
      logger.error(`端口 ${PORT} 已被占用`);
    } else {
      logger.error(`服务器错误: ${error.message}`);
    }
    process.exit(1);
  });
};

/**
 * 优雅关闭函数
 */
const gracefulShutdown = () => {
  logger.info('正在关闭服务器...');
  
  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
  
  // 如果10秒内没有关闭，强制退出
  setTimeout(() => {
    logger.error('强制关闭服务器');
    process.exit(1);
  }, 10000);
};

// 注册进程事件监听器
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);
process.on('uncaughtException', (error) => {
  logger.error(`未捕获的异常: ${error.message}`, { stack: error.stack });
  gracefulShutdown();
});
process.on('unhandledRejection', (reason) => {
  logger.error(`未处理的Promise拒绝: ${reason}`);
});

// 创建日志目录
import fs from 'fs';
if (!fs.existsSync('logs')) {
  fs.mkdirSync('logs');
}

// 启动服务器
startServer(); 