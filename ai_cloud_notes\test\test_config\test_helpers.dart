import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

/// 测试辅助工具类
/// 提供常用的测试工具方法和扩展
class TestHelpers {
  /// 等待异步操作完成
  static Future<void> waitForAsync(WidgetTester tester, {int milliseconds = 100}) async {
    await tester.pump(Duration(milliseconds: milliseconds));
  }

  /// 等待动画完成
  static Future<void> waitForAnimations(WidgetTester tester) async {
    await tester.pumpAndSettle();
  }

  /// 查找并点击按钮
  static Future<void> tapButton(WidgetTester tester, String buttonText) async {
    final button = find.text(buttonText);
    expect(button, findsOneWidget);
    await tester.tap(button);
    await tester.pump();
  }

  /// 查找并点击图标按钮
  static Future<void> tapIconButton(WidgetTester tester, IconData icon) async {
    final iconButton = find.byIcon(icon);
    expect(iconButton, findsOneWidget);
    await tester.tap(iconButton);
    await tester.pump();
  }

  /// 输入文本到指定字段
  static Future<void> enterText(WidgetTester tester, String fieldKey, String text) async {
    final textField = find.byKey(Key(fieldKey));
    expect(textField, findsOneWidget);
    await tester.enterText(textField, text);
    await tester.pump();
  }

  /// 输入文本到指定类型的字段
  static Future<void> enterTextByType(WidgetTester tester, Type fieldType, String text) async {
    final textField = find.byType(fieldType);
    expect(textField, findsOneWidget);
    await tester.enterText(textField, text);
    await tester.pump();
  }

  /// 验证文本是否存在
  static void expectTextExists(String text) {
    expect(find.text(text), findsOneWidget);
  }

  /// 验证文本不存在
  static void expectTextNotExists(String text) {
    expect(find.text(text), findsNothing);
  }

  /// 验证Widget是否存在
  static void expectWidgetExists(Type widgetType) {
    expect(find.byType(widgetType), findsOneWidget);
  }

  /// 验证Widget不存在
  static void expectWidgetNotExists(Type widgetType) {
    expect(find.byType(widgetType), findsNothing);
  }

  /// 验证加载指示器是否显示
  static void expectLoadingIndicator() {
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  }

  /// 验证加载指示器不显示
  static void expectNoLoadingIndicator() {
    expect(find.byType(CircularProgressIndicator), findsNothing);
  }

  /// 验证错误消息是否显示
  static void expectErrorMessage(String message) {
    expect(find.text(message), findsOneWidget);
  }

  /// 滚动到指定Widget
  static Future<void> scrollToWidget(WidgetTester tester, Finder finder) async {
    await tester.scrollUntilVisible(finder, 100.0);
    await tester.pump();
  }

  /// 等待Provider状态更新
  static Future<void> waitForProviderUpdate(WidgetTester tester) async {
    await tester.pump();
    await tester.pump(const Duration(milliseconds: 100));
  }

  /// 模拟网络延迟
  static Future<void> simulateNetworkDelay({int milliseconds = 500}) async {
    await Future.delayed(Duration(milliseconds: milliseconds));
  }

  /// 验证表单验证错误
  static void expectFormValidationError(String errorMessage) {
    expect(find.text(errorMessage), findsOneWidget);
  }

  /// 清空文本字段
  static Future<void> clearTextField(WidgetTester tester, String fieldKey) async {
    final textField = find.byKey(Key(fieldKey));
    expect(textField, findsOneWidget);
    await tester.enterText(textField, '');
    await tester.pump();
  }

  /// 验证导航是否发生
  static void expectNavigation(String routeName) {
    // 这个方法需要配合路由观察者使用
    // 在实际测试中，我们会通过检查当前页面的特征来验证导航
  }

  /// 创建测试用的Provider包装器
  static Widget createProviderWrapper<T extends ChangeNotifier>({
    required T provider,
    required Widget child,
  }) {
    return ChangeNotifierProvider<T>.value(
      value: provider,
      child: MaterialApp(
        home: Scaffold(body: child),
      ),
    );
  }

  /// 创建多Provider包装器
  static Widget createMultiProviderWrapper({
    required List<ChangeNotifierProvider> providers,
    required Widget child,
  }) {
    return MultiProvider(
      providers: providers,
      child: MaterialApp(
        home: Scaffold(body: child),
      ),
    );
  }

  /// 验证SnackBar是否显示
  static void expectSnackBar(String message) {
    expect(find.byType(SnackBar), findsOneWidget);
    expect(find.text(message), findsOneWidget);
  }

  /// 关闭SnackBar
  static Future<void> dismissSnackBar(WidgetTester tester) async {
    await tester.pump(const Duration(seconds: 4)); // SnackBar默认显示时间
  }

  /// 验证对话框是否显示
  static void expectDialog() {
    expect(find.byType(AlertDialog), findsOneWidget);
  }

  /// 关闭对话框
  static Future<void> dismissDialog(WidgetTester tester, String buttonText) async {
    await tapButton(tester, buttonText);
    await waitForAnimations(tester);
  }

  /// 验证底部弹出框是否显示
  static void expectBottomSheet() {
    expect(find.byType(BottomSheet), findsOneWidget);
  }

  /// 模拟设备返回按钮
  static Future<void> simulateBackButton(WidgetTester tester) async {
    final NavigatorState navigator = tester.state(find.byType(Navigator));
    navigator.pop();
    await tester.pump();
  }

  /// 验证AppBar标题
  static void expectAppBarTitle(String title) {
    expect(find.descendant(
      of: find.byType(AppBar),
      matching: find.text(title),
    ), findsOneWidget);
  }

  /// 验证FloatingActionButton是否存在
  static void expectFloatingActionButton() {
    expect(find.byType(FloatingActionButton), findsOneWidget);
  }

  /// 点击FloatingActionButton
  static Future<void> tapFloatingActionButton(WidgetTester tester) async {
    final fab = find.byType(FloatingActionButton);
    expect(fab, findsOneWidget);
    await tester.tap(fab);
    await tester.pump();
  }

  /// 验证列表项数量
  static void expectListItemCount(Type listType, int count) {
    expect(find.byType(listType), findsNWidgets(count));
  }

  /// 验证空状态显示
  static void expectEmptyState(String emptyMessage) {
    expect(find.text(emptyMessage), findsOneWidget);
  }

  /// 模拟下拉刷新
  static Future<void> simulatePullToRefresh(WidgetTester tester) async {
    await tester.fling(find.byType(RefreshIndicator), const Offset(0, 300), 1000);
    await tester.pump();
    await tester.pump(const Duration(seconds: 1)); // 等待刷新完成
  }

  /// 验证刷新指示器
  static void expectRefreshIndicator() {
    expect(find.byType(RefreshIndicator), findsOneWidget);
  }
}
