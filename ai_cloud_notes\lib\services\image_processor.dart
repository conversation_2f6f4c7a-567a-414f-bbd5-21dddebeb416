import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/utils/url_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';

/// 图片处理服务
/// 
/// 处理笔记导入导出过程中的图片复制和URL更新
class ImageProcessor {
  /// 处理富文本笔记中的图片
  /// 
  /// [content] 笔记内容（JSON格式的富文本内容）
  /// [noteId] 新笔记的ID
  /// [noteProvider] 笔记提供者，用于上传图片
  /// 
  /// 返回处理后的笔记内容
  static Future<String> processImportedRichTextImages(
    String content, 
    String noteId, 
    NoteProvider noteProvider
  ) async {
    try {
      // 解析JSON内容
      final dynamic jsonData = jsonDecode(content);
      if (jsonData == null) return content;
      
      // 查找并处理所有图片URL
      final processedData = await _processJsonData(jsonData, noteId, noteProvider);
      
      // 将处理后的数据转换回JSON字符串
      return jsonEncode(processedData);
    } catch (e) {
      print('处理导入笔记图片失败: $e');
      return content; // 出错时返回原始内容
    }
  }
  
  /// 递归处理JSON数据中的图片URL
  static Future<dynamic> _processJsonData(
    dynamic data, 
    String noteId, 
    NoteProvider noteProvider
  ) async {
    if (data == null) return data;
    
    // 处理数组
    if (data is List) {
      List<dynamic> result = [];
      for (var item in data) {
        result.add(await _processJsonData(item, noteId, noteProvider));
      }
      return result;
    }
    
    // 处理对象
    if (data is Map) {
      Map<String, dynamic> result = {};
      
      // 检查是否是图片对象
      if (_isImageObject(data)) {
        return await _processImageObject(data, noteId, noteProvider);
      }
      
      // 检查是否是嵌入图片
      if (_isEmbeddedImage(data)) {
        return await _processEmbeddedImage(data, noteId, noteProvider);
      }
      
      // 递归处理所有字段
      for (var key in data.keys) {
        result[key] = await _processJsonData(data[key], noteId, noteProvider);
      }
      
      return result;
    }
    
    // 其他类型直接返回
    return data;
  }
  
  /// 检查是否是图片对象
  static bool _isImageObject(Map<dynamic, dynamic> data) {
    return data.containsKey('source_type') && 
           data.containsKey('source') && 
           data['source_type'] == 'url' &&
           data['source'] is String &&
           _isNoteImageUrl(data['source']);
  }
  
  /// 检查是否是嵌入图片
  static bool _isEmbeddedImage(Map<dynamic, dynamic> data) {
    return data.containsKey('embed') && 
           data['embed'] is Map && 
           data['embed']['type'] == 'image' &&
           data['embed']['data'] is Map &&
           data['embed']['data']['source_type'] == 'url' &&
           data['embed']['data']['source'] is String &&
           _isNoteImageUrl(data['embed']['data']['source']);
  }
  
  /// 检查URL是否是笔记图片URL
  static bool _isNoteImageUrl(String url) {
    return url.contains('/uploads/notes/');
  }
  
  /// 处理图片对象
  static Future<Map<dynamic, dynamic>> _processImageObject(
    Map<dynamic, dynamic> data, 
    String noteId, 
    NoteProvider noteProvider
  ) async {
    final sourceUrl = data['source'] as String;
    
    // 如果图片URL已经在当前笔记的目录中，不需要处理
    if (sourceUrl.contains('/uploads/notes/$noteId/')) {
      return data;
    }
    
    try {
      // 下载并上传图片到新笔记的目录
      final newUrl = await _copyImageToNoteDirectory(sourceUrl, noteId, noteProvider);
      
      if (newUrl != null) {
        // 创建新的数据对象，更新图片URL
        final newData = Map<dynamic, dynamic>.from(data);
        newData['source'] = newUrl;
        return newData;
      }
    } catch (e) {
      print('处理图片对象失败: $e');
    }
    
    // 出错时返回原始数据
    return data;
  }
  
  /// 处理嵌入图片
  static Future<Map<dynamic, dynamic>> _processEmbeddedImage(
    Map<dynamic, dynamic> data, 
    String noteId, 
    NoteProvider noteProvider
  ) async {
    final sourceUrl = data['embed']['data']['source'] as String;
    
    // 如果图片URL已经在当前笔记的目录中，不需要处理
    if (sourceUrl.contains('/uploads/notes/$noteId/')) {
      return data;
    }
    
    try {
      // 下载并上传图片到新笔记的目录
      final newUrl = await _copyImageToNoteDirectory(sourceUrl, noteId, noteProvider);
      
      if (newUrl != null) {
        // 创建新的数据对象，更新图片URL
        final newData = Map<dynamic, dynamic>.from(data);
        final newEmbedData = Map<dynamic, dynamic>.from(data['embed']['data']);
        newEmbedData['source'] = newUrl;
        
        final newEmbed = Map<dynamic, dynamic>.from(data['embed']);
        newEmbed['data'] = newEmbedData;
        
        newData['embed'] = newEmbed;
        return newData;
      }
    } catch (e) {
      print('处理嵌入图片失败: $e');
    }
    
    // 出错时返回原始数据
    return data;
  }
  
  /// 将图片从一个笔记目录复制到另一个笔记目录
  /// 
  /// [sourceUrl] 原始图片URL
  /// [noteId] 目标笔记ID
  /// [noteProvider] 笔记提供者，用于上传图片
  /// 
  /// 返回新的图片URL，失败时返回null
  static Future<String?> _copyImageToNoteDirectory(
    String sourceUrl, 
    String noteId, 
    NoteProvider noteProvider
  ) async {
    try {
      // 下载图片
      final imageBytes = await _downloadImage(sourceUrl);
      if (imageBytes == null) return null;
      
      // 上传图片到新笔记的目录
      final result = await noteProvider.uploadNoteImage(imageBytes, noteId: noteId);
      
      if (result['success'] == true && result['data'] != null) {
        return result['data']['url'];
      }
    } catch (e) {
      print('复制图片到笔记目录失败: $e');
    }
    
    return null;
  }
  
  /// 下载图片
  /// 
  /// [url] 图片URL
  /// 
  /// 返回图片字节数据，失败时返回null
  static Future<Uint8List?> _downloadImage(String url) async {
    try {
      // 确保URL是完整的
      final fullUrl = url.startsWith('http') ? url : UrlHelper.getFullImageUrl(url);
      
      // 下载图片
      final response = await http.get(Uri.parse(fullUrl));
      
      if (response.statusCode == 200) {
        return response.bodyBytes;
      }
    } catch (e) {
      print('下载图片失败: $e');
    }
    
    return null;
  }
}
