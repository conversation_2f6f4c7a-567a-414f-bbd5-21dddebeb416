import express from 'express';
import authRoutes from '../../routes/auth.routes';
import notesRoutes from '../../routes/notes.routes';
import tagsRoutes from '../../routes/tags.routes';
import usersRoutes from '../../routes/users.routes';
import searchRoutes from '../../routes/search.routes';
import themeSettingsRoutes from '../../routes/theme_settings.routes';
import aiSettingsRoutes from '../../routes/ai_settings.routes';
import aiRoutes from '../../routes/ai.routes';
const router = express.Router();

/**
 * API路由主入口
 */

// 健康检查端点
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'API服务正常运行',
    timestamp: new Date().toISOString(),
  });
});

// 认证相关路由
router.use('/auth', authRoutes);

// 笔记相关路由
router.use('/notes', notesRoutes);

// 标签相关路由
router.use('/tags', tagsRoutes);

// 用户相关路由
router.use('/users', usersRoutes);

// 搜索相关路由
router.use('/search', searchRoutes);

// 主题设置相关路由
router.use('/theme-settings', themeSettingsRoutes);

// AI设置相关路由
router.use('/ai-settings', aiSettingsRoutes);

// AI功能相关路由
router.use('/ai', aiRoutes);

export default router;