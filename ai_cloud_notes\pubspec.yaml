name: ai_cloud_notes
description: 基于Flutter开发的跨平台云笔记应用，支持智能AI辅助功能。
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  # 状态管理
  provider: ^6.0.5
  flutter_riverpod: ^2.3.6

  # 本地存储
  shared_preferences: ^2.5.3
  sqflite: ^2.3.0
  path: ^1.8.3

  # 网络请求
  dio: ^5.8.0+1
  http: ^1.1.0
  http_parser: ^4.0.2
  connectivity_plus: ^4.0.2

  # UI组件
  google_fonts: ^5.1.0
  flutter_markdown: ^0.6.17
  cached_network_image: ^3.2.3
  image_picker: ^1.1.2
  flutter_slidable: ^4.0.0
  shimmer: ^3.0.0
  lottie: ^2.6.0

  # 工具
  intl: ^0.19.0
  uuid: ^4.5.1
  path_provider: ^2.1.1
  url_launcher: ^6.3.1
  package_info_plus: ^4.1.0
  flutter_secure_storage: ^9.0.0

  # 富文本编辑器
  fleather: ^1.20.1
  parchment: ^1.8.1

  # 动画
  animations: ^2.0.8

  # 本地化
  flutter_localizations:
    sdk: flutter
  speech_to_text: ^7.0.0
  qr_flutter: ^4.1.0

  # 文件和分享
  file_picker: ^5.2.10
  file_saver: ^0.2.9
  share_plus: ^11.0.0
  universal_html: ^2.2.4
  cross_file: ^0.3.3+4

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # 测试工具
  mockito: ^5.4.4
  network_image_mock: ^2.1.1
  fake_async: ^1.3.1
  test: ^1.24.9

  # 代码质量工具
  flutter_lints: ^2.0.3
  build_runner: ^2.4.6
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.3.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # fonts:
  #   - family: NotoSansSC
  #     fonts:
  #       - asset: assets/fonts/NotoSansSC-Regular.otf
  #       - asset: assets/fonts/NotoSansSC-Bold.otf
  #         weight: 700
  #       - asset: assets/fonts/NotoSansSC-Medium.otf
  #         weight: 500
  #       - asset: assets/fonts/NotoSansSC-Light.otf
  #         weight: 300

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
