<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>