import express from 'express';
import { body } from 'express-validator';
import * as tagsController from '../controllers/tags.controller';
import { authenticate } from '../middlewares/auth.middleware';

const router = express.Router();

// 所有标签路由都需要认证
router.use(authenticate);

/**
 * 获取热门标签
 * GET /api/tags/popular
 */
router.get('/popular', tagsController.getPopularTags);

/**
 * 获取用户所有标签
 * GET /api/tags
 */
router.get('/', tagsController.getTags);

/**
 * 创建新标签
 * POST /api/tags
 */
router.post(
  '/',
  [
    // 验证标签名称
    body('name')
      .notEmpty()
      .withMessage('标签名称不能为空')
      .isString()
      .withMessage('标签名称必须是字符串')
      .isLength({ max: 30 })
      .withMessage('标签名称最多30个字符'),
    
    // 验证标签颜色
    body('color')
      .optional()
      .isString()
      .withMessage('颜色值必须是字符串')
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('颜色必须是有效的十六进制颜色格式'),
  ],
  tagsController.createTag
);

/**
 * 获取标签详情
 * GET /api/tags/:id
 */
router.get('/:id', tagsController.getTagById);

/**
 * 获取标签关联的笔记
 * GET /api/tags/:id/notes
 */
router.get('/:id/notes', tagsController.getNotesByTag);

/**
 * 更新标签
 * PUT /api/tags/:id
 */
router.put(
  '/:id',
  [
    // 验证标签名称
    body('name')
      .optional()
      .isString()
      .withMessage('标签名称必须是字符串')
      .isLength({ max: 30 })
      .withMessage('标签名称最多30个字符'),
    
    // 验证标签颜色
    body('color')
      .optional()
      .isString()
      .withMessage('颜色值必须是字符串')
      .matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
      .withMessage('颜色必须是有效的十六进制颜色格式'),
  ],
  tagsController.updateTag
);

/**
 * 删除标签
 * DELETE /api/tags/:id
 */
router.delete('/:id', tagsController.deleteTag);

export default router; 