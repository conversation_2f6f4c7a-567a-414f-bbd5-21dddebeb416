<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智云笔记 - 原型设计</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        :root {
            --primary-color: #5D5FEF;
            --primary-light: #EEEEFF;
            --secondary-color: #06B6D4;
            --accent-color: #F59E0B;
            --light-gray: #F9FAFB;
            --medium-gray: #E5E7EB;
            --dark-gray: #6B7280;
            --white: #ffffff;
            --black: #1F2937;
            --success: #10B981;
            --danger: #EF4444;
            --border-radius-sm: 8px;
            --border-radius: 12px;
            --border-radius-lg: 20px;
            --border-radius-xl: 32px;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
            --shadow: 0 4px 12px rgba(0,0,0,0.08);
            --shadow-lg: 0 10px 25px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        
        body {
            font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            color: var(--black);
            background-color: #F5F7FA;
            line-height: 1.6;
        }
        
        .main-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 30px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--black);
            margin-bottom: 16px;
            letter-spacing: -0.5px;
        }

        .page-description {
            font-size: 1.1rem;
            color: var(--dark-gray);
            max-width: 800px;
            margin: 0 auto 30px;
            line-height: 1.6;
        }

        .flow-section {
            margin-bottom: 80px;
            position: relative;
        }

        .flow-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 30px;
            color: var(--black);
            padding-bottom: 12px;
            border-bottom: 2px solid var(--primary-light);
            position: relative;
        }
        
        .flow-title::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: -2px;
            width: 60px;
            height: 2px;
            background-color: var(--primary-color);
        }
        
        .prototype-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 40px;
            padding: 20px 0;
        }
        
        .screen {
            background-color: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            width: 360px;
            height: 720px;
            position: relative;
            margin-bottom: 40px;
            transition: var(--transition);
        }
        
        .screen:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 30px rgba(0, 0, 0, 0.12);
        }
        
        .screen-title {
            text-align: center;
            font-weight: 600;
            margin-top: 24px;
            margin-bottom: 20px;
            color: var(--black);
            font-size: 1.2rem;
        }

        .screen-details {
            text-align: center;
            color: var(--dark-gray);
            font-size: 0.9rem;
            margin-top: -10px;
            margin-bottom: 20px;
            max-width: 340px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.5;
        }

        .screen-nav {
            display: flex;
            justify-content: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 8px;
        }

        .screen-nav-button {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border: none;
            padding: 6px 12px;
            border-radius: var(--border-radius-sm);
            font-size: 0.85rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .screen-nav-button:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        /* 底部导航栏样式 */
        .bottom-nav {
            display: flex;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--white);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
            border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
            padding: 12px 0 25px;
            z-index: 100;
        }
        
        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            color: var(--dark-gray);
            font-size: 0.8rem;
            gap: 4px;
            cursor: pointer;
            transition: color 0.2s ease;
        }
        
        .nav-item.active {
            color: var(--primary-color);
        }
        
        .nav-item i {
            font-size: 1.2rem;
        }
        
        .nav-item.new-note {
            margin-top: -20px;
        }
        
        .new-note-btn {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 10px rgba(93, 95, 239, 0.3);
            color: white;
            font-size: 1.2rem;
            transition: transform 0.2s;
        }
        
        .new-note-btn:hover {
            transform: scale(1.05);
        }
        
        /* 登录/注册界面 */
        .login-container, .register-container, .forgot-password-container {
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0 40px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #F3F4F6 0%, #FFFFFF 100%);
        }
        
        .login-container::before, .register-container::before, .forgot-password-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 0 0 50% 50% / 0 0 20% 20%;
            z-index: 0;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 40px;
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo i {
            font-size: 2.2rem;
        }
        
        .login-form, .register-form, .forgot-password-form {
            width: 100%;
            background-color: var(--white);
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            position: relative;
            z-index: 1;
        }
        
        .form-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 24px;
            text-align: center;
            color: var(--black);
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: var(--dark-gray);
        }
        
        .input-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius-sm);
            background-color: var(--light-gray);
            font-size: 0.95rem;
            transition: var(--transition);
        }
        
        .input-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            background-color: var(--white);
            box-shadow: 0 0 0 3px rgba(93, 95, 239, 0.1);
        }
        
        .forgot-password {
            text-align: right;
            margin-bottom: 24px;
        }
        
        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .login-btn, .register-btn, .reset-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
        }
        
        .login-btn:hover, .register-btn:hover, .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(93, 95, 239, 0.3);
        }
        
        .register-link, .login-link {
            text-align: center;
            margin-top: 24px;
            font-size: 0.9rem;
            color: var(--dark-gray);
        }
        
        .register-link a, .login-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .back-link i {
            margin-right: 8px;
        }
        
        .terms-checkbox {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .terms-checkbox input {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-color);
        }
        
        .terms-checkbox label {
            font-size: 0.85rem;
            color: var(--dark-gray);
        }
        
        .terms-checkbox a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .version-info {
            position: absolute;
            bottom: 20px;
            text-align: center;
            color: var(--dark-gray);
            font-size: 0.8rem;
            width: 100%;
            left: 0;
        }
        
        .form-description {
            text-align: center;
            color: var(--dark-gray);
            font-size: 0.9rem;
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .verification-code {
            display: flex;
            gap: 10px;
        }
        
        .verification-code input {
            flex: 1;
        }
        
        .verification-code button {
            padding: 12px;
            background-color: var(--primary-light);
            color: var(--primary-color);
            border: none;
            border-radius: var(--border-radius-sm);
            font-size: 0.9rem;
            white-space: nowrap;
            transition: var(--transition);
        }
        
        .verification-code button:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        /* 欢迎/引导页样式 */
        .welcome-container {
            height: 100%;
            width: 100%;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            padding: 0 40px;
        }
        
        .welcome-logo {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .welcome-logo i {
            font-size: 3rem;
        }
        
        .welcome-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .welcome-description {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .welcome-btn {
            background-color: white;
            color: var(--primary-color);
            border: none;
            padding: 16px 32px;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .welcome-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        .welcome-version {
            position: absolute;
            bottom: 30px;
            opacity: 0.7;
            font-size: 0.9rem;
        }
        
        /* 主页/笔记列表 */
        .home-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            background-color: #FAFAFA;
        }
        
        .header {
            padding: 24px 20px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: white;
            border-bottom: none;
        }

        .header h4 {
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
            color: var(--black);
        }
        
        .user-avatar {
            width: 44px;
            height: 44px;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.2rem;
            box-shadow: 0 4px 10px rgba(93, 95, 239, 0.2);
            cursor: pointer;
            transition: var(--transition);
        }

        .user-avatar:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(93, 95, 239, 0.3);
        }
        
        .search-box {
            padding: 0 20px 16px;
            background-color: white;
            border-bottom-left-radius: var(--border-radius-lg);
            border-bottom-right-radius: var(--border-radius-lg);
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
        }
        
        .search-input {
            width: 100%;
            padding: 14px 16px;
            border-radius: var(--border-radius);
            border: 1px solid var(--medium-gray);
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            transition: var(--transition);
        }

        .search-input:hover {
            border-color: var(--primary-color);
            background-color: white;
        }
        
        .search-input i {
            color: var(--dark-gray);
            margin-right: 10px;
        }
        
        .categories {
            padding: 4px 20px 16px;
            display: flex;
            overflow-x: auto;
            gap: 12px;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .categories::-webkit-scrollbar {
            display: none;
        }
        
        .category-tag {
            background-color: white;
            padding: 8px 16px;
            border-radius: var(--border-radius-sm);
            white-space: nowrap;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--dark-gray);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
        }

        .category-tag:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .category-tag.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
            border-color: var(--primary-light);
            box-shadow: 0 2px 6px rgba(93, 95, 239, 0.1);
        }
        
        .notes-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px;
        }
        
        .note-item {
            padding: 20px;
            border-radius: var(--border-radius);
            background-color: white;
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 1px solid var(--medium-gray);
        }

        .note-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }
        
        .note-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--black);
            font-size: 1.1rem;
        }
        
        .note-preview {
            color: var(--dark-gray);
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.5;
            margin-bottom: 10px;
        }
        
        .note-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--dark-gray);
            font-size: 0.85rem;
        }

        .note-tag {
            padding: 3px 8px;
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: var(--border-radius-sm);
            font-weight: 500;
        }
        
        /* 笔记编辑界面 */
        .editor-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            background-color: white;
        }
        
        .editor-header {
            padding: 18px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--medium-gray);
        }

        .editor-header i {
            font-size: 1.2rem;
            color: var(--dark-gray);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
        }

        .editor-header i:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .editor-area {
            flex: 1;
            padding: 24px;
            position: relative;
            overflow-y: auto;
        }
        
        .editor-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 24px;
            border: none;
            width: 100%;
            outline: none;
            color: var(--black);
        }
        
        .editor-content {
            font-size: 1rem;
            line-height: 1.8;
            min-height: 200px;
            outline: none;
            color: var(--dark-gray);
        }
        
        .prediction-hint {
            color: var(--dark-gray);
            background-color: var(--primary-light);
            padding: 3px 8px;
            border-radius: var(--border-radius-sm);
            font-size: 0.9rem;
            display: inline-block;
            margin: 0 2px;
            opacity: 0.8;
            cursor: pointer;
            transition: var(--transition);
        }

        .prediction-hint:hover {
            opacity: 1;
            background-color: rgba(93, 95, 239, 0.15);
        }
        
        .editor-toolbar {
            display: flex;
            padding: 16px 20px;
            border-top: 1px solid var(--medium-gray);
            justify-content: space-between;
            background-color: white;
        }
        
        .toolbar-btn {
            color: var(--dark-gray);
            padding: 10px;
            font-size: 1.1rem;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
            cursor: pointer;
        }

        .toolbar-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        /* 设置界面 */
        .settings-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .settings-header {
            padding: 20px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--medium-gray);
        }

        .settings-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .settings-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
        }

        .settings-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .settings-list {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .settings-group {
            margin-bottom: 24px;
        }
        
        .settings-group-title {
            color: var(--dark-gray);
            font-size: 0.9rem;
            margin-bottom: 16px;
            font-weight: 500;
            letter-spacing: 0.5px;
            padding: 0 8px;
        }
        
        .settings-item {
            padding: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: white;
            border-radius: var(--border-radius);
            margin-bottom: 12px;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 1px solid var(--medium-gray);
        }

        .settings-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }
        
        .settings-item-left {
            display: flex;
            align-items: center;
        }
        
        .settings-item-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--border-radius-sm);
            background-color: var(--primary-light);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 16px;
            color: var(--primary-color);
        }

        .settings-item-info {
            display: flex;
            flex-direction: column;
        }
        
        .settings-item-title {
            font-weight: 500;
            color: var(--black);
            margin-bottom: 4px;
        }

        .settings-item-description {
            font-size: 0.85rem;
            color: var(--dark-gray);
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 26px;
            background-color: var(--medium-gray);
            border-radius: 20px;
            transition: var(--transition);
            cursor: pointer;
        }

        .toggle-switch.active {
            background-color: var(--primary-color);
        }

        .toggle-switch::after {
            content: "";
            position: absolute;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            left: 2px;
            transition: var(--transition);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active::after {
            left: 26px;
        }
        
        /* 分类管理界面 */
        .tags-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .tags-header {
            padding: 20px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--medium-gray);
        }

        .tags-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .tags-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
        }

        .tags-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .tags-list {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }
        
        .tag-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            margin-bottom: 12px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 1px solid var(--medium-gray);
        }

        .tag-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .tag-item-left {
            display: flex;
            align-items: center;
        }
        
        .tag-color {
            width: 20px;
            height: 20px;
            border-radius: 6px;
            margin-right: 12px;
        }
        
        .tag-name {
            font-weight: 500;
            color: var(--black);
        }
        
        .tag-count {
            color: var(--dark-gray);
            font-size: 0.85rem;
            background-color: var(--light-gray);
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .add-tag-btn {
            margin: 10px 20px 20px;
            padding: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            background-color: white;
            border-radius: var(--border-radius);
            font-weight: 500;
            box-shadow: var(--shadow-sm);
            border: 1px dashed var(--primary-color);
            transition: var(--transition);
        }

        .add-tag-btn:hover {
            background-color: var(--primary-light);
            box-shadow: var(--shadow);
            transform: translateY(-2px);
        }
        
        .add-tag-btn i {
            margin-right: 8px;
            font-size: 1rem;
        }
        
        /* 搜索结果界面 */
        .search-results-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }
        
        .search-header {
            padding: 16px 20px;
            display: flex;
            align-items: center;
            background-color: white;
            border-bottom: 1px solid var(--medium-gray);
        }
        
        .search-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
            margin-right: 12px;
        }

        .search-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .search-header .search-input {
            flex: 1;
            border-radius: var(--border-radius);
            padding: 12px 16px;
        }

        .clear-search {
            margin-left: 10px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
        }

        .clear-search:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .results-count {
            padding: 16px 20px;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }
        
        .search-results {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px;
        }
        
        .search-result-item {
            padding: 16px;
            margin-bottom: 12px;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 1px solid var(--medium-gray);
        }

        .search-result-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .result-title {
            font-weight: 600;
            color: var(--black);
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .result-preview {
            color: var(--dark-gray);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .result-highlight {
            background-color: rgba(245, 158, 11, 0.2);
            color: var(--accent-color);
            padding: 0 2px;
            border-radius: 3px;
            font-weight: 500;
        }

        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: var(--dark-gray);
        }

        .result-tag {
            background-color: var(--primary-light);
            color: var(--primary-color);
            padding: 3px 8px;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
        }

        /* 用户个人资料页面 */
        .profile-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .profile-header {
            padding: 20px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--medium-gray);
        }

        .profile-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .profile-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
        }

        .profile-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .profile-save-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .profile-save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(93, 95, 239, 0.3);
        }

        .profile-avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px 20px;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: var(--border-radius-lg);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            box-shadow: 0 6px 16px rgba(93, 95, 239, 0.2);
            position: relative;
        }

        .profile-avatar-edit {
            position: absolute;
            bottom: -5px;
            right: -5px;
            background-color: white;
            color: var(--primary-color);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: var(--transition);
        }

        .profile-avatar-edit:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .profile-info-section {
            padding: 0 20px;
        }

        .profile-info-item {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
        }

        .profile-info-label {
            font-size: 0.9rem;
            color: var(--dark-gray);
            margin-bottom: 10px;
        }

        .profile-info-input {
            width: 100%;
            border: none;
            border-bottom: 1px solid var(--medium-gray);
            padding: 8px 0;
            font-size: 1rem;
            color: var(--black);
            outline: none;
            transition: var(--transition);
        }

        .profile-info-input:focus {
            border-color: var(--primary-color);
        }

        .profile-stats {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            margin-top: 10px;
        }

        .profile-stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .profile-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .profile-stat-label {
            font-size: 0.9rem;
            color: var(--dark-gray);
        }

        /* 笔记阅读模式 */
        .reading-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: white;
        }

        .reading-header {
            padding: 18px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--medium-gray);
        }

        .reading-header-actions {
            display: flex;
            gap: 16px;
        }

        .reading-header i {
            font-size: 1.2rem;
            color: var(--dark-gray);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
        }

        .reading-header i:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .reading-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .reading-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--black);
            margin-bottom: 12px;
        }

        .reading-meta {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .reading-tags {
            display: flex;
            gap: 8px;
            margin-right: 16px;
        }

        .reading-tag {
            padding: 3px 8px;
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            font-size: 0.85rem;
        }

        .reading-time {
            margin-left: auto;
        }

        .reading-text {
            font-size: 1rem;
            line-height: 1.8;
            color: var(--black);
        }

        .reading-text p {
            margin-bottom: 16px;
        }

        .reading-footer {
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            border-top: 1px solid var(--medium-gray);
        }

        .reading-stats {
            display: flex;
            align-items: center;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .reading-stat {
            display: flex;
            align-items: center;
            margin-right: 16px;
        }

        .reading-stat i {
            margin-right: 6px;
        }

        .reading-actions {
            display: flex;
            gap: 16px;
        }

        .reading-action {
            color: var(--dark-gray);
            transition: var(--transition);
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
        }

        .reading-action.active {
            color: var(--primary-color);
        }

        .reading-action:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        /* 夜间模式主题设置 */
        .theme-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .theme-header {
            padding: 20px;
            background-color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--medium-gray);
            box-shadow: var(--shadow-sm);
        }

        .theme-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .theme-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
        }

        .theme-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .theme-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .theme-section {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
        }

        .theme-section-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--black);
            margin-bottom: 16px;
        }

        .theme-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--medium-gray);
        }

        .theme-option:last-child {
            border-bottom: none;
        }

        .theme-option-label {
            font-size: 0.95rem;
            color: var(--black);
        }

        .theme-previews {
            display: flex;
            gap: 16px;
            margin-top: 20px;
        }

        .theme-preview {
            flex: 1;
            height: 180px;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            position: relative;
            transition: var(--transition);
            cursor: pointer;
            border: 1px solid var(--medium-gray);
        }

        .theme-preview:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow);
        }

        .theme-preview.active {
            border: 2px solid var(--primary-color);
            transform: translateY(-2px);
        }

        .theme-preview.active::after {
            content: "\f00c";
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .theme-preview-light {
            background-color: white;
        }

        .theme-preview-dark {
            background-color: #121212;
        }

        .theme-preview-inner {
            padding: 16px;
        }

        .theme-preview-header {
            height: 24px;
            margin-bottom: 12px;
            border-radius: 4px;
        }

        .theme-preview-light .theme-preview-header {
            background-color: var(--medium-gray);
        }

        .theme-preview-dark .theme-preview-header {
            background-color: #2D2D2D;
        }

        .theme-preview-line {
            height: 12px;
            margin-bottom: 8px;
            border-radius: 2px;
        }

        .theme-preview-light .theme-preview-line {
            background-color: var(--light-gray);
        }

        .theme-preview-dark .theme-preview-line {
            background-color: #2D2D2D;
        }

        .theme-preview-line.short {
            width: 70%;
        }

        .theme-color-options {
            display: flex;
            gap: 16px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .theme-color-option {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
        }

        .theme-color-option:hover {
            transform: scale(1.1);
        }

        .theme-color-option.active {
            box-shadow: 0 0 0 2px white, 0 0 0 4px var(--primary-color);
        }

        .theme-color-purple {
            background: linear-gradient(135deg, #5D5FEF, #6366F1);
        }

        .theme-color-blue {
            background: linear-gradient(135deg, #0EA5E9, #38BDF8);
        }

        .theme-color-green {
            background: linear-gradient(135deg, #10B981, #34D399);
        }

        .theme-color-orange {
            background: linear-gradient(135deg, #F59E0B, #FBBF24);
        }

        .theme-color-red {
            background: linear-gradient(135deg, #EF4444, #F87171);
        }

        .dark-mode-time {
            margin-top: 20px;
        }

        .dark-mode-time-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            background-color: var(--light-gray);
            border-radius: var(--border-radius);
            padding: 16px;
        }

        .dark-mode-time-input {
            display: flex;
            align-items: center;
        }

        .dark-mode-time-input span {
            margin: 0 10px;
            color: var(--dark-gray);
        }

        .time-input {
            width: 80px;
            padding: 8px 12px;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--medium-gray);
            background-color: white;
            text-align: center;
            font-size: 0.95rem;
        }

        /* AI智能建议页面 */
        .ai-suggestion-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: white;
        }

        .ai-suggestion-header {
            padding: 18px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--medium-gray);
        }

        .ai-suggestion-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .ai-suggestion-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
        }

        .ai-suggestion-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .ai-assistant-section {
            padding: 24px;
            background: linear-gradient(135deg, rgba(93, 95, 239, 0.08), rgba(6, 182, 212, 0.08));
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            border-bottom: 1px solid var(--medium-gray);
        }

        .ai-assistant-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            color: white;
            font-size: 2rem;
            box-shadow: 0 8px 16px rgba(93, 95, 239, 0.2);
        }

        .ai-assistant-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
            margin-bottom: 8px;
        }

        .ai-assistant-desc {
            font-size: 0.9rem;
            color: var(--dark-gray);
            margin-bottom: 16px;
            max-width: 280px;
            line-height: 1.5;
        }

        .ai-suggestion-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .ai-suggestion-section {
            margin-bottom: 24px;
        }

        .ai-suggestion-section-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--black);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }

        .ai-suggestion-section-title i {
            color: var(--primary-color);
            margin-right: 8px;
        }

        .ai-suggestion-card {
            background-color: white;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }

        .ai-suggestion-card:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .ai-suggestion-card-title {
            font-weight: 600;
            color: var(--black);
            margin-bottom: 8px;
            font-size: 0.95rem;
        }

        .ai-suggestion-card-content {
            color: var(--dark-gray);
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .ai-tag-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 12px;
        }

        .ai-tag-suggestion {
            background-color: var(--primary-light);
            color: var(--primary-color);
            padding: 6px 12px;
            border-radius: var(--border-radius-sm);
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            transition: var(--transition);
            cursor: pointer;
        }

        .ai-tag-suggestion i {
            margin-right: 6px;
            font-size: 0.8rem;
        }

        .ai-tag-suggestion:hover {
            background-color: rgba(93, 95, 239, 0.15);
            transform: translateY(-2px);
        }

        .ai-content-suggestion {
            background-color: var(--light-gray);
            padding: 12px;
            border-radius: var(--border-radius-sm);
            margin-top: 12px;
            position: relative;
            border-left: 3px solid var(--primary-color);
            font-size: 0.9rem;
            line-height: 1.6;
        }

        .ai-suggestion-actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 12px;
        }

        .ai-suggestion-action {
            color: var(--primary-color);
            font-size: 0.85rem;
            font-weight: 500;
            padding: 6px 12px;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
            cursor: pointer;
        }

        .ai-suggestion-action:hover {
            background-color: var(--primary-light);
        }

        .ai-related-notes {
            margin-top: 16px;
        }

        .ai-related-note {
            display: flex;
            padding: 12px;
            background-color: var(--light-gray);
            border-radius: var(--border-radius-sm);
            margin-bottom: 10px;
            transition: var(--transition);
        }

        .ai-related-note:hover {
            background-color: #F3F4F6;
            transform: translateY(-2px);
        }

        .ai-related-note-icon {
            width: 32px;
            height: 32px;
            background-color: white;
            border-radius: var(--border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: var(--primary-color);
            border: 1px solid var(--primary-light);
        }

        .ai-related-note-info {
            flex: 1;
        }

        .ai-related-note-title {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--black);
            margin-bottom: 4px;
        }

        .ai-related-note-meta {
            font-size: 0.8rem;
            color: var(--dark-gray);
        }

        /* 收藏笔记页面 */
        .favorites-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .favorites-header {
            padding: 24px 20px 16px;
            background-color: white;
            border-bottom-left-radius: var(--border-radius-lg);
            border-bottom-right-radius: var(--border-radius-lg);
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
        }

        .favorites-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 12px;
            color: var(--black);
        }

        .favorites-count {
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .favorites-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 20px;
        }

        .favorite-item {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 1px solid var(--medium-gray);
            position: relative;
        }

        .favorite-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .favorite-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--black);
            font-size: 1.1rem;
            padding-right: 24px;
        }

        .favorite-content {
            color: var(--dark-gray);
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .favorite-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.85rem;
            color: var(--dark-gray);
        }

        .favorite-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .favorite-tag {
            padding: 3px 8px;
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-radius: var(--border-radius-sm);
            font-weight: 500;
        }

        .favorite-date {
            color: var(--dark-gray);
        }

        .favorite-star {
            position: absolute;
            top: 16px;
            right: 16px;
            color: var(--accent-color);
            font-size: 1.1rem;
        }

        /* 注册页面 */
        .register-container {
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0 40px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #F3F4F6 0%, #FFFFFF 100%);
        }

        .register-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 0 0 50% 50% / 0 0 20% 20%;
            z-index: 0;
        }

        .register-form {
            width: 100%;
            background-color: var(--white);
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            position: relative;
            z-index: 1;
        }

        .terms-checkbox {
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .terms-checkbox input {
            width: 16px;
            height: 16px;
            margin-top: 3px;
            accent-color: var(--primary-color);
        }

        .terms-checkbox label {
            font-size: 0.85rem;
            color: var(--dark-gray);
            line-height: 1.4;
        }

        .terms-checkbox a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .register-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(93, 95, 239, 0.3);
        }

        .login-link {
            text-align: center;
            margin-top: 24px;
            font-size: 0.9rem;
            color: var(--dark-gray);
        }

        .login-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        /* 笔记历史版本页面 */
        .history-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: white;
        }

        .history-header {
            padding: 18px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--medium-gray);
        }

        .history-header-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .history-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
        }

        .history-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .history-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .history-note-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
            margin-bottom: 20px;
        }

        .history-timeline {
            position: relative;
            padding-left: 28px;
        }

        .history-timeline::before {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 9px;
            width: 2px;
            background-color: var(--medium-gray);
        }

        .history-item {
            position: relative;
            margin-bottom: 24px;
        }

        .history-item:last-child {
            margin-bottom: 0;
        }

        .history-marker {
            position: absolute;
            top: 0;
            left: -28px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid var(--primary-color);
            z-index: 1;
        }

        .history-item.current .history-marker {
            background-color: var(--primary-color);
        }

        .history-card {
            background-color: white;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            padding: 16px;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }

        .history-card:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
        }

        .history-item.current .history-card {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(93, 95, 239, 0.1);
        }

        .history-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .history-date {
            font-size: 0.9rem;
            color: var(--dark-gray);
        }

        .history-author {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: var(--dark-gray);
        }

        .history-author-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: var(--primary-light);
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            margin-right: 6px;
            font-weight: 600;
        }

        .history-content-preview {
            font-size: 0.9rem;
            color: var(--dark-gray);
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .history-changes {
            font-size: 0.85rem;
            color: var(--black);
            background-color: var(--light-gray);
            padding: 10px;
            border-radius: var(--border-radius-sm);
            margin-bottom: 12px;
        }

        .history-changes-item {
            margin-bottom: 6px;
            display: flex;
            align-items: flex-start;
        }

        .history-changes-item:last-child {
            margin-bottom: 0;
        }

        .history-changes-icon {
            margin-right: 8px;
            color: var(--dark-gray);
        }

        .history-changes-text {
            flex: 1;
            line-height: 1.4;
        }

        .history-changes-text .added {
            color: var(--success);
            background-color: rgba(16, 185, 129, 0.1);
            padding: 0 3px;
            border-radius: 3px;
        }

        .history-changes-text .removed {
            color: var(--danger);
            background-color: rgba(239, 68, 68, 0.1);
            padding: 0 3px;
            border-radius: 3px;
            text-decoration: line-through;
        }

        .history-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .history-action {
            font-size: 0.85rem;
            color: var(--primary-color);
            padding: 6px 12px;
            border-radius: var(--border-radius-sm);
            cursor: pointer;
            transition: var(--transition);
        }

        .history-action:hover {
            background-color: var(--primary-light);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="page-header">
            <h1 class="page-title">智云笔记应用原型设计</h1>
            <p class="page-description">
                这是智云笔记应用的完整UI设计原型，展示了应用的所有核心界面、用户流程和交互逻辑。
                整个设计遵循简约、高效的设计理念，对各功能模块的入口和用户路径进行了明确定义。
            </p>
        </div>

        <!-- 欢迎页面 -->
        <div class="flow-section">
            <h2 class="flow-title">1. 欢迎与引导</h2>
        <div class="prototype-container">
                <!-- 欢迎页 -->
            <div>
                    <h3 class="screen-title">欢迎页</h3>
                    <p class="screen-details">应用首次启动时的欢迎页面，提供品牌介绍和功能引导</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">开始使用</button>
                        <button class="screen-nav-button">了解更多</button>
                    </div>
                    <div class="screen">
                        <div class="welcome-container">
                            <div class="welcome-logo">
                                <i class="fas fa-cloud"></i> 智云笔记
                            </div>
                            <h1 class="welcome-title">智能笔记，随时随地</h1>
                            <p class="welcome-description">
                                基于AI技术的智能云笔记应用，让记录和创作更加高效、便捷，随时随地捕捉灵感。
                            </p>
                            <button class="welcome-btn">开始体验</button>
                            <div class="welcome-version">版本 v0.5.1</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户认证流程 -->
        <div class="flow-section">
            <h2 class="flow-title">2. 用户认证流程</h2>
            <div class="prototype-container">
                <!-- 登录界面 -->
                <div>
                    <h3 class="screen-title">登录界面</h3>
                    <p class="screen-details">应用入口页面，用户可以登录已有账号或前往注册</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">前往注册</button>
                        <button class="screen-nav-button">忘记密码</button>
                    </div>
                <div class="screen">
                    <div class="login-container">
                        <div class="logo">
                                <i class="fas fa-cloud"></i> 智云笔记
                        </div>
                        <div class="login-form">
                                <h2 class="form-title">账号登录</h2>
                                <div class="input-group">
                                    <label>用户名/邮箱</label>
                                    <input type="text" placeholder="请输入账号">
                                </div>
                                <div class="input-group">
                                    <label>密码</label>
                                    <input type="password" placeholder="请输入密码">
                                </div>
                                <div class="forgot-password">
                                    <a href="#">忘记密码？</a>
                                </div>
                                <button class="login-btn">登 录</button>
                            <div class="register-link">
                                没有账号？<a href="#">立即注册</a>
                            </div>
                                </div>
                            <div class="version-info">当前版本：v0.5.1</div>
                            </div>
                        </div>
                    </div>
                
                <!-- 注册页面 -->
                <div>
                    <h3 class="screen-title">注册页面</h3>
                    <p class="screen-details">新用户注册页面，收集必要的用户信息</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回登录</button>
                    </div>
                    <div class="screen">
                        <div class="register-container">
                            <div class="logo">
                                <i class="fas fa-cloud"></i> 智云笔记
                            </div>
                            <div class="register-form">
                                <h2 class="form-title">创建账号</h2>
                                <div class="input-group">
                                    <label>用户名</label>
                                    <input type="text" placeholder="请设置用户名">
                                </div>
                                <div class="input-group">
                                    <label>邮箱</label>
                                    <input type="email" placeholder="请输入邮箱">
                                </div>
                                <div class="input-group">
                                    <label>密码</label>
                                    <input type="password" placeholder="请设置密码">
                                </div>
                                <div class="input-group">
                                    <label>确认密码</label>
                                    <input type="password" placeholder="请再次输入密码">
                                </div>
                                <div class="terms-checkbox">
                                    <input type="checkbox" id="terms">
                                    <label for="terms">我已阅读并同意<a href="#">《用户协议》</a>和<a href="#">《隐私政策》</a></label>
                                </div>
                                <button class="register-btn">注 册</button>
                                <div class="login-link">
                                    已有账号？<a href="#">立即登录</a>
                                </div>
                            </div>
                            <div class="version-info">当前版本：v0.5.1</div>
                        </div>
                </div>
            </div>
                
                <!-- 忘记密码页面 -->
                <div>
                    <h3 class="screen-title">忘记密码</h3>
                    <p class="screen-details">用户找回密码的流程，通过邮箱验证重置密码</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回登录</button>
                        <button class="screen-nav-button">重置密码</button>
                    </div>
                    <div class="screen">
                        <div class="forgot-password-container">
                            <div class="logo">
                                <i class="fas fa-cloud"></i> 智云笔记
                            </div>
                            <div class="forgot-password-form">
                                <h2 class="form-title">找回密码</h2>
                                <p class="form-description">
                                    请输入您的注册邮箱，我们将发送验证码用于重置密码。
                                </p>
                                <div class="input-group">
                                    <label>邮箱</label>
                                    <input type="email" placeholder="请输入注册邮箱">
                                </div>
                                <div class="input-group">
                                    <label>验证码</label>
                                    <div class="verification-code">
                                        <input type="text" placeholder="请输入验证码">
                                        <button>获取验证码</button>
                                    </div>
                                </div>
                                <div class="input-group">
                                    <label>新密码</label>
                                    <input type="password" placeholder="请设置新密码">
                                </div>
                                <div class="input-group">
                                    <label>确认新密码</label>
                                    <input type="password" placeholder="请再次输入新密码">
                                </div>
                                <button class="reset-btn">重置密码</button>
                                <div class="login-link">
                                    <a href="#" class="back-link">
                                        <i class="fas fa-arrow-left"></i> 返回登录
                                    </a>
                                </div>
                            </div>
                            <div class="version-info">当前版本：v0.5.1</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保持其他页面不变，稍后会在后续的修改中处理 -->

        <!-- 主页/笔记列表 -->
        <div class="flow-section">
            <h2 class="flow-title">2. 主页与笔记管理</h2>
            <div class="prototype-container">
            <!-- 主页/笔记列表 -->
            <div>
                <h3 class="screen-title">主页/笔记列表</h3>
                    <p class="screen-details">登录后的默认页面，显示所有笔记，按最近编辑排序</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">创建笔记</button>
                        <button class="screen-nav-button">搜索</button>
                        <button class="screen-nav-button">标签管理</button>
                    </div>
                <div class="screen">
                    <div class="home-container">
                            <div class="home-header">
                                <div class="header-top">
                                    <div class="user-profile">
                                        <img src="https://ui-avatars.com/api/?name=用户&background=4F46E5&color=fff" alt="User Avatar">
                                        <span>用户名</span>
                        </div>
                                    <div class="header-actions">
                                        <i class="fas fa-bell"></i>
                                        <i class="fas fa-cog"></i>
                                    </div>
                                </div>
                        <div class="search-box">
                                <i class="fas fa-search"></i>
                                    <input type="text" placeholder="搜索笔记">
                            </div>
                        </div>
                            <div class="notes-section">
                                <div class="section-header">
                                    <h2>我的笔记</h2>
                                    <div class="view-options">
                                        <i class="fas fa-th-large active"></i>
                                        <i class="fas fa-list"></i>
                        </div>
                                </div>
                                <div class="notes-grid">
                            <div class="note-item">
                                        <div class="note-title">工作计划</div>
                                        <div class="note-content">本周需要完成的任务：1. 项目计划书 2. 客户会议 3. 团队...</div>
                                        <div class="note-meta">
                                            <span class="note-date">2023-08-15</span>
                                            <span class="note-tags">工作</span>
                                </div>
                            </div>
                            <div class="note-item">
                                        <div class="note-title">学习笔记</div>
                                        <div class="note-content">Flutter框架学习：Widget、State、BuildContext的概念...</div>
                                        <div class="note-meta">
                                            <span class="note-date">2023-08-14</span>
                                            <span class="note-tags">学习</span>
                                </div>
                            </div>
                            <div class="note-item">
                                        <div class="note-title">旅行计划</div>
                                        <div class="note-content">云南之行：1. 大理 2. 丽江 3. 香格里拉 住宿和交通...</div>
                                        <div class="note-meta">
                                            <span class="note-date">2023-08-12</span>
                                            <span class="note-tags">旅行</span>
                                </div>
                            </div>
                            <div class="note-item">
                                        <div class="note-title">读书笔记</div>
                                        <div class="note-content">《原子习惯》读书笔记：1. 复合效应 2. 微习惯 3. 环境...</div>
                                        <div class="note-meta">
                                            <span class="note-date">2023-08-10</span>
                                            <span class="note-tags">阅读</span>
                                </div>
                            </div>
                        </div>
                            </div>
                            <!-- 添加底部导航栏 -->
                        <div class="bottom-nav">
                            <div class="nav-item active">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-star"></i>
                                <span>收藏</span>
                            </div>
                                <div class="nav-item new-note">
                                    <div class="new-note-btn">
                                        <i class="fas fa-plus"></i>
                                    </div>
                            </div>
                            <div class="nav-item">
                                    <i class="fas fa-tags"></i>
                                <span>标签</span>
                            </div>
                            <div class="nav-item">
                                    <i class="fas fa-user"></i>
                                    <span>我的</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
                <!-- 笔记编辑页面 -->
            <div>
                    <h3 class="screen-title">笔记编辑</h3>
                    <p class="screen-details">创建或编辑笔记的界面，包含丰富的编辑工具和AI辅助功能</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回首页</button>
                        <button class="screen-nav-button">查看历史版本</button>
                        <button class="screen-nav-button">保存并分享</button>
                    </div>
                <div class="screen">
                    <div class="editor-container">
                        <div class="editor-header">
                                <div class="editor-actions">
                            <i class="fas fa-arrow-left"></i>
                                    <div class="spacer"></div>
                                    <i class="fas fa-history"></i>
                                    <i class="fas fa-share-alt"></i>
                                    <i class="fas fa-ellipsis-v"></i>
                            </div>
                                <input type="text" class="editor-title" value="工作计划">
                        </div>
                            <div class="editor-toolbar">
                                <i class="fas fa-bold"></i>
                                <i class="fas fa-italic"></i>
                                <i class="fas fa-underline"></i>
                                <i class="fas fa-list-ul"></i>
                                <i class="fas fa-list-ol"></i>
                                <i class="fas fa-tasks"></i>
                                <i class="fas fa-link"></i>
                                <i class="fas fa-image"></i>
                                <i class="fas fa-undo"></i>
                                <i class="fas fa-redo"></i>
                            </div>
                            <div class="editor-content">
                                <p><strong>本周需要完成的任务：</strong></p>
                                <ul>
                                    <li>项目计划书</li>
                                    <li>客户会议</li>
                                    <li>团队协调</li>
                                    <li>进度报告</li>
                                </ul>
                                <p>项目截止日期：2023-08-20</p>
                                <p>预计所需时间：15小时</p>
                            </div>
                            <div class="prediction-hint">
                                <i class="fas fa-lightbulb"></i>
                                <span>AI建议：添加"准备演示文稿"到任务列表</span>
                                <button class="accept-btn">接受</button>
                            </div>
                            <div class="editor-tags">
                                <span class="tag">工作</span>
                                <span class="tag">计划</span>
                                <span class="add-tag">+ 添加标签</span>
                            </div>
                        </div>
                            </div>
                        </div>
                        
                <!-- 笔记阅读模式 -->
                <div>
                    <h3 class="screen-title">笔记阅读模式</h3>
                    <p class="screen-details">提供专注阅读体验的模式，减少干扰元素</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回编辑</button>
                        <button class="screen-nav-button">分享笔记</button>
                        <button class="screen-nav-button">添加收藏</button>
                            </div>
                    <div class="screen">
                        <div class="reading-container">
                            <div class="reading-header">
                                <div class="header-actions">
                                    <i class="fas fa-arrow-left"></i>
                                    <div class="spacer"></div>
                                    <i class="fas fa-edit"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-share-alt"></i>
                            </div>
                        </div>
                            <div class="reading-content">
                                <h1 class="reading-title">工作计划</h1>
                                <div class="reading-meta">
                                    <span class="reading-date">最后编辑：2023-08-15</span>
                                    <div class="reading-tags">
                                        <span class="tag">工作</span>
                                        <span class="tag">计划</span>
                                    </div>
                                </div>
                                <div class="reading-body">
                                    <p><strong>本周需要完成的任务：</strong></p>
                                    <ul>
                                        <li>项目计划书</li>
                                        <li>客户会议</li>
                                        <li>团队协调</li>
                                        <li>进度报告</li>
                                    </ul>
                                    <p>项目截止日期：2023-08-20</p>
                                    <p>预计所需时间：15小时</p>
                                </div>
                            </div>
                            <div class="reading-footer">
                                <div class="footer-actions">
                                    <div class="action">
                                        <i class="fas fa-history"></i>
                                        <span>历史版本</span>
                                    </div>
                                    <div class="action">
                                        <i class="fas fa-print"></i>
                                        <span>打印</span>
                                    </div>
                                    <div class="action">
                                        <i class="fas fa-file-export"></i>
                                        <span>导出</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
            
            <!-- 设置界面 -->
            <div>
                <h3 class="screen-title">设置界面</h3>
                <div class="screen">
                    <div class="settings-container">
                        <div class="header">
                            <h4>设置</h4>
                        <i class="fas fa-question-circle" style="font-size: 20px; color: var(--dark-gray);"></i>
                        </div>
                        
                        <div class="settings-list">
                            <div class="settings-group">
                                <div class="settings-group-title">账户</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <div class="settings-item-title">个人资料</div>
                                        <div class="settings-item-description">编辑您的个人信息</div>
                                        </div>
                                    </div>
                                    <div>
                                    <i class="fas fa-chevron-right" style="color: var(--dark-gray);"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-lock"></i>
                                        </div>
                                        <div>
                                            <div class="settings-item-title">隐私与安全</div>
                                        <div class="settings-item-description">管理数据与安全选项</div>
                                        </div>
                                    </div>
                                    <div>
                                    <i class="fas fa-chevron-right" style="color: var(--dark-gray);"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="settings-group">
                                <div class="settings-group-title">应用</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-robot"></i>
                                        </div>
                                        <div>
                                            <div class="settings-item-title">AI设置</div>
                                        <div class="settings-item-description">配置智能预测功能</div>
                                        </div>
                                    </div>
                                    <div>
                                    <i class="fas fa-chevron-right" style="color: var(--dark-gray);"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-cloud"></i>
                                        </div>
                                        <div>
                                            <div class="settings-item-title">同步设置</div>
                                        <div class="settings-item-description">配置云同步选项</div>
                                        </div>
                                    </div>
                                    <div>
                                    <i class="fas fa-chevron-right" style="color: var(--dark-gray);"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-bell"></i>
                                        </div>
                                        <div>
                                            <div class="settings-item-title">通知设置</div>
                                        <div class="settings-item-description">提醒和推送管理</div>
                                        </div>
                                    </div>
                                <div class="toggle-switch active"></div>
                                </div>
                            </div>
                            
                            <div class="settings-group">
                                <div class="settings-group-title">其他</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-question-circle"></i>
                                        </div>
                                        <div>
                                            <div class="settings-item-title">帮助与反馈</div>
                                        <div class="settings-item-description">获取支持和提交问题</div>
                                        </div>
                                    </div>
                                    <div>
                                    <i class="fas fa-chevron-right" style="color: var(--dark-gray);"></i>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                        <div class="settings-item-icon">
                                            <i class="fas fa-info-circle"></i>
                                        </div>
                                        <div>
                                            <div class="settings-item-title">关于</div>
                                        <div class="settings-item-description">版本信息与开源协议</div>
                                        </div>
                                    </div>
                                    <div>
                                    <i class="fas fa-chevron-right" style="color: var(--dark-gray);"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-star"></i>
                                <span>收藏</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-tag"></i>
                                <span>标签</span>
                            </div>
                            <div class="nav-item active">
                                <i class="fas fa-cog"></i>
                                <span>设置</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分类/标签管理 -->
        <div class="flow-section">
            <h2 class="flow-title">3. 标签与搜索功能</h2>
            <div class="prototype-container">
                <!-- 标签管理 -->
            <div>
                    <h3 class="screen-title">标签管理</h3>
                    <p class="screen-details">管理和组织笔记标签，可以创建、编辑和删除标签</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回首页</button>
                        <button class="screen-nav-button">创建新标签</button>
                    </div>
                <div class="screen">
                    <div class="tags-container">
                            <div class="tags-header">
                                <div class="header-top">
                                    <i class="fas fa-arrow-left"></i>
                                    <h3>标签管理</h3>
                            <i class="fas fa-plus"></i>
                        </div>
                                <div class="search-box">
                                    <i class="fas fa-search"></i>
                                    <input type="text" placeholder="搜索标签">
                                </div>
                            </div>
                        <div class="tags-list">
                            <div class="tag-item">
                                    <div class="tag-info">
                                        <div class="tag-color" style="background-color: #4F46E5;"></div>
                                    <div class="tag-name">工作</div>
                                </div>
                                    <div class="tag-count">12</div>
                                    <div class="tag-actions">
                                        <i class="fas fa-ellipsis-v"></i>
                            </div>
                                </div>
                            <div class="tag-item">
                                    <div class="tag-info">
                                        <div class="tag-color" style="background-color: #06B6D4;"></div>
                                    <div class="tag-name">学习</div>
                                </div>
                                    <div class="tag-count">8</div>
                                    <div class="tag-actions">
                                        <i class="fas fa-ellipsis-v"></i>
                            </div>
                                </div>
                            <div class="tag-item">
                                    <div class="tag-info">
                                        <div class="tag-color" style="background-color: #F59E0B;"></div>
                                        <div class="tag-name">旅行</div>
                                </div>
                                    <div class="tag-count">5</div>
                                    <div class="tag-actions">
                                        <i class="fas fa-ellipsis-v"></i>
                            </div>
                                </div>
                            <div class="tag-item">
                                    <div class="tag-info">
                                        <div class="tag-color" style="background-color: #10B981;"></div>
                                        <div class="tag-name">阅读</div>
                                </div>
                                    <div class="tag-count">3</div>
                                    <div class="tag-actions">
                                        <i class="fas fa-ellipsis-v"></i>
                            </div>
                                </div>
                            <div class="tag-item">
                                    <div class="tag-info">
                                        <div class="tag-color" style="background-color: #EF4444;"></div>
                                        <div class="tag-name">灵感</div>
                                </div>
                                    <div class="tag-count">7</div>
                                    <div class="tag-actions">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 底部导航栏 -->
                            <div class="bottom-nav">
                                <div class="nav-item">
                                    <i class="fas fa-home"></i>
                                    <span>首页</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-star"></i>
                                    <span>收藏</span>
                                </div>
                                <div class="nav-item new-note">
                                    <div class="new-note-btn">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                </div>
                                <div class="nav-item active">
                                    <i class="fas fa-tags"></i>
                                    <span>标签</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-user"></i>
                                    <span>我的</span>
                                </div>
                            </div>
                        </div>
                            </div>
                        </div>
                        
                <!-- 搜索结果 -->
                <div>
                    <h3 class="screen-title">搜索结果</h3>
                    <p class="screen-details">展示基于关键词的搜索结果，可以按相关性或时间排序</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回首页</button>
                        <button class="screen-nav-button">高级筛选</button>
                    </div>
                    <div class="screen">
                        <div class="search-results-container">
                            <div class="search-header">
                                <i class="fas fa-arrow-left"></i>
                                <div class="search-input-box">
                                    <i class="fas fa-search"></i>
                                    <input type="text" value="工作计划">
                                    <i class="fas fa-times"></i>
                                </div>
                            </div>
                            <div class="search-filters">
                                <div class="filter active">全部</div>
                                <div class="filter">笔记</div>
                                <div class="filter">标签</div>
                                <div class="filter">内容</div>
                            </div>
                            <div class="search-count">找到 8 条结果</div>
                            <div class="search-results">
                                <div class="search-result-item">
                                    <div class="result-title">工作计划</div>
                                    <div class="result-preview">本周需要完成的任务：1. 项目计划书 2. 客户会议 3. 团队协调...</div>
                                    <div class="result-meta">
                                        <span class="result-tag">工作</span>
                                        <span class="result-date">2023-08-15</span>
                                    </div>
                                </div>
                                <div class="search-result-item">
                                    <div class="result-title">月度工作计划模板</div>
                                    <div class="result-preview">这是一个月度工作计划模板，包含目标设定、任务分解、时间安排...</div>
                                    <div class="result-meta">
                                        <span class="result-tag">工作</span>
                                        <span class="result-date">2023-07-20</span>
                                    </div>
                                </div>
                                <div class="search-result-item">
                                    <div class="result-title">团队协作计划</div>
                                    <div class="result-preview">团队协作的核心原则和实施<mark>计划</mark>，包括沟通机制、任务分配...</div>
                                    <div class="result-meta">
                                        <span class="result-tag">工作</span>
                                        <span class="result-date">2023-06-10</span>
                                    </div>
                                </div>
                                <div class="search-result-item">
                                    <div class="result-title">个人发展计划</div>
                                    <div class="result-preview">未来一年的个人发展<mark>计划</mark>，包括技能提升、阅读清单...</div>
                                    <div class="result-meta">
                                        <span class="result-tag">学习</span>
                                        <span class="result-date">2023-05-18</span>
                                    </div>
                                </div>
                            </div>
                            <!-- 底部导航栏 -->
                            <div class="bottom-nav">
                                <div class="nav-item active">
                                    <i class="fas fa-home"></i>
                                    <span>首页</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-star"></i>
                                    <span>收藏</span>
                                </div>
                                <div class="nav-item new-note">
                                    <div class="new-note-btn">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-tags"></i>
                                    <span>标签</span>
                                </div>
                                <div class="nav-item">
                                    <i class="fas fa-user"></i>
                                    <span>我的</span>
                                </div>
                            </div>
                        </div>
                    </div>
                        </div>
                        
                <!-- 标签详情页 -->
                <div>
                    <h3 class="screen-title">标签详情</h3>
                    <p class="screen-details">展示特定标签下的所有笔记，可以编辑标签属性</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回标签管理</button>
                        <button class="screen-nav-button">编辑标签</button>
                    </div>
                    <div class="screen">
                        <div class="tag-detail-container">
                            <div class="tag-detail-header">
                                <i class="fas fa-arrow-left"></i>
                                <div class="tag-detail-info">
                                    <div class="tag-color-large" style="background-color: #4F46E5;"></div>
                                    <h3>工作</h3>
                                </div>
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                            <div class="tag-stats">
                                <div class="tag-stat">
                                    <div class="tag-stat-value">12</div>
                                    <div class="tag-stat-label">笔记数</div>
                                </div>
                                <div class="tag-stat">
                                    <div class="tag-stat-value">5</div>
                                    <div class="tag-stat-label">今日新增</div>
                                </div>
                                <div class="tag-stat">
                                    <div class="tag-stat-value">2021-05-15</div>
                                    <div class="tag-stat-label">创建日期</div>
                                </div>
                            </div>
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" placeholder="搜索此标签下的笔记">
                            </div>
                            <div class="tag-notes">
                                <div class="section-header">
                                    <h3>笔记列表</h3>
                                    <div class="sort-option">
                                        <span>最近更新</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                </div>
                                <div class="notes-list">
                                    <div class="note-item">
                                        <div class="note-title">工作计划</div>
                                        <div class="note-content">本周需要完成的任务：1. 项目计划书 2. 客户会议 3. 团队...</div>
                                        <div class="note-meta">
                                            <span class="note-date">2023-08-15</span>
                                        </div>
                                    </div>
                                    <div class="note-item">
                                        <div class="note-title">会议纪要：产品讨论会</div>
                                        <div class="note-content">与设计和开发团队讨论新功能的实施方案，重点关注用户体验...</div>
                                        <div class="note-meta">
                                            <span class="note-date">2023-08-14</span>
                                        </div>
                                    </div>
                                    <div class="note-item">
                                        <div class="note-title">月度工作计划模板</div>
                                        <div class="note-content">这是一个月度工作计划模板，包含目标设定、任务分解、时间安排...</div>
                                        <div class="note-meta">
                                            <span class="note-date">2023-07-20</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 底部导航栏 -->
                        <div class="bottom-nav">
                            <div class="nav-item">
                                <i class="fas fa-home"></i>
                                <span>首页</span>
                            </div>
                            <div class="nav-item">
                                <i class="fas fa-star"></i>
                                <span>收藏</span>
                            </div>
                                <div class="nav-item new-note">
                                    <div class="new-note-btn">
                                        <i class="fas fa-plus"></i>
                                    </div>
                            </div>
                            <div class="nav-item active">
                                    <i class="fas fa-tags"></i>
                                <span>标签</span>
                            </div>
                            <div class="nav-item">
                                    <i class="fas fa-user"></i>
                                    <span>我的</span>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        <!-- 用户个人资料页面 (新增) -->
            <div>
            <h3 class="screen-title">用户个人资料</h3>
                <div class="screen">
                <div class="profile-container">
                    <div class="profile-header">
                            <div class="back-btn">
                                <i class="fas fa-arrow-left"></i>
                            </div>
                        <div class="profile-header-title">个人资料</div>
                        <button class="profile-save-btn">保存</button>
                    </div>
                    
                    <div class="profile-avatar-section">
                        <div class="profile-avatar">张</div>
                        <div class="profile-avatar-edit">
                            <i class="fas fa-camera"></i>
                            <span>更换头像</span>
                                    </div>
                                </div>
                    
                    <div class="profile-info-section">
                        <div class="profile-info-item">
                            <div class="profile-info-label">用户名</div>
                            <input type="text" class="profile-info-input" value="张小明" placeholder="输入用户名">
                                </div>
                        
                        <div class="profile-info-item">
                            <div class="profile-info-label">邮箱</div>
                            <input type="email" class="profile-info-input" value="<EMAIL>" placeholder="输入邮箱">
                            </div>
                        
                        <div class="profile-info-item">
                            <div class="profile-info-label">手机</div>
                            <input type="tel" class="profile-info-input" value="138****1234" placeholder="输入手机号码">
                                    </div>
                        
                        <div class="profile-info-item">
                            <div class="profile-info-label">个人简介</div>
                            <input type="text" class="profile-info-input" value="热爱学习和记录的产品经理，对AI技术充满热情。" placeholder="简单介绍一下自己">
                                </div>
                                </div>
                    
                    <div class="profile-stats">
                        <div class="profile-stat-item">
                            <div class="profile-stat-value">42</div>
                            <div class="profile-stat-label">笔记</div>
                            </div>
                        <div class="profile-stat-item">
                            <div class="profile-stat-value">6</div>
                            <div class="profile-stat-label">标签</div>
                                    </div>
                        <div class="profile-stat-item">
                            <div class="profile-stat-value">15</div>
                            <div class="profile-stat-label">收藏</div>
                                </div>
                    </div>
                                </div>
                            </div>
                        </div>
                        
        <!-- 夜间模式主题设置 (新增) -->
                                    <div>
            <h3 class="screen-title">夜间模式主题设置</h3>
            <div class="screen">
                <div class="theme-container">
                    <div class="theme-header">
                        <div class="back-btn">
                            <i class="fas fa-arrow-left"></i>
                                    </div>
                        <div class="theme-header-title">外观与主题</div>
                        <div style="width: 36px;"></div>
                                </div>
                    
                    <div class="theme-content">
                        <div class="theme-section">
                            <div class="theme-section-title">主题模式</div>
                            <div class="theme-previews">
                                <div class="theme-preview theme-preview-light active">
                                    <div class="theme-preview-inner">
                                        <div class="theme-preview-header"></div>
                                        <div class="theme-preview-line"></div>
                                        <div class="theme-preview-line"></div>
                                        <div class="theme-preview-line short"></div>
                                </div>
                            </div>
                                <div class="theme-preview theme-preview-dark">
                                    <div class="theme-preview-inner">
                                        <div class="theme-preview-header"></div>
                                        <div class="theme-preview-line"></div>
                                        <div class="theme-preview-line"></div>
                                        <div class="theme-preview-line short"></div>
                                    </div>
                                </div>
                                </div>
                            
                            <div class="theme-option">
                                <div class="theme-option-label">跟随系统</div>
                                <div class="toggle-switch active"></div>
                            </div>
                            
                            <div class="dark-mode-time">
                                <div class="theme-option-label">自动夜间模式时间</div>
                                <div class="dark-mode-time-selector">
                                    <div class="dark-mode-time-input">
                                        <input type="text" class="time-input" value="21:00">
                                        <span>至</span>
                                        <input type="text" class="time-input" value="06:00">
                                    </div>
                                    <div class="toggle-switch"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="theme-section">
                            <div class="theme-section-title">主题颜色</div>
                            <div class="theme-color-options">
                                <div class="theme-color-option theme-color-purple active"></div>
                                <div class="theme-color-option theme-color-blue"></div>
                                <div class="theme-color-option theme-color-green"></div>
                                <div class="theme-color-option theme-color-orange"></div>
                                <div class="theme-color-option theme-color-red"></div>
                            </div>
                        </div>
                        
                        <div class="theme-section">
                            <div class="theme-option">
                                <div class="theme-option-label">字体大小</div>
                                <div style="color: var(--dark-gray);">中</div>
                            </div>
                            <div class="theme-option">
                                <div class="theme-option-label">纯文本编辑</div>
                                <div class="toggle-switch"></div>
                            </div>
                            <div class="theme-option">
                                <div class="theme-option-label">列表视图显示预览</div>
                                <div class="toggle-switch active"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- AI智能建议页面 (新增) -->
                                    <div>
            <h3 class="screen-title">AI智能建议</h3>
            <div class="screen">
                <div class="ai-suggestion-container">
                    <div class="ai-suggestion-header">
                        <i class="fas fa-arrow-left"></i>
                        <div class="reading-header-actions">
                            <i class="fas fa-ellipsis-h"></i>
                                    </div>
                                </div>
                    
                    <div class="ai-assistant-section">
                        <div class="ai-assistant-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-assistant-title">智能助手</div>
                        <div class="ai-assistant-desc">
                            基于您的笔记内容，我们为您提供以下智能建议和相关资源
                        </div>
                    </div>
                    
                    <div class="ai-suggestion-content">
                        <div class="ai-suggestion-section">
                            <div class="ai-suggestion-section-title">
                                <i class="fas fa-lightbulb"></i>
                                <span>内容优化建议</span>
                            </div>
                            
                            <div class="ai-suggestion-card">
                                <div class="ai-suggestion-card-title">补充产品功能细节</div>
                                <div class="ai-suggestion-card-content">
                                    您的需求文档可以在"智能文本预测"部分添加更多关于AI模型训练和个性化预测的细节。
                                </div>
                                <div class="ai-content-suggestion">
                                    建议添加：智能文本预测功能将基于用户的历史笔记和写作习惯，通过机器学习不断优化预测准确性。
                                </div>
                                <div class="ai-suggestion-actions">
                                    <div class="ai-suggestion-action">忽略</div>
                                    <div class="ai-suggestion-action">应用</div>
                                </div>
                            </div>
                            
                            <div class="ai-suggestion-card">
                                <div class="ai-suggestion-card-title">完善产品目标受众</div>
                                <div class="ai-suggestion-card-content">
                                    文档中未明确指出产品的目标用户群体，可以补充这部分信息。
                                </div>
                                <div class="ai-content-suggestion">
                                    建议添加：本产品主要面向学生、知识工作者和创意从业者，帮助他们更高效地记录和管理知识。
                                </div>
                                <div class="ai-suggestion-actions">
                                    <div class="ai-suggestion-action">忽略</div>
                                    <div class="ai-suggestion-action">应用</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ai-suggestion-section">
                            <div class="ai-suggestion-section-title">
                                <i class="fas fa-tag"></i>
                                <span>标签建议</span>
                            </div>
                            
                            <div class="ai-suggestion-card">
                                <div class="ai-suggestion-card-content">
                                    根据当前笔记内容，我们推荐以下标签：
                                </div>
                                <div class="ai-tag-suggestions">
                                    <div class="ai-tag-suggestion">
                                        <i class="fas fa-plus"></i>
                                        <span>产品需求</span>
                                    </div>
                                    <div class="ai-tag-suggestion">
                                        <i class="fas fa-plus"></i>
                                <span>AI功能</span>
                            </div>
                                    <div class="ai-tag-suggestion">
                                        <i class="fas fa-plus"></i>
                                        <span>项目文档</span>
                                    </div>
                                    <div class="ai-tag-suggestion">
                                        <i class="fas fa-plus"></i>
                                        <span>云服务</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="ai-suggestion-section">
                            <div class="ai-suggestion-section-title">
                                <i class="fas fa-link"></i>
                                <span>相关笔记</span>
                        </div>
                        
                            <div class="ai-suggestion-card">
                                <div class="ai-suggestion-card-content">
                                    您可能对这些相关笔记感兴趣：
                                </div>
                                <div class="ai-related-notes">
                                    <div class="ai-related-note">
                                        <div class="ai-related-note-icon">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div class="ai-related-note-info">
                                            <div class="ai-related-note-title">AI云笔记竞品分析</div>
                                            <div class="ai-related-note-meta">上周更新 · 工作</div>
                                        </div>
                                    </div>
                                    <div class="ai-related-note">
                                        <div class="ai-related-note-icon">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div class="ai-related-note-info">
                                            <div class="ai-related-note-title">项目开发时间表</div>
                                            <div class="ai-related-note-meta">3天前更新 · 工作</div>
                                        </div>
                                    </div>
                                    <div class="ai-related-note">
                                        <div class="ai-related-note-icon">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div class="ai-related-note-info">
                                            <div class="ai-related-note-title">云同步技术方案</div>
                                            <div class="ai-related-note-meta">2周前更新 · 技术</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                                </div>
                            </div>
                            
        <!-- 收藏笔记页面 (新增) -->
                                <div>
            <h3 class="screen-title">收藏笔记</h3>
            <div class="screen">
                <div class="favorites-container">
                    <div class="favorites-header">
                        <div class="favorites-title">收藏笔记</div>
                        <div class="favorites-count">15条收藏内容</div>
                                </div>
                    
                    <div class="favorites-list">
                        <div class="favorite-item">
                            <div class="favorite-star">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="note-title">产品需求文档：AI云笔记应用</div>
                            <div class="note-preview">本文档描述了AI云笔记应用的主要功能和界面设计。该应用旨在提供智能文本预测功能，帮助用户更高效地记录笔记。</div>
                                <div class="note-info">
                                <span class="note-tag">工作</span>
                                <span>1小时前</span>
                                </div>
                            </div>
                            
                        <div class="favorite-item">
                            <div class="favorite-star">
                                <i class="fas fa-star"></i>
                </div>
                            <div class="note-title">设计灵感：极简主义UI</div>
                            <div class="note-preview">收集了一些极简主义设计风格的UI案例，可以作为应用界面设计的参考。重点关注留白的运用和视觉层次的构建。</div>
                                <div class="note-info">
                                <span class="note-tag">设计</span>
                                <span>昨天</span>
                                </div>
                            </div>
                            
                        <div class="favorite-item">
                            <div class="favorite-star">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="note-title">Flutter开发技巧总结</div>
                            <div class="note-preview">记录了一些在Flutter开发中的实用技巧和常见问题解决方案，包括状态管理、性能优化和跨平台适配等方面。</div>
                                <div class="note-info">
                                <span class="note-tag">技术</span>
                                <span>3天前</span>
                                </div>
                            </div>
                            
                        <div class="favorite-item">
                            <div class="favorite-star">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="note-title">年度目标计划</div>
                            <div class="note-preview">制定了今年在个人成长和职业发展方面的主要目标，包括技能提升、项目完成和自我管理等各个方面。</div>
                                <div class="note-info">
                                <span class="note-tag">个人</span>
                                <span>上周</span>
                                </div>
                            </div>
                        </div>
                    
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                    </div>
                        <div class="nav-item active">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tag"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-cog"></i>
                            <span>设置</span>
                        </div>
                    </div>
                </div>
                </div>
            </div>
            
        <!-- 笔记历史版本页面 (新增) -->
            <div>
            <h3 class="screen-title">笔记历史版本</h3>
                <div class="screen">
                <div class="history-container">
                    <div class="history-header">
                        <i class="fas fa-arrow-left"></i>
                        <div class="history-header-title">历史版本</div>
                        <div style="width: 24px;"></div>
                    </div>
                    
                    <div class="history-content">
                        <div class="history-note-title">产品需求文档：AI云笔记应用</div>
                        
                        <div class="history-timeline">
                            <div class="history-item current">
                                <div class="history-marker"></div>
                                <div class="history-card">
                                    <div class="history-meta">
                                        <div class="history-date">今天 14:30</div>
                                        <div class="history-author">
                                            <div class="history-author-avatar">张</div>
                                            <span>张小明</span>
                                        </div>
                                    </div>
                                    <div class="history-content-preview">
                                        本文档描述了AI云笔记应用的主要功能和界面设计。该应用旨在提供智能文本预测功能，帮助用户更高效地记录笔记。
                                    </div>
                                    <div class="history-changes">
                                        <div class="history-changes-item">
                                            <div class="history-changes-icon"><i class="fas fa-plus"></i></div>
                                            <div class="history-changes-text">
                                                添加了<span class="added">语音转文字</span>功能描述
                                            </div>
                                        </div>
                                        <div class="history-changes-item">
                                            <div class="history-changes-icon"><i class="fas fa-edit"></i></div>
                                            <div class="history-changes-text">
                                                修改了<span class="removed">云同步</span>为<span class="added">云端同步和多设备访问</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="history-actions">
                                        <div class="history-action">预览</div>
                                        <div class="history-action">恢复</div>
                                    </div>
                            </div>
                        </div>
                        
                            <div class="history-item">
                                <div class="history-marker"></div>
                                <div class="history-card">
                                    <div class="history-meta">
                                        <div class="history-date">今天 10:15</div>
                                        <div class="history-author">
                                            <div class="history-author-avatar">张</div>
                                            <span>张小明</span>
                                        </div>
                                    </div>
                                    <div class="history-content-preview">
                                        本文档描述了AI云笔记应用的主要功能和界面设计。该应用旨在提供智能文本预测功能...
                                    </div>
                                    <div class="history-changes">
                                        <div class="history-changes-item">
                                            <div class="history-changes-icon"><i class="fas fa-plus"></i></div>
                                            <div class="history-changes-text">
                                                添加了<span class="added">智能内容建议</span>功能描述
                                            </div>
                                        </div>
                                    </div>
                                    <div class="history-actions">
                                        <div class="history-action">预览</div>
                                        <div class="history-action">恢复</div>
                                    </div>
                                </div>
                        </div>
                        
                            <div class="history-item">
                                <div class="history-marker"></div>
                                <div class="history-card">
                                    <div class="history-meta">
                                        <div class="history-date">昨天 16:45</div>
                                        <div class="history-author">
                                            <div class="history-author-avatar">张</div>
                                            <span>张小明</span>
                        </div>
                                    </div>
                                    <div class="history-content-preview">
                                        本文档描述了AI云笔记应用的主要功能...
                                    </div>
                                    <div class="history-actions">
                                        <div class="history-action">预览</div>
                                        <div class="history-action">恢复</div>
                                    </div>
                        </div>
                    </div>
                    
                            <div class="history-item">
                                <div class="history-marker"></div>
                                <div class="history-card">
                                    <div class="history-meta">
                                        <div class="history-date">3天前</div>
                                        <div class="history-author">
                                            <div class="history-author-avatar">李</div>
                                            <span>李明</span>
                        </div>
                        </div>
                                    <div class="history-content-preview">
                                        初始文档创建
                                    </div>
                                    <div class="history-actions">
                                        <div class="history-action">预览</div>
                                        <div class="history-action">恢复</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 笔记分享页面 (新增) -->
                                        <div>
            <h3 class="screen-title">笔记分享</h3>
            <div class="screen">
                <div class="share-container">
                    <div class="share-header">
                        <i class="fas fa-arrow-left"></i>
                        <div class="share-header-title">分享笔记</div>
                        <div style="width: 24px;"></div>
                                        </div>
                    
                    <div class="share-content">
                        <div class="share-preview">
                            <div class="share-preview-header">
                                <div class="share-preview-title">产品需求文档：AI云笔记应用</div>
                                <div class="share-preview-meta">
                                    <div class="share-preview-author">
                                        <div class="share-preview-author-avatar">张</div>
                                        <span>张小明</span>
                                    </div>
                                </div>
                            </div>
                            <div class="share-preview-content">
                                <div class="share-preview-text">
                                    本文档描述了AI云笔记应用的主要功能和界面设计。该应用旨在提供智能文本预测功能，帮助用户更高效地记录笔记。
                                    <br><br>
                                    主要功能包括：<br>
                                    1. 智能文本预测<br>
                                    2. 笔记管理和分类<br>
                                    3. 云端同步和多设备访问<br>
                                    4. 智能内容建议<br>
                                    5. 语音转文字
                                </div>
                                <div class="share-preview-fade"></div>
                                <div class="share-preview-watermark">
                                    <i class="fas fa-cloud"></i>
                                    <span>AI云笔记</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="share-options">
                            <div class="share-option-title">权限设置</div>
                            <div class="share-permission-options">
                                <div class="share-permission-option selected">
                                    <div class="share-permission-radio"></div>
                                    <div class="share-permission-info">
                                        <div class="share-permission-title">仅查看</div>
                                        <div class="share-permission-desc">被分享者只能查看笔记内容</div>
                                    </div>
                                </div>
                                <div class="share-permission-option">
                                    <div class="share-permission-radio"></div>
                                    <div class="share-permission-info">
                                        <div class="share-permission-title">可编辑</div>
                                        <div class="share-permission-desc">被分享者可以查看和编辑笔记</div>
                                    </div>
                                </div>
                                <div class="share-permission-option">
                                    <div class="share-permission-radio"></div>
                                    <div class="share-permission-info">
                                        <div class="share-permission-title">公开分享</div>
                                        <div class="share-permission-desc">任何人都可以通过链接查看</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="share-expiry">
                                <div class="share-option-title">有效期</div>
                                <select class="share-expiry-select">
                                    <option>永久有效</option>
                                    <option>7天</option>
                                    <option>30天</option>
                                    <option selected>24小时</option>
                                    <option>自定义</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="share-option-title">分享方式</div>
                        <div class="share-methods">
                            <div class="share-method share-method-wechat">
                                <div class="share-method-icon">
                                    <i class="fab fa-weixin"></i>
                                </div>
                                <div class="share-method-label">微信</div>
                            </div>
                            <div class="share-method share-method-link">
                                <div class="share-method-icon">
                                    <i class="fas fa-link"></i>
                                </div>
                                <div class="share-method-label">复制链接</div>
                            </div>
                            <div class="share-method share-method-qrcode">
                                <div class="share-method-icon">
                                    <i class="fas fa-qrcode"></i>
                                </div>
                                <div class="share-method-label">二维码</div>
                            </div>
                            <div class="share-method share-method-more">
                                <div class="share-method-icon">
                                    <i class="fas fa-ellipsis-h"></i>
                                </div>
                                <div class="share-method-label">更多</div>
                            </div>
                        </div>
                        
                        <div class="share-link-container">
                            <input type="text" class="share-link-input" value="https://ai-cloud-notes.com/s/1a2b3c4d" readonly>
                            <div class="share-link-copy">复制</div>
                        </div>
                        
                        <button class="share-btn">确认分享</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置页面部分 -->
        <!-- 设置页面 -->
                                    <div>
            <h3 class="screen-title">设置页面</h3>
            <p class="screen-details">管理应用设置和用户偏好，包括主题、通知和同步选项</p>
            <div class="screen-nav">
                <button class="screen-nav-button">返回首页</button>
                <button class="screen-nav-button">查看个人信息</button>
                <button class="screen-nav-button">主题设置</button>
                                    </div>
            <div class="screen">
                <div class="settings-container">
                    <div class="settings-header">
                        <i class="fas fa-arrow-left"></i>
                        <h3>应用设置</h3>
                                </div>
                    <div class="settings-content">
                        <div class="settings-group">
                            <div class="settings-group-title">账号与安全</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                    <i class="fas fa-user"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">个人资料</div>
                                        <div class="settings-item-desc">编辑个人信息和头像</div>
                                        </div>
                                    </div>
                                        <i class="fas fa-chevron-right"></i>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                    <i class="fas fa-lock"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">密码与安全</div>
                                        <div class="settings-item-desc">修改密码和安全设置</div>
                                        </div>
                                    </div>
                                        <i class="fas fa-chevron-right"></i>
                                    </div>
                            <div class="settings-item">
                                <div class="settings-item-left">
                                    <i class="fas fa-bell"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">通知设置</div>
                                        <div class="settings-item-desc">管理应用通知和提醒</div>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                            
                            <div class="settings-group">
                            <div class="settings-group-title">显示与主题</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                    <i class="fas fa-moon"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">夜间模式</div>
                                        <div class="settings-item-desc">深色主题设置</div>
                                        </div>
                                    </div>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="darkMode">
                                    <label for="darkMode"></label>
                                    </div>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                    <i class="fas fa-palette"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">主题颜色</div>
                                        <div class="settings-item-desc">自定义应用颜色方案</div>
                                        </div>
                                    </div>
                                <i class="fas fa-chevron-right"></i>
                                    </div>
                            <div class="settings-item">
                                <div class="settings-item-left">
                                    <i class="fas fa-font"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">字体设置</div>
                                        <div class="settings-item-desc">调整字体大小和类型</div>
                                </div>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                        
                        <div class="settings-group">
                            <div class="settings-group-title">数据与存储</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                    <i class="fas fa-sync"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">数据同步</div>
                                        <div class="settings-item-desc">管理云同步设置</div>
                                        </div>
                                    </div>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="syncData" checked>
                                    <label for="syncData"></label>
                                    </div>
                                </div>
                            <div class="settings-item">
                                <div class="settings-item-left">
                                    <i class="fas fa-hdd"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">存储管理</div>
                                        <div class="settings-item-desc">查看和清理存储空间</div>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                            <div class="settings-item">
                                <div class="settings-item-left">
                                    <i class="fas fa-download"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">导出数据</div>
                                        <div class="settings-item-desc">导出笔记和设置</div>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                                </div>
                            </div>
                            
                            <div class="settings-group">
                            <div class="settings-group-title">关于</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                    <i class="fas fa-info-circle"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">应用信息</div>
                                        <div class="settings-item-desc">版本 0.5.1</div>
                                        </div>
                                    </div>
                                    </div>
                            <div class="settings-item">
                                <div class="settings-item-left">
                                    <i class="fas fa-book"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">使用帮助</div>
                                        <div class="settings-item-desc">查看使用教程和常见问题</div>
                                    </div>
                                </div>
                                <i class="fas fa-chevron-right"></i>
                                </div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                    <i class="fas fa-file-alt"></i>
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">用户协议与隐私政策</div>
                                        </div>
                                    </div>
                                <i class="fas fa-chevron-right"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item active">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 夜间模式主题设置 -->
                                    <div>
            <h3 class="screen-title">夜间模式主题设置</h3>
            <p class="screen-details">调整夜间模式显示、开启时间和暗色主题偏好</p>
            <div class="screen-nav">
                <button class="screen-nav-button">返回设置</button>
                <button class="screen-nav-button">应用设置</button>
                                    </div>
            <div class="screen">
                <div class="theme-container">
                    <div class="theme-header">
                        <i class="fas fa-arrow-left"></i>
                        <h3>夜间模式</h3>
                                </div>
                    <div class="theme-content">
                        <div class="theme-section">
                            <div class="section-title">主题模式</div>
                            <div class="theme-options">
                                <div class="theme-option light active">
                                    <div class="theme-preview light-preview"></div>
                                    <div class="theme-name">浅色</div>
                                </div>
                                <div class="theme-option dark">
                                    <div class="theme-preview dark-preview"></div>
                                    <div class="theme-name">深色</div>
                                </div>
                                <div class="theme-option auto">
                                    <div class="theme-preview auto-preview">
                                        <div class="auto-preview-half light"></div>
                                        <div class="auto-preview-half dark"></div>
                                    </div>
                                    <div class="theme-name">跟随系统</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="theme-section">
                            <div class="section-title">自动夜间模式</div>
                                <div class="settings-item">
                                    <div class="settings-item-left">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">定时开启</div>
                                        <div class="settings-item-desc">在设定的时间段内自动切换至夜间模式</div>
                                    </div>
                                </div>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="autoNightMode">
                                    <label for="autoNightMode"></label>
                                </div>
                            </div>
                            <div class="time-settings">
                                <div class="time-setting">
                                    <label>开始时间</label>
                                    <div class="time-picker">21:00</div>
                                </div>
                                <div class="time-setting">
                                    <label>结束时间</label>
                                    <div class="time-picker">7:00</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="theme-section">
                            <div class="section-title">深色主题颜色</div>
                            <div class="color-options">
                                <div class="color-option" style="background-color: #121212;"></div>
                                <div class="color-option" style="background-color: #1F1F1F;"></div>
                                <div class="color-option active" style="background-color: #2D2D2D;"></div>
                                <div class="color-option" style="background-color: #1A237E;"></div>
                                <div class="color-option" style="background-color: #0D47A1;"></div>
                                <div class="color-option" style="background-color: #006064;"></div>
                            </div>
                        </div>
                        
                        <div class="theme-section">
                            <div class="settings-item">
                                <div class="settings-item-left">
                                    <div class="settings-item-info">
                                        <div class="settings-item-title">高对比度</div>
                                        <div class="settings-item-desc">增强文本和背景色的对比度</div>
                                    </div>
                                </div>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="highContrast">
                                    <label for="highContrast"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 用户个人资料 -->
                                        <div>
            <h3 class="screen-title">用户个人资料</h3>
            <p class="screen-details">查看和编辑个人信息，包括头像、用户名和邮箱</p>
            <div class="screen-nav">
                <button class="screen-nav-button">返回设置</button>
                <button class="screen-nav-button">编辑资料</button>
                                        </div>
            <div class="screen">
                <div class="profile-container">
                    <div class="profile-header">
                        <i class="fas fa-arrow-left"></i>
                        <h3>个人资料</h3>
                        <span class="edit-profile">编辑</span>
                                    </div>
                    <div class="profile-content">
                        <div class="profile-avatar-section">
                            <div class="profile-avatar">
                                <img src="https://ui-avatars.com/api/?name=用户&background=4F46E5&color=fff&size=200" alt="User Avatar">
                                <div class="change-avatar">
                                    <i class="fas fa-camera"></i>
                                </div>
                            </div>
                            <h2 class="profile-name">用户名</h2>
                            <p class="profile-email"><EMAIL></p>
                        </div>
                        
                        <div class="profile-info-section">
                            <div class="profile-info-item">
                                <div class="info-label">用户名</div>
                                <div class="info-value">用户名</div>
                            </div>
                            <div class="profile-info-item">
                                <div class="info-label">邮箱</div>
                                <div class="info-value"><EMAIL></div>
                            </div>
                            <div class="profile-info-item">
                                <div class="info-label">手机号</div>
                                <div class="info-value">未绑定</div>
                                <div class="info-action">绑定</div>
                            </div>
                            <div class="profile-info-item">
                                <div class="info-label">登录设备</div>
                                <div class="info-value">3台设备</div>
                                <div class="info-action">管理</div>
                            </div>
                        </div>
                        
                        <div class="profile-stats-section">
                            <div class="profile-stats-title">笔记统计</div>
                            <div class="profile-stats">
                                <div class="profile-stat">
                                    <div class="profile-stat-value">68</div>
                                    <div class="profile-stat-label">笔记</div>
                                </div>
                                <div class="profile-stat">
                                    <div class="profile-stat-value">12</div>
                                    <div class="profile-stat-label">标签</div>
                                </div>
                                <div class="profile-stat">
                                    <div class="profile-stat-value">15</div>
                                    <div class="profile-stat-label">收藏</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item active">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <h2 class="flow-title">5. AI智能功能</h2>
            <div class="prototype-container">
                <!-- AI建议功能 -->
                                    <div>
                    <h3 class="screen-title">AI建议功能</h3>
                    <p class="screen-details">提供AI生成的内容建议和智能补全功能</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回编辑</button>
                        <button class="screen-nav-button">接受建议</button>
                        <button class="screen-nav-button">生成更多</button>
                                    </div>
                    <div class="screen">
                        <div class="ai-suggestions-container">
                            <div class="ai-header">
                                <i class="fas fa-arrow-left"></i>
                                <h3>AI智能建议</h3>
                                <i class="fas fa-ellipsis-v"></i>
                                </div>
                            <div class="ai-content">
                                <div class="ai-assistant-info">
                                    <div class="ai-assistant-avatar">
                                        <i class="fas fa-robot"></i>
                            </div>
                                    <div class="ai-assistant-details">
                                        <div class="ai-assistant-name">智云助手</div>
                                        <div class="ai-assistant-desc">基于您的笔记内容提供智能建议</div>
                        </div>
                    </div>
                                
                                <div class="suggestions-for">
                                    <div class="suggestion-source">建议针对：<span>工作计划</span></div>
                                </div>
                                
                                <div class="suggestion-section">
                                    <div class="section-title">内容补充建议</div>
                                    <div class="suggestion-cards">
                                        <div class="suggestion-card">
                                            <div class="suggestion-title">添加任务项</div>
                                            <div class="suggestion-content">
                                                <ul>
                                                    <li>准备演示文稿</li>
                                                    <li>邮件沟通项目进度</li>
                                                    <li>安排下周团队会议</li>
                                                </ul>
                                            </div>
                                            <div class="suggestion-actions">
                                                <button class="accept-btn">全部接受</button>
                                                <button class="partial-btn">部分接受</button>
                                            </div>
                                        </div>
                                        <div class="suggestion-card">
                                            <div class="suggestion-title">优先级分类</div>
                                            <div class="suggestion-content">
                                                <p>根据截止日期，建议任务优先级：</p>
                                                <p><strong>高优先级：</strong>项目计划书、客户会议</p>
                                                <p><strong>中优先级：</strong>团队协调</p>
                                                <p><strong>低优先级：</strong>进度报告</p>
                                            </div>
                                            <div class="suggestion-actions">
                                                <button class="accept-btn">接受</button>
                                                <button class="ignore-btn">忽略</button>
                                            </div>
                                        </div>
                </div>
            </div>
            
                                <div class="suggestion-section">
                                    <div class="section-title">相关内容</div>
                                    <div class="related-notes">
                                        <div class="related-note">
                                            <div class="related-note-title">上周工作总结</div>
                                            <div class="related-note-preview">本周工作计划中的一些任务与上周工作有关联，建议参考。</div>
                                            <div class="related-note-actions">
                                                <button class="view-btn">查看</button>
                                            </div>
                                        </div>
                                        <div class="related-note">
                                            <div class="related-note-title">项目时间表模板</div>
                                            <div class="related-note-preview">您有一个项目时间表模板，可以用来安排本周的工作计划。</div>
                                            <div class="related-note-actions">
                                                <button class="view-btn">查看</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 笔记历史版本 -->
            <div>
                    <h3 class="screen-title">笔记历史版本</h3>
                    <p class="screen-details">查看笔记的历史修改记录，可以恢复之前的版本</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回笔记</button>
                        <button class="screen-nav-button">对比版本</button>
                        <button class="screen-nav-button">恢复版本</button>
                    </div>
                <div class="screen">
                        <div class="history-container">
                            <div class="history-header">
                                <i class="fas fa-arrow-left"></i>
                                <h3>版本历史</h3>
                            </div>
                            <div class="history-content">
                                <div class="current-note-title">工作计划</div>
                                <div class="history-timeline">
                                    <div class="history-item current">
                                        <div class="history-metadata">
                                            <div class="history-date">
                                                <div class="date">今天 15:30</div>
                                                <div class="author">当前版本</div>
                                            </div>
                                            <div class="history-actions">
                                                <button class="view-btn">查看</button>
                                            </div>
                                        </div>
                                        <div class="history-preview">
                                            <p><strong>本周需要完成的任务：</strong></p>
                                            <ul>
                                                <li>项目计划书</li>
                                                <li>客户会议</li>
                                                <li>团队协调</li>
                                                <li>进度报告</li>
                                            </ul>
                                            <p>项目截止日期：2023-08-20</p>
                                            <p>预计所需时间：15小时</p>
                                        </div>
                                    </div>
                                    
                                    <div class="history-item">
                                        <div class="history-metadata">
                                            <div class="history-date">
                                                <div class="date">今天 14:25</div>
                                                <div class="author">您</div>
                                            </div>
                                            <div class="history-actions">
                                                <button class="view-btn">查看</button>
                                                <button class="restore-btn">恢复</button>
                                            </div>
                                        </div>
                                        <div class="history-preview">
                                            <p><strong>本周需要完成的任务：</strong></p>
                                            <ul>
                                                <li>项目计划书</li>
                                                <li>客户会议</li>
                                                <li>团队协调</li>
                                            </ul>
                                            <p>项目截止日期：2023-08-20</p>
                                            <div class="change-indicator removed">
                                                <i class="fas fa-minus-circle"></i>
                                                <span>预计所需时间：12小时</span>
                                            </div>
                                            <div class="change-indicator added">
                                                <i class="fas fa-plus-circle"></i>
                                                <span>预计所需时间：15小时</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="history-item">
                                        <div class="history-metadata">
                                            <div class="history-date">
                                                <div class="date">昨天 18:10</div>
                                                <div class="author">您</div>
                                            </div>
                                            <div class="history-actions">
                                                <button class="view-btn">查看</button>
                                                <button class="restore-btn">恢复</button>
                                            </div>
                                        </div>
                                        <div class="history-preview">
                                            <p><strong>本周需要完成的任务：</strong></p>
                                            <ul>
                                                <li>项目计划书</li>
                                                <li>客户会议</li>
                                                <div class="change-indicator added">
                                                    <i class="fas fa-plus-circle"></i>
                                                    <span>团队协调</span>
                                                </div>
                                            </ul>
                                            <p>项目截止日期：2023-08-20</p>
                                            <p>预计所需时间：12小时</p>
                                        </div>
                                    </div>
                                    
                                    <div class="history-item">
                                        <div class="history-metadata">
                                            <div class="history-date">
                                                <div class="date">2023-08-14</div>
                                                <div class="author">您</div>
                                            </div>
                                            <div class="history-actions">
                                                <button class="view-btn">查看</button>
                                                <button class="restore-btn">恢复</button>
                                            </div>
                                        </div>
                                        <div class="history-preview">
                                            <p><strong>本周需要完成的任务：</strong></p>
                                            <ul>
                                                <li>项目计划书</li>
                                                <li>客户会议</li>
                                            </ul>
                                            <p>项目截止日期：2023-08-20</p>
                                            <p>预计所需时间：8小时</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-section">
            <h2 class="flow-title">6. 笔记分享与协作</h2>
            <div class="prototype-container">
                <!-- 笔记分享页面 -->
                            <div>
                    <h3 class="screen-title">笔记分享</h3>
                    <p class="screen-details">分享笔记给他人，设置查看和编辑权限</p>
                    <div class="screen-nav">
                        <button class="screen-nav-button">返回笔记</button>
                        <button class="screen-nav-button">分享历史</button>
                        <button class="screen-nav-button">确认分享</button>
                    </div>
                    <div class="screen">
                        <div class="share-container">
                            <div class="share-header">
                                <i class="fas fa-arrow-left"></i>
                                <h3>分享笔记</h3>
                            </div>
                            <div class="share-content">
                                <div class="share-preview">
                                    <div class="preview-title">工作计划</div>
                                    <div class="preview-meta">
                                        <div class="preview-author">您 · 今天 15:30</div>
                                        <div class="preview-tag">工作</div>
                                    </div>
                                    <div class="preview-content">
                                        本周需要完成的任务：1. 项目计划书 2. 客户会议 3. 团队协调 4. 进度报告...
                            </div>
                        </div>
                        
                                <div class="share-settings">
                                    <div class="settings-title">权限设置</div>
                                    <div class="permission-options">
                                        <div class="permission-option">
                                            <input type="radio" id="viewOnly" name="permission" checked>
                                            <label for="viewOnly">
                                                <div class="option-info">
                                                    <div class="option-title">
                                                        <i class="fas fa-eye"></i>
                                                        <span>仅查看</span>
                                                    </div>
                                                    <div class="option-desc">接收者只能查看笔记内容</div>
                                                </div>
                                            </label>
                                        </div>
                                        <div class="permission-option">
                                            <input type="radio" id="editable" name="permission">
                                            <label for="editable">
                                                <div class="option-info">
                                                    <div class="option-title">
                                                        <i class="fas fa-edit"></i>
                                                        <span>可编辑</span>
                                                    </div>
                                                    <div class="option-desc">接收者可以查看和编辑笔记</div>
                                                </div>
                                            </label>
                                        </div>
                                        <div class="permission-option">
                                            <input type="radio" id="public" name="permission">
                                            <label for="public">
                                                <div class="option-info">
                                                    <div class="option-title">
                                                        <i class="fas fa-globe"></i>
                                                        <span>公开分享</span>
                                                    </div>
                                                    <div class="option-desc">任何人都可以通过链接查看</div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                        </div>
                        
                                <div class="expiry-settings">
                                    <div class="settings-title">过期设置</div>
                                    <div class="expiry-selector">
                                        <select>
                                            <option>永久有效</option>
                                            <option>7天后过期</option>
                                            <option>30天后过期</option>
                                            <option>24小时后过期</option>
                                            <option>自定义</option>
                                        </select>
                                        <i class="fas fa-chevron-down"></i>
                            </div>
                                </div>
                                
                                <div class="share-methods">
                                    <div class="settings-title">分享方式</div>
                                    <div class="share-methods-grid">
                                        <div class="share-method">
                                            <div class="method-icon wechat">
                                                <i class="fab fa-weixin"></i>
                                            </div>
                                            <div class="method-name">微信</div>
                                        </div>
                                        <div class="share-method">
                                            <div class="method-icon link">
                                                <i class="fas fa-link"></i>
                                            </div>
                                            <div class="method-name">复制链接</div>
                                        </div>
                                        <div class="share-method">
                                            <div class="method-icon qrcode">
                                                <i class="fas fa-qrcode"></i>
                                            </div>
                                            <div class="method-name">二维码</div>
                                        </div>
                                        <div class="share-method">
                                            <div class="method-icon more">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </div>
                                            <div class="method-name">更多</div>
                                        </div>
                            </div>
                        </div>
                        
                                <div class="share-link">
                                    <div class="link-input">
                                        <input type="text" value="https://aicloud.notes/share/JK38Ls9" readonly>
                                        <button class="copy-btn">复制</button>
                            </div>
                            </div>
                                
                                <button class="share-btn">确认分享</button>
                        </div>
                    </div>
                </div>
            </div>

                <!-- 页面设计导航图 -->
                <div>
                    <h3 class="screen-title">页面导航图</h3>
                    <p class="screen-details">展示应用所有页面之间的关联和导航路径</p>
                    <div class="screen">
                        <div class="navigation-map-container">
                            <div class="navigation-map-header">
                                <h3>页面导航关系图</h3>
                            </div>
                            <div class="navigation-map">
                                <svg width="320" height="640" viewBox="0 0 320 640">
                                    <!-- 登录/注册流程 -->
                                    <rect x="40" y="40" width="100" height="40" rx="5" fill="#4F46E5" stroke="none" />
                                    <text x="90" y="65" font-size="12" fill="white" text-anchor="middle">登录页面</text>
                                    
                                    <rect x="180" y="40" width="100" height="40" rx="5" fill="#4F46E5" stroke="none" />
                                    <text x="230" y="65" font-size="12" fill="white" text-anchor="middle">注册页面</text>
                                    
                                    <line x1="140" y1="60" x2="180" y2="60" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="176,56 184,60 176,64" fill="#6B7280" />
                                    
                                    <!-- 主页流程 -->
                                    <rect x="90" y="120" width="140" height="40" rx="5" fill="#10B981" stroke="none" />
                                    <text x="160" y="145" font-size="12" fill="white" text-anchor="middle">主页/笔记列表</text>
                                    
                                    <line x1="90" y1="80" x2="90" y2="120" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="86,116 90,124 94,116" fill="#6B7280" />
                                    
                                    <line x1="230" y1="80" x2="160" y2="120" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="158,116 162,124 166,116" fill="#6B7280" />
                                    
                                    <!-- 笔记管理流程 -->
                                    <rect x="20" y="200" width="80" height="40" rx="5" fill="#06B6D4" stroke="none" />
                                    <text x="60" y="225" font-size="12" fill="white" text-anchor="middle">笔记编辑</text>
                                    
                                    <rect x="120" y="200" width="80" height="40" rx="5" fill="#06B6D4" stroke="none" />
                                    <text x="160" y="225" font-size="12" fill="white" text-anchor="middle">笔记阅读</text>
                                    
                                    <rect x="220" y="200" width="80" height="40" rx="5" fill="#06B6D4" stroke="none" />
                                    <text x="260" y="225" font-size="12" fill="white" text-anchor="middle">历史版本</text>
                                    
                                    <line x1="160" y1="160" x2="60" y2="200" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="58,196 62,204 66,196" fill="#6B7280" />
                                    
                                    <line x1="160" y1="160" x2="160" y2="200" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="156,196 160,204 164,196" fill="#6B7280" />
                                    
                                    <line x1="160" y1="160" x2="260" y2="200" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="258,196 262,204 266,196" fill="#6B7280" />
                                    
                                    <!-- 标签管理流程 -->
                                    <rect x="20" y="280" width="80" height="40" rx="5" fill="#F59E0B" stroke="none" />
                                    <text x="60" y="305" font-size="12" fill="white" text-anchor="middle">标签管理</text>
                                    
                                    <rect x="120" y="280" width="80" height="40" rx="5" fill="#F59E0B" stroke="none" />
                                    <text x="160" y="305" font-size="12" fill="white" text-anchor="middle">标签详情</text>
                                    
                                    <rect x="220" y="280" width="80" height="40" rx="5" fill="#F59E0B" stroke="none" />
                                    <text x="260" y="305" font-size="12" fill="white" text-anchor="middle">搜索结果</text>
                                    
                                    <line x1="160" y1="160" x2="60" y2="280" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="58,276 62,284 66,276" fill="#6B7280" />
                                    
                                    <line x1="60" y1="280" x2="120" y2="280" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="116,276 124,280 116,284" fill="#6B7280" />
                                    
                                    <line x1="160" y1="160" x2="260" y2="280" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="258,276 262,284 266,276" fill="#6B7280" />
                                    
                                    <!-- 收藏与设置流程 -->
                                    <rect x="20" y="360" width="80" height="40" rx="5" fill="#EF4444" stroke="none" />
                                    <text x="60" y="385" font-size="12" fill="white" text-anchor="middle">收藏页面</text>
                                    
                                    <rect x="120" y="360" width="80" height="40" rx="5" fill="#EF4444" stroke="none" />
                                    <text x="160" y="385" font-size="12" fill="white" text-anchor="middle">设置页面</text>
                                    
                                    <rect x="220" y="360" width="80" height="40" rx="5" fill="#EF4444" stroke="none" />
                                    <text x="260" y="385" font-size="12" fill="white" text-anchor="middle">个人资料</text>
                                    
                                    <line x1="160" y1="160" x2="60" y2="360" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="58,356 62,364 66,356" fill="#6B7280" />
                                    
                                    <line x1="160" y1="160" x2="160" y2="360" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="156,356 160,364 164,356" fill="#6B7280" />
                                    
                                    <line x1="160" y1="360" x2="220" y2="360" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="216,356 224,360 216,364" fill="#6B7280" />
                                    
                                    <!-- AI功能流程 -->
                                    <rect x="20" y="440" width="80" height="40" rx="5" fill="#8B5CF6" stroke="none" />
                                    <text x="60" y="465" font-size="12" fill="white" text-anchor="middle">AI建议</text>
                                    
                                    <line x1="60" y1="240" x2="60" y2="440" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="56,436 60,444 64,436" fill="#6B7280" />
                                    
                                    <!-- 分享流程 -->
                                    <rect x="120" y="440" width="80" height="40" rx="5" fill="#EC4899" stroke="none" />
                                    <text x="160" y="465" font-size="12" fill="white" text-anchor="middle">笔记分享</text>
                                    
                                    <line x1="160" y1="240" x2="160" y2="440" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="156,436 160,444 164,436" fill="#6B7280" />
                                    
                                    <!-- 夜间模式 -->
                                    <rect x="220" y="440" width="80" height="40" rx="5" fill="#1F2937" stroke="none" />
                                    <text x="260" y="465" font-size="12" fill="white" text-anchor="middle">夜间模式</text>
                                    
                                    <line x1="160" y1="400" x2="260" y2="440" stroke="#6B7280" stroke-width="2" />
                                    <polygon points="258,436 262,444 266,436" fill="#6B7280" />
                                    
                                    <!-- 底部导航 -->
                                    <rect x="20" y="520" width="280" height="30" rx="5" fill="#6B7280" stroke="none" />
                                    <text x="160" y="540" font-size="12" fill="white" text-anchor="middle">底部导航栏（首页、收藏、添加、标签、我的）</text>
                                    
                                    <line x1="60" y1="480" x2="60" y2="520" stroke="#6B7280" stroke-width="2" />
                                    <line x1="160" y1="480" x2="160" y2="520" stroke="#6B7280" stroke-width="2" />
                                    <line x1="260" y1="480" x2="260" y2="520" stroke="#6B7280" stroke-width="2" />
                                    
                                    <!-- 图例 -->
                                    <rect x="20" y="580" width="10" height="10" fill="#4F46E5" stroke="none" />
                                    <text x="40" y="590" font-size="10" fill="#1F2937">用户认证</text>
                                    
                                    <rect x="120" y="580" width="10" height="10" fill="#10B981" stroke="none" />
                                    <text x="140" y="590" font-size="10" fill="#1F2937">主页</text>
                                    
                                    <rect x="220" y="580" width="10" height="10" fill="#06B6D4" stroke="none" />
                                    <text x="240" y="590" font-size="10" fill="#1F2937">笔记管理</text>
                                    
                                    <rect x="20" y="600" width="10" height="10" fill="#F59E0B" stroke="none" />
                                    <text x="40" y="610" font-size="10" fill="#1F2937">标签管理</text>
                                    
                                    <rect x="120" y="600" width="10" height="10" fill="#EF4444" stroke="none" />
                                    <text x="140" y="610" font-size="10" fill="#1F2937">收藏与设置</text>
                                    
                                    <rect x="220" y="600" width="10" height="10" fill="#8B5CF6" stroke="none" />
                                    <text x="240" y="610" font-size="10" fill="#1F2937">AI功能</text>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
    
        <style>
            /* 笔记分享页面样式 */
            .share-container {
                height: 100%;
                width: 100%;
                display: flex;
                flex-direction: column;
                background-color: var(--white);
            }
            
            .share-header {
                padding: 16px;
                display: flex;
                align-items: center;
                background-color: var(--white);
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            }
            
            .share-header h3 {
                font-size: 1.2rem;
                font-weight: 600;
                margin: 0 0 0 16px;
            }
            
            .share-content {
                flex: 1;
                overflow-y: auto;
                padding: 16px;
                display: flex;
                flex-direction: column;
                gap: 24px;
            }
            
            .share-preview {
                background-color: var(--light-gray);
                border-radius: 12px;
                padding: 16px;
            }
            
            .preview-title {
                font-weight: 600;
                font-size: 1.1rem;
                margin-bottom: 8px;
            }
            
            .preview-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                font-size: 0.8rem;
            }
            
            .preview-author {
                color: var(--dark-gray);
            }
            
            .preview-tag {
                background-color: var(--primary-light);
                color: var(--primary-color);
                padding: 2px 8px;
                border-radius: 4px;
            }
            
            .preview-content {
                color: var(--dark-gray);
                font-size: 0.9rem;
            }
            
            .settings-title {
                font-weight: 600;
                margin-bottom: 12px;
            }
            
            .permission-options {
                display: flex;
                flex-direction: column;
                gap: 12px;
            }
            
            .permission-option {
                position: relative;
            }
            
            .permission-option input[type="radio"] {
                position: absolute;
                opacity: 0;
            }
            
            .permission-option label {
                display: flex;
                align-items: center;
                padding: 12px;
                background-color: var(--light-gray);
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.2s;
            }
            
            .permission-option input[type="radio"]:checked + label {
                background-color: var(--primary-light);
                border-left: 3px solid var(--primary-color);
            }
            
            .option-info {
                margin-left: 12px;
            }
            
            .option-title {
                display: flex;
                align-items: center;
                font-weight: 500;
                margin-bottom: 4px;
            }
            
            .option-title i {
                margin-right: 8px;
                color: var(--primary-color);
            }
            
            .option-desc {
                color: var(--dark-gray);
                font-size: 0.8rem;
            }
            
            .expiry-selector {
                position: relative;
                background-color: var(--light-gray);
                border-radius: 8px;
            }
            
            .expiry-selector select {
                width: 100%;
                padding: 12px;
                appearance: none;
                border: none;
                background: transparent;
                font-family: inherit;
                cursor: pointer;
            }
            
            .expiry-selector i {
                position: absolute;
                right: 12px;
                top: 50%;
                transform: translateY(-50%);
                color: var(--dark-gray);
                pointer-events: none;
            }
            
            .share-methods-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 12px;
            }
            
            .share-method {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 8px;
            }
            
            .method-icon {
                width: 48px;
                height: 48px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.4rem;
                color: white;
            }
            
            .method-icon.wechat {
                background-color: #07C160;
            }
            
            .method-icon.link {
                background-color: #3B82F6;
            }
            
            .method-icon.qrcode {
                background-color: #10B981;
            }
            
            .method-icon.more {
                background-color: #6B7280;
            }
            
            .method-name {
                font-size: 0.8rem;
                color: var(--dark-gray);
            }
            
            .share-link {
                margin-top: 8px;
            }
            
            .link-input {
                display: flex;
                align-items: center;
                background-color: var(--light-gray);
                border-radius: 8px;
                padding: 4px;
            }
            
            .link-input input {
                flex: 1;
                border: none;
                background: transparent;
                padding: 8px 12px;
                font-size: 0.9rem;
            }
            
            .copy-btn {
                background-color: var(--primary-color);
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                cursor: pointer;
                transition: background-color 0.2s;
            }
            
            .copy-btn:hover {
                background-color: #4338ca;
            }
            
            .share-btn {
                background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
                color: white;
                border: none;
                padding: 12px;
                border-radius: 8px;
                font-weight: 500;
                cursor: pointer;
                transition: transform 0.2s;
                margin-top: 8px;
            }
            
            .share-btn:hover {
                transform: translateY(-2px);
            }
            
            /* 页面导航图样式 */
            .navigation-map-container {
                height: 100%;
                width: 100%;
                display: flex;
                flex-direction: column;
                background-color: var(--white);
            }
            
            .navigation-map-header {
                padding: 16px;
                text-align: center;
                background-color: var(--white);
                box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            }
            
            .navigation-map-header h3 {
                font-size: 1.2rem;
                font-weight: 600;
                margin: 0;
            }
            
            .navigation-map {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 16px;
                overflow: auto;
            }
            
            .navigation-map svg {
                max-width: 100%;
                height: auto;
            }
            
            /* 页面底部样式 */
            .page-footer {
                text-align: center;
                margin-top: 40px;
                margin-bottom: 20px;
                color: var(--dark-gray);
                font-size: 0.9rem;
            }
            
            .page-footer p {
                margin: 5px 0;
            }
        </style>

        <!-- 页面底部 -->
        <div class="page-footer">
            <p>智云笔记应用原型设计 © 2023 版权所有</p>
            <p>设计者：AI助手</p>
        </div>

    </div>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-copyright">
                © 2023 智云笔记 - 让记录和创作更加智能
            </div>
            <div class="footer-info">
                原型设计版本 v0.5.1 | 由 Claude 3.7 Sonnet 智能设计
            </div>
        </div>
    </footer>
    
    <style>
        .footer {
            margin-top: 60px;
            padding: 40px 0;
            background-color: var(--light-gray);
            border-top: 1px solid var(--medium-gray);
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }
        
        .footer-copyright {
            font-size: 1rem;
            color: var(--black);
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .footer-info {
            font-size: 0.9rem;
            color: var(--dark-gray);
        }
    </style>
</body>
</html> 

