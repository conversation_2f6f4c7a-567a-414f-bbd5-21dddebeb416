import 'package:flutter/material.dart';

/// 日期时间处理工具类
///
/// 提供统一的日期时间格式化和转换方法，确保整个应用中对时间的处理保持一致
class DateTimeHelper {
  /// 私有构造函数，防止实例化
  DateTimeHelper._();

  /// 将后端返回的时间(UTC)转换为本地时间
  ///
  /// @param dateTime 需要转换的DateTime对象
  /// @return 转换后的本地时间DateTime对象
  static DateTime toLocalDateTime(DateTime dateTime) {
    return dateTime.toLocal();
  }

  /// 格式化日期时间为'YYYY-MM-DD HH:MM'格式
  ///
  /// @param dateTime 需要格式化的DateTime对象
  /// @param toLocal 是否需要转换为本地时间，默认为true
  /// @return 格式化后的字符串
  static String formatDateTime(DateTime dateTime, {bool toLocal = true}) {
    // 如有必要，转换为本地时间
    final localDateTime = toLocal ? dateTime.toLocal() : dateTime;

    // 格式化为'YYYY-MM-DD HH:MM'格式
    return '${localDateTime.year}-${localDateTime.month.toString().padLeft(2, '0')}-${localDateTime.day.toString().padLeft(2, '0')} '
        '${localDateTime.hour.toString().padLeft(2, '0')}:${localDateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化日期为'YYYY-MM-DD'格式
  ///
  /// @param dateTime 需要格式化的DateTime对象
  /// @param toLocal 是否需要转换为本地时间，默认为true
  /// @return 格式化后的字符串
  static String formatDate(DateTime dateTime, {bool toLocal = true}) {
    final localDateTime = toLocal ? dateTime.toLocal() : dateTime;
    return '${localDateTime.year}-${localDateTime.month.toString().padLeft(2, '0')}-${localDateTime.day.toString().padLeft(2, '0')}';
  }

  /// 格式化时间为'HH:MM'格式
  ///
  /// @param dateTime 需要格式化的DateTime对象
  /// @param toLocal 是否需要转换为本地时间，默认为true
  /// @return 格式化后的字符串
  static String formatTime(DateTime dateTime, {bool toLocal = true}) {
    final localDateTime = toLocal ? dateTime.toLocal() : dateTime;
    return '${localDateTime.hour.toString().padLeft(2, '0')}:${localDateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化为相对时间（如"刚刚"、"5分钟前"等）
  ///
  /// @param dateTime 需要格式化的DateTime对象
  /// @param toLocal 是否需要转换为本地时间，默认为true
  /// @return 格式化后的字符串
  static String formatRelativeTime(DateTime dateTime, {bool toLocal = true}) {
    final localDateTime = toLocal ? dateTime.toLocal() : dateTime;
    final now = DateTime.now();
    final difference = now.difference(localDateTime);

    if (difference.inDays > 365) {
      return formatDateTime(localDateTime, toLocal: false); // 超过一年，显示完整日期时间
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return '$months 个月前';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} 天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} 小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} 分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 从ISO8601字符串解析DateTime
  ///
  /// @param dateTimeString ISO8601格式的日期时间字符串
  /// @return 解析后的DateTime对象，如果解析失败则返回null
  static DateTime? parseDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) {
      return null;
    }
    try {
      return DateTime.parse(dateTimeString);
    } catch (e) {
      print('ERROR: [DateTimeHelper] 解析日期时间失败: $e, 字符串: $dateTimeString');
      return null;
    }
  }
} 