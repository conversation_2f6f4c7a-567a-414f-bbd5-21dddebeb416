# 智云笔记后端服务

这是智云笔记应用的后端服务，基于Node.js、Express和TypeScript开发，提供RESTful API支持应用的数据存储和业务逻辑处理。

## 技术栈

- **运行环境**: Node.js 18+
- **主框架**: Express 4.18+
- **语言**: TypeScript 5+
- **数据库**: MongoDB 5+
- **ORM**: Mongoose
- **认证**: JWT (JSON Web Token)
- **日志**: Winston、Morgan
- **测试**: Jest、Supertest
- **代码质量**: ESLint

## 目录结构

```
ai_cloud_notes_backend/
├── src/                # 源代码目录
│   ├── config/         # 配置文件
│   ├── controllers/    # 控制器
│   ├── middlewares/    # 中间件
│   ├── models/         # 数据模型
│   ├── routes/         # 路由
│   ├── tests/          # 测试文件
│   ├── utils/          # 工具函数
│   └── app.ts          # 应用入口
├── dist/               # 编译后的代码
├── logs/               # 日志文件
├── .env                # 环境变量
├── .env.example        # 环境变量示例
├── tsconfig.json       # TypeScript配置
└── package.json        # 项目配置和依赖
```

## 安装

1. 克隆项目并安装依赖:

```bash
git clone <仓库地址>
cd ai_cloud_notes_backend
npm install
```

2. 配置环境变量:

```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑.env文件，设置数据库连接等
```

## 开发运行

```bash
# 开发模式运行（带热重载）
npm run dev

# 构建项目
npm run build

# 生产模式运行
npm run start

# 运行测试
npm test
```

## API文档

启动服务后，可以通过以下端点查看和测试API:

- 主页: `GET /`
- 健康检查: `GET /health`

## 贡献指南

1. 确保遵循项目的代码规范和提交规范
2. 编写单元测试和集成测试
3. 使用有意义的提交信息
4. 创建功能分支进行开发，完成后提交Pull Request

## 许可证

本项目采用MIT许可证，详情请查看LICENSE文件。 