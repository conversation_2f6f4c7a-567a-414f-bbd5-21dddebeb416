# 智云笔记后端 - 本地开发环境设置

本文档提供在本地环境设置和运行智云笔记后端服务的详细步骤。

## 前置条件

在开始之前，请确保您的系统已安装以下软件：

1. **Node.js** (v18.0.0 或更高版本)
2. **npm** (v8.0.0 或更高版本)
3. **MongoDB** (v5.0 或更高版本)
4. **Git**

## 步骤 1: 克隆项目

```bash
git clone <项目仓库URL>
cd ai_cloud_notes_backend
```

## 步骤 2: 安装依赖

```bash
npm install
```

## 步骤 3: 环境配置

1. 复制环境变量示例文件：

```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，根据您的本地环境设置以下变量：

```
# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ai_cloud_notes

# JWT密钥配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# 日志配置
LOG_LEVEL=debug
```

## 步骤 4: 启动 MongoDB

确保您的 MongoDB 实例正在运行。如果尚未运行，请启动：

Windows:
```
# 如果已安装为服务
net start MongoDB

# 或手动启动
"C:\Program Files\MongoDB\Server\5.0\bin\mongod.exe" --dbpath="C:\data\db"
```

Linux/macOS:
```bash
sudo systemctl start mongod
# 或
mongod --dbpath /var/lib/mongodb
```

## 步骤 5: 运行应用

### 开发模式

```bash
npm run dev
```

这将使用 nodemon 启动服务器，当您更改源代码时会自动重启。

### 生产模式

```bash
# 首先编译 TypeScript 代码
npm run build

# 然后运行编译后的 JavaScript
npm start
```

## 步骤 6: 验证服务是否正常运行

在浏览器中打开以下 URL 或使用 curl 命令：

```
http://localhost:3000/health
```

您应该会看到类似以下的 JSON 响应：

```json
{
  "status": "UP",
  "timestamp": "2025-04-01T08:26:42.713Z",
  "service": "ai-cloud-notes-backend"
}
```

## 步骤 7: 运行测试

```bash
npm test
```

## 常见问题解决

### 连接 MongoDB 失败

1. 确保 MongoDB 服务正在运行
2. 检查 `.env` 中的 `MONGODB_URI` 是否正确
3. 确认防火墙或网络设置是否允许连接到数据库端口

### 端口冲突

如果端口 3000 已被其他服务使用，请在 `.env` 文件中更改 `PORT` 值。

### 权限问题

在 Linux/macOS 上，如果出现权限问题，请确保当前用户对项目目录有适当的读写权限。

## 下一步

现在您已经成功设置并运行了后端服务，可以继续开发：

1. 探索 API 端点实现
2. 添加新的 API 功能
3. 修改数据模型
4. 编写更多测试

如果您有任何问题或需要帮助，请参考项目 README 或联系项目维护人员。 