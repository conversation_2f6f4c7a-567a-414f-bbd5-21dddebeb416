import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ai_cloud_notes/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Integration Test', () {
    testWidgets('should find a text widget on the initial screen', (WidgetTester tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();

      // 查找一个示例文本 Widget。
      // 注意：这里需要根据实际应用的初始界面来查找存在的文本。
      // 如果应用启动后没有直接显示文本，可能需要查找其他类型的Widget或导航到特定页面。
      // 假设应用启动后会显示一个包含 "AI Cloud Notes" 的文本。
      expect(find.text('AI Cloud Notes'), findsOneWidget);
    });
  });
}