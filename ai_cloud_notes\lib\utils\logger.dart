import 'dart:developer' as developer;

/// 日志工具类
///
/// 提供统一的日志记录功能，支持不同级别的日志
class Log {
  /// 是否启用调试日志
  static bool debugEnabled = true;

  /// 记录调试信息
  /// 
  /// 仅在 debugEnabled 为 true 时输出
  static void debug(String message) {
    if (debugEnabled) {
      developer.log('📘 DEBUG: $message', name: 'ai_cloud_notes');
    }
  }

  /// 记录一般信息
  static void info(String message) {
    developer.log('📗 INFO: $message', name: 'ai_cloud_notes');
  }

  /// 记录警告信息
  static void warning(String message) {
    developer.log('📙 WARNING: $message', name: 'ai_cloud_notes');
  }

  /// 记录错误信息
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    if (error != null) {
      developer.log('📕 ERROR: $message\nError: $error', name: 'ai_cloud_notes', error: error, stackTrace: stackTrace);
    } else {
      developer.log('📕 ERROR: $message', name: 'ai_cloud_notes');
    }
  }

  /// 记录严重错误信息
  static void severe(String message, [dynamic error, StackTrace? stackTrace]) {
    if (error != null) {
      developer.log('⛔ SEVERE: $message\nError: $error', name: 'ai_cloud_notes', error: error, stackTrace: stackTrace);
    } else {
      developer.log('⛔ SEVERE: $message', name: 'ai_cloud_notes');
    }
  }
} 