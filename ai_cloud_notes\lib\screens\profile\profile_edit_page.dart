import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/providers/user_provider.dart';
import 'package:ai_cloud_notes/models/user_profile.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/utils/url_helper.dart';

class ProfileEditPage extends StatefulWidget {
  const ProfileEditPage({Key? key}) : super(key: key);

  @override
  State<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends State<ProfileEditPage> {
  final TextEditingController _nicknameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _bioController = TextEditingController();

  // 头像相关
  final ImagePicker _picker = ImagePicker();
  File? _avatarImageFile;       // 移动平台使用
  Uint8List? _avatarImageWeb;   // Web平台使用
  bool _hasDefaultAvatar = true;

  // 获取当前头像（兼容Web和移动平台）
  dynamic get _avatarImage => kIsWeb ? _avatarImageWeb : _avatarImageFile;

  @override
  void initState() {
    super.initState();
    // 加载用户资料
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<UserProvider>().loadProfile();
    });
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _emailController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Consumer<UserProvider>(
        builder: (context, userProvider, child) {
          // 如果正在加载，显示加载指示器
          if (userProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          // 如果有错误，显示错误信息
          if (userProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(userProvider.error!),
                  ElevatedButton(
                    onPressed: () => userProvider.loadProfile(),
                    child: const Text('重试'),
                  ),
                ],
              ),
            );
          }

          // 如果用户资料已加载，更新控制器的值
          final profile = userProvider.profile;
          if (profile != null) {
            _nicknameController.text = profile.username;
            _emailController.text = profile.email;
            _bioController.text = profile.bio ?? '';
            _hasDefaultAvatar = profile.avatar == null;
          }

          return SafeArea(
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildAvatarSection(),
                          const SizedBox(height: 20),
                          _buildStatsSection(),
                          const SizedBox(height: 20),
                          _buildPersonalInfoSection(),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).appBarTheme.backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.arrow_back,
                color: Theme.of(context).appBarTheme.iconTheme?.color,
                size: 20,
              ),
            ),
          ),
          Text(
            '个人资料',
            style: Theme.of(context).appBarTheme.titleTextStyle,
          ),
          TextButton(
            onPressed: _saveProfile,
            child: Text(
              '保存',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatarSection() {
    final userProvider = context.watch<UserProvider>();
    final profile = userProvider.profile;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Stack(
            children: [
              // 头像 - 支持显示选择的图片
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: AppTheme.lightGrayColor,
                  image: _getAvatarDecorationImage(profile),
                  // 无头像时显示渐变背景和文字
                  gradient: _shouldShowGradient(profile)
                      ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppTheme.primaryColor,
                            AppTheme.secondaryColor,
                          ],
                        )
                      : null,
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: _shouldShowGradient(profile)
                    ? Center(
                        child: Text(
                          profile?.username.isNotEmpty == true
                              ? profile!.username.substring(0, 1)
                              : '?',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                    : null,
              ),
              // 更换头像按钮
              Positioned(
                right: 0,
                bottom: 0,
                child: Material(
                  color: Colors.transparent,
                  shape: const CircleBorder(),
                  clipBehavior: Clip.antiAlias,
                  child: InkWell(
                    onTap: _changeAvatar,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.camera_alt,
                        color: AppTheme.primaryColor,
                        size: 18,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            profile?.username ?? '',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 4),
          Text(
            profile?.email ?? '',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 8),
          if (profile?.isVerified == true)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.verified,
                    color: AppTheme.primaryColor,
                    size: 14,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '已认证用户',
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 10),
          Text(
            '点击更换头像',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    final userProvider = context.watch<UserProvider>();
    final stats = userProvider.profile?.stats;

    if (stats == null) {
      return const SizedBox.shrink();
    }

    final Map<String, int> statsMap = {
      '笔记': stats.totalNotes,
      '收藏': stats.favoriteNotes,
      '标签': stats.totalTags,
    };

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          ...statsMap.entries.map((entry) {
            return _buildStatItem(entry.value.toString(), entry.key);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildStatItem(String count, String label) {
    return Column(
      children: [
        Text(
          count,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '个人信息',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // 昵称
              _buildInputField(
                label: '昵称',
                controller: _nicknameController,
                hintText: '请输入昵称',
              ),
              _buildDivider(),
              // 邮箱
              _buildInputField(
                label: '邮箱',
                controller: _emailController,
                hintText: '请输入邮箱',
                keyboardType: TextInputType.emailAddress,
                enabled: false,
              ),
              _buildDivider(),
              // 个人签名
              _buildInputField(
                label: '个人签名',
                controller: _bioController,
                hintText: '介绍一下自己吧',
                maxLines: 3,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    required String hintText,
    TextInputType keyboardType = TextInputType.text,
    int maxLines = 1,
    bool enabled = true,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: maxLines > 1 ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: Theme.of(context).textTheme.titleSmall,
            ),
          ),
          Expanded(
            child: TextField(
              controller: controller,
              style: TextStyle(
                fontSize: Theme.of(context).textTheme.bodyLarge?.fontSize,
                color: enabled
                    ? Theme.of(context).textTheme.bodyLarge?.color
                    : Theme.of(context).textTheme.bodyMedium?.color,
              ),
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(
                  color: Theme.of(context).hintColor,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(
                    color: AppTheme.primaryColor.withOpacity(0.8),
                    width: 1.5,
                  ),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                isDense: true,
              ),
              keyboardType: keyboardType,
              maxLines: maxLines,
              enabled: enabled,
            ),
          ),
        ],
      ),
    );
  }

  void _changeAvatar() async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      backgroundColor: Theme.of(context).bottomSheetTheme.backgroundColor,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(
                  Icons.photo_camera,
                  color: AppTheme.primaryColor, // 保留品牌色
                ),
                title: Text('拍照', style: Theme.of(context).textTheme.bodyLarge),
                onTap: () {
                  Navigator.pop(context);
                  _getImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.photo_library,
                  color: AppTheme.primaryColor, // 保留品牌色
                ),
                title: Text('从相册选择', style: Theme.of(context).textTheme.bodyLarge),
                onTap: () {
                  Navigator.pop(context);
                  _getImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _getImage(ImageSource source) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        maxWidth: 500,
        maxHeight: 500,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final userProvider = context.read<UserProvider>();
        final authProvider = context.read<AuthProvider>();
        bool success = false;

        if (kIsWeb) {
          // Web平台处理
          final bytes = await pickedFile.readAsBytes();

          // 添加文件类型验证 - 检查文件魔数
          if (bytes.length > 3) { // 确保有足够字节进行判断
            // 检查是否为PNG (以89 50 4E 47开头)
            bool isPng = bytes[0] == 0x89 && bytes[1] == 0x50 && bytes[2] == 0x4E && bytes[3] == 0x47;
            // 检查是否为JPEG (以FF D8开头)
            bool isJpeg = bytes[0] == 0xFF && bytes[1] == 0xD8;
            // 检查是否为GIF (以47 49 46开头 - "GIF")
            bool isGif = bytes[0] == 0x47 && bytes[1] == 0x49 && bytes[2] == 0x46;

            if (!isPng && !isJpeg && !isGif) {
              // 显示错误信息
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('选择的文件不是有效的图片格式，请重新选择')),
                );
              }
              return;
            }
          } else {
             if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('文件太小，无法判断图片格式')),
                );
              }
              return;
          }


          setState(() {
            _avatarImageWeb = bytes;
            _avatarImageFile = null; // 清除移动平台图片
            _hasDefaultAvatar = false;
          });

          // 上传头像
          success = await userProvider.uploadAvatar(bytes, authProvider: authProvider);
        } else {
          // 移动平台处理
          setState(() {
            _avatarImageFile = File(pickedFile.path);
            _avatarImageWeb = null; // 清除Web平台图片
            _hasDefaultAvatar = false;
          });

          // 上传头像
          success = await userProvider.uploadAvatar(_avatarImageFile, authProvider: authProvider);
        }

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('头像已更新')),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(userProvider.error ?? '上传头像失败')),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('选择图片失败: ${e.toString()}')),
        );
      }
    }
  }

  void _saveProfile() async {
    final userProvider = context.read<UserProvider>();

    final success = await userProvider.updateProfile(
      username: _nicknameController.text,
      email: _emailController.text, // 邮箱通常不允许在此编辑，但保持逻辑
      bio: _bioController.text,
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('个人资料保存成功')),
        );
        Navigator.pop(context);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(userProvider.error ?? '保存失败')),
        );
      }
    }
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 1,
      color: Theme.of(context).dividerColor,
      indent: 16,
      endIndent: 16,
    );
  }

  // 判断是否应该显示渐变背景
  bool _shouldShowGradient(UserProfile? profile) {
    // 当没有选择本地图片，且用户没有网络头像，并且 _hasDefaultAvatar 为 false（意味着不是因为网络图片加载失败而显示默认图）
    return _avatarImageFile == null && _avatarImageWeb == null && profile?.avatar == null && !_hasDefaultAvatar;
  }


  // 获取头像装饰图像
  DecorationImage? _getAvatarDecorationImage(UserProfile? profile) {
    if (_avatarImageFile != null) {
      return DecorationImage(
        image: FileImage(_avatarImageFile!),
        fit: BoxFit.cover,
      );
    } else if (_avatarImageWeb != null) {
      return DecorationImage(
        image: MemoryImage(_avatarImageWeb!),
        fit: BoxFit.cover,
      );
    } else if (profile?.avatar != null && !_hasDefaultAvatar) { // 仅当没有强制使用默认头像时加载网络头像
      final fullAvatarUrl = UrlHelper.getFullAvatarUrl(profile!.avatar!);
      return DecorationImage(
        image: NetworkImage(fullAvatarUrl),
        fit: BoxFit.cover,
        onError: (error, stackTrace) {
          if (mounted) {
            setState(() {
              _hasDefaultAvatar = true; // 网络图片加载失败，强制使用默认头像
            });
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('头像加载失败，已显示默认头像')),
            );
          }
        },
      );
    }
    // 如果以上条件都不满足（包括 _hasDefaultAvatar 为 true 的情况），则显示默认占位图
    return const DecorationImage(
      image: AssetImage('assets/images/avatar_placeholder.png'), // 确保此路径正确
      fit: BoxFit.cover,
    );
  }
}