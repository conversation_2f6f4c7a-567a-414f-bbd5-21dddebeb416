import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
// OnboardingPage 将通过路由名导航，不再直接导入
// import 'package:ai_cloud_notes/screens/welcome/onboarding_page.dart';
import 'package:shared_preferences/shared_preferences.dart'; // 导入 shared_preferences

class WelcomePage extends StatefulWidget {
  const WelcomePage({Key? key}) : super(key: key);

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

class _WelcomePageState extends State<WelcomePage> with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  // 定义动画对象
  late Animation<Offset> _logoSlideAnimation;
  late Animation<double> _logoFadeAnimation;

  late Animation<Offset> _titleSlideAnimation;
  late Animation<double> _titleFadeAnimation;

  late Animation<Offset> _descriptionSlideAnimation;
  late Animation<double> _descriptionFadeAnimation;

  late Animation<Offset> _buttonSlideAnimation;
  late Animation<double> _buttonFadeAnimation;

  late Animation<Offset> _versionSlideAnimation;
  late Animation<double> _versionFadeAnimation;


  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 1), // 动画总时长
      vsync: this,
    );

    // 初始化动画对象，设置不同的延迟和曲线
    _logoSlideAnimation = Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut), // 0% - 60% 的时间进行动画
      ),
    );
    _logoFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _titleSlideAnimation = Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.1, 0.7, curve: Curves.easeOut), // 10% - 70% 的时间进行动画
      ),
    );
    _titleFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.1, 0.7, curve: Curves.easeOut),
      ),
    );

    _descriptionSlideAnimation = Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOut), // 20% - 80% 的时间进行动画
      ),
    );
    _descriptionFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
      ),
    );

    _buttonSlideAnimation = Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 0.9, curve: Curves.easeOut), // 30% - 90% 的时间进行动画
      ),
    );
    _buttonFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.3, 0.9, curve: Curves.easeOut),
      ),
    );

    _versionSlideAnimation = Tween<Offset>(begin: const Offset(0, 0.5), end: Offset.zero).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.4, 1.0, curve: Curves.easeOut), // 40% - 100% 的时间进行动画
      ),
    );
    _versionFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.4, 1.0, curve: Curves.easeOut),
      ),
    );


    _controller.forward(); // 启动动画
  }

  @override
  void dispose() {
    _controller.dispose(); // 释放控制器
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: AppTheme.fixedLightTheme,
      child: Scaffold(
        body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor,
              AppTheme.secondaryColor,
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 欢迎Logo
                  AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _logoFadeAnimation,
                        child: SlideTransition(
                          position: _logoSlideAnimation,
                          child: child,
                        ),
                      );
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.cloud,
                          size: 48,
                          color: Colors.white,
                        ),
                        SizedBox(width: 16),
                        Text(
                          '智云笔记',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24), // 间距

                  // 欢迎标题
                  AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _titleFadeAnimation,
                        child: SlideTransition(
                          position: _titleSlideAnimation,
                          child: child,
                        ),
                      );
                    },
                    child: Text(
                      '智能笔记，随时随地',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16), // 间距

                  // 欢迎描述
                  AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _descriptionFadeAnimation,
                        child: SlideTransition(
                          position: _descriptionSlideAnimation,
                          child: child,
                        ),
                      );
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 40),
                      child: Text(
                        '基于AI技术的智能云笔记应用，让记录和创作更加高效、便捷，随时随地捕捉灵感。',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withOpacity(0.9),
                          height: 1.6,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 40), // 间距

                  // 开始体验按钮
                  AnimatedBuilder(
                    animation: _controller,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _buttonFadeAnimation,
                        child: SlideTransition(
                          position: _buttonSlideAnimation,
                          child: child,
                        ),
                      );
                    },
                    child: ElevatedButton(
                      onPressed: () async { // 改为 async
                        // 设置引导已完成标记
                        try {
                          final prefs = await SharedPreferences.getInstance();
                          await prefs.setBool('hasSeenOnboarding', true);
                        } catch (e) {
                          // 处理可能的错误，例如 SharedPreferences 初始化失败
                          debugPrint('设置 hasSeenOnboarding 标记失败: $e');
                        }
                        // 使用命名路由替换当前页面，导航到引导页
                        // 这有助于浏览器 URL 更新
                        if (mounted) { // 检查 widget 是否仍然挂载
                           Navigator.of(context).pushReplacementNamed(AppRoutes.onboarding);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: AppTheme.primaryColor,
                        padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                        textStyle: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      child: Text('开始体验'),
                    ),
                  ),
                ],
              ),

              // 版本信息
              AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _versionFadeAnimation,
                    child: SlideTransition(
                      position: _versionSlideAnimation,
                      child: child,
                    ),
                  );
                },
                child: Positioned(
                  bottom: 30,
                  left: 0,
                  right: 0,
                  child: Text(
                    '版本 v1.0.0',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      ),
    );
  }
}