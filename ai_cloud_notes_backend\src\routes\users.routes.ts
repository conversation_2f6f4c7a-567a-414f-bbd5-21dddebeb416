import express from 'express';
import { body } from 'express-validator';
import * as usersController from '../controllers/users.controller';
import { authenticate } from '../middlewares/auth.middleware';

const router = express.Router();

// 所有用户路由都需要认证
router.use(authenticate);

/**
 * 获取用户个人资料
 * GET /api/users/profile
 */
router.get('/profile', usersController.getUserProfile);

/**
 * 更新用户个人资料
 * PUT /api/users/profile
 */
router.put(
  '/profile',
  [
    // 验证用户名
    body('username')
      .optional()
      .isString()
      .trim()
      .isLength({ min: 3, max: 20 })
      .withMessage('用户名长度必须在3-20个字符之间'),
    
    // 验证邮箱
    body('email')
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage('请提供有效的邮箱地址'),
    
    // 验证个人简介
    body('bio')
      .optional()
      .isString()
      .isLength({ max: 200 })
      .withMessage('个人简介最多200个字符'),
  ],
  usersController.updateUserProfile
);

/**
 * 上传用户头像
 * POST /api/users/avatar
 */
router.post(
  '/avatar',
  usersController.upload.single('avatar'),
  usersController.uploadAvatar
);

/**
 * 获取用户统计信息
 * GET /api/users/stats
 */
router.get('/stats', usersController.getUserStats);

export default router; 