import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:fleather/fleather.dart';
import 'package:parchment/parchment.dart';
import 'dart:convert';

/// 通用笔记内容预览组件
///
/// 根据笔记的contentType自动选择合适的渲染方式
/// 支持富文本和Markdown两种格式
class NoteContentPreview extends StatefulWidget {
  final Note note;
  final int maxLines;
  final bool isExpanded;
  final double? fontSize;
  final Color? textColor;

  const NoteContentPreview({
    Key? key,
    required this.note,
    this.maxLines = 2,
    this.isExpanded = false,
    this.fontSize,
    this.textColor,
  }) : super(key: key);

  @override
  State<NoteContentPreview> createState() => _NoteContentPreviewState();
}

class _NoteContentPreviewState extends State<NoteContentPreview> {
  late FleatherController _fleatherController;
  late ParchmentDocument _document;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeContent();
  }

  @override
  void didUpdateWidget(NoteContentPreview oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.note.id != widget.note.id ||
        oldWidget.note.content != widget.note.content ||
        oldWidget.note.contentType != widget.note.contentType) {
      _initializeContent();
    }
  }

  void _initializeContent() {
    if (widget.note.contentType == 'markdown') {
      // 对于Markdown格式，使用简单的文本文档
      _document = ParchmentDocument.fromJson([
        {"insert": widget.note.content}
      ]);
    } else {
      // 对于富文本格式，尝试解析Delta JSON
      try {
        if (widget.note.content.trim().startsWith('[') ||
            widget.note.content.trim().startsWith('{')) {
          dynamic deltaJson = jsonDecode(widget.note.content);
          _document = ParchmentDocument.fromJson(deltaJson);
        } else {
          // 如果不是有效的JSON，作为纯文本处理
          _document = ParchmentDocument.fromJson([
            {"insert": widget.note.content}
          ]);
        }
      } catch (e) {
        // 解析失败，使用纯文本
        _document = ParchmentDocument.fromJson([
          {"insert": widget.note.content}
        ]);
      }
    }

    _fleatherController = FleatherController(document: _document);
    _isInitialized = true;
    if (mounted) setState(() {});
  }

  @override
  void dispose() {
    _fleatherController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const SizedBox.shrink();
    }

    // 根据笔记类型选择不同的渲染方式
    if (widget.note.contentType == 'markdown') {
      return _buildMarkdownPreview();
    } else {
      return _buildRichTextPreview();
    }
  }

  Widget _buildMarkdownPreview() {
    // 对于列表项预览，只显示纯文本
    if (!widget.isExpanded) {
      return Text(
        widget.note.plainTextContent,
        style: TextStyle(
          fontSize: widget.fontSize ?? 14,
          color: widget.textColor ?? AppTheme.darkGrayColor,
          height: 1.4,
        ),
        maxLines: widget.maxLines,
        overflow: TextOverflow.ellipsis,
      );
    }

    // 对于展开视图，使用Markdown渲染
    return MarkdownBody(
      data: widget.note.content,
      selectable: true,
      softLineBreak: true,
      shrinkWrap: true,
      styleSheet: MarkdownStyleSheet(
        p: TextStyle(
          fontSize: widget.fontSize ?? 14,
          color: widget.textColor ?? AppTheme.darkGrayColor,
          height: 1.4,
        ),
      ),
    );
  }

  Widget _buildRichTextPreview() {
    // 对于列表项预览，只显示纯文本
    if (!widget.isExpanded) {
      return Text(
        widget.note.plainTextContent,
        style: TextStyle(
          fontSize: widget.fontSize ?? 14,
          color: widget.textColor ?? AppTheme.darkGrayColor,
          height: 1.4,
        ),
        maxLines: widget.maxLines,
        overflow: TextOverflow.ellipsis,
      );
    }

    // 对于展开视图，使用Fleather渲染
    return Container(
      constraints: BoxConstraints(maxHeight: 200), // 限制最大高度
      child: FleatherEditor(
        controller: _fleatherController,
        focusNode: FocusNode(),
        readOnly: true,
        padding: EdgeInsets.zero,
        enableInteractiveSelection: true,
        scrollable: false,
        expands: false,
        showCursor: false,
      ),
    );
  }
}
