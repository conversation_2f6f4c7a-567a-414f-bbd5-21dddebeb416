import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import ThemeSettingsService from '../services/theme_settings.service';
import { AuthenticatedRequest } from '../types/express';

/**
 * 主题设置控制器类
 */
class ThemeSettingsController {
  /**
   * 获取用户的主题设置
   * @param req 请求对象
   * @param res 响应对象
   * @returns 主题设置响应
   */
  async getUserThemeSettings(req: AuthenticatedRequest, res: Response) {
    try {
      // 获取请求中的用户ID
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未授权，无法获取主题设置'
        });
      }
      
      // 调用服务获取主题设置
      const themeSettings = await ThemeSettingsService.getUserThemeSettings(userId);
      
      return res.status(200).json({
        success: true,
        data: themeSettings
      });
    } catch (error) {
      logger.error(`获取用户主题设置出错: ${error}`);
      return res.status(500).json({
        success: false,
        message: '获取主题设置失败'
      });
    }
  }

  /**
   * 更新用户的主题设置
   * @param req 请求对象
   * @param res 响应对象
   * @returns 更新响应
   */
  async updateUserThemeSettings(req: AuthenticatedRequest, res: Response) {
    try {
      // 获取请求中的用户ID
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '未授权，无法更新主题设置'
        });
      }
      
      // 获取请求体中的设置
      const { themeMode, fontColor, textSize, titleSize } = req.body;
      
      // 数据验证
      if (themeMode !== undefined && ![0, 1, 2].includes(themeMode)) {
        return res.status(400).json({
          success: false,
          message: '无效的主题模式值'
        });
      }
      
      // 调用服务更新主题设置
      const updatedSettings = await ThemeSettingsService.updateUserThemeSettings(userId, {
        themeMode,
        fontColor,
        textSize,
        titleSize
      });
      
      return res.status(200).json({
        success: true,
        message: '主题设置已更新',
        data: updatedSettings
      });
    } catch (error) {
      logger.error(`更新用户主题设置出错: ${error}`);
      return res.status(500).json({
        success: false,
        message: '更新主题设置失败'
      });
    }
  }
}

export default new ThemeSettingsController(); 