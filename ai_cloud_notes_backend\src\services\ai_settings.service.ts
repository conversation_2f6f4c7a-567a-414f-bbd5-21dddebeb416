import mongoose from 'mongoose';
import AISettings, { IAISettings } from '../models/ai_settings.model';
import { logger } from '../utils/logger';

/**
 * AI设置服务类
 */
class AISettingsService {
  /**
   * 获取用户的AI设置
   * @param userId 用户ID
   * @returns 用户AI设置
   */
  async getUserAISettings(userId: string): Promise<IAISettings | null> {
    try {
      // 查找用户的AI设置
      const userAISettings = await AISettings.findOne({ userId: new mongoose.Types.ObjectId(userId) });
      
      // 如果不存在，则创建默认AI设置
      if (!userAISettings) {
        const defaultSettings = await this.createDefaultAISettings(userId);
        return defaultSettings;
      }
      
      return userAISettings;
    } catch (error) {
      logger.error(`获取用户AI设置失败: ${error}`);
      throw error;
    }
  }

  /**
   * 更新用户AI设置
   * @param userId 用户ID
   * @param settings AI设置数据
   * @returns 更新后的AI设置
   */
  async updateUserAISettings(userId: string, settings: Partial<IAISettings>): Promise<IAISettings | null> {
    try {
      // 更新用户AI设置，如果不存在则创建
      const updatedSettings = await AISettings.findOneAndUpdate(
        { userId: new mongoose.Types.ObjectId(userId) },
        { 
          $set: {
            ...settings,
            updatedAt: new Date()
          } 
        },
        { new: true, upsert: true }
      );
      
      return updatedSettings;
    } catch (error) {
      logger.error(`更新用户AI设置失败: ${error}`);
      throw error;
    }
  }

  /**
   * 创建默认AI设置
   * @param userId 用户ID
   * @returns 默认AI设置
   */
  private async createDefaultAISettings(userId: string): Promise<IAISettings> {
    try {
      const defaultSettings = new AISettings({
        userId: new mongoose.Types.ObjectId(userId),
        aiAssistantEnabled: true,
        smartSuggestionsEnabled: true,
        autoTaggingEnabled: true,
        contentSummaryEnabled: false,
        selectedModel: '默认模型',
        suggestionFrequency: '输入停顿后',
        localProcessingEnabled: false,
        allowDataCollection: true,
        updatedAt: new Date()
      });
      
      await defaultSettings.save();
      return defaultSettings;
    } catch (error) {
      logger.error(`创建默认AI设置失败: ${error}`);
      throw error;
    }
  }
}

export default new AISettingsService();
