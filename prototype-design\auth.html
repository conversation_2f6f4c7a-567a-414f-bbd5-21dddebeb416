<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智云笔记 - 用户认证</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common.css">
    <style>
        /* 登录/注册界面 */
        .auth-container {
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0 40px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #F3F4F6 0%, #FFFFFF 100%);
        }
        
        .auth-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 200px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 0 0 50% 50% / 0 0 20% 20%;
            z-index: 0;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 40px;
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo i {
            font-size: 2.2rem;
        }
        
        .auth-form {
            width: 100%;
            background-color: var(--white);
            padding: 30px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            position: relative;
            z-index: 1;
        }
        
        .form-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 24px;
            text-align: center;
            color: var(--black);
        }
        
        .form-description {
            text-align: center;
            color: var(--dark-gray);
            font-size: 0.9rem;
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .verification-code {
            display: flex;
            gap: 10px;
        }
        
        .verification-code input {
            flex: 1;
        }
        
        .verification-code button {
            padding: 12px;
            background-color: var(--primary-light);
            color: var(--primary-color);
            border: none;
            border-radius: var(--border-radius-sm);
            font-size: 0.9rem;
            white-space: nowrap;
            transition: var(--transition);
        }
        
        .verification-code button:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .forgot-password {
            text-align: right;
            margin-bottom: 24px;
        }
        
        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .login-btn, .register-btn, .reset-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--border-radius-sm);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1rem;
        }
        
        .login-btn:hover, .register-btn:hover, .reset-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(93, 95, 239, 0.3);
        }
        
        .register-link, .login-link {
            text-align: center;
            margin-top: 24px;
            font-size: 0.9rem;
            color: var(--dark-gray);
        }
        
        .register-link a, .login-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .back-link i {
            margin-right: 8px;
        }
        
        .terms-checkbox {
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }
        
        .terms-checkbox input {
            width: 16px;
            height: 16px;
            margin-top: 3px;
            accent-color: var(--primary-color);
        }
        
        .terms-checkbox label {
            font-size: 0.85rem;
            color: var(--dark-gray);
            line-height: 1.4;
        }
        
        .terms-checkbox a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .version-info {
            position: absolute;
            bottom: 20px;
            text-align: center;
            color: var(--dark-gray);
            font-size: 0.8rem;
            width: 100%;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title text-center">智云笔记应用原型设计</h1>
        <p class="page-description text-center">
            用户认证相关页面，包括登录、注册和找回密码等功能。所有页面保持一致的设计风格和交互逻辑。
        </p>

        <!-- 登录界面 -->
        <div>
            <h3 class="screen-title">登录界面</h3>
            <p class="screen-description">应用入口页面，用户可以登录已有账号或前往注册</p>
            <div class="screen">
                <div class="auth-container">
                    <div class="logo">
                        <i class="fas fa-cloud"></i> 智云笔记
                    </div>
                    <div class="auth-form">
                        <h2 class="form-title">账号登录</h2>
                        <div class="input-group">
                            <label>用户名/邮箱</label>
                            <input type="text" placeholder="请输入账号">
                        </div>
                        <div class="input-group">
                            <label>密码</label>
                            <input type="password" placeholder="请输入密码">
                        </div>
                        <div class="forgot-password">
                            <a href="#">忘记密码？</a>
                        </div>
                        <button class="login-btn">登 录</button>
                        <div class="register-link">
                            没有账号？<a href="#">立即注册</a>
                        </div>
                    </div>
                    <div class="version-info">当前版本：v1.0.0</div>
                </div>
            </div>
        </div>
        
        <!-- 注册页面 (改进版) -->
        <div>
            <h3 class="screen-title">注册页面</h3>
            <p class="screen-description">新用户注册页面，包含邮箱验证码功能，确保邮箱真实有效</p>
            <div class="screen">
                <div class="auth-container">
                    <div class="logo">
                        <i class="fas fa-cloud"></i> 智云笔记
                    </div>
                    <div class="auth-form">
                        <h2 class="form-title">创建账号</h2>
                        <div class="input-group">
                            <label>用户名</label>
                            <input type="text" placeholder="请设置用户名">
                        </div>
                        <div class="input-group">
                            <label>邮箱</label>
                            <input type="email" placeholder="请输入邮箱">
                        </div>
                        <div class="input-group">
                            <label>邮箱验证码</label>
                            <div class="verification-code">
                                <input type="text" placeholder="请输入验证码">
                                <button>获取验证码</button>
                            </div>
                        </div>
                        <div class="input-group">
                            <label>密码</label>
                            <input type="password" placeholder="请设置密码">
                        </div>
                        <div class="input-group">
                            <label>确认密码</label>
                            <input type="password" placeholder="请再次输入密码">
                        </div>
                        <div class="terms-checkbox">
                            <input type="checkbox" id="terms">
                            <label for="terms">我已阅读并同意<a href="#">《用户协议》</a>和<a href="#">《隐私政策》</a></label>
                        </div>
                        <button class="register-btn">注 册</button>
                        <div class="login-link">
                            已有账号？<a href="#">立即登录</a>
                        </div>
                    </div>
                    <div class="version-info">当前版本：v1.0.0</div>
                </div>
            </div>
        </div>
        
        <!-- 忘记密码页面 -->
        <div>
            <h3 class="screen-title">忘记密码</h3>
            <p class="screen-description">用户找回密码的流程，通过邮箱验证重置密码</p>
            <div class="screen">
                <div class="auth-container">
                    <div class="logo">
                        <i class="fas fa-cloud"></i> 智云笔记
                    </div>
                    <div class="auth-form">
                        <h2 class="form-title">找回密码</h2>
                        <p class="form-description">
                            请输入您的注册邮箱，我们将发送验证码用于重置密码。
                        </p>
                        <div class="input-group">
                            <label>邮箱</label>
                            <input type="email" placeholder="请输入注册邮箱">
                        </div>
                        <div class="input-group">
                            <label>验证码</label>
                            <div class="verification-code">
                                <input type="text" placeholder="请输入验证码">
                                <button>获取验证码</button>
                            </div>
                        </div>
                        <div class="input-group">
                            <label>新密码</label>
                            <input type="password" placeholder="请设置新密码">
                        </div>
                        <div class="input-group">
                            <label>确认新密码</label>
                            <input type="password" placeholder="请再次输入新密码">
                        </div>
                        <button class="reset-btn">重置密码</button>
                        <div class="login-link">
                            <a href="#" class="back-link">
                                <i class="fas fa-arrow-left"></i> 返回登录
                            </a>
                        </div>
                    </div>
                    <div class="version-info">当前版本：v1.0.0</div>
                </div>
            </div>
        </div>

        <!-- 成功页面 -->
        <div>
            <h3 class="screen-title">操作成功</h3>
            <p class="screen-description">提供操作成功的反馈和后续步骤指引</p>
            <div class="screen">
                <div class="auth-container">
                    <div class="logo">
                        <i class="fas fa-cloud"></i> 智云笔记
                    </div>
                    <div class="auth-form">
                        <div class="text-center mb-4">
                            <i class="fas fa-check-circle" style="font-size: 4rem; color: var(--success); margin-bottom: 20px;"></i>
                            <h2 class="form-title">操作成功</h2>
                            <p class="form-description">
                                您的密码已重置成功！现在可以使用新密码登录您的账号。
                            </p>
                        </div>
                        <button class="login-btn">返回登录</button>
                    </div>
                    <div class="version-info">当前版本：v1.0.0</div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>智云笔记应用原型设计 &copy; 2023</p>
            <p>
                <a href="welcome.html">欢迎页面</a> | 
                <a href="auth.html">认证页面</a> | 
                <a href="home.html">主页</a> | 
                <a href="editor.html">编辑器</a> | 
                <a href="settings.html">设置</a> | 
                <a href="tags.html">标签</a>
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 