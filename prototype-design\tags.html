<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智云笔记 - 标签和搜索</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common.css">
    <style>
        /* 标签管理页面样式 */
        .tags-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .tags-header {
            padding: 16px 20px;
            background-color: white;
            box-shadow: var(--shadow-sm);
            border-bottom-left-radius: var(--border-radius-lg);
            border-bottom-right-radius: var(--border-radius-lg);
            margin-bottom: 16px;
        }

        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .header-top h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--black);
        }

        .header-top i {
            font-size: 1.2rem;
            color: var(--dark-gray);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
            cursor: pointer;
        }

        .header-top i:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: var(--border-radius);
            padding: 12px 16px;
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
        }

        .search-box:hover {
            border-color: var(--primary-color);
            background-color: white;
        }

        .search-box i {
            color: var(--dark-gray);
            margin-right: 10px;
        }

        .search-box input {
            border: none;
            background: transparent;
            flex: 1;
            outline: none;
            color: var(--black);
            font-size: 0.95rem;
        }

        .tags-content {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px 80px;
        }

        .tag-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background-color: white;
            border-radius: var(--border-radius);
            margin-bottom: 12px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
            cursor: pointer;
        }

        .tag-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .tag-info {
            display: flex;
            align-items: center;
        }

        .tag-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 12px;
        }

        .tag-name {
            font-weight: 500;
            color: var(--black);
        }

        .tag-count {
            color: var(--dark-gray);
            font-size: 0.9rem;
            padding: 2px 8px;
            background-color: var(--light-gray);
            border-radius: var(--border-radius-sm);
        }

        .tag-actions {
            color: var(--dark-gray);
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
        }

        .tag-actions:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .add-tag-btn {
            margin: 10px 0 20px;
            padding: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            background-color: white;
            border-radius: var(--border-radius);
            font-weight: 500;
            box-shadow: var(--shadow-sm);
            border: 1px dashed var(--primary-color);
            transition: var(--transition);
            cursor: pointer;
        }

        .add-tag-btn:hover {
            background-color: var(--primary-light);
            box-shadow: var(--shadow);
            transform: translateY(-2px);
        }
        
        .add-tag-btn i {
            margin-right: 8px;
            font-size: 1rem;
        }

        /* 标签详情页面 */
        .tag-details-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }

        .tag-details-header {
            padding: 16px 20px;
            background-color: white;
            box-shadow: var(--shadow-sm);
        }

        .tag-details-title {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .tag-details-title .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
            margin-right: 12px;
            cursor: pointer;
        }

        .tag-details-title .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .tag-details-name {
            display: flex;
            align-items: center;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
        }

        .tag-details-name .tag-color {
            margin-right: 10px;
        }

        .tag-details-actions {
            display: flex;
            gap: 12px;
            margin-left: auto;
        }

        .tag-details-actions i {
            color: var(--dark-gray);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            transition: var(--transition);
            cursor: pointer;
        }

        .tag-details-actions i:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }

        .tag-notes {
            padding: 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-header h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--black);
            margin: 0;
        }

        .sort-option {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--dark-gray);
            font-size: 0.9rem;
            cursor: pointer;
        }

        /* 搜索结果界面 */
        .search-results-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: #FAFAFA;
        }
        
        .search-header {
            padding: 16px 20px;
            display: flex;
            align-items: center;
            background-color: white;
            border-bottom: 1px solid var(--medium-gray);
            box-shadow: var(--shadow-sm);
        }
        
        .search-header .back-btn {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-sm);
            color: var(--dark-gray);
            transition: var(--transition);
            margin-right: 12px;
            cursor: pointer;
        }

        .search-header .back-btn:hover {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .search-header .search-input {
            flex: 1;
            display: flex;
            align-items: center;
            background-color: var(--light-gray);
            border-radius: var(--border-radius);
            padding: 12px 16px;
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
        }
        
        .search-header .search-input:focus-within {
            border-color: var(--primary-color);
            background-color: white;
        }
        
        .search-header .search-input i {
            color: var(--dark-gray);
            margin-right: 10px;
        }
        
        .search-header .search-input input {
            border: none;
            background: transparent;
            flex: 1;
            outline: none;
            color: var(--black);
            font-size: 0.95rem;
        }
        
        .search-filters {
            padding: 16px 20px;
            background-color: white;
            border-bottom: 1px solid var(--medium-gray);
            display: flex;
            gap: 12px;
            overflow-x: auto;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        
        .search-filters::-webkit-scrollbar {
            display: none;
        }
        
        .filter-tag {
            padding: 6px 12px;
            border-radius: var(--border-radius-sm);
            background-color: var(--light-gray);
            border: 1px solid var(--medium-gray);
            color: var(--dark-gray);
            font-size: 0.85rem;
            white-space: nowrap;
            transition: var(--transition);
            cursor: pointer;
        }
        
        .filter-tag.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            border-color: var(--primary-light);
        }
        
        .search-results {
            flex: 1;
            overflow-y: auto;
            padding: 20px 20px 80px;
        }
        
        .result-section {
            margin-bottom: 24px;
        }
        
        .result-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--black);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .result-count {
            font-size: 0.9rem;
            color: var(--dark-gray);
            font-weight: normal;
        }
        
        .no-results {
            text-align: center;
            padding: 40px 20px;
            color: var(--dark-gray);
        }
        
        .no-results i {
            font-size: 3rem;
            color: var(--medium-gray);
            margin-bottom: 16px;
        }
        
        .no-results h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-gray);
            margin-bottom: 8px;
        }
        
        .no-results p {
            font-size: 0.95rem;
            max-width: 260px;
            margin: 0 auto;
            line-height: 1.5;
        }
        
        .highlight {
            background-color: rgba(246, 246, 106, 0.3);
            padding: 0 2px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title text-center">智云笔记应用原型设计</h1>
        <p class="page-description text-center">
            标签管理和搜索功能，帮助用户高效组织和查找笔记内容。
        </p>

        <!-- 标签管理页面 -->
        <div>
            <h3 class="screen-title">标签管理</h3>
            <p class="screen-description">管理和组织笔记标签，可以创建、编辑和删除标签</p>
            <div class="screen">
                <div class="tags-container">
                    <div class="tags-header">
                        <div class="header-top">
                            <i class="fas fa-arrow-left"></i>
                            <h3>标签管理</h3>
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索标签">
                        </div>
                    </div>
                    
                    <div class="tags-content">
                        <div class="add-tag-btn">
                            <i class="fas fa-plus"></i> 创建新标签
                        </div>
                        
                        <div class="tag-item">
                            <div class="tag-info">
                                <div class="tag-color" style="background-color: #4F46E5;"></div>
                                <div class="tag-name">工作</div>
                            </div>
                            <div class="tag-count">12</div>
                            <div class="tag-actions">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        
                        <div class="tag-item">
                            <div class="tag-info">
                                <div class="tag-color" style="background-color: #06B6D4;"></div>
                                <div class="tag-name">学习</div>
                            </div>
                            <div class="tag-count">8</div>
                            <div class="tag-actions">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        
                        <div class="tag-item">
                            <div class="tag-info">
                                <div class="tag-color" style="background-color: #F59E0B;"></div>
                                <div class="tag-name">旅行</div>
                            </div>
                            <div class="tag-count">5</div>
                            <div class="tag-actions">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        
                        <div class="tag-item">
                            <div class="tag-info">
                                <div class="tag-color" style="background-color: #10B981;"></div>
                                <div class="tag-name">阅读</div>
                            </div>
                            <div class="tag-count">3</div>
                            <div class="tag-actions">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        
                        <div class="tag-item">
                            <div class="tag-info">
                                <div class="tag-color" style="background-color: #EF4444;"></div>
                                <div class="tag-name">灵感</div>
                            </div>
                            <div class="tag-count">7</div>
                            <div class="tag-actions">
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item active">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 标签详情页面 -->
        <div>
            <h3 class="screen-title">标签详情</h3>
            <p class="screen-description">展示特定标签下的所有笔记</p>
            <div class="screen">
                <div class="tag-details-container">
                    <div class="tag-details-header">
                        <div class="tag-details-title">
                            <div class="back-btn">
                                <i class="fas fa-arrow-left"></i>
                            </div>
                            <div class="tag-details-name">
                                <div class="tag-color" style="background-color: #4F46E5;"></div>
                                工作
                            </div>
                            <div class="tag-details-actions">
                                <i class="fas fa-edit"></i>
                                <i class="fas fa-ellipsis-v"></i>
                            </div>
                        </div>
                        
                        <div class="search-box">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索此标签下的笔记">
                        </div>
                    </div>
                    
                    <div class="tag-notes">
                        <div class="section-header">
                            <h3>笔记列表</h3>
                            <div class="sort-option">
                                <span>最近更新</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        
                        <div class="notes-list">
                            <div class="note-list-item">
                                <div class="note-list-title">工作计划</div>
                                <div class="note-list-content">本周需要完成的任务：1. 项目计划书 2. 客户会议 3. 团队协调 4. 进度报告</div>
                                <div class="note-list-meta">
                                    <span class="note-date">2023-08-15</span>
                                </div>
                            </div>
                            
                            <div class="note-list-item">
                                <div class="note-list-title">会议纪要：产品讨论会</div>
                                <div class="note-list-content">与设计和开发团队讨论新功能的实施方案，重点关注用户体验...</div>
                                <div class="note-list-meta">
                                    <span class="note-date">2023-08-14</span>
                                </div>
                            </div>
                            
                            <div class="note-list-item">
                                <div class="note-list-title">项目进度汇报</div>
                                <div class="note-list-content">本月项目进展情况：1. 设计阶段完成90% 2. 开发阶段完成60% 3. 测试阶段开始准备中</div>
                                <div class="note-list-meta">
                                    <span class="note-date">2023-08-12</span>
                                </div>
                            </div>
                            
                            <div class="note-list-item">
                                <div class="note-list-title">客户需求整理</div>
                                <div class="note-list-content">收集客户反馈，整理关键需求点，优先级排序，准备下一轮迭代...</div>
                                <div class="note-list-meta">
                                    <span class="note-date">2023-08-10</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item active">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 搜索结果页面 -->
        <div>
            <h3 class="screen-title">搜索结果</h3>
            <p class="screen-description">展示笔记搜索结果，支持筛选和排序</p>
            <div class="screen">
                <div class="search-results-container">
                    <div class="search-header">
                        <div class="back-btn">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="search-input">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索笔记..." value="工作计划">
                            <i class="fas fa-times" style="margin-left: 10px;"></i>
                        </div>
                    </div>
                    
                    <div class="search-filters">
                        <div class="filter-tag active">全部</div>
                        <div class="filter-tag">标题</div>
                        <div class="filter-tag">内容</div>
                        <div class="filter-tag">工作</div>
                        <div class="filter-tag">最近7天</div>
                        <div class="filter-tag">最近30天</div>
                    </div>
                    
                    <div class="search-results">
                        <div class="result-section">
                            <div class="result-section-title">
                                搜索结果 <span class="result-count">(3)</span>
                            </div>
                            
                            <div class="note-list-item">
                                <div class="note-list-title"><span class="highlight">工作计划</span></div>
                                <div class="note-list-content">本周需要完成的任务：1. 项目<span class="highlight">计划</span>书 2. 客户会议 3. 团队协调 4. 进度报告</div>
                                <div class="note-list-meta">
                                    <span class="note-tags">工作</span>
                                    <span class="note-date">2023-08-15</span>
                                </div>
                            </div>
                            
                            <div class="note-list-item">
                                <div class="note-list-title">月度<span class="highlight">工作计划</span>安排</div>
                                <div class="note-list-content">九月份<span class="highlight">工作计划</span>：1. 完成产品迭代 2. 进行用户调研 3. 制定季度目标...</div>
                                <div class="note-list-meta">
                                    <span class="note-tags">工作</span>
                                    <span class="note-date">2023-08-10</span>
                                </div>
                            </div>
                            
                            <div class="note-list-item">
                                <div class="note-list-title">团队<span class="highlight">工作计划</span>模板</div>
                                <div class="note-list-content">标准化团队<span class="highlight">工作计划</span>模板，包含目标设定、任务分解、进度跟踪...</div>
                                <div class="note-list-meta">
                                    <span class="note-tags">工作</span>
                                    <span class="note-date">2023-07-25</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 无搜索结果 -->
        <div>
            <h3 class="screen-title">无搜索结果</h3>
            <p class="screen-description">当搜索无匹配结果时的提示界面</p>
            <div class="screen">
                <div class="search-results-container">
                    <div class="search-header">
                        <div class="back-btn">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="search-input">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索笔记..." value="未知内容xyz">
                            <i class="fas fa-times" style="margin-left: 10px;"></i>
                        </div>
                    </div>
                    
                    <div class="search-filters">
                        <div class="filter-tag active">全部</div>
                        <div class="filter-tag">标题</div>
                        <div class="filter-tag">内容</div>
                        <div class="filter-tag">最近7天</div>
                        <div class="filter-tag">最近30天</div>
                    </div>
                    
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <h3>未找到相关笔记</h3>
                        <p>尝试使用其他关键词或更改筛选条件进行搜索</p>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>智云笔记应用原型设计 &copy; 2023</p>
            <p>
                <a href="welcome.html">欢迎页面</a> | 
                <a href="auth.html">认证页面</a> | 
                <a href="home.html">主页</a> | 
                <a href="editor.html">编辑器</a> | 
                <a href="settings.html">设置</a> | 
                <a href="tags.html">标签</a>
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
