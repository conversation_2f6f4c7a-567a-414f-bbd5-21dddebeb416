import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as Math;
import 'dart:typed_data';

import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/utils/date_time_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

// 导入必要的依赖
import 'package:file_picker/file_picker.dart';
import 'package:file_saver/file_saver.dart';
import 'package:share_plus/share_plus.dart';
import 'package:universal_html/html.dart' as html;
import 'package:cross_file/cross_file.dart';

/// 笔记导出服务
///
/// 提供导出笔记到文件和导入笔记从文件的功能
class ExportService {
  // 标签ID到名称的映射缓存
  Map<String, String> _tagIdToNameCache = {};

  /// 设置标签ID到名称的映射
  void setTagIdToNameMap(Map<String, String> tagMap) {
    _tagIdToNameCache = tagMap;
    print('DEBUG: [ExportService] 设置标签映射: $_tagIdToNameCache');
  }

  /// 根据ID获取标签名称
  String getTagNameById(String tagId) {
    return _tagIdToNameCache[tagId] ?? tagId;
  }

  /// 导出单个笔记到文件
  ///
  /// [note] 要导出的笔记
  /// [exportFormat] 导出格式，可以是'markdown'或'json'(富文本)
  Future<bool> exportNoteToFile(Note note,
      {String exportFormat = 'json'}) async {
    try {
      // 准备导出数据
      final String fileName =
          _sanitizeFileName('${note.title.isEmpty ? "未命名笔记" : note.title}');
      String fileContent;
      String extension;
      String mimeType;

      if (exportFormat == 'markdown') {
        // 导出为Markdown格式
        // 构建Markdown内容
        StringBuffer markdownContent = StringBuffer();

        // 添加标题
        markdownContent.writeln('# ${note.title.isEmpty ? "无标题" : note.title}');
        markdownContent.writeln();

        // 添加元数据 (使用HTML注释格式，避免在导入时被当作内容)
        markdownContent.writeln('<!-- 笔记元数据');
        markdownContent.writeln('创建时间: ${note.createdAt.toIso8601String()}');
        markdownContent.writeln('更新时间: ${note.updatedAt.toIso8601String()}');
        markdownContent.writeln('笔记ID: ${note.id}');
        markdownContent.writeln('内容类型: ${note.contentType}');
        markdownContent.writeln('-->');
        markdownContent.writeln();

        // 添加内容
        if (note.contentType == 'markdown') {
          // Markdown格式直接添加
          markdownContent.writeln(note.content);
        } else {
          // 富文本格式转换为Markdown - 简单处理，提取纯文本
          try {
            // 尝试解析JSON格式的富文本内容
            final dynamic jsonData = jsonDecode(note.content);
            if (jsonData is List) {
              // 提取纯文本内容
              StringBuffer plainText = StringBuffer();
              for (var op in jsonData) {
                if (op is Map && op.containsKey('insert')) {
                  var insert = op['insert'];
                  if (insert is String) {
                    plainText.write(insert);
                  }
                }
              }
              markdownContent.writeln(plainText.toString());
            } else {
              // 无法解析的情况，直接使用原始内容
              markdownContent.writeln(note.content);
            }
          } catch (e) {
            // JSON解析失败，直接使用原始内容
            markdownContent.writeln(note.content);
          }
        }

        fileContent = markdownContent.toString();
        extension = 'md';
        mimeType = 'text/markdown';
      } else {
        // 导出为JSON格式(富文本)
        final Map<String, dynamic> exportData = {
          'title': note.title,
          'content': note.content,
          'contentType': note.contentType,
          'createdAt': note.createdAt.toIso8601String(),
          'updatedAt': note.updatedAt.toIso8601String(),
          'tags': note.tagIds,
          'isFavorite': note.isFavorite,
          'isArchived': note.isArchived,
        };
        fileContent = jsonEncode(exportData);
        extension = 'json';
        mimeType = 'application/json';
      }

      // 根据平台选择不同的保存方式
      if (kIsWeb) {
        // Web平台使用FileSaver
        final bytes = Uint8List.fromList(utf8.encode(fileContent));
        await FileSaver.instance.saveFile(
          name: fileName,
          bytes: bytes,
          ext: extension,
          mimeType: MimeType.custom,
          customMimeType: mimeType,
        );
      } else {
        // 移动平台使用File API
        final directory = await getApplicationDocumentsDirectory();
        final filePath = '${directory.path}/$fileName.$extension';
        final file = File(filePath);

        // 检查目录是否存在，不存在则创建
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }

        // 检查磁盘空间（简单检查）
        final fileSize = utf8.encode(fileContent).length;
        if (fileSize > 100 * 1024 * 1024) {
          // 100MB限制
          throw Exception('文件过大，无法导出');
        }

        // 写入文件，添加重试机制
        int retryCount = 0;
        const maxRetries = 3;
        while (retryCount < maxRetries) {
          try {
            await file.writeAsString(fileContent);
            break;
          } catch (e) {
            retryCount++;
            if (retryCount >= maxRetries) {
              throw Exception('文件写入失败，请检查存储空间: $e');
            }
            await Future.delayed(Duration(milliseconds: 500 * retryCount));
          }
        }

        // 验证文件是否写入成功
        if (!await file.exists()) {
          throw Exception('文件创建失败');
        }

        // 分享文件
        try {
          await Share.shareXFiles(
            [XFile(filePath)],
            subject: '分享笔记: ${note.title}',
          );
        } catch (e) {
          // 分享失败时，至少文件已经保存了
          throw Exception('文件已保存但分享失败: $e');
        }
      }

      return true;
    } catch (e) {
      print('导出笔记失败: $e');
      return false;
    }
  }

  /// 导出多个笔记到单个文件
  ///
  /// [notes] 要导出的笔记列表
  /// [exportFormat] 导出格式，可以是'markdown'或'json'(富文本)
  Future<bool> exportNotesToZip(List<Note> notes,
      {String exportFormat = 'json'}) async {
    try {
      String fileContent;
      String extension;
      String mimeType;
      String fileName = 'notes_export_${DateTime.now().millisecondsSinceEpoch}';

      if (exportFormat == 'markdown') {
        // 导出为Markdown格式 - 将所有笔记合并为一个Markdown文件
        StringBuffer markdownContent = StringBuffer();

        for (var note in notes) {
          // 添加标题
          markdownContent
              .writeln('# ${note.title.isEmpty ? "无标题" : note.title}');
          markdownContent.writeln();

          // 添加元数据 (使用HTML注释格式，避免在导入时被当作内容)
          markdownContent.writeln('<!-- 笔记元数据');
          markdownContent.writeln('创建时间: ${note.createdAt.toIso8601String()}');
          markdownContent.writeln('更新时间: ${note.updatedAt.toIso8601String()}');
          markdownContent.writeln('笔记ID: ${note.id}');
          markdownContent.writeln('内容类型: ${note.contentType}');
          markdownContent.writeln('标签: ${note.tagIds.join(",")}');
          markdownContent.writeln('收藏: ${note.isFavorite}');
          markdownContent.writeln('归档: ${note.isArchived}');
          markdownContent.writeln('-->');
          markdownContent.writeln();

          // 添加内容 - 根据笔记类型处理
          if (note.contentType == 'markdown') {
            // Markdown格式直接添加
            markdownContent.writeln(note.content);
          } else {
            // 富文本格式转换为纯文本
            try {
              // 尝试解析JSON格式的富文本内容
              final dynamic jsonData = jsonDecode(note.content);
              if (jsonData is List) {
                // 提取纯文本内容
                StringBuffer plainText = StringBuffer();
                for (var op in jsonData) {
                  if (op is Map && op.containsKey('insert')) {
                    var insert = op['insert'];
                    if (insert is String) {
                      plainText.write(insert);
                    }
                  }
                }
                markdownContent.writeln(plainText.toString());
              } else {
                // 无法解析的情况，直接使用原始内容
                markdownContent.writeln(note.content);
              }
            } catch (e) {
              // JSON解析失败，直接使用原始内容
              markdownContent.writeln(note.content);
            }
          }

          // 添加分隔符
          markdownContent.writeln('\n---\n');
        }

        fileContent = markdownContent.toString();
        extension = 'md';
        mimeType = 'text/markdown';
      } else {
        // 导出为JSON格式(富文本)
        final Map<String, dynamic> exportData = {
          'notes': notes
              .map((note) => {
                    'title': note.title,
                    'content': note.content,
                    'contentType': note.contentType,
                    'createdAt': DateTimeHelper.formatDateTime(note.createdAt),
                    'updatedAt': DateTimeHelper.formatDateTime(note.updatedAt),
                    'tagNames': note.tagIds
                        .map((id) => getTagNameById(id))
                        .toList(), // 只保留标签名称
                  })
              .toList(),
          'exportTime': DateTime.now().toIso8601String(),
          'totalNotes': notes.length,
        };

        fileContent = jsonEncode(exportData);
        extension = 'json';
        mimeType = 'application/json';
      }

      // 根据平台选择不同的保存方式
      if (kIsWeb) {
        // Web平台使用FileSaver
        final bytes = Uint8List.fromList(utf8.encode(fileContent));
        await FileSaver.instance.saveFile(
          name: fileName,
          bytes: bytes,
          ext: extension,
          mimeType:
              exportFormat == 'markdown' ? MimeType.custom : MimeType.json,
          customMimeType: exportFormat == 'markdown' ? 'text/markdown' : null,
        );
      } else {
        // 移动平台使用File API
        final directory = await getApplicationDocumentsDirectory();
        final filePath = '${directory.path}/$fileName.$extension';
        final file = File(filePath);
        await file.writeAsString(fileContent);

        // 分享文件
        await Share.shareXFiles(
          [XFile(filePath)],
          subject: '分享笔记导出',
        );
      }

      return true;
    } catch (e) {
      print('导出笔记失败: $e');
      return false;
    }
  }

  /// 清理文件名，移除不允许的字符
  String _sanitizeFileName(String fileName) {
    print('DEBUG: [ExportService] 清理文件名: $fileName');
    // 移除文件名中不允许的字符
    String sanitized = fileName
        .replaceAll(RegExp(r'[\\/:*?"<>|]'), '_') // 替换Windows不允许的字符
        .replaceAll(RegExp(r'[\x00-\x1F]'), '') // 替换控制字符
        .trim();

    // 限制文件名长度
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100);
    }

    // 确保文件名不为空
    if (sanitized.isEmpty) {
      sanitized = 'untitled';
    }

    print('DEBUG: [ExportService] 清理后的文件名: $sanitized');
    return sanitized;
  }

  /// 将每个笔记导出为单独的文件
  ///
  /// [notes] 要导出的笔记列表
  Future<bool> exportNotesToSeparateFiles(List<Note> notes) async {
    try {
      print('DEBUG: [ExportService] 开始导出单独文件，笔记数量: ${notes.length}');
      // 创建临时目录用于存放导出的文件
      Directory tempDir;
      List<XFile> exportedFiles = [];

      if (!kIsWeb) {
        // 移动平台：创建临时目录
        print('DEBUG: [ExportService] 移动平台导出');
        tempDir = await getTemporaryDirectory();
        final exportDir = Directory(
            '${tempDir.path}/notes_export_${DateTime.now().millisecondsSinceEpoch}');
        print('DEBUG: [ExportService] 导出目录: ${exportDir.path}');
        if (!await exportDir.exists()) {
          await exportDir.create(recursive: true);
          print('DEBUG: [ExportService] 创建导出目录');
        }

        // 为每个笔记创建单独的文件
        for (var note in notes) {
          print(
              'DEBUG: [ExportService] 处理笔记: ${note.id}, 标题: ${note.title}, 类型: ${note.contentType}');
          final String fileName = _sanitizeFileName(note.title.isEmpty
              ? "无标题_${note.id.substring(0, 8)}"
              : note.title);
          print('DEBUG: [ExportService] 文件名: $fileName');
          String extension;
          String fileContent;

          if (note.contentType == 'markdown') {
            // Markdown笔记导出为.md文件
            extension = 'md';

            // 创建Markdown内容
            StringBuffer markdownContent = StringBuffer();

            // 添加标题
            markdownContent
                .writeln('# ${note.title.isEmpty ? "无标题" : note.title}');
            markdownContent.writeln();

            // 添加元数据 (使用HTML注释格式，避免在导入时被当作内容)
            markdownContent.writeln('<!-- 笔记元数据');
            // 使用DateTimeHelper转换为本地时间并格式化
            final localCreatedAt =
                DateTimeHelper.formatDateTime(note.createdAt);
            final localUpdatedAt =
                DateTimeHelper.formatDateTime(note.updatedAt);
            markdownContent.writeln('创建时间: $localCreatedAt');
            markdownContent.writeln('更新时间: $localUpdatedAt');
            markdownContent.writeln('内容类型: ${note.contentType}');

            // 获取标签名称
            List<String> tagNames = [];
            if (note.tagIds.isNotEmpty) {
              print('DEBUG: [ExportService] 导出笔记标签IDs: ${note.tagIds}');
              // 使用标签ID到名称的映射获取标签名称
              tagNames = note.tagIds.map((id) => getTagNameById(id)).toList();
              print('DEBUG: [ExportService] 导出笔记标签名称: $tagNames');
            }

            markdownContent.writeln('标签: ${tagNames.join(",")}');
            markdownContent.writeln('-->');
            markdownContent.writeln();

            // 添加内容
            markdownContent.writeln(note.content);

            fileContent = markdownContent.toString();
          } else {
            // 富文本笔记导出为.json文件
            extension = 'json';

            // 创建JSON内容
            final Map<String, dynamic> jsonData = {
              'title': note.title,
              'content': note.content,
              'contentType': note.contentType,
              'createdAt': DateTimeHelper.formatDateTime(note.createdAt),
              'updatedAt': DateTimeHelper.formatDateTime(note.updatedAt),
              'tagNames': note.tagIds
                  .map((id) => getTagNameById(id))
                  .toList(), // 只保留标签名称
            };

            fileContent = jsonEncode(jsonData);
          }

          // 写入文件，添加异常处理
          final filePath = '${exportDir.path}/$fileName.$extension';
          print('DEBUG: [ExportService] 写入文件: $filePath');
          final file = File(filePath);

          // 检查文件大小
          final fileSize = utf8.encode(fileContent).length;
          if (fileSize > 50 * 1024 * 1024) {
            // 50MB限制
            print('DEBUG: [ExportService] 文件过大，跳过: $fileName');
            continue; // 跳过过大的文件
          }

          // 写入文件，添加重试机制
          int retryCount = 0;
          const maxRetries = 3;
          bool writeSuccess = false;

          while (retryCount < maxRetries && !writeSuccess) {
            try {
              await file.writeAsString(fileContent);
              writeSuccess = true;
              print('DEBUG: [ExportService] 文件写入成功');
            } catch (e) {
              retryCount++;
              print(
                  'DEBUG: [ExportService] 文件写入失败 (尝试 $retryCount/$maxRetries): $e');
              if (retryCount >= maxRetries) {
                print('DEBUG: [ExportService] 文件写入最终失败，跳过: $fileName');
                continue; // 跳过写入失败的文件
              }
              await Future.delayed(Duration(milliseconds: 500 * retryCount));
            }
          }

          // 验证文件是否写入成功
          if (!await file.exists()) {
            print('DEBUG: [ExportService] 文件创建验证失败，跳过: $fileName');
            continue;
          }

          // 添加到导出文件列表
          exportedFiles.add(XFile(filePath));
          print(
              'DEBUG: [ExportService] 添加到导出列表，当前列表大小: ${exportedFiles.length}');
        }

        // 分享所有文件
        if (exportedFiles.isNotEmpty) {
          print('DEBUG: [ExportService] 开始分享文件，数量: ${exportedFiles.length}');
          try {
            await Share.shareXFiles(
              exportedFiles,
              subject: '分享笔记导出',
            );
            print('DEBUG: [ExportService] 文件分享成功');
          } catch (e) {
            print('DEBUG: [ExportService] 文件分享失败: $e');
            throw e; // 重新抛出异常，确保上层能捕获到
          }
        }
      } else {
        // Web平台：逐个导出文件
        for (var note in notes) {
          final String fileName = _sanitizeFileName(note.title.isEmpty
              ? "无标题_${note.id.substring(0, 8)}"
              : note.title);
          String extension;
          String fileContent;
          MimeType mimeType;
          String? customMimeType;

          if (note.contentType == 'markdown') {
            // Markdown笔记导出为.md文件
            extension = 'md';
            mimeType = MimeType.custom;
            customMimeType = 'text/markdown';

            // 创建Markdown内容
            StringBuffer markdownContent = StringBuffer();

            // 添加标题
            markdownContent
                .writeln('# ${note.title.isEmpty ? "无标题" : note.title}');
            markdownContent.writeln();

            // 添加元数据 (使用HTML注释格式，避免在导入时被当作内容)
            markdownContent.writeln('<!-- 笔记元数据');
            // 使用DateTimeHelper转换为本地时间并格式化
            final localCreatedAt =
                DateTimeHelper.formatDateTime(note.createdAt);
            final localUpdatedAt =
                DateTimeHelper.formatDateTime(note.updatedAt);
            markdownContent.writeln('创建时间: $localCreatedAt');
            markdownContent.writeln('更新时间: $localUpdatedAt');
            markdownContent.writeln('内容类型: ${note.contentType}');
            // 获取标签名称
            List<String> tagNames = [];
            if (note.tagIds.isNotEmpty) {
              print('DEBUG: [ExportService] Web导出笔记标签IDs: ${note.tagIds}');
              // 使用标签ID到名称的映射获取标签名称
              tagNames = note.tagIds.map((id) => getTagNameById(id)).toList();
              print('DEBUG: [ExportService] Web导出笔记标签名称: $tagNames');
            }
            markdownContent.writeln('标签: ${tagNames.join(",")}');
            markdownContent.writeln('-->');
            markdownContent.writeln();

            // 添加内容
            markdownContent.writeln(note.content);

            fileContent = markdownContent.toString();
          } else {
            // 富文本笔记导出为.json文件
            extension = 'json';
            mimeType = MimeType.json;
            customMimeType = null;

            // 创建JSON内容
            final Map<String, dynamic> jsonData = {
              'title': note.title,
              'content': note.content,
              'contentType': note.contentType,
              'createdAt': DateTimeHelper.formatDateTime(note.createdAt),
              'updatedAt': DateTimeHelper.formatDateTime(note.updatedAt),
              'tagNames': note.tagIds
                  .map((id) => getTagNameById(id))
                  .toList(), // 只保留标签名称
            };

            fileContent = jsonEncode(jsonData);
          }

          // 保存文件
          final bytes = Uint8List.fromList(utf8.encode(fileContent));
          await FileSaver.instance.saveFile(
            name: fileName,
            bytes: bytes,
            ext: extension,
            mimeType: mimeType,
            customMimeType: customMimeType,
          );

          // Web平台需要等待一下，避免浏览器阻止多个下载
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      return true;
    } catch (e) {
      print('导出笔记失败: $e');
      return false;
    }
  }

  /// 从文件导入笔记
  ///
  /// 返回导入的笔记列表，可以直接传递给NoteProvider.importNotes方法
  Future<List<Map<String, dynamic>>?> importNotesFromFile() async {
    try {
      // 选择文件，添加超时处理
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json', 'md', 'markdown', 'txt'],
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException('文件选择超时', const Duration(seconds: 30));
        },
      );

      if (result == null || result.files.isEmpty) {
        return null; // 用户取消了选择
      }

      final file = result.files.first;
      String fileContent;

      if (kIsWeb) {
        // Web平台读取文件内容
        fileContent = utf8.decode(file.bytes!);
      } else {
        // 移动平台读取文件内容
        final filePath = file.path!;
        fileContent = await File(filePath).readAsString();
      }

      // 根据文件扩展名处理不同格式
      final extension = file.extension?.toLowerCase() ?? '';

      if (extension == 'json') {
        // 处理JSON格式
        final dynamic jsonData = jsonDecode(fileContent);

        if (jsonData is Map && jsonData.containsKey('notes')) {
          // 批量导入格式
          final List<dynamic> notesData = jsonData['notes'];

          // 处理每个笔记的标签
          return notesData.map<Map<String, dynamic>>((note) {
            final Map<String, dynamic> noteMap =
                Map<String, dynamic>.from(note);

            // 检查是否有标签字段，并转换为标签名称
            List<String> tagNames = [];

            // 优先从 tagNames 字段获取标签名称
            if (noteMap.containsKey('tagNames') &&
                noteMap['tagNames'] is List) {
              print(
                  'DEBUG: [ExportService] 从tagNames字段获取标签名称: ${noteMap['tagNames']}');
              tagNames.addAll(List<String>.from(noteMap['tagNames']));
            }
            // 从 tagIds 字段获取标签
            else if (noteMap.containsKey('tagIds') &&
                noteMap['tagIds'] is List) {
              print(
                  'DEBUG: [ExportService] 从tagIds字段获取标签: ${noteMap['tagIds']}');
              tagNames.addAll(List<String>.from(noteMap['tagIds']));
            }
            // 从 tags 字段获取标签（兼容旧版本）
            else if (noteMap.containsKey('tags') && noteMap['tags'] is List) {
              print('DEBUG: [ExportService] 从tags字段获取标签: ${noteMap['tags']}');
              tagNames.addAll(List<String>.from(noteMap['tags']));
            }

            // 更新笔记数据
            if (tagNames.isNotEmpty) {
              noteMap['tagNames'] = tagNames;
              print('DEBUG: [ExportService] 设置标签名称: $tagNames');
            }

            // 移除原始标签字段，避免混淆
            if (noteMap.containsKey('tags')) noteMap.remove('tags');
            if (noteMap.containsKey('tagIds')) noteMap.remove('tagIds');

            // 设置默认状态，不导入原有状态
            noteMap['isFavorite'] = false;
            noteMap['isArchived'] = false;

            return noteMap;
          }).toList();
        } else if (jsonData is Map) {
          // 单个笔记格式
          final Map<String, dynamic> noteMap =
              Map<String, dynamic>.from(jsonData);

          // 检查是否有标签字段，并转换为标签名称
          List<String> tagNames = [];

          // 优先从 tagNames 字段获取标签名称
          if (noteMap.containsKey('tagNames') && noteMap['tagNames'] is List) {
            print(
                'DEBUG: [ExportService] 单个笔记从tagNames字段获取标签名称: ${noteMap['tagNames']}');
            tagNames.addAll(List<String>.from(noteMap['tagNames']));
          }
          // 从 tagIds 字段获取标签
          else if (noteMap.containsKey('tagIds') && noteMap['tagIds'] is List) {
            print(
                'DEBUG: [ExportService] 单个笔记从tagIds字段获取标签: ${noteMap['tagIds']}');
            tagNames.addAll(List<String>.from(noteMap['tagIds']));
          }
          // 从 tags 字段获取标签（兼容旧版本）
          else if (noteMap.containsKey('tags') && noteMap['tags'] is List) {
            print('DEBUG: [ExportService] 单个笔记从tags字段获取标签: ${noteMap['tags']}');
            tagNames.addAll(List<String>.from(noteMap['tags']));
          }

          // 更新笔记数据
          if (tagNames.isNotEmpty) {
            noteMap['tagNames'] = tagNames;
            print('DEBUG: [ExportService] 单个笔记设置标签名称: $tagNames');
          }

          // 移除原始标签字段，避免混淆
          if (noteMap.containsKey('tags')) noteMap.remove('tags');
          if (noteMap.containsKey('tagIds')) noteMap.remove('tagIds');

          // 设置默认状态，不导入原有状态
          noteMap['isFavorite'] = false;
          noteMap['isArchived'] = false;

          return [noteMap];
        }
      } else if (extension == 'md' ||
          extension == 'markdown' ||
          extension == 'txt') {
        // 处理Markdown或文本格式
        String title = file.name.replaceAll('.$extension', '');
        String content = fileContent;
        List<String> tagNames = []; // 存储标签名称
        bool isFavorite = false;
        bool isArchived = false;

        // 定义时间变量
        DateTime? parsedCreatedAt;
        DateTime? parsedUpdatedAt;

        // 尝试从Markdown内容中提取标题和元数据
        try {
          print(
              'DEBUG: [ExportService] 开始解析Markdown内容，长度: ${fileContent.length}');
          final lines = fileContent.split('\n');
          print('DEBUG: [ExportService] 文件行数: ${lines.length}');

          // 打印前几行内容，帮助调试
          for (int i = 0; i < Math.min(20, lines.length); i++) {
            print('DEBUG: [ExportService] 第${i + 1}行: "${lines[i]}"');
          }

          // 检查第一行是否是标题格式 (# 标题)
          if (lines.isNotEmpty && lines[0].startsWith('# ')) {
            title = lines[0].substring(2).trim();
            print('DEBUG: [ExportService] 提取到标题: $title');

            // 移除标题行
            lines.removeAt(0);

            // 检查是否有元数据行 (HTML注释格式或旧的 > 格式)
            int metadataStartIndex = -1;
            int metadataEndIndex = -1;
            Map<String, String> metadata = {};

            print('DEBUG: [ExportService] 开始检查元数据');

            // 首先找到元数据的开始和结束位置
            for (int i = 0; i < lines.length; i++) {
              String line = lines[i].trim();
              print('DEBUG: [ExportService] 检查元数据行索引 $i: "$line"');

              if (line.contains('<!--') && line.contains('笔记元数据')) {
                print('DEBUG: [ExportService] 发现元数据开始标记，索引: $i');
                metadataStartIndex = i;
              } else if (metadataStartIndex != -1 && line.contains('-->')) {
                print('DEBUG: [ExportService] 发现元数据结束标记，索引: $i');
                metadataEndIndex = i;
                break;
              }
            }

            // 如果找到了元数据的开始和结束位置，解析元数据
            if (metadataStartIndex != -1 && metadataEndIndex != -1) {
              print(
                  'DEBUG: [ExportService] 找到元数据，范围: $metadataStartIndex-$metadataEndIndex');

              // 解析元数据行
              for (int i = metadataStartIndex + 1; i < metadataEndIndex; i++) {
                String line = lines[i].trim();
                print('DEBUG: [ExportService] 解析元数据行: "$line"');

                if (line.contains(':')) {
                  final parts = line.split(':');
                  if (parts.length >= 2) {
                    final key = parts[0].trim();
                    final value = parts.sublist(1).join(':').trim();
                    metadata[key] = value;
                    print('DEBUG: [ExportService] 解析到元数据: $key = $value');
                  }
                }
              }

              // 移除元数据行
              lines.removeRange(metadataStartIndex, metadataEndIndex + 1);
              print(
                  'DEBUG: [ExportService] 移除元数据行，范围: $metadataStartIndex-${metadataEndIndex + 1}');

              // 如果下一行是空行，也移除它
              if (lines.isNotEmpty && lines[0].trim().isEmpty) {
                print('DEBUG: [ExportService] 移除元数据后的空行');
                lines.removeAt(0);
              }
            } else {
              print('DEBUG: [ExportService] 未找到完整的元数据标记');

              // 尝试检查旧格式的元数据 (> 创建时间: xxx)
              int oldMetadataEndIndex = 0;
              while (oldMetadataEndIndex < lines.length) {
                String line = lines[oldMetadataEndIndex].trim();

                if (line.startsWith('> ') && line.contains(':')) {
                  final parts = line.substring(2).split(':');
                  if (parts.length >= 2) {
                    final key = parts[0].trim();
                    final value = parts.sublist(1).join(':').trim();
                    metadata[key] = value;
                    print('DEBUG: [ExportService] 解析旧格式元数据: $key = $value');
                  }
                  oldMetadataEndIndex++;
                } else {
                  // 不是元数据行，结束检查
                  break;
                }
              }

              // 如果找到了旧格式元数据，移除它们
              if (oldMetadataEndIndex > 0) {
                print(
                    'DEBUG: [ExportService] 移除旧格式元数据行，范围: 0-$oldMetadataEndIndex');
                lines.removeRange(0, oldMetadataEndIndex);

                // 如果下一行是空行，也移除它
                if (lines.isNotEmpty && lines[0].trim().isEmpty) {
                  print('DEBUG: [ExportService] 移除元数据后的空行');
                  lines.removeAt(0);
                }
              } else {
                print('DEBUG: [ExportService] 未找到任何元数据');
              }
            }

            // 从元数据中提取标签和时间
            print('DEBUG: [ExportService] 元数据内容: $metadata');

            // 提取标签
            if (metadata.containsKey('标签')) {
              final tagsStr = metadata['标签'] ?? '';
              print('DEBUG: [ExportService] 提取到标签字符串: $tagsStr');
              if (tagsStr.isNotEmpty) {
                tagNames = tagsStr
                    .split(',')
                    .map((t) => t.trim())
                    .where((t) => t.isNotEmpty)
                    .toList();
                print('DEBUG: [ExportService] 解析后的标签列表: $tagNames');
              }
            }

            // 提取创建时间
            if (metadata.containsKey('创建时间')) {
              final createdAtStr = metadata['创建时间'] ?? '';
              print('DEBUG: [ExportService] 提取到创建时间: $createdAtStr');
              parsedCreatedAt = DateTimeHelper.parseDateTime(createdAtStr);
              if (parsedCreatedAt != null) {
                print('DEBUG: [ExportService] 解析后的创建时间: $parsedCreatedAt');
              }
            }

            // 提取更新时间
            if (metadata.containsKey('更新时间')) {
              final updatedAtStr = metadata['更新时间'] ?? '';
              print('DEBUG: [ExportService] 提取到更新时间: $updatedAtStr');
              parsedUpdatedAt = DateTimeHelper.parseDateTime(updatedAtStr);
              if (parsedUpdatedAt != null) {
                print('DEBUG: [ExportService] 解析后的更新时间: $parsedUpdatedAt');
              }
            }

            // 不导入收藏和归档状态，使用默认值
            isFavorite = false;
            isArchived = false;

            // 重新组合内容
            content = lines.join('\n');
            print('DEBUG: [ExportService] 处理后的内容长度: ${content.length}');
          }
        } catch (e) {
          print('解析Markdown内容时出错: $e');
          // 出错时使用原始内容
        }

        // 创建笔记数据
        Map<String, dynamic> noteData = {
          'title': title,
          'content': content,
          'contentType': 'markdown',
          'tagNames': tagNames, // 使用标签名称而不是ID
          'isFavorite': false, // 默认不收藏
          'isArchived': false, // 默认不归档
        };

        // 添加时间信息（如果有）
        if (parsedCreatedAt != null) {
          noteData['createdAt'] = parsedCreatedAt.toIso8601String();
        }

        if (parsedUpdatedAt != null) {
          noteData['updatedAt'] = parsedUpdatedAt.toIso8601String();
        }

        return [noteData];
      }

      return null; // 不支持的格式
    } catch (e) {
      print('导入笔记失败: $e');
      return null;
    }
  }

  // 已在上方定义了 _sanitizeFileName 方法
}
