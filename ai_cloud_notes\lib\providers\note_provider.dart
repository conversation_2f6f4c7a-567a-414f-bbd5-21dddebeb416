import 'package:flutter/material.dart';
import '../models/note_model.dart';
import '../services/api_service.dart';

/// 笔记过滤类型
enum NoteFilter {
  all, // 全部笔记
  favorite, // 收藏笔记
  tagged, // 特定标签的笔记
}

/// 笔记排序方式
enum NoteSortOrder {
  createdAt, // 按创建时间排序
  updatedAt, // 按更新时间排序
  title, // 按标题排序
}

/// 笔记状态提供者
///
/// 负责管理应用中的笔记状态，包括：
/// - 笔记列表获取与缓存
/// - 笔记筛选(全部/收藏/归档)
/// - 笔记排序
/// - 笔记搜索
/// - 笔记统计信息
class NoteProvider with ChangeNotifier {
  final ApiService _apiService = ApiService();

  // 笔记列表
  List<Note> _notes = [];
  List<Note> get notes => _notes;

  // 当前选中的笔记
  Note? _selectedNote;
  Note? get selectedNote => _selectedNote;

  // 状态
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String _error = '';
  String get error => _error;

  // --- 历史版本相关状态 ---
  /// 存储当前查看笔记的历史版本列表
  List<Note> _noteHistory = [];

  /// 公开的历史版本列表 getter
  List<Note> get noteHistory => _noteHistory;

  /// 是否正在加载历史记录的状态标志
  bool _isLoadingHistory = false;

  /// 公开的加载历史记录状态 getter
  bool get isLoadingHistory => _isLoadingHistory;

  /// 加载历史记录时的错误信息
  String _historyError = '';

  /// 公开的历史记录错误信息 getter
  String get historyError => _historyError;

  /// 是否正在恢复版本的状态标志
  bool _isRestoringVersion = false;

  /// 公开的恢复版本状态 getter
  bool get isRestoringVersion => _isRestoringVersion;
  // --- 结束 历史版本相关状态 ---

  // 筛选状态
  NoteFilter _filterType = NoteFilter.all;
  NoteFilter get filterType => _filterType;

  // 排序方式
  NoteSortOrder _sortOrder = NoteSortOrder.updatedAt;
  NoteSortOrder get sortOrder => _sortOrder;

  // 归档状态筛选（true表示只显示已归档，false表示显示未归档，null表示不过滤）
  bool? _archivedFilter = false;
  bool? get archivedFilter => _archivedFilter;

  // 搜索关键词
  String _searchQuery = '';
  String get searchQuery => _searchQuery;

  // 分页
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMore = true;
  bool get hasMore => _hasMore;

  // 笔记统计
  Map<String, dynamic> _noteStats = {
    'total': 0,
    'favorite': 0,
    'archived': 0,
    'today': 0,
    'thisWeek': 0,
    'thisMonth': 0,
  };
  Map<String, dynamic> get noteStats => _noteStats;

  // 用于标记各种筛选类型的缓存是否需要刷新
  Map<NoteFilter, bool> _refreshFlags = {
    NoteFilter.all: false,
    NoteFilter.favorite: false,
    NoteFilter.tagged: false,
  };

  // 缓存不同筛选类型下的笔记列表
  Map<NoteFilter, List<Note>> _cachedNotes = {
    NoteFilter.all: [],
    NoteFilter.favorite: [],
    NoteFilter.tagged: [],
  };

  // 缓存不同筛选类型下的分页状态
  Map<NoteFilter, int> _cachedPages = {
    NoteFilter.all: 1,
    NoteFilter.favorite: 1,
    NoteFilter.tagged: 1,
  };

  Map<NoteFilter, bool> _cachedHasMore = {
    NoteFilter.all: true,
    NoteFilter.favorite: true,
    NoteFilter.tagged: true,
  };

  /// 标记所有笔记视图需要刷新
  /// 而不实际切换到该视图
  void markAllNotesForRefresh() {
    // 标记所有类型需要刷新
    _refreshFlags.forEach((key, value) {
      _refreshFlags[key] = true;
    });
  }

  /// 设置筛选类型
  void setFilter(NoteFilter type) {
    final bool needsRefresh = _refreshFlags[type] == true;
    final bool isTypeChange = _filterType != type;

    if (isTypeChange || needsRefresh) {
      // 如果是切换类型，保存当前类型的笔记和分页状态
      if (isTypeChange) {
        _cachedNotes[_filterType] = List.from(_notes);
        _cachedPages[_filterType] = _currentPage;
        _cachedHasMore[_filterType] = _hasMore;
      }

      // 切换到新类型
      _filterType = type;

      // 如果新类型有缓存且不需要刷新，直接使用缓存
      if (_cachedNotes[type]!.isNotEmpty && !needsRefresh) {
        _notes = List.from(_cachedNotes[type]!);
        _currentPage = _cachedPages[type]!;
        _hasMore = _cachedHasMore[type]!;
        notifyListeners();
      } else {
        // 否则重置状态并获取新数据
        _currentPage = 1;
        _notes = [];
        _hasMore = true;
        _refreshFlags[type] = false;
        fetchNotes(refresh: true);
      }

      // 如果没有实际切换类型但需要刷新，则通知监听器
      if (!isTypeChange && needsRefresh) {
        notifyListeners();
      }
    }
  }

  /// 设置归档状态筛选
  /// [archived] true表示只显示已归档，false表示只显示未归档，null表示不过滤
  void setArchiveFilter(bool? archived) {
    if (_archivedFilter != archived) {
      _archivedFilter = archived;
      _currentPage = 1;
      _notes = [];
      _hasMore = true;
      fetchNotes(refresh: true);
      notifyListeners();
    }
  }

  /// 设置排序方式
  void setSortOrder(NoteSortOrder order) {
    if (_sortOrder != order) {
      _sortOrder = order;
      _currentPage = 1;
      _notes = [];
      _hasMore = true;
      fetchNotes(refresh: true);
      notifyListeners();
    }
  }

  /// 设置搜索关键词
  void setSearchQuery(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      _currentPage = 1;
      _notes = [];
      _hasMore = true;

      if (query.isNotEmpty) {
        searchNotes();
      } else {
        fetchNotes(refresh: true);
      }

      notifyListeners();
    }
  }

  /// 选择笔记
  void selectNote(Note note) {
    _selectedNote = note;
    notifyListeners();
  }

  /// 清除选中的笔记
  void clearSelectedNote() {
    _selectedNote = null;
    notifyListeners();
  }

  /// 重置错误信息
  void resetError() {
    _error = '';
    notifyListeners();
  }

  /// 初始化加载数据
  Future<void> initialize() async {
    await Future.wait([
      fetchNotes(refresh: true),
      fetchNoteStats(),
    ]);
  }

  /// 获取笔记列表
  Future<void> fetchNotes({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _notes = [];
      _hasMore = true;
    }

    if (!_hasMore) return;

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      bool? favorite;

      // 根据筛选类型设置查询参数
      switch (_filterType) {
        case NoteFilter.favorite:
          favorite = true;
          break;
        default:
          // 不设置favorite参数
          break;
      }

      final result = await _apiService.getNotes(
        page: _currentPage,
        limit: 20,
        favorite: favorite,
        archived: _archivedFilter,
      );

      if (result['success'] == true) {
        final data = result['data'];
        final List<dynamic> notesData = data['notes'];

        final newNotes =
            notesData.map((noteData) => Note.fromJson(noteData)).toList();

        // 如果需要按更新时间排序
        if (_sortOrder == NoteSortOrder.updatedAt) {
          newNotes.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
        } else if (_sortOrder == NoteSortOrder.createdAt) {
          newNotes.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        } else if (_sortOrder == NoteSortOrder.title) {
          newNotes.sort((a, b) => a.title.compareTo(b.title));
        }

        if (refresh) {
          _notes = newNotes;
        } else {
          _notes.addAll(newNotes);
        }

        _currentPage++;
        _totalPages = data['totalPages'] ?? 1;
        _hasMore = _currentPage <= _totalPages;

        _error = '';
      } else {
        _error = result['error']['message'] ?? '获取笔记失败';
      }
    } catch (e) {
      _error = '获取笔记列表失败: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 搜索笔记
  Future<void> searchNotes() async {
    if (_searchQuery.isEmpty) {
      await fetchNotes(refresh: true);
      return;
    }

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.searchNotes(
        query: _searchQuery,
        page: _currentPage,
        limit: 20,
      );

      if (result['success'] == true) {
        final data = result['data'];
        final List<dynamic> notesData = data['notes'];

        final newNotes =
            notesData.map((noteData) => Note.fromJson(noteData)).toList();

        if (_currentPage == 1) {
          _notes = newNotes;
        } else {
          _notes.addAll(newNotes);
        }

        _currentPage++;
        _totalPages = data['totalPages'] ?? 1;
        _hasMore = _currentPage <= _totalPages;

        _error = '';
      } else {
        _error = result['error']['message'] ?? '搜索笔记失败';
      }
    } catch (e) {
      _error = '搜索笔记失败: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 获取笔记详情
  Future<Note?> fetchNoteById(String id) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.getNoteById(id);

      if (result['success'] == true) {
        final noteData = result['data'];
        final note = Note.fromJson(noteData);

        // 更新列表中对应的笔记
        final index = _notes.indexWhere((n) => n.id == id);
        if (index != -1) {
          _notes[index] = note;
        }

        // 如果当前选中的是这个笔记，也更新选中的笔记
        if (_selectedNote?.id == id) {
          _selectedNote = note;
        }

        _error = '';
        notifyListeners();
        return note;
      } else {
        _error = result['error']['message'] ?? '获取笔记详情失败';
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = '获取笔记详情失败: $e';
      notifyListeners();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 创建笔记
  Future<Note?> createNote({
    required String title,
    required String content,
    String contentType = 'rich-text',
    List<String> tags = const [],
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.createNote(
        title: title,
        content: content,
        contentType: contentType,
        tags: tags,
      );

      if (result['success'] == true) {
        final noteData = result['data'];
        final newNote = Note.fromJson(noteData);

        // 将新笔记添加到列表开头
        _notes.insert(0, newNote);

        // 更新笔记统计
        await fetchNoteStats();

        _error = '';
        notifyListeners();
        return newNote;
      } else {
        _error = result['error']['message'] ?? '创建笔记失败';
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = '创建笔记失败: $e';
      notifyListeners();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 更新笔记
  Future<Note?> updateNote({
    required String id,
    String? title,
    String? content,
    String? contentType,
    List<String>? tags,
    bool isCompletingInitialSave = false,
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.updateNote(
        id: id,
        title: title,
        content: content,
        contentType: contentType,
        tags: tags,
        isCompletingInitialSave: isCompletingInitialSave,
      );

      if (result['success'] == true) {
        final noteData = result['data'];
        final updatedNote = Note.fromJson(noteData);

        // 更新列表中对应的笔记
        final index = _notes.indexWhere((n) => n.id == id);
        if (index != -1) {
          _notes[index] = updatedNote;
        }

        // 如果当前选中的是这个笔记，也更新选中的笔记
        if (_selectedNote?.id == id) {
          _selectedNote = updatedNote;
        }

        _error = '';
        notifyListeners();
        return updatedNote;
      } else {
        _error = result['error']['message'] ?? '更新笔记失败';
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = '更新笔记失败: $e';
      notifyListeners();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 删除笔记
  Future<bool> deleteNote(String id) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.deleteNote(id);

      if (result['success'] == true) {
        // 从列表中移除该笔记
        _notes.removeWhere((note) => note.id == id);

        // 如果当前选中的是这个笔记，清除选中
        if (_selectedNote?.id == id) {
          _selectedNote = null;
        }

        // 更新笔记统计
        await fetchNoteStats();

        _error = '';
        notifyListeners();
        return true;
      } else {
        _error = result['error']['message'] ?? '删除笔记失败';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '删除笔记失败: $e';
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 切换笔记收藏状态
  Future<bool> toggleFavorite(String id) async {
    if (id.isEmpty) {
      _error = '无效的笔记ID';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // 先获取当前笔记的状态，以便稍后更新UI
      final currentNoteIndex = _notes.indexWhere((n) => n.id == id);
      final bool wasFavorite =
          currentNoteIndex >= 0 ? _notes[currentNoteIndex].isFavorite : false;

      final result = await _apiService.toggleFavorite(id);

      if (result['success'] == true) {
        final noteData = result['data'];
        final updatedNote = Note.fromJson(noteData);

        // 更新当前列表中的笔记
        if (_filterType == NoteFilter.favorite && !updatedNote.isFavorite) {
          // 如果当前是收藏视图且取消了收藏，从列表中移除
          _notes.removeWhere((note) => note.id == id);

          // 同时从收藏缓存中移除
          _cachedNotes[NoteFilter.favorite]!
              .removeWhere((note) => note.id == id);

          // 标记所有其他视图需要刷新
          _refreshFlags[NoteFilter.all] = true;
          _refreshFlags[NoteFilter.tagged] = true;
        } else {
          // 更新当前视图中的笔记
          final index = _notes.indexWhere((n) => n.id == id);
          if (index != -1) {
            _notes[index] = updatedNote;
          }

          // 更新所有视图的缓存状态
          for (final filterType in NoteFilter.values) {
            final cacheIndex =
                _cachedNotes[filterType]!.indexWhere((n) => n.id == id);
            if (cacheIndex != -1) {
              if (filterType == NoteFilter.favorite &&
                  !updatedNote.isFavorite) {
                // 如果是收藏视图且取消了收藏，移除笔记
                _cachedNotes[filterType]!.removeAt(cacheIndex);
              } else {
                // 否则更新笔记
                _cachedNotes[filterType]![cacheIndex] = updatedNote;
              }
            } else if (filterType == NoteFilter.favorite &&
                updatedNote.isFavorite) {
              // 如果是收藏视图且添加了收藏，添加到缓存
              _cachedNotes[filterType]!.insert(0, updatedNote);
            } else {
              // 如果笔记不在缓存中，标记该视图需要刷新
              _refreshFlags[filterType] = true;
            }
          }
        }

        // 如果当前选中的是这个笔记，也更新选中的笔记
        if (_selectedNote?.id == id) {
          _selectedNote = updatedNote;
        }

        // 更新笔记统计
        await fetchNoteStats();

        _error = '';
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = result['error']['message'] ?? '切换收藏状态失败';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '切换收藏状态失败: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// 切换笔记归档状态
  Future<bool> toggleArchive(String id) async {
    if (id.isEmpty) {
      _error = '无效的笔记ID';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // 先获取当前笔记的状态，以便稍后更新UI
      final currentNoteIndex = _notes.indexWhere((n) => n.id == id);
      final bool wasArchived =
          currentNoteIndex >= 0 ? _notes[currentNoteIndex].isArchived : false;

      final result = await _apiService.toggleArchive(id);

      if (result['success'] == true) {
        final noteData = result['data'];
        final updatedNote = Note.fromJson(noteData);

        // 如果是筛选状态，可能需要从当前列表中移除
        if ((_filterType == NoteFilter.all && updatedNote.isArchived) ||
            (_archivedFilter == false && updatedNote.isArchived) ||
            (_archivedFilter == true && !updatedNote.isArchived)) {
          _notes.removeWhere((note) => note.id == id);
        } else {
          // 否则，更新列表中对应的笔记
          final index = _notes.indexWhere((n) => n.id == id);
          if (index != -1) {
            _notes[index] = updatedNote;
          } else if (wasArchived != updatedNote.isArchived) {
            // 如果笔记状态已经变化但在列表中找不到，可能是因为UI没有正确更新
            // 在这里强制刷新一次笔记列表
            await fetchNotes(refresh: true);
          }
        }

        // 如果当前选中的是这个笔记，也更新选中的笔记
        if (_selectedNote?.id == id) {
          _selectedNote = updatedNote;
        }

        // 更新笔记统计
        await fetchNoteStats();

        _error = '';
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = result['error']['message'] ?? '切换归档状态失败';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '切换归档状态失败: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// 批量操作笔记
  Future<bool> batchOperation({
    required List<String> noteIds,
    required String operation,
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.batchOperation(
        noteIds: noteIds,
        operation: operation,
      );

      if (result['success'] == true) {
        // 根据操作类型更新本地数据
        switch (operation) {
          case 'delete':
            // 从列表中移除这些笔记
            _notes.removeWhere((note) => noteIds.contains(note.id));
            // 如果当前选中的笔记在这些笔记中，清除选中
            if (_selectedNote != null && noteIds.contains(_selectedNote!.id)) {
              _selectedNote = null;
            }
            break;

          case 'archive':
          case 'unarchive':
            // 如果是在过滤后的视图中，可能需要从当前列表中移除
            if (_filterType == NoteFilter.all && operation == 'archive') {
              _notes.removeWhere((note) => noteIds.contains(note.id));
            } else if (_filterType == NoteFilter.tagged &&
                operation == 'unarchive') {
              _notes.removeWhere((note) => noteIds.contains(note.id));
            } else {
              // 否则需要刷新列表以获取更新后的状态
              await fetchNotes(refresh: true);
            }
            break;

          case 'favorite':
          case 'unfavorite':
            // 如果是在收藏视图中，且操作是取消收藏，则从列表中移除
            if (_filterType == NoteFilter.favorite &&
                operation == 'unfavorite') {
              _notes.removeWhere((note) => noteIds.contains(note.id));
            } else {
              // 否则需要刷新列表以获取更新后的状态
              await fetchNotes(refresh: true);
            }
            break;
        }

        // 更新笔记统计
        await fetchNoteStats();

        _error = '';
        notifyListeners();
        return true;
      } else {
        _error = result['error']['message'] ?? '批量操作失败';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '批量操作失败: $e';
      notifyListeners();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// 获取笔记统计信息
  Future<void> fetchNoteStats() async {
    try {
      final result = await _apiService.getNoteStats();

      if (result['success'] == true) {
        _noteStats = result['data'];
        notifyListeners();
      }
    } catch (e) {
      // 统计信息获取失败不阻塞主要功能
      print('获取笔记统计信息失败: $e');
    }
  }

  /// 获取标签相关的笔记
  ///
  /// [tagId] 标签ID
  /// [page] 页码，从1开始
  /// [limit] 每页数量
  /// [sortBy] 排序字段，默认按更新时间
  /// [order] 排序方向，默认降序
  ///
  /// 返回包含笔记列表和总数的Map，失败返回null
  Future<Map<String, dynamic>?> getNotesByTag({
    required String tagId,
    int page = 1,
    int limit = 20,
    String sortBy = 'updatedAt',
    String order = 'desc',
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final response = await _apiService.getNotesByTag(
        id: tagId,
        page: page,
        limit: limit,
        sortBy: sortBy,
        order: order,
      );

      _isLoading = false;

      if (response['success'] == true && response['data']?['notes'] != null) {
        final List<dynamic> notesData = response['data']['notes'];
        final List<Note> notes = notesData.map((json) {
          // 确保笔记有标签信息
          // 如果后端返回的笔记中没有标签信息，手动添加当前标签ID
          if (json['tagIds'] == null ||
              (json['tagIds'] is List && !json['tagIds'].contains(tagId))) {
            if (json['tagIds'] == null) {
              json['tagIds'] = [tagId];
            } else if (json['tagIds'] is List) {
              json['tagIds'] = [...json['tagIds'], tagId];
            }
          }
          return Note.fromJson(json);
        }).toList();

        final int totalNotes = response['data']['totalNotes'] ?? notes.length;

        notifyListeners();
        return {
          'notes': notes,
          'total': totalNotes,
        };
      } else {
        _error = response['error']?['message'] ?? '获取标签笔记失败';
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = '获取标签笔记失败: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  /// 创建笔记分享链接
  Future<String?> createShareLink({
    required String id,
    int? expireHours,
    bool? isPublic,
    String? accessType,
    String? password,
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.createShareLink(
        id: id,
        expireHours: expireHours,
        isPublic: isPublic,
        accessType: accessType,
        password: password,
      );

      if (result['success'] == true && result['data'] != null) {
        // API返回的字段是'shareLink'而不是'shareUrl'
        final shareUrl = result['data']['shareLink'];
        _error = '';
        _isLoading = false;
        notifyListeners();
        return shareUrl;
      } else {
        _error = result['error']['message'] ?? '创建分享链接失败';
        _isLoading = false;
        notifyListeners();
        return null;
      }
    } catch (e) {
      _error = '创建分享链接失败: $e';
      _isLoading = false;
      notifyListeners();
      return null;
    }
  }

  /// 取消笔记分享
  Future<bool> cancelShareLink({required String id}) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.cancelShare(id);

      if (result['success'] == true) {
        // 更新本地笔记的分享状态
        final index = _notes.indexWhere((note) => note.id == id);
        if (index != -1) {
          _notes[index].shareToken = null;
          _notes[index].isPublicShared = false;
        }

        _error = '';
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = result['error']['message'] ?? '取消分享链接失败';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '取消分享链接失败: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// 上传笔记图片
  ///
  /// [file] 图片文件，可以是File(移动平台)或Uint8List(Web平台)，统一处理
  /// [noteId] 可选的笔记ID，用于将图片存储在特定笔记的子目录中
  /// [shareToken] 可选的分享令牌（用于分享页面上传图片）
  /// 返回上传结果，包含图片URL等信息
  Future<Map<String, dynamic>> uploadNoteImage(dynamic file,
      {String? noteId, String? shareToken}) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // 调用API服务上传图片
      final result = await _apiService.uploadNoteImage(file,
          noteId: noteId, shareToken: shareToken);

      _isLoading = false;

      if (result['success'] == true) {
        _error = '';
        notifyListeners();
        return result;
      } else {
        _error = result['error']?['message'] ?? '上传图片失败';
        notifyListeners();
        return {
          'success': false,
          'error': {'message': _error}
        };
      }
    } catch (e) {
      _error = '上传图片失败: $e';
      _isLoading = false;
      notifyListeners();
      return {
        'success': false,
        'error': {'message': _error}
      };
    }
  }

  /// 取消笔记分享
  Future<bool> cancelShare(String id) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.cancelShare(id);

      if (result['success'] == true) {
        _error = '';
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = result['error']['message'] ?? '取消分享失败';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '取消分享失败: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // 密码需求标志
  bool _isPasswordRequired = false;
  bool get isPasswordRequired => _isPasswordRequired;

  /// 获取分享的笔记
  ///
  /// [token] 分享令牌
  /// [password] 访问密码，可选
  Future<Note?> getSharedNote(String token, {String? password}) async {
    // 先设置状态，但不立即通知监听器
    _isLoading = true;
    _error = '';
    _isPasswordRequired = false;
    // 使用Future.microtask延迟通知，避免在构建过程中调用notifyListeners
    Future.microtask(() => notifyListeners());

    try {
      final result = await _apiService.getSharedNote(token, password: password);

      if (result['success'] == true && result['data'] != null) {
        // 处理不同的API响应结构
        Map<String, dynamic>? noteData;

        // 尝试不同的数据结构路径
        if (result['data']['note'] != null) {
          // 结构一：data.note
          noteData = result['data']['note'];
        } else if (result['data'] is Map && result['data'].containsKey('id')) {
          // 结构二：data本身就是笔记数据
          noteData = result['data'];
        }

        if (noteData != null) {
          final note = Note.fromJson(noteData);

          // 更新状态
          _error = '';
          _isLoading = false;
          _isPasswordRequired = false;
          // 延迟通知
          Future.microtask(() => notifyListeners());
          return note;
        } else {
          _error = '获取分享笔记失败：笔记数据为空或格式不正确';
          _isLoading = false;
          _isPasswordRequired = false;
          // 延迟通知
          Future.microtask(() => notifyListeners());
          return null;
        }
      } else {
        // 检查是否需要密码
        if (result['error'] != null &&
            result['error']['requirePassword'] == true) {
          _error = result['error']['message'] ?? '访问此笔记需要密码';
          _isPasswordRequired = true;
        } else {
          _error = result['error']?['message'] ?? '获取分享笔记失败';
          _isPasswordRequired = false;
        }

        _isLoading = false;
        // 延迟通知
        Future.microtask(() => notifyListeners());
        return null;
      }
    } catch (e) {
      _error = '获取分享笔记失败: $e';
      _isLoading = false;
      _isPasswordRequired = false;
      // 延迟通知
      Future.microtask(() => notifyListeners());
      return null;
    }
  }

  /// 更新分享的笔记
  ///
  /// [token] 分享令牌
  /// [title] 标题（可选）
  /// [content] 内容（可选）
  /// [password] 访问密码（如果笔记受密码保护）
  Future<bool> updateSharedNote({
    required String token,
    String? title,
    String? content,
    String? password,
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.updateSharedNote(
        token: token,
        title: title,
        content: content,
        password: password,
      );

      if (result['success'] == true) {
        _error = '';
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        // 检查是否需要密码
        if (result['error'] != null &&
            result['error']['requirePassword'] == true) {
          _error = result['error']['message'] ?? '更新此笔记需要密码';
          _isPasswordRequired = true;
        } else {
          _error = result['error']?['message'] ?? '更新分享笔记失败';
          _isPasswordRequired = false;
        }

        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = '更新分享笔记失败: $e';
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  /// 获取上次同步后的变更
  Future<Map<String, dynamic>> getChanges({
    DateTime? lastSyncTime,
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.getChanges(
        lastSyncTime: lastSyncTime,
      );

      if (result['success'] == true) {
        final data = result['data'];
        final List<dynamic> notesData = data['notes'];
        final syncTime = DateTime.parse(data['syncTime']);

        // 将服务器变更的笔记转换为Note对象
        final List<Note> changedNotes =
            notesData.map((noteData) => Note.fromJson(noteData)).toList();

        // 标记所有视图需要刷新
        markAllNotesForRefresh();

        _error = '';
        _isLoading = false;
        notifyListeners();

        return {
          'success': true,
          'notes': changedNotes,
          'syncTime': syncTime,
        };
      } else {
        _error = result['error']['message'] ?? '获取变更失败';
        _isLoading = false;
        notifyListeners();

        return {
          'success': false,
          'error': _error,
        };
      }
    } catch (e) {
      _error = '获取变更失败: $e';
      _isLoading = false;
      notifyListeners();

      return {
        'success': false,
        'error': _error,
      };
    }
  }

  /// 上传本地变更
  Future<Map<String, dynamic>> pushChanges({
    required List<Note> notes,
    List<String> deletedNoteIds = const [],
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // 将Note对象转换为JSON
      final List<Map<String, dynamic>> notesJson =
          notes.map((note) => note.toJson()).toList();

      final result = await _apiService.pushChanges(
        notes: notesJson,
        deletedNoteIds: deletedNoteIds,
      );

      if (result['success'] == true) {
        final data = result['data'];
        final List<dynamic> successIds = data['results']['success'];
        final List<dynamic> conflicts = data['results']['conflicts'];
        final int deleted = data['results']['deleted'];
        final DateTime syncTime = DateTime.parse(data['syncTime']);

        // 标记所有视图需要刷新
        markAllNotesForRefresh();

        // 更新笔记统计
        await fetchNoteStats();

        _error = '';
        _isLoading = false;
        notifyListeners();

        return {
          'success': true,
          'successIds': successIds,
          'conflicts': conflicts,
          'deleted': deleted,
          'syncTime': syncTime,
        };
      } else {
        _error = result['error']['message'] ?? '上传变更失败';
        _isLoading = false;
        notifyListeners();

        return {
          'success': false,
          'error': _error,
        };
      }
    } catch (e) {
      _error = '上传变更失败: $e';
      _isLoading = false;
      notifyListeners();

      return {
        'success': false,
        'error': _error,
      };
    }
  }

  /// 导入笔记
  Future<Map<String, dynamic>> importNotes({
    required List<Map<String, dynamic>> notes,
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.importNotes(
        notes: notes,
      );

      if (result['success'] == true) {
        final data = result['data'];
        final int imported = data['results']['imported'];
        final int failed = data['results']['failed'];
        final List<String>? importedIds = data['results']['importedIds'] != null
            ? List<String>.from(data['results']['importedIds'])
            : null;

        // 标记所有视图需要刷新
        markAllNotesForRefresh();

        // 更新笔记统计
        await fetchNoteStats();

        // 如果有导入的笔记ID，获取这些笔记的详细信息
        List<Note> importedNotes = [];
        if (importedIds != null && importedIds.isNotEmpty) {
          // 获取所有笔记
          await fetchNotes(refresh: true);

          // 从所有笔记中筛选出导入的笔记
          importedNotes =
              _notes.where((note) => importedIds.contains(note.id)).toList();
        }

        _error = '';
        _isLoading = false;
        notifyListeners();

        return {
          'success': true,
          'imported': imported,
          'failed': failed,
          'importedNotes': importedNotes,
        };
      } else {
        _error = result['error']['message'] ?? '导入笔记失败';
        _isLoading = false;
        notifyListeners();

        return {
          'success': false,
          'error': _error,
        };
      }
    } catch (e) {
      _error = '导入笔记失败: $e';
      _isLoading = false;
      notifyListeners();

      return {
        'success': false,
        'error': _error,
      };
    }
  }

  /// 导出笔记
  Future<Map<String, dynamic>> exportNotes({
    String filter = 'all',
  }) async {
    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      final result = await _apiService.exportNotes(
        filter: filter,
      );

      if (result['success'] == true) {
        final data = result['data'];
        final List<dynamic> notesData = data['notes'];
        final int totalNotes = data['totalNotes'];
        final DateTime exportTime = DateTime.parse(data['exportTime']);

        // 将导出的笔记转换为Note对象
        final List<Note> exportedNotes =
            notesData.map((noteData) => Note.fromJson(noteData)).toList();

        _error = '';
        _isLoading = false;
        notifyListeners();

        return {
          'success': true,
          'notes': exportedNotes,
          'totalNotes': totalNotes,
          'exportTime': exportTime,
        };
      } else {
        _error = result['error']['message'] ?? '导出笔记失败';
        _isLoading = false;
        notifyListeners();

        return {
          'success': false,
          'error': _error,
        };
      }
    } catch (e) {
      _error = '导出笔记失败: $e';
      _isLoading = false;
      notifyListeners();

      return {
        'success': false,
        'error': _error,
      };
    }
  }

  /// 异步获取指定笔记的历史版本列表
  ///
  /// @param noteId 要获取历史记录的笔记ID
  Future<void> fetchNoteHistory(String noteId) async {
    // 防止重复加载
    if (_isLoadingHistory) return;

    // 设置加载状态并通知UI
    _isLoadingHistory = true;
    _historyError = '';
    _noteHistory = []; // 清空旧历史记录
    notifyListeners();

    try {
      // 调用 ApiService 获取历史数据
      final historyDataList = await _apiService.getNoteHistory(noteId);
      // 将获取到的 Map 列表转换为 Note 对象列表
      _noteHistory = historyDataList
          .map((itemJson) {
            try {
              // 使用 Note.fromJson 解析，需要保证模型兼容性或进行适配
              return Note.fromJson(itemJson);
            } catch (e) {
              // 如果解析失败，返回null以便过滤
              return null;
            }
          })
          .where((note) => note != null) // 过滤掉解析失败的项
          .cast<Note>() // 明确类型
          .toList();
      _historyError = ''; // 清除错误信息
    } catch (e) {
      // 捕获所有异常
      _historyError = '加载历史记录失败: $e';
      _noteHistory = []; // 清空列表
    } finally {
      // 无论成功或失败，清除加载状态并通知UI
      _isLoadingHistory = false;
      notifyListeners();
    }
  }

  /// 异步请求恢复笔记到指定版本
  ///
  /// @param noteId 要恢复的笔记ID
  /// @param version 要恢复到的历史版本号
  /// @return bool 操作是否成功
  Future<bool> restoreVersion(String noteId, int version) async {
    // 防止重复操作
    if (_isRestoringVersion) return false;

    // 设置恢复状态并通知UI
    _isRestoringVersion = true;
    _error = ''; // 清除可能存在的旧错误
    notifyListeners();

    try {
      // 调用 ApiService 执行恢复操作
      final updatedNoteData =
          await _apiService.restoreNoteVersion(noteId, version);
      // --- 操作成功后的处理 ---

      // 1. 标记所有笔记视图需要刷新，让页面下次显示时重新获取
      markAllNotesForRefresh();

      // 2. 如果当前 selectedNote 就是被恢复的笔记，也更新它
      if (_selectedNote?.id == noteId) {
        _selectedNote = Note.fromJson(updatedNoteData);
      }

      // 3. 清空当前加载的历史记录，因为它们可能不再准确反映最新状态
      _noteHistory = [];

      _error = ''; // 清除错误
      notifyListeners(); // 通知UI更新
      return true; // 返回成功
    } catch (e) {
      // 捕获异常
      _error = '恢复版本失败: $e';
      notifyListeners(); // 通知UI显示错误
      return false; // 返回失败
    } finally {
      // 清除恢复状态
      _isRestoringVersion = false;
      // 再次通知，确保UI状态正确
      notifyListeners();
    }
  }
}
