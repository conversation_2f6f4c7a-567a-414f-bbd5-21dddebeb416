import express from 'express';
import { body } from 'express-validator';
import * as authController from '../controllers/auth.controller';
import { authenticate } from '../middlewares/auth.middleware';

const router = express.Router();

/**
 * 发送邮箱验证码路由
 * POST /api/auth/send-verification-code
 */
router.post(
  '/send-verification-code',
  [
    // 验证邮箱
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('请提供有效的邮箱地址'),
  ],
  authController.sendVerificationCode
);

/**
 * 注册路由
 * POST /api/auth/register
 */
router.post(
  '/register',
  [
    // 验证用户名
    body('username')
      .isString()
      .trim()
      .isLength({ min: 3, max: 20 })
      .withMessage('用户名长度必须在3-20个字符之间'),

    // 验证邮箱
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('请提供有效的邮箱地址'),

    // 验证密码
    body('password')
      .isString()
      .isLength({ min: 6 })
      .withMessage('密码长度至少为6个字符'),

    // 验证码
    body('verificationCode')
      .isString()
      .isLength({ min: 6, max: 6 })
      .withMessage('验证码必须是6位数字'),
  ],
  authController.register
);

/**
 * 登录路由
 * POST /api/auth/login
 */
router.post(
  '/login',
  [
    // 验证用户名或邮箱
    body('usernameOrEmail')
      .isString()
      .trim()
      .not()
      .isEmpty()
      .withMessage('请提供用户名或邮箱'),

    // 验证密码
    body('password')
      .isString()
      .not()
      .isEmpty()
      .withMessage('请提供密码'),
  ],
  authController.login
);

/**
 * 忘记密码路由
 * POST /api/auth/forgot-password
 */
router.post(
  '/forgot-password',
  [
    // 验证邮箱
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('请提供有效的邮箱地址'),
  ],
  authController.forgotPassword
);

/**
 * 重置密码路由
 * POST /api/auth/reset-password
 */
router.post(
  '/reset-password',
  [
    // 验证邮箱
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('请提供有效的邮箱地址'),

    // 验证令牌
    body('token')
      .isString()
      .not()
      .isEmpty()
      .withMessage('令牌不能为空'),

    // 验证新密码
    body('newPassword')
      .isString()
      .isLength({ min: 6 })
      .withMessage('新密码长度至少为6个字符'),
  ],
  authController.resetPassword
);

/**
 * 修改密码路由
 * POST /api/auth/change-password
 * 需要认证
 */
router.post(
  '/change-password',
  [
    authenticate, // 认证中间件

    // 验证当前密码
    body('currentPassword')
      .isString()
      .not()
      .isEmpty()
      .withMessage('当前密码不能为空'),

    // 验证新密码
    body('newPassword')
      .isString()
      .isLength({ min: 6 })
      .withMessage('新密码长度至少为6个字符'),
  ],
  authController.changePassword
);

/**
 * 获取当前用户信息路由
 * GET /api/auth/me
 * 需要认证
 */
router.get('/me', authenticate, authController.getCurrentUser);

/**
 * 登出路由
 * POST /api/auth/logout
 * 需要认证
 */
router.post('/logout', authenticate, authController.logout);

export default router;