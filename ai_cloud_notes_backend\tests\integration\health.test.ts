import request from 'supertest';
// 假设你的 Express 应用在 src/app.ts 中导出
import app from '../../src/app';

// 模拟 auth 模块，避免其内部的类型错误影响测试
jest.mock('../../src/utils/auth', () => ({
  // 提供 auth 模块中被使用的函数的模拟实现
  // 例如，如果 auth 模块导出了一个 verifyToken 函数，你可以在这里模拟它
  // verifyToken: jest.fn((req, res, next) => next()),
  // 如果 auth 模块导出了其他函数，也需要在这里模拟
  // 为了让测试能够运行，我们暂时提供一个简单的模拟，可能需要根据实际使用情况调整
  __esModule: true, // 这是一个 ES Module
  // 假设 auth 模块导出了一个用于生成 token 的函数，这里简单模拟返回一个字符串
  generateToken: jest.fn(() => 'mocked_token'),
  // 假设 auth 模块导出了一个用于验证 token 的中间件，这里简单模拟调用 next()
  verifyToken: jest.fn((req, res, next) => next()),
}));


describe('Health Check', () => {
  it('should return 200 OK for the health endpoint', async () => {
    // 请求 /api/health 端点
    const res = await request(app).get('/api/health');
    expect(res.status).toBe(200);
  });
});