import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/utils/date_time_helper.dart'; // 导入时间工具类
import 'package:ai_cloud_notes/widgets/main_layout.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({Key? key}) : super(key: key);

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage> with AutomaticKeepAliveClientMixin<FavoritesPage> {
  @override
  bool get wantKeepAlive => true; // 保持页面状态

  @override
  void initState() {
    super.initState();
    // 在页面创建后加载收藏数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadFavorites();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当依赖变化时（可能是页面变得可见）重新加载数据
    _loadFavorites();
  }

  // 加载收藏笔记
  Future<void> _loadFavorites() async {
    final noteProvider = Provider.of<NoteProvider>(context, listen: false);
    // 设置筛选类型为收藏
    noteProvider.setFilter(NoteFilter.favorite);
    // 强制刷新数据
    await noteProvider.fetchNotes(refresh: true);
    return Future.value();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用super.build

    // 检查当前路由，判断是否是作为独立页面打开的
    final currentRoute = ModalRoute.of(context)?.settings.name;
    final isStandalonePage = currentRoute == AppRoutes.favorites;

    return Consumer<NoteProvider>(
      builder: (context, noteProvider, child) {
        // 检查是否正在加载
        if (noteProvider.isLoading && noteProvider.notes.isEmpty) {
          if (isStandalonePage) {
            return MainLayout(
              currentIndex: 1, // 收藏页索引为1
              body: _buildLoadingState(),
              showFab: true,
            );
          } else {
            return _buildLoadingState();
          }
        }

        final content = RefreshIndicator(
          onRefresh: _loadFavorites,
          child: SafeArea(
            child: Column(
              children: [
                _buildHeader(noteProvider),
                _buildFavoritesList(noteProvider),
              ],
            ),
          ),
        );

        // 如果是作为独立页面打开的，则需要包装在MainLayout中
        return isStandalonePage
            ? MainLayout(
                currentIndex: 1, // 收藏页索引为1
                body: content,
                showFab: true,
              )
            : content;
      },
    );
  }

  Widget _buildLoadingState() {
    return const SafeArea(
      child: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildHeader(NoteProvider noteProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.05),
            blurRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '我的收藏',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            '共 ${noteProvider.notes.length} 条收藏笔记',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).hintColor,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildFavoritesList(NoteProvider noteProvider) {
    return Expanded(
      child: noteProvider.notes.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 100),
              itemCount: noteProvider.notes.length + (noteProvider.hasMore ? 1 : 0),
              itemBuilder: (context, index) {
                // 如果到达列表末尾且还有更多数据，加载更多
                if (index == noteProvider.notes.length) {
                  noteProvider.fetchNotes();
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final note = noteProvider.notes[index];
                return _buildFavoriteItem(note);
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.star_border,
            size: 64,
            color: Theme.of(context).iconTheme.color?.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            '暂无收藏笔记',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).hintColor,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击笔记右上角的星标图标收藏喜欢的笔记',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).hintColor.withOpacity(0.7),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteItem(Note note) {
    return GestureDetector(
      onTap: () {
        // 使用命名路由导航到笔记编辑页面
        Navigator.pushNamed(
          context,
          AppRoutes.editor,
          arguments: note,
        ).then((_) {
          // 返回时刷新收藏列表
          _loadFavorites();
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Theme.of(context).dividerColor),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withOpacity(0.03),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  note.title,
                  style: Theme.of(context).textTheme.titleMedium,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12),
                Text(
                  note.plainTextContent,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).hintColor,
                        height: 1.4,
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (note.tags.isNotEmpty)
                      Text(
                        note.tags.first,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 12,
                          color: Theme.of(context).iconTheme.color?.withOpacity(0.7),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${DateTimeHelper.formatDateTime(note.updatedAt)}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).hintColor,
                              ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: () async {
                  // 切换收藏状态
                  final provider = Provider.of<NoteProvider>(context, listen: false);
                  await provider.toggleFavorite(note.id);

                  // 保持在收藏视图，但将全部笔记的列表标记为需要刷新
                  if (provider.filterType == NoteFilter.favorite) {
                    // 标记全部笔记列表需要刷新，但不实际切换视图
                    provider.markAllNotesForRefresh();
                  }
                },
                child: Icon(
                  Icons.star,
                  color: Theme.of(context).colorScheme.secondary,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}