<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智云笔记 - 欢迎页面</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common.css">
    <style>
        /* 欢迎/引导页样式 */
        .welcome-container {
            height: 100%;
            width: 100%;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            padding: 0 40px;
        }
        
        .welcome-logo {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .welcome-logo i {
            font-size: 3rem;
        }
        
        .welcome-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .welcome-description {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .welcome-btn {
            background-color: white;
            color: var(--primary-color);
            border: none;
            padding: 16px 32px;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: 1.1rem;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .welcome-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        .welcome-version {
            position: absolute;
            bottom: 30px;
            opacity: 0.7;
            font-size: 0.9rem;
        }

        /* 引导页样式 */
        .onboarding-container {
            height: 100%;
            width: 100%;
            position: relative;
            overflow: hidden;
            background-color: var(--white);
        }
        
        .onboarding-slides {
            display: flex;
            height: 100%;
            transition: transform 0.3s ease;
        }
        
        .onboarding-slide {
            min-width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 0 40px;
            text-align: center;
        }
        
        .slide-image {
            width: 200px;
            height: 200px;
            margin-bottom: 40px;
            background: linear-gradient(135deg, rgba(93, 95, 239, 0.1), rgba(6, 182, 212, 0.1));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .slide-image i {
            font-size: 5rem;
            color: var(--primary-color);
        }
        
        .slide-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--black);
        }
        
        .slide-description {
            font-size: 1rem;
            color: var(--dark-gray);
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .onboarding-dots {
            display: flex;
            justify-content: center;
            position: absolute;
            bottom: 100px;
            left: 0;
            right: 0;
            gap: 8px;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: var(--medium-gray);
        }
        
        .dot.active {
            background-color: var(--primary-color);
            width: 20px;
            border-radius: 4px;
        }
        
        .onboarding-actions {
            position: absolute;
            bottom: 40px;
            left: 40px;
            right: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .onboarding-actions button {
            background: none;
            border: none;
            color: var(--primary-color);
            font-weight: 500;
            font-size: 1rem;
            cursor: pointer;
        }
        
        .onboarding-actions .next-btn {
            color: white;
            background-color: var(--primary-color);
            padding: 12px 24px;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }
        
        .onboarding-actions .next-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(93, 95, 239, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title text-center">智云笔记应用原型设计</h1>
        <p class="page-description text-center">
            欢迎与引导页面展示了应用的初次启动体验，包括品牌介绍和功能引导。
        </p>

        <!-- 欢迎页 -->
        <div>
            <h3 class="screen-title">欢迎页</h3>
            <p class="screen-description">应用首次启动时的欢迎页面，提供品牌介绍和功能引导</p>
            <div class="screen">
                <div class="welcome-container">
                    <div class="welcome-logo">
                        <i class="fas fa-cloud"></i> 智云笔记
                    </div>
                    <h1 class="welcome-title">智能笔记，随时随地</h1>
                    <p class="welcome-description">
                        基于AI技术的智能云笔记应用，让记录和创作更加高效、便捷，随时随地捕捉灵感。
                    </p>
                    <button class="welcome-btn">开始体验</button>
                    <div class="welcome-version">版本 v1.0.0</div>
                </div>
            </div>
        </div>

        <!-- 功能引导页 - 第一页 -->
        <div>
            <h3 class="screen-title">功能引导 - 第一页</h3>
            <p class="screen-description">介绍应用的核心功能和特点</p>
            <div class="screen">
                <div class="onboarding-container">
                    <div class="onboarding-slides">
                        <div class="onboarding-slide">
                            <div class="slide-image">
                                <i class="fas fa-edit"></i>
                            </div>
                            <h2 class="slide-title">轻松创建笔记</h2>
                            <p class="slide-description">
                                随时随地记录您的想法和灵感，支持富文本编辑，让您的笔记更加丰富多彩。
                            </p>
                        </div>
                    </div>
                    <div class="onboarding-dots">
                        <div class="dot active"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                    <div class="onboarding-actions">
                        <button class="skip-btn">跳过</button>
                        <button class="next-btn">下一步</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能引导页 - 第二页 -->
        <div>
            <h3 class="screen-title">功能引导 - 第二页</h3>
            <p class="screen-description">介绍AI智能辅助功能</p>
            <div class="screen">
                <div class="onboarding-container">
                    <div class="onboarding-slides">
                        <div class="onboarding-slide">
                            <div class="slide-image">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h2 class="slide-title">AI智能辅助</h2>
                            <p class="slide-description">
                                智能预测和内容建议，让您的写作更加高效。自动标签和分类，让笔记管理更加便捷。
                            </p>
                        </div>
                    </div>
                    <div class="onboarding-dots">
                        <div class="dot"></div>
                        <div class="dot active"></div>
                        <div class="dot"></div>
                    </div>
                    <div class="onboarding-actions">
                        <button class="skip-btn">跳过</button>
                        <button class="next-btn">下一步</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能引导页 - 第三页 -->
        <div>
            <h3 class="screen-title">功能引导 - 第三页</h3>
            <p class="screen-description">介绍云同步和多设备访问功能</p>
            <div class="screen">
                <div class="onboarding-container">
                    <div class="onboarding-slides">
                        <div class="onboarding-slide">
                            <div class="slide-image">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h2 class="slide-title">云同步，多设备访问</h2>
                            <p class="slide-description">
                                笔记自动同步到云端，支持多设备访问和编辑，让您的数据随时随地可用。
                            </p>
                        </div>
                    </div>
                    <div class="onboarding-dots">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot active"></div>
                    </div>
                    <div class="onboarding-actions">
                        <button class="skip-btn">跳过</button>
                        <button class="next-btn">立即开始</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>智云笔记应用原型设计 &copy; 2023</p>
            <p>
                <a href="welcome.html">欢迎页面</a> | 
                <a href="auth.html">认证页面</a> | 
                <a href="home.html">主页</a> | 
                <a href="editor.html">编辑器</a> | 
                <a href="settings.html">设置</a> | 
                <a href="tags.html">标签</a>
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 