import { Document } from 'mongoose';
import { Request } from 'express';

/**
 * 扩展Express请求对象，添加用户属性
 */
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username: string;
        email: string;
        iat?: number;
        exp?: number;
      };
    }
  }
}

/**
 * 已认证的请求接口
 */
export interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    username: string;
    email: string;
    iat?: number;
    exp?: number;
  };
} 