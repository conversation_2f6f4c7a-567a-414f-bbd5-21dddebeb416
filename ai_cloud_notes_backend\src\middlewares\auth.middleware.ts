import { Request, Response, NextFunction } from 'express';
import { extractTokenFromHeader, verifyToken } from '../utils/auth';
import User from '../models/user.model';
import { logger } from '../utils/logger';
import { isTokenBlacklisted } from '../utils/redis';

/**
 * 认证中间件，验证用户是否已登录
 */
export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // 从请求头获取Authorization头
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({ 
        success: false, 
        error: { 
          message: '未提供认证令牌' 
        } 
      });
    }

    // 从头中提取令牌
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        error: { 
          message: '无效的认证令牌格式' 
        } 
      });
    }
    
    // 检查令牌是否在黑名单中
    const isBlacklisted = await isTokenBlacklisted(token);
    if (isBlacklisted) {
      return res.status(401).json({ 
        success: false, 
        error: { 
          message: '令牌已失效，请重新登录' 
        } 
      });
    }

    // 验证令牌
    const { valid, expired, decoded } = verifyToken(token);

    if (expired) {
      return res.status(401).json({ 
        success: false, 
        error: { 
          message: '认证令牌已过期' 
        } 
      });
    }

    if (!valid || !decoded) {
      return res.status(401).json({ 
        success: false, 
        error: { 
          message: '无效的认证令牌' 
        } 
      });
    }

    // 在请求对象中添加用户信息
    // @ts-ignore - 添加自定义属性到Express.Request
    req.user = decoded;

    // 继续处理请求
    return next();
  } catch (error: any) {
    logger.error(`认证中间件错误: ${error.message}`, { stack: error.stack });
    return res.status(500).json({ 
      success: false, 
      error: { 
        message: '认证处理失败' 
      } 
    });
  }
}; 