import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:flutter/services.dart';
import 'package:ai_cloud_notes/models/note_model.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:qr_flutter/qr_flutter.dart';

class ShareNotePage extends StatefulWidget {
  final Note note;

  const ShareNotePage({
    Key? key,
    required this.note,
  }) : super(key: key);

  @override
  State<ShareNotePage> createState() => _ShareNotePageState();
}

class _ShareNotePageState extends State<ShareNotePage> {
  // 权限设置
  bool _isReadOnly = true;
  bool _isPasswordProtected = false;
  final TextEditingController _passwordController = TextEditingController();

  // 有效期设置
  String _validityDuration = '7天';
  final List<String> _validityOptions = ['1天', '3天', '7天', '30天', '永久'];

  // 分享链接
  String? _shareLink;

  // 是否已经分享
  bool _isAlreadyShared = false;

  // 是否正在取消分享
  bool _cancelingShare = false;

  // 加载状态
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _checkIfAlreadyShared();
  }

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  /// 检查笔记是否已经分享
  void _checkIfAlreadyShared() {
    // 检查笔记是否已经有分享链接
    if (widget.note.shareToken != null) {
      setState(() {
        _isAlreadyShared = true;
        _shareLink = '${Uri.base.origin}/share/${widget.note.shareToken}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildNotePreview(),
                      const SizedBox(height: 24),
                      _buildPermissionSettings(),
                      const SizedBox(height: 24),
                      _buildValiditySettings(),
                      const SizedBox(height: 24),
                      _buildSharingMethods(),
                      const SizedBox(height: 24),
                      _buildShareLink(),
                      const SizedBox(height: 24),
                      _buildShareButton(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          InkWell(
            onTap: () => Navigator.pop(context),
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.arrow_back,
                color: Theme.of(context).iconTheme.color,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Text(
            '分享笔记',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleLarge?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotePreview() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? Colors.grey.shade800.withOpacity(0.5)
            : AppTheme.lightGrayColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '笔记预览',
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).textTheme.bodySmall?.color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.note,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.note.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).textTheme.titleMedium?.color,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.note.plainTextContent.length > 100
              ? '${widget.note.plainTextContent.substring(0, 100)}...'
              : widget.note.plainTextContent,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: Text(
            '权限设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleMedium?.color,
            ),
          ),
        ),
        const SizedBox(height: 16),

        // 访问权限选择
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _isReadOnly = true;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _isReadOnly
                        ? AppTheme.primaryColor.withOpacity(0.1)
                        : Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _isReadOnly
                          ? AppTheme.primaryColor.withOpacity(0.4)
                          : Theme.of(context).dividerColor,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.remove_red_eye,
                        color: _isReadOnly ? AppTheme.primaryColor : AppTheme.darkGrayColor,
                        size: 20,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '只读',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: _isReadOnly ? AppTheme.primaryColor : Theme.of(context).textTheme.titleMedium?.color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '被分享者只能查看笔记内容',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _isReadOnly = false;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: !_isReadOnly
                        ? AppTheme.primaryColor.withOpacity(0.1)
                        : Theme.of(context).cardColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: !_isReadOnly
                          ? AppTheme.primaryColor.withOpacity(0.4)
                          : Theme.of(context).dividerColor,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.edit,
                        color: !_isReadOnly ? AppTheme.primaryColor : AppTheme.darkGrayColor,
                        size: 20,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '可编辑',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: !_isReadOnly ? AppTheme.primaryColor : Theme.of(context).textTheme.titleMedium?.color,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '被分享者可以编辑笔记内容',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).textTheme.bodySmall?.color,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 密码保护
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _isPasswordProtected
                  ? AppTheme.primaryColor.withOpacity(0.4)
                  : AppTheme.lightGrayColor,
            ),
          ),
          child: Column(
            children: [
              ListTile(
                title: Text(
                  '设置访问密码',
                  style: TextStyle(
                    color: Theme.of(context).textTheme.titleMedium?.color,
                  ),
                ),
                subtitle: Text(
                  '需要密码才能查看笔记内容',
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
                trailing: Switch(
                  value: _isPasswordProtected,
                  onChanged: (value) {
                    setState(() {
                      _isPasswordProtected = value;
                    });
                  },
                  activeColor: AppTheme.primaryColor,
                ),
              ),
              if (_isPasswordProtected)
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: TextField(
                    controller: _passwordController,
                    decoration: InputDecoration(
                      hintText: '输入4-6位密码',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: AppTheme.lightGrayColor,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 12,
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    maxLength: 6,
                    obscureText: true,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildValiditySettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: Text(
            '有效期设置',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleMedium?.color,
            ),
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).dividerColor,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _validityDuration,
              isExpanded: true,
              icon: const Icon(Icons.keyboard_arrow_down),
              iconSize: 24,
              elevation: 16,
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: 16,
              ),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  setState(() {
                    _validityDuration = newValue;
                  });
                }
              },
              items: _validityOptions.map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSharingMethods() {
    // 分享方式
    final List<Map<String, dynamic>> sharingMethods = [
      {
        'icon': Icons.wechat,
        'name': '微信',
        'color': const Color(0xFF1AAD19),
        'action': _shareToWeChat,
      },
      {
        'icon': Icons.message,
        'name': 'QQ',
        'color': const Color(0xFF12B7F5),
        'action': _shareToQQ,
      },
      {
        'icon': Icons.rss_feed,
        'name': '微博',
        'color': const Color(0xFFFF763B),
        'action': _shareToWeibo,
      },
      {
        'icon': Icons.link,
        'name': '复制链接',
        'color': const Color(0xFF718096),
        'action': _copyShareLink,
      },
      {
        'icon': Icons.qr_code,
        'name': '二维码',
        'color': const Color(0xFF4A5568),
        'action': _showQRCode,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: Text(
            '分享方式',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleMedium?.color,
            ),
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.1,
          ),
          itemCount: sharingMethods.length,
          itemBuilder: (context, index) {
            final method = sharingMethods[index];
            return GestureDetector(
              onTap: () {
                if (_shareLink == null) {
                  SnackbarHelper.showWarning(
                    context: context,
                    message: '请先点击"确认分享"生成分享链接',
                  );
                  return;
                }
                // 执行对应的分享方式操作
                method['action']?.call();
              },
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: method['color'].withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        method['icon'],
                        color: method['color'],
                        size: 20,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      method['name'],
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  // 分享到微信
  void _shareToWeChat() {
    if (_shareLink == null) return;

    // 构建分享内容
    final title = widget.note.title.isNotEmpty ? widget.note.title : '无标题笔记';
    final content = widget.note.plainTextContent.length > 50
        ? '${widget.note.plainTextContent.substring(0, 50)}...'
        : widget.note.plainTextContent;

    // 构建微信分享 URL
    final encodedTitle = Uri.encodeComponent(title);
    final encodedContent = Uri.encodeComponent('我分享了一篇笔记: $content');
    final encodedUrl = Uri.encodeComponent(_shareLink!);

    // 在移动端使用微信分享 SDK，在 Web 端打开微信网页版
    final url = 'https://wechat.com/'; // 实际上微信没有直接的网页分享接口

    // 由于浏览器不能直接调用微信，所以我们复制链接并提示用户
    Clipboard.setData(ClipboardData(text: _shareLink!));
    SnackbarHelper.showSuccess(
      context: context,
      message: '链接已复制，请手动粘贴到微信中分享',
    );
  }

  // 分享到QQ
  void _shareToQQ() {
    if (_shareLink == null) return;

    // 构建分享内容
    final title = widget.note.title.isNotEmpty ? widget.note.title : '无标题笔记';
    final content = widget.note.plainTextContent.length > 50
        ? '${widget.note.plainTextContent.substring(0, 50)}...'
        : widget.note.plainTextContent;

    // 构建 QQ 分享 URL
    final encodedTitle = Uri.encodeComponent(title);
    final encodedContent = Uri.encodeComponent('我分享了一篇笔记: $content');
    final encodedUrl = Uri.encodeComponent(_shareLink!);

    // QQ 分享接口
    final url = 'https://connect.qq.com/widget/shareqq/index.html?url=$encodedUrl&title=$encodedTitle&summary=$encodedContent';

    // 打开链接
    _launchUrl(url);
  }

  // 分享到微博
  void _shareToWeibo() {
    if (_shareLink == null) return;

    // 构建分享内容
    final title = widget.note.title.isNotEmpty ? widget.note.title : '无标题笔记';
    final content = widget.note.plainTextContent.length > 50
        ? '${widget.note.plainTextContent.substring(0, 50)}...'
        : widget.note.plainTextContent;

    // 构建微博分享 URL
    final encodedContent = Uri.encodeComponent('我分享了一篇笔记 "$title": $content ${ _shareLink!}');

    // 微博分享接口
    final url = 'https://service.weibo.com/share/share.php?text=$encodedContent';

    // 打开链接
    _launchUrl(url);
  }

  // 复制分享链接
  void _copyShareLink() {
    if (_shareLink == null) return;

    Clipboard.setData(ClipboardData(text: _shareLink!));
    SnackbarHelper.showSuccess(
      context: context,
      message: '链接已复制到剪贴板',
    );
  }

  // 显示二维码
  void _showQRCode() {
    if (_shareLink == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('扫描二维码分享'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 使用QR Flutter生成二维码
            Container(
              width: 200,
              height: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: QrImageView(
                data: _shareLink!,
                version: QrVersions.auto,
                size: 180,
                backgroundColor: Colors.white,
                errorStateBuilder: (context, error) {
                  return Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 40,
                        ),
                        const SizedBox(height: 8),
                        Text('生成二维码失败'),
                      ],
                    ),
                  );
                },
                embeddedImage: const AssetImage('assets/images/logo.png'),
                embeddedImageStyle: QrEmbeddedImageStyle(
                  size: const Size(30, 30),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _shareLink!,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: _copyShareLink,
            child: const Text('复制链接'),
          ),
          TextButton(
            onPressed: () {
              // 保存二维码图片
              SnackbarHelper.showInfo(
                context: context,
                message: '保存二维码功能开发中',
              );
            },
            child: const Text('保存图片'),
          ),
        ],
      ),
    );
  }

  // 打开URL的辅助方法
  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        // 如果无法打开链接，复制到剪贴板
        Clipboard.setData(ClipboardData(text: _shareLink!));
        SnackbarHelper.showWarning(
          context: context,
          message: '无法打开分享页面，链接已复制到剪贴板',
        );
      }
    } catch (e) {
      // 发生错误时，复制到剪贴板
      Clipboard.setData(ClipboardData(text: _shareLink!));
      SnackbarHelper.showError(
        context: context,
        message: '分享失败，链接已复制到剪贴板',
      );
    }
  }

  Widget _buildShareLink() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 4),
          child: Text(
            '分享链接',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleMedium?.color,
            ),
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).dividerColor,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: _shareLink != null
                    ? Text(
                        _shareLink!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).textTheme.bodyMedium?.color,
                        ),
                        overflow: TextOverflow.ellipsis,
                      )
                    : Text(
                        '点击“确认分享”生成分享链接',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).hintColor,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                ),
              ),
              TextButton(
                onPressed: _shareLink != null
                  ? () {
                      Clipboard.setData(ClipboardData(text: _shareLink!));
                      SnackbarHelper.showSuccess(
                        context: context,
                        message: '链接已复制到剪贴板',
                      );
                    }
                  : null,
                style: TextButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topRight: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                child: const Text('复制'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildShareButton() {
    if (_isAlreadyShared) {
      // 已经分享过，显示撤销和更新按钮
      return Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.yellow.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.yellow.shade700),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.yellow.shade800,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '该笔记已经分享。您可以更新分享设置或取消分享。',
                    style: TextStyle(
                      color: Colors.yellow.shade800,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // 取消分享按钮
              Expanded(
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _cancelShare,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade50,
                    foregroundColor: Colors.red,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(color: Colors.red.shade300),
                    ),
                  ),
                  child: _isLoading && _cancelingShare
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                        ),
                      )
                    : const Text('取消分享'),
                ),
              ),
              const SizedBox(width: 12),
              // 更新分享按钮
              Expanded(
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _updateShareLink,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading && !_cancelingShare
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('更新分享'),
                ),
              ),
            ],
          ),
        ],
      );
    } else {
      // 还没有分享，显示创建分享按钮
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _isLoading ? null : _createShareLink,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                '确认分享',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
        ),
      );
    }
  }

  /// 创建分享链接
  Future<void> _createShareLink() async {
    // 检查是否需要输入密码
    if (_isPasswordProtected && _passwordController.text.length < 4) {
      SnackbarHelper.showError(
        context: context,
        message: '请输入至少4位的访问密码',
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _cancelingShare = false;
    });

    try {
      // 计算过期时间
      int? expireHours;
      switch (_validityDuration) {
        case '1天':
          expireHours = 24;
          break;
        case '3天':
          expireHours = 72;
          break;
        case '7天':
          expireHours = 168;
          break;
        case '30天':
          expireHours = 720;
          break;
        case '永久':
          expireHours = null;
          break;
      }

      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      final shareUrl = await noteProvider.createShareLink(
        id: widget.note.id,
        expireHours: expireHours,
        isPublic: true, // 目前所有分享都是公开的
        accessType: _isReadOnly ? 'readonly' : 'editable', // 设置访问类型
        password: _isPasswordProtected ? _passwordController.text : null, // 设置密码
      );

      if (shareUrl != null) {
        setState(() {
          _shareLink = shareUrl;
          _isLoading = false;
          _isAlreadyShared = true;
        });

        // 自动复制到剪贴板
        Clipboard.setData(ClipboardData(text: shareUrl));

        SnackbarHelper.showSuccess(
          context: context,
          message: '分享链接已创建并复制到剪贴板',
        );
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = noteProvider.error;
        });

        SnackbarHelper.showError(
          context: context,
          message: '创建分享链接失败: ${noteProvider.error}',
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });

      SnackbarHelper.showError(
        context: context,
        message: '创建分享链接失败: $e',
      );
    }
  }

  /// 更新分享链接
  Future<void> _updateShareLink() async {
    // 检查是否需要输入密码
    if (_isPasswordProtected && _passwordController.text.length < 4) {
      SnackbarHelper.showError(
        context: context,
        message: '请输入至少4位的访问密码',
      );
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _cancelingShare = false;
    });

    try {
      // 计算过期时间
      int? expireHours;
      switch (_validityDuration) {
        case '1天':
          expireHours = 24;
          break;
        case '3天':
          expireHours = 72;
          break;
        case '7天':
          expireHours = 168;
          break;
        case '30天':
          expireHours = 720;
          break;
        case '永久':
          expireHours = null;
          break;
      }

      // 先取消分享，然后重新创建
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);

      // 取消分享
      final cancelResult = await noteProvider.cancelShareLink(id: widget.note.id);

      if (cancelResult) {
        // 重新创建分享
        final shareUrl = await noteProvider.createShareLink(
          id: widget.note.id,
          expireHours: expireHours,
          isPublic: true,
          accessType: _isReadOnly ? 'readonly' : 'editable',
          password: _isPasswordProtected ? _passwordController.text : null,
        );

        if (shareUrl != null) {
          setState(() {
            _shareLink = shareUrl;
            _isLoading = false;
            _isAlreadyShared = true;
          });

          // 自动复制到剪贴板
          Clipboard.setData(ClipboardData(text: shareUrl));

          SnackbarHelper.showSuccess(
            context: context,
            message: '分享链接已更新并复制到剪贴板',
          );
        } else {
          setState(() {
            _isLoading = false;
            _errorMessage = noteProvider.error;
            _isAlreadyShared = false; // 取消分享成功但创建新分享失败
          });

          SnackbarHelper.showError(
            context: context,
            message: '更新分享链接失败: ${noteProvider.error}',
          );
        }
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = noteProvider.error;
        });

        SnackbarHelper.showError(
          context: context,
          message: '取消原分享链接失败: ${noteProvider.error}',
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });

      SnackbarHelper.showError(
        context: context,
        message: '更新分享链接失败: $e',
      );
    }
  }

  /// 取消分享
  Future<void> _cancelShare() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _cancelingShare = true;
    });

    try {
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      final result = await noteProvider.cancelShareLink(id: widget.note.id);

      if (result) {
        setState(() {
          _shareLink = null;
          _isLoading = false;
          _isAlreadyShared = false;
          _cancelingShare = false;
        });

        SnackbarHelper.showSuccess(
          context: context,
          message: '分享链接已取消',
        );
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = noteProvider.error;
          _cancelingShare = false;
        });

        SnackbarHelper.showError(
          context: context,
          message: '取消分享链接失败: ${noteProvider.error}',
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
        _cancelingShare = false;
      });

      SnackbarHelper.showError(
        context: context,
        message: '取消分享链接失败: $e',
      );
    }
  }
}