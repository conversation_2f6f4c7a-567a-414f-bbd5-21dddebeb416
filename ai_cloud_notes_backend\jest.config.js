/** @type {import('ts-jest').JestConfigWithTsJest} */
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  testMatch: ['**/*.test.ts'],
  verbose: true,
  forceExit: true,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  // 忽略特定的 TypeScript 错误代码，以便在不修改功能代码的情况下运行测试
  // TS2717 是关于后续属性声明类型不一致的错误
  // 注意：这只是一个临时的解决方案，根本问题应该在功能代码中修复
  globals: {
    'ts-jest': {
      diagnostics: {
        ignoreCodes: [2717]
      }
    }
  }
};