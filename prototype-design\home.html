<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智云笔记 - 主页</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="common.css">
    <style>
        /* 主页/笔记列表 */
        .home-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            background-color: #FAFAFA;
        }

        .header {
            padding: 24px 20px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: white;
            border-bottom: none;
        }

        .header-title {
            font-weight: 700;
            margin: 0;
            font-size: 1.5rem;
            color: var(--black);
        }

        .user-avatar {
            width: 44px;
            height: 44px;
            border-radius: var(--border-radius);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.2rem;
            box-shadow: 0 4px 10px rgba(93, 95, 239, 0.2);
            cursor: pointer;
            transition: var(--transition);
        }

        .user-avatar:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(93, 95, 239, 0.3);
        }

        .search-box {
            padding: 0 20px 16px;
            background-color: white;
            border-bottom-left-radius: var(--border-radius-lg);
            border-bottom-right-radius: var(--border-radius-lg);
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
        }

        .search-input {
            width: 100%;
            padding: 14px 16px;
            border-radius: var(--border-radius);
            border: 1px solid var(--medium-gray);
            background-color: var(--light-gray);
            display: flex;
            align-items: center;
            transition: var(--transition);
        }

        .search-input:hover {
            border-color: var(--primary-color);
            background-color: white;
        }

        .search-input i {
            color: var(--dark-gray);
            margin-right: 10px;
        }

        .search-input input {
            border: none;
            background: transparent;
            flex: 1;
            outline: none;
            color: var(--black);
            font-size: 0.95rem;
        }

        .categories {
            padding: 4px 20px 16px;
            display: flex;
            overflow-x: auto;
            gap: 12px;
            -ms-overflow-style: none;
            scrollbar-width: none;
        }

        .categories::-webkit-scrollbar {
            display: none;
        }

        .category-tag {
            background-color: white;
            padding: 8px 16px;
            border-radius: var(--border-radius-sm);
            white-space: nowrap;
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--dark-gray);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
            cursor: pointer;
        }

        .category-tag:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .category-tag.active {
            background-color: var(--primary-light);
            color: var(--primary-color);
            font-weight: 600;
            border-color: var(--primary-light);
            box-shadow: 0 2px 6px rgba(93, 95, 239, 0.1);
        }

        .notes-section {
            padding: 16px 20px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-header h2 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--black);
            margin: 0;
        }

        .view-options {
            display: flex;
            gap: 12px;
        }

        .view-options i {
            color: var(--dark-gray);
            font-size: 1.1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .view-options i:hover {
            color: var(--primary-color);
        }

        .view-options i.active {
            color: var(--primary-color);
        }

        .notes-container {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 80px; /* 为底部导航腾出空间 */
        }

        .notes-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 16px;
        }

        .note-item {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
            display: flex;
            flex-direction: column;
            height: 180px;
        }

        .note-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .note-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--black);
            font-size: 1.1rem;
        }

        .note-content {
            color: var(--dark-gray);
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            line-height: 1.5;
            margin-bottom: 10px;
            flex: 1;
        }

        .note-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--dark-gray);
            font-size: 0.85rem;
        }

        .note-date {
            color: var(--dark-gray);
        }

        .note-tags {
            color: var(--primary-color);
            font-weight: 500;
        }

        .notes-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .note-list-item {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--medium-gray);
            transition: var(--transition);
        }

        .note-list-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .note-list-title {
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--black);
            font-size: 1.05rem;
        }

        .note-list-content {
            color: var(--dark-gray);
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .note-list-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: var(--dark-gray);
            font-size: 0.85rem;
        }

        /* 收藏页面样式 */
        .favorites-header {
            padding: 24px 20px 16px;
            background-color: white;
            border-bottom-left-radius: var(--border-radius-lg);
            border-bottom-right-radius: var(--border-radius-lg);
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
        }

        .favorites-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: var(--black);
        }

        .favorites-count {
            color: var(--dark-gray);
            font-size: 0.9rem;
        }

        .favorite-item {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
            border: 1px solid var(--medium-gray);
            position: relative;
        }

        .favorite-item:hover {
            box-shadow: var(--shadow);
            transform: translateY(-2px);
            border-color: var(--primary-light);
        }

        .favorite-star {
            position: absolute;
            top: 16px;
            right: 16px;
            color: var(--accent-color);
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title text-center">智云笔记应用原型设计</h1>
        <p class="page-description text-center">
            主页与笔记列表页面展示了用户的笔记内容，支持不同的视图模式、分类过滤和搜索功能。
        </p>

        <!-- 主页/笔记列表 - 网格视图 -->
        <div>
            <h3 class="screen-title">主页 - 网格视图</h3>
            <p class="screen-description">登录后的默认页面，以网格形式展示笔记卡片</p>
            <div class="screen">
                <div class="home-container">
                    <div class="header">
                        <h4 class="header-title">我的笔记</h4>
                        <div class="user-avatar">
                            <span>张</span>
                        </div>
                    </div>
                    
                    <div class="search-box">
                        <div class="search-input">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索笔记...">
                        </div>
                    </div>
                    
                    <div class="categories">
                        <div class="category-tag active">全部</div>
                        <div class="category-tag">最近</div>
                        <div class="category-tag">工作</div>
                        <div class="category-tag">学习</div>
                        <div class="category-tag">旅行</div>
                        <div class="category-tag">阅读</div>
                        <div class="category-tag">灵感</div>
                    </div>
                    
                    <div class="notes-container">
                        <div class="notes-section">
                            <div class="section-header">
                                <h2>所有笔记</h2>
                                <div class="view-options">
                                    <i class="fas fa-th-large active"></i>
                                    <i class="fas fa-list"></i>
                                </div>
                            </div>
                            
                            <div class="notes-grid">
                                <div class="note-item">
                                    <div class="note-title">工作计划</div>
                                    <div class="note-content">本周需要完成的任务：1. 项目计划书 2. 客户会议 3. 团队协调 4. 进度报告</div>
                                    <div class="note-meta">
                                        <span class="note-date">2023-08-15</span>
                                        <span class="note-tags">工作</span>
                                    </div>
                                </div>
                                
                                <div class="note-item">
                                    <div class="note-title">学习笔记</div>
                                    <div class="note-content">Flutter框架学习：Widget、State、BuildContext的概念和使用方法，以及常见组件的属性和用法。</div>
                                    <div class="note-meta">
                                        <span class="note-date">2023-08-14</span>
                                        <span class="note-tags">学习</span>
                                    </div>
                                </div>
                                
                                <div class="note-item">
                                    <div class="note-title">旅行计划</div>
                                    <div class="note-content">云南之行：1. 大理 2. 丽江 3. 香格里拉 住宿和交通安排，景点推荐。</div>
                                    <div class="note-meta">
                                        <span class="note-date">2023-08-12</span>
                                        <span class="note-tags">旅行</span>
                                    </div>
                                </div>
                                
                                <div class="note-item">
                                    <div class="note-title">读书笔记</div>
                                    <div class="note-content">《原子习惯》读书笔记：1. 复合效应 2. 微习惯 3. 环境设计 4. 身份认同</div>
                                    <div class="note-meta">
                                        <span class="note-date">2023-08-10</span>
                                        <span class="note-tags">阅读</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主页/笔记列表 - 列表视图 -->
        <div>
            <h3 class="screen-title">主页 - 列表视图</h3>
            <p class="screen-description">笔记的列表形式展示，更适合查看更多笔记标题</p>
            <div class="screen">
                <div class="home-container">
                    <div class="header">
                        <h4 class="header-title">我的笔记</h4>
                        <div class="user-avatar">
                            <span>张</span>
                        </div>
                    </div>
                    
                    <div class="search-box">
                        <div class="search-input">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="搜索笔记...">
                        </div>
                    </div>
                    
                    <div class="categories">
                        <div class="category-tag active">全部</div>
                        <div class="category-tag">最近</div>
                        <div class="category-tag">工作</div>
                        <div class="category-tag">学习</div>
                        <div class="category-tag">旅行</div>
                        <div class="category-tag">阅读</div>
                        <div class="category-tag">灵感</div>
                    </div>
                    
                    <div class="notes-container">
                        <div class="notes-section">
                            <div class="section-header">
                                <h2>所有笔记</h2>
                                <div class="view-options">
                                    <i class="fas fa-th-large"></i>
                                    <i class="fas fa-list active"></i>
                                </div>
                            </div>
                            
                            <div class="notes-list">
                                <div class="note-list-item">
                                    <div class="note-list-title">工作计划</div>
                                    <div class="note-list-content">本周需要完成的任务：1. 项目计划书 2. 客户会议 3. 团队协调 4. 进度报告</div>
                                    <div class="note-list-meta">
                                        <span class="note-date">2023-08-15</span>
                                        <span class="note-tags">工作</span>
                                    </div>
                                </div>
                                
                                <div class="note-list-item">
                                    <div class="note-list-title">学习笔记</div>
                                    <div class="note-list-content">Flutter框架学习：Widget、State、BuildContext的概念和使用方法，以及常见组件的属性和用法。</div>
                                    <div class="note-list-meta">
                                        <span class="note-date">2023-08-14</span>
                                        <span class="note-tags">学习</span>
                                    </div>
                                </div>
                                
                                <div class="note-list-item">
                                    <div class="note-list-title">旅行计划</div>
                                    <div class="note-list-content">云南之行：1. 大理 2. 丽江 3. 香格里拉 住宿和交通安排，景点推荐。</div>
                                    <div class="note-list-meta">
                                        <span class="note-date">2023-08-12</span>
                                        <span class="note-tags">旅行</span>
                                    </div>
                                </div>
                                
                                <div class="note-list-item">
                                    <div class="note-list-title">读书笔记</div>
                                    <div class="note-list-content">《原子习惯》读书笔记：1. 复合效应 2. 微习惯 3. 环境设计 4. 身份认同</div>
                                    <div class="note-list-meta">
                                        <span class="note-date">2023-08-10</span>
                                        <span class="note-tags">阅读</span>
                                    </div>
                                </div>
                                
                                <div class="note-list-item">
                                    <div class="note-list-title">会议纪要</div>
                                    <div class="note-list-content">产品讨论会议：功能优先级排序、界面设计调整、开发进度汇报。</div>
                                    <div class="note-list-meta">
                                        <span class="note-date">2023-08-08</span>
                                        <span class="note-tags">工作</span>
                                    </div>
                                </div>
                                
                                <div class="note-list-item">
                                    <div class="note-list-title">购物清单</div>
                                    <div class="note-list-content">周末采购：1. 水果蔬菜 2. 牛奶面包 3. 洗衣液 4. 纸巾</div>
                                    <div class="note-list-meta">
                                        <span class="note-date">2023-08-05</span>
                                        <span class="note-tags">生活</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item active">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 收藏笔记页面 -->
        <div>
            <h3 class="screen-title">收藏笔记</h3>
            <p class="screen-description">展示用户收藏的笔记列表</p>
            <div class="screen">
                <div class="home-container">
                    <div class="favorites-header">
                        <h4 class="favorites-title">我的收藏</h4>
                        <div class="favorites-count">共 3 条收藏笔记</div>
                    </div>
                    
                    <div class="notes-container">
                        <div class="notes-section">
                            <div class="favorite-item">
                                <div class="note-title">工作计划</div>
                                <div class="note-content">本周需要完成的任务：1. 项目计划书 2. 客户会议 3. 团队协调 4. 进度报告</div>
                                <div class="note-meta">
                                    <span class="note-tags">工作</span>
                                    <span class="note-date">2023-08-15</span>
                                </div>
                                <div class="favorite-star">
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            
                            <div class="favorite-item">
                                <div class="note-title">旅行计划</div>
                                <div class="note-content">云南之行：1. 大理 2. 丽江 3. 香格里拉 住宿和交通安排，景点推荐。</div>
                                <div class="note-meta">
                                    <span class="note-tags">旅行</span>
                                    <span class="note-date">2023-08-12</span>
                                </div>
                                <div class="favorite-star">
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                            
                            <div class="favorite-item">
                                <div class="note-title">读书笔记</div>
                                <div class="note-content">《原子习惯》读书笔记：1. 复合效应 2. 微习惯 3. 环境设计 4. 身份认同</div>
                                <div class="note-meta">
                                    <span class="note-tags">阅读</span>
                                    <span class="note-date">2023-08-10</span>
                                </div>
                                <div class="favorite-star">
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部导航栏 -->
                    <div class="bottom-nav">
                        <div class="nav-item">
                            <i class="fas fa-home"></i>
                            <span>首页</span>
                        </div>
                        <div class="nav-item active">
                            <i class="fas fa-star"></i>
                            <span>收藏</span>
                        </div>
                        <div class="nav-item new-note">
                            <div class="new-note-btn">
                                <i class="fas fa-plus"></i>
                            </div>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-tags"></i>
                            <span>标签</span>
                        </div>
                        <div class="nav-item">
                            <i class="fas fa-user"></i>
                            <span>我的</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>智云笔记应用原型设计 &copy; 2023</p>
            <p>
                <a href="welcome.html">欢迎页面</a> | 
                <a href="auth.html">认证页面</a> | 
                <a href="home.html">主页</a> | 
                <a href="editor.html">编辑器</a> | 
                <a href="settings.html">设置</a> | 
                <a href="tags.html">标签</a>
            </p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 