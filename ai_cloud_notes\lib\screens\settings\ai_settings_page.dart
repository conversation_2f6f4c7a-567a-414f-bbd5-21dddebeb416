import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/providers/ai_provider.dart';
import 'package:ai_cloud_notes/services/api_service.dart';

class AISettingsPage extends StatefulWidget {
  const AISettingsPage({Key? key}) : super(key: key);

  @override
  State<AISettingsPage> createState() => _AISettingsPageState();
}

class _AISettingsPageState extends State<AISettingsPage> {
  late AIProvider _aiProvider;

  // 临时存储用户选择的设置，只有在点击保存按钮时才会应用
  late bool _aiAssistantEnabled;
  late bool _smartSuggestionsEnabled;
  late bool _autoTaggingEnabled;
  late bool _contentSummaryEnabled;
  late String _selectedModel;
  late String _suggestionFrequency;
  late bool _localProcessingEnabled;
  late bool _allowDataCollection;
  bool _isInitialized = false;
  bool _userHasMadeChanges = false; // 新增：跟踪用户是否已更改设置

  @override
  void initState() {
    super.initState();
    // 在 initState 中，我们仍然使用 context.read 来获取初始值，
    // 因为 Consumer/watch 只能在 build 方法或 didChangeDependencies 中使用。
    _aiProvider = context.read<AIProvider>();

    // 初始化临时变量为当前 Provider 的设置
    _aiAssistantEnabled = _aiProvider.aiAssistantEnabled;
    _smartSuggestionsEnabled = _aiProvider.smartSuggestionsEnabled;
    _autoTaggingEnabled = _aiProvider.autoTaggingEnabled;
    _contentSummaryEnabled = _aiProvider.contentSummaryEnabled;
    _selectedModel = _aiProvider.selectedModel;
    _suggestionFrequency = _aiProvider.suggestionFrequency;
    _localProcessingEnabled = _aiProvider.localProcessingEnabled;
    _allowDataCollection = _aiProvider.allowDataCollection;

    // 标记初始化完成
    // 使用 addPostFrameCallback 确保 _isInitialized 在第一帧构建完成后才为 true
    // 这样 Consumer 中的逻辑在首次构建时不会错误地更新状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 使用 Consumer 来监听 AIProvider 的变化
    return Consumer<AIProvider>(
      builder: (context, aiProvider, child) {
        // 更新 _aiProvider 的引用，以便其他方法可以使用最新的 provider 实例
        _aiProvider = aiProvider;

        // 如果 provider 更新了，并且用户没有在当前页面做任何修改，
        // 并且页面已经初始化完成，则用 provider 的最新数据更新页面的本地状态。
        if (_isInitialized && !_userHasMadeChanges) {
          bool needsSetState = false;
          if (_aiAssistantEnabled != aiProvider.aiAssistantEnabled) {
            _aiAssistantEnabled = aiProvider.aiAssistantEnabled;
            needsSetState = true;
          }
          if (_smartSuggestionsEnabled != aiProvider.smartSuggestionsEnabled) {
            _smartSuggestionsEnabled = aiProvider.smartSuggestionsEnabled;
            needsSetState = true;
          }
          if (_autoTaggingEnabled != aiProvider.autoTaggingEnabled) {
            _autoTaggingEnabled = aiProvider.autoTaggingEnabled;
            needsSetState = true;
          }
          if (_contentSummaryEnabled != aiProvider.contentSummaryEnabled) {
            _contentSummaryEnabled = aiProvider.contentSummaryEnabled;
            needsSetState = true;
          }
          if (_selectedModel != aiProvider.selectedModel) {
            _selectedModel = aiProvider.selectedModel;
            needsSetState = true;
          }
          if (_suggestionFrequency != aiProvider.suggestionFrequency) {
            _suggestionFrequency = aiProvider.suggestionFrequency;
            needsSetState = true;
          }
          if (_localProcessingEnabled != aiProvider.localProcessingEnabled) {
            _localProcessingEnabled = aiProvider.localProcessingEnabled;
            needsSetState = true;
          }
          if (_allowDataCollection != aiProvider.allowDataCollection) {
            _allowDataCollection = aiProvider.allowDataCollection;
            needsSetState = true;
          }

          if (needsSetState) {
            // 使用 addPostFrameCallback 确保 setState 在 build 周期之后安全调用
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                // 检查 widget 是否仍然挂载
                setState(() {});
              }
            });
          }
        }

        // 原有的 Scaffold 和 UI 构建逻辑
        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: _buildHeader(),
          body: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildAIAssistantSwitch(),
                    _buildGeneralSection(),
                    const SizedBox(height: 16),
                    _buildModelSection(),
                    const SizedBox(height: 16),
                    _buildPrivacySection(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  AppBar _buildHeader() {
    return AppBar(
      backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
      elevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: Theme.of(context).iconTheme.color,
        ),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        'AI设置',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Theme.of(context).textTheme.titleLarge?.color,
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 16),
          child: ElevatedButton(
            onPressed: _saveSettings,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
            ),
            child: const Text(
              '保存',
              style: TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAIAssistantSwitch() {
    return Card(
      color: Theme.of(context).cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: _buildSwitchOption(
          icon: Icons.smart_toy,
          iconBackground: const Color(0xFFF5F3FF),
          iconColor: const Color(0xFF8B5CF6),
          title: 'AI助手',
          subtitle: '启用智能笔记助手功能',
          value: _aiAssistantEnabled,
          onChanged: (value) {
            setState(() {
              _aiAssistantEnabled = value;
              _userHasMadeChanges = true;
            });
          },
        ),
      ),
    );
  }

  Widget _buildGeneralSection() {
    return _buildSection(
      title: '常规设置',
      child: Column(
        children: [
          _buildSwitchOption(
            icon: Icons.lightbulb,
            iconBackground: const Color(0xFFFFFBEB),
            iconColor: const Color(0xFFF59E0B),
            title: '智能建议',
            subtitle: '根据笔记内容自动提供建议',
            value: _smartSuggestionsEnabled,
            onChanged: _aiAssistantEnabled
                ? (value) {
                    setState(() {
                      _smartSuggestionsEnabled = value;
                      _userHasMadeChanges = true;
                    });
                  }
                : null,
          ),
          const SizedBox(height: 16),
          _buildSwitchOption(
            icon: Icons.tag,
            iconBackground: const Color(0xFFF0F9FF),
            iconColor: const Color(0xFF0EA5E9),
            title: '自动标签',
            subtitle: '根据笔记内容自动添加标签',
            value: _autoTaggingEnabled,
            onChanged: _aiAssistantEnabled
                ? (value) {
                    setState(() {
                      _autoTaggingEnabled = value;
                      _userHasMadeChanges = true;
                    });
                  }
                : null,
          ),
          const SizedBox(height: 16),
          _buildSwitchOption(
            icon: Icons.summarize,
            iconBackground: const Color(0xFFF0FDF4),
            iconColor: const Color(0xFF10B981),
            title: '内容摘要',
            subtitle: '自动生成笔记内容摘要',
            value: _contentSummaryEnabled,
            onChanged: _aiAssistantEnabled
                ? (value) {
                    setState(() {
                      _contentSummaryEnabled = value;
                      _userHasMadeChanges = true;
                    });
                  }
                : null,
          ),
          if (_smartSuggestionsEnabled && _aiAssistantEnabled) ...[
            const SizedBox(height: 24),
            _buildFrequencySelector(),
          ],
        ],
      ),
    );
  }

  Widget _buildFrequencySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '建议频率',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).textTheme.titleMedium?.color,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: _aiProvider.frequencyOptions.map((frequency) {
            final bool isSelected = _suggestionFrequency == frequency;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _suggestionFrequency = frequency;
                  _userHasMadeChanges = true;
                });
              },
              child: Container(
                width: 80,
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppTheme.primaryColor.withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.lightGrayColor,
                  ),
                ),
                alignment: Alignment.center,
                child: Text(
                  frequency,
                  style: TextStyle(
                    fontSize: 14,
                    color: isSelected
                        ? AppTheme.primaryColor
                        : AppTheme.darkGrayColor,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 10),
        Text(
          _getFrequencyDescription(),
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).textTheme.bodyMedium?.color,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  String _getFrequencyDescription() {
    return _aiProvider.getFrequencyDescription(_suggestionFrequency);
  }

  Widget _buildModelSection() {
    return _buildSection(
      title: '语言模型设置',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择AI模型',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              // 当AI助手关闭时显示为灰色
              color: _aiAssistantEnabled
                  ? Theme.of(context).textTheme.titleMedium?.color
                  : Theme.of(context).disabledColor,
            ),
          ),
          const SizedBox(height: 16),
          ..._aiProvider.modelOptions.map((model) =>
              _buildModelOption(model, enabled: _aiAssistantEnabled)),
        ],
      ),
    );
  }

  Widget _buildModelOption(Map<String, dynamic> model, {bool enabled = true}) {
    final bool isSelected = _selectedModel == model['name'];

    return GestureDetector(
      onTap: enabled
          ? () {
              setState(() {
                _selectedModel = model['name'] as String;
                _userHasMadeChanges = true;
              });
            }
          : null,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: enabled
              ? (isSelected
                  ? AppTheme.primaryColor.withOpacity(0.1)
                  : Theme.of(context).cardColor)
              : Theme.of(context).disabledColor.withOpacity(0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: enabled
                ? (isSelected ? AppTheme.primaryColor : AppTheme.lightGrayColor)
                : Theme.of(context).disabledColor.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: enabled
                    ? AppTheme.primaryColor.withOpacity(0.1)
                    : Theme.of(context).disabledColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.auto_awesome,
                color: enabled
                    ? AppTheme.primaryColor
                    : Theme.of(context).disabledColor,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        model['name'] as String,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: enabled
                              ? (isSelected
                                  ? AppTheme.primaryColor
                                  : Theme.of(context)
                                      .textTheme
                                      .titleMedium
                                      ?.color)
                              : Theme.of(context).disabledColor,
                        ),
                      ),
                      if (model['isPro'] as bool)
                        Container(
                          margin: const EdgeInsets.only(left: 8),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFFEF4444),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'PRO',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    model['description'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      color: enabled
                          ? Theme.of(context).textTheme.bodyMedium?.color
                          : Theme.of(context).disabledColor,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: enabled
                    ? AppTheme.primaryColor
                    : Theme.of(context).disabledColor,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacySection() {
    return _buildSection(
      title: '隐私设置',
      child: Column(
        children: [
          _buildSwitchOption(
            icon: Icons.phone_android,
            iconBackground: const Color(0xFFF3F4F6),
            iconColor: const Color(0xFF6B7280),
            title: '仅本地处理',
            subtitle: '所有AI处理在设备本地完成，不上传内容',
            value: _localProcessingEnabled,
            onChanged: _aiAssistantEnabled
                ? (value) {
                    setState(() {
                      _localProcessingEnabled = value;
                      if (value) {
                        _allowDataCollection = false;
                      }
                      _userHasMadeChanges = true;
                    });
                  }
                : null,
          ),
          const SizedBox(height: 16),
          _buildSwitchOption(
            icon: Icons.data_usage,
            iconBackground: const Color(0xFFF3F4F6),
            iconColor: const Color(0xFF6B7280),
            title: '数据收集',
            subtitle: '允许收集匿名数据以改进AI体验',
            value: _allowDataCollection,
            onChanged: _aiAssistantEnabled
                ? (value) {
                    if (_localProcessingEnabled && value) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('启用仅本地处理时无法开启数据收集')),
                      );
                      return;
                    }
                    setState(() {
                      _allowDataCollection = value;
                      _userHasMadeChanges = true;
                    });
                  }
                : null,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFFFF7ED),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFFFBD38D),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.info_outline,
                  color: Color(0xFFF59E0B),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '隐私说明：您可以随时调整AI功能的隐私级别。启用仅本地处理将降低部分AI功能的准确度，但可以提高隐私保护级别。查看我们的隐私政策了解更多信息。',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchOption({
    required IconData icon,
    required Color iconBackground,
    required Color iconColor,
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool)? onChanged,
  }) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: iconBackground,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: onChanged != null
                      ? Theme.of(context).textTheme.titleLarge?.color
                      : Theme.of(context).disabledColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 14,
                  color: onChanged != null
                      ? Theme.of(context).textTheme.bodyMedium?.color
                      : Theme.of(context).disabledColor,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
          activeTrackColor: onChanged != null
              ? null
              : Theme.of(context).disabledColor.withOpacity(0.3),
          inactiveTrackColor: onChanged != null
              ? null
              : Theme.of(context).disabledColor.withOpacity(0.1),
          activeColor: AppTheme.primaryColor,
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required Widget child,
  }) {
    return Card(
      color: Theme.of(context).cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.only(bottom: 0),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).textTheme.titleLarge?.color,
              ),
            ),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }

  void _saveSettings() async {
    // 保存设置到AIProvider
    final success = await _aiProvider.saveSettings(
      aiAssistantEnabled: _aiAssistantEnabled,
      smartSuggestionsEnabled: _smartSuggestionsEnabled,
      autoTaggingEnabled: _autoTaggingEnabled,
      contentSummaryEnabled: _contentSummaryEnabled,
      selectedModel: _selectedModel,
      suggestionFrequency: _suggestionFrequency,
      localProcessingEnabled: _localProcessingEnabled,
      allowDataCollection: _allowDataCollection,
    );

    if (mounted) {
      // 检查 widget 是否仍然挂载
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(success ? 'AI设置已保存' : 'AI设置保存失败')),
      );
      if (success) {
        setState(() {
          _userHasMadeChanges = false; // 重置更改标志
        });
        // 延迟后返回
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            Navigator.pop(context);
          }
        });
      }
    }
  }
}
