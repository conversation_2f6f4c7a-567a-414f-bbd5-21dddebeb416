import 'package:flutter/material.dart';
import 'package:ai_cloud_notes/services/api_service.dart';
import 'package:provider/provider.dart';
import 'package:ai_cloud_notes/providers/theme_service.dart';

/// 用户认证状态枚举
enum AuthStatus {
  uninitialized, // 未初始化
  authenticated, // 已认证
  unauthenticated, // 未认证
}

/// 用户认证状态管理类
class AuthProvider with ChangeNotifier {
  // API服务实例
  final ApiService _apiService;

  // 构造函数，接收 ApiService 实例
  AuthProvider(this._apiService);

  // 当前认证状态
  AuthStatus _status = AuthStatus.uninitialized;
  // 用户数据
  Map<String, dynamic>? _user;
  // 错误信息
  String? _error;
  // 加载状态
  bool _isLoading = false;

  // Getters
  AuthStatus get status => _status;
  Map<String, dynamic>? get user => _user;
  String? get error => _error;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _status == AuthStatus.authenticated;

  /// 初始化方法
  Future<void> init() async {
    _setLoading(true);
    try {
      // ApiService 应该在外部初始化

      // 尝试获取当前用户信息
      final response = await _apiService.getCurrentUser();

      if (response['success'] == true && response['data'] != null) {
        _user = response['data']['user'];
        _status = AuthStatus.authenticated;
      } else {
        _status = AuthStatus.unauthenticated;
      }
    } catch (e) {
      _error = '初始化认证状态失败: $e';
      _status = AuthStatus.unauthenticated;
    } finally {
      _setLoading(false);
    }
  }

  /// 发送验证码
  Future<Map<String, dynamic>> sendVerificationCode(String email) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await _apiService.sendVerificationCode(email);
      return response;
    } catch (e) {
      _error = '发送验证码失败: $e';
      return {
        'success': false,
        'error': {'message': _error}
      };
    } finally {
      _setLoading(false);
    }
  }

  /// 注册新用户
  Future<bool> register({
    required String username,
    required String email,
    required String password,
    required String verificationCode,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await _apiService.register(
        username: username,
        email: email,
        password: password,
        verificationCode: verificationCode,
      );

      if (response['success'] == true) {
        return true;
      } else {
        _error = response['error']?['message'] ?? '注册失败';
        return false;
      }
    } catch (e) {
      _error = '注册失败: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 用户登录
  Future<bool> login({
    required String usernameOrEmail,
    required String password,
    BuildContext? context,
  }) async {
    debugPrint('AuthProvider: login started.');
    _setLoading(true);
    _error = null;

    try {
      debugPrint('AuthProvider: Calling login API...');
      final response = await _apiService.login(
        usernameOrEmail: usernameOrEmail,
        password: password,
      );

      if (response['success'] == true && response['data'] != null) {
        debugPrint('AuthProvider: Login API success.');
        _user = response['data']['user'];
        _status = AuthStatus.authenticated; // 设置状态，但不立即通知

        // 登录成功后同步主题设置
        if (context != null) {
          try {
            final themeService =
                Provider.of<ThemeService>(context, listen: false);
            // 显式更新 ThemeService 的认证状态，确保 syncAfterLogin 内部逻辑正确
            // 因为 ChangeNotifierProxyProvider 的更新可能尚未完成
            debugPrint(
                'AuthProvider: Calling themeService.updateAuthenticationStatus(true)...');
            themeService.updateAuthenticationStatus(true);
            debugPrint(
                'AuthProvider: Calling await themeService.syncAfterLogin()...');
            await themeService.syncAfterLogin(); // 等待主题同步完成
            debugPrint(
                'AuthProvider: themeService.syncAfterLogin() completed.');
          } catch (e) {
            debugPrint('同步主题设置失败: $e');
            // 不影响登录流程，主题同步失败也应视为登录成功的一部分
          }
        }

        debugPrint('AuthProvider: Calling notifyListeners()...');
        notifyListeners(); // 在主题同步完成后再通知，以便UI能获取到所有最新状态
        debugPrint('AuthProvider: login finished.');
        return true;
      } else {
        debugPrint('AuthProvider: Login API failed.');
        _error = response['error']?['message'] ?? '登录失败';
        debugPrint('AuthProvider: login finished.');
        return false;
      }
    } catch (e) {
      debugPrint('AuthProvider: Login API failed with exception: $e');
      _error = '登录失败: $e';
      debugPrint('AuthProvider: login finished.');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 忘记密码
  Future<bool> forgotPassword(String email) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await _apiService.forgotPassword(email);

      if (response['success'] == true) {
        return true;
      } else {
        _error = response['error']?['message'] ?? '发送重置密码邮件失败';
        return false;
      }
    } catch (e) {
      _error = '发送重置密码邮件失败: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 重置密码
  Future<bool> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await _apiService.resetPassword(
        email: email,
        token: token,
        newPassword: newPassword,
      );

      if (response['success'] == true) {
        return true;
      } else {
        _error = response['error']?['message'] ?? '重置密码失败';
        return false;
      }
    } catch (e) {
      _error = '重置密码失败: $e';
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 修改密码
  Future<Map<String, dynamic>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _setLoading(true);
    _error = null;

    try {
      final response = await _apiService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (response['success'] == true) {
        return {'success': true, 'message': response['message'] ?? '密码修改成功'};
      } else {
        _error = response['error']?['message'] ?? '修改密码失败';
        return {
          'success': false,
          'error': {'message': _error}
        };
      }
    } catch (e) {
      _error = '修改密码失败: $e';
      return {
        'success': false,
        'error': {'message': _error}
      };
    } finally {
      _setLoading(false);
    }
  }

  /// 用户登出
  Future<void> logout() async {
    _setLoading(true);

    try {
      await _apiService.logout();
      _user = null;
      _status = AuthStatus.unauthenticated;
    } catch (e) {
      _error = '登出失败: $e';
    } finally {
      _setLoading(false);
    }
  }

  /// 更新用户头像
  void updateUserAvatar(String? avatarUrl) {
    if (_user != null) {
      _user!['avatar'] = avatarUrl;
      notifyListeners();
    }
  }

  /// 刷新用户信息
  Future<void> refreshUserInfo() async {
    if (_status != AuthStatus.authenticated) return;

    try {
      final response = await _apiService.getCurrentUser();

      if (response['success'] == true && response['data'] != null) {
        _user = response['data']['user'];
        notifyListeners();
      }
    } catch (e) {
      _error = '刷新用户信息失败: $e';
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}
