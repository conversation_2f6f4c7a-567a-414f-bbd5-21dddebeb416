import 'package:flutter/material.dart';
import 'package:shared_preferences.dart';
import 'package:ai_cloud_notes/services/api_service.dart';
import 'package:provider/provider.dart';

/// 主题服务类，用于管理应用的主题设置
class ThemeService extends ChangeNotifier {
  // 主题模式枚举
  static const String _themeModeKey = 'theme_mode';
  static const String _fontColorKey = 'font_color';
  static const String _textSizeKey = 'text_size';
  static const String _titleSizeKey = 'title_size';

  late ApiService _apiService;
  bool _isInitialized = false;
  bool _isSyncing = false;

  // 主题模式
  ThemeMode _themeMode = ThemeMode.system;
  // 字体颜色
  Color _fontColor = Colors.black;
  // 正文字体大小
  double _textSize = 16.0;
  // 标题字体大小
  double _titleSize = 18.0;

  // 字体颜色选项
  final List<Map<String, dynamic>> _fontColorOptions = [
    {
      'name': '黑色',
      'color': Colors.black,
    },
    {
      'name': '白色',
      'color': Colors.white,
    },
    {
      'name': '灰色',
      'color': Colors.grey,
    },
    {
      'name': '蓝色',
      'color': Colors.blue,
    },
    {
      'name': '绿色',
      'color': Colors.green,
    },
  ];

  // 字体大小选项
  final List<Map<String, dynamic>> _fontSizeOptions = [
    {
      'name': '小',
      'size': 14.0,
    },
    {
      'name': '中',
      'size': 16.0,
    },
    {
      'name': '大',
      'size': 18.0,
    },
  ];

  ThemeService() {
    _loadSettings();
  }

  // 初始化API服务
  void initApiService(BuildContext context) {
    if (!_isInitialized) {
      _apiService = Provider.of<ApiService>(context, listen: false);
      _isInitialized = true;
      _syncFromServer();
    }
  }

  // 获取主题模式
  ThemeMode get themeMode => _themeMode;
  
  // 获取字体颜色
  Color get fontColor => _fontColor;
  
  // 获取正文字体大小
  double get textSize => _textSize;
  
  // 获取标题字体大小
  double get titleSize => _titleSize;
  
  // 获取字体颜色选项
  List<Map<String, dynamic>> get fontColorOptions => _fontColorOptions;
  
  // 获取字体大小选项
  List<Map<String, dynamic>> get fontSizeOptions => _fontSizeOptions;

  // 是否正在同步
  bool get isSyncing => _isSyncing;

  // 从本地加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // 加载主题模式
    final themeModeIndex = prefs.getInt(_themeModeKey) ?? 0;
    _themeMode = ThemeMode.values[themeModeIndex];
    
    // 加载字体颜色
    final fontColorValue = prefs.getInt(_fontColorKey) ?? Colors.black.value;
    _fontColor = Color(fontColorValue);
    
    // 加载字体大小
    _textSize = prefs.getDouble(_textSizeKey) ?? 16.0;
    _titleSize = prefs.getDouble(_titleSizeKey) ?? 18.0;
    
    notifyListeners();
  }

  // 保存设置到本地
  Future<void> _saveSettingsLocally({
    required ThemeMode themeMode,
    required Color fontColor,
    required double textSize,
    required double titleSize,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    
    // 保存主题模式
    await prefs.setInt(_themeModeKey, themeMode.index);
    _themeMode = themeMode;
    
    // 保存字体颜色
    await prefs.setInt(_fontColorKey, fontColor.value);
    _fontColor = fontColor;
    
    // 保存字体大小
    await prefs.setDouble(_textSizeKey, textSize);
    _textSize = textSize;
    
    await prefs.setDouble(_titleSizeKey, titleSize);
    _titleSize = titleSize;
    
    notifyListeners();
  }

  // 从服务器同步设置
  Future<void> _syncFromServer() async {
    if (!_isInitialized || _isSyncing) return;
    
    _isSyncing = true;
    notifyListeners();
    
    try {
      final settings = await _apiService.getUserThemeSettings();
      
      if (settings != null) {
        // 解析主题模式
        final themeMode = ThemeMode.values[settings['themeMode'] ?? 0];
        
        // 解析字体颜色
        final fontColorHex = settings['fontColor'] ?? '#000000';
        final fontColor = Color(int.parse(fontColorHex.replaceFirst('#', '0xFF')));
        
        // 解析字体大小
        final textSize = (settings['textSize'] ?? 16.0).toDouble();
        final titleSize = (settings['titleSize'] ?? 18.0).toDouble();
        
        // 保存到本地
        await _saveSettingsLocally(
          themeMode: themeMode,
          fontColor: fontColor,
          textSize: textSize,
          titleSize: titleSize,
        );
      }
    } catch (e) {
      debugPrint('从服务器同步主题设置失败: $e');
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  // 同步设置到服务器
  Future<void> _syncToServer({
    required ThemeMode themeMode,
    required Color fontColor,
    required double textSize,
    required double titleSize,
  }) async {
    if (!_isInitialized) return;
    
    try {
      // 将颜色转换为十六进制字符串
      final fontColorHex = '#${fontColor.value.toRadixString(16).substring(2)}';
      
      await _apiService.updateUserThemeSettings(
        themeMode: themeMode.index,
        fontColor: fontColorHex,
        textSize: textSize,
        titleSize: titleSize,
      );
    } catch (e) {
      debugPrint('同步主题设置到服务器失败: $e');
    }
  }

  // 保存设置 (本地 + 服务器)
  Future<void> saveSettings({
    required ThemeMode themeMode,
    required Color fontColor,
    required double textSize,
    required double titleSize,
  }) async {
    // 先保存到本地
    await _saveSettingsLocally(
      themeMode: themeMode,
      fontColor: fontColor,
      textSize: textSize,
      titleSize: titleSize,
    );
    
    // 如果已初始化API，同步到服务器
    if (_isInitialized) {
      _syncToServer(
        themeMode: themeMode,
        fontColor: fontColor,
        textSize: textSize,
        titleSize: titleSize,
      );
    }
  }

  // 根据主题模式自动调整字体颜色
  Color getAutoFontColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? Colors.white : Colors.black;
  }
} 