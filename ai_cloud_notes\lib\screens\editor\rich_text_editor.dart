import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';

import 'package:ai_cloud_notes/themes/app_theme.dart';
import 'package:ai_cloud_notes/utils/snackbar_helper.dart';
import 'package:fleather/fleather.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:parchment/parchment.dart';
import 'package:parchment_delta/parchment_delta.dart';
// import 'package:provider/provider.dart'; // Not used directly in this widget for now
// import 'package:ai_cloud_notes/providers/note_provider.dart'; // For image upload, handled by callback
import 'package:uuid/uuid.dart';

// Callback types
typedef ImageUploadCallback = Future<String?> Function(
    dynamic imageFile, String? noteId);
typedef PasteCallback = Future<void> Function();

class RichTextEditor extends StatefulWidget {
  final ParchmentDocument? initialDocument;
  final ValueChanged<ParchmentDocument>? onDocumentChanged;
  final ValueChanged<TextSelection>? onSelectionChanged;
  final FocusNode? focusNode;
  final bool readOnly;
  final String? noteId; // Required for image uploading to the correct note
  final ImageUploadCallback? onImageUpload; // Callback for uploading image
  final bool showAiSuggestionsHint; // To control AI hint in toolbar
  final PasteCallback? onPaste; // Callback for pasting content
  final bool isPasting; // 粘贴状态

  const RichTextEditor({
    Key? key,
    this.initialDocument,
    this.onDocumentChanged,
    this.onSelectionChanged,
    this.focusNode,
    this.readOnly = false,
    this.noteId,
    this.onImageUpload,
    this.showAiSuggestionsHint = false, // Default to false
    this.onPaste,
    this.isPasting = false, // 默认不在粘贴状态
  }) : super(key: key);

  @override
  RichTextEditorState createState() => RichTextEditorState();
}

class RichTextEditorState extends State<RichTextEditor> {
  late FleatherController _controller;
  late ParchmentDocument _document;
  final FocusNode _internalFocusNode = FocusNode();
  Timer? _selectionTimer;

  FocusNode get _effectiveFocusNode => widget.focusNode ?? _internalFocusNode;

  // Image cache with size limit
  static final Map<String, Widget> _imageCache = {};
  static const int _maxCacheSize = 50; // Maximum number of cached images

  // Cache management method
  static void _addToImageCache(String imageId, Widget imageWidget) {
    // If cache is full, remove oldest entries (FIFO)
    if (_imageCache.length >= _maxCacheSize) {
      final keysToRemove = _imageCache.keys
          .take(_imageCache.length - _maxCacheSize + 1)
          .toList();
      for (final key in keysToRemove) {
        _imageCache.remove(key);
      }
    }
    _imageCache[imageId] = imageWidget;
  }

  // Method to clear cache when needed
  static void clearImageCache() {
    _imageCache.clear();
  }

  @override
  void initState() {
    super.initState();
    _document = widget.initialDocument ??
        ParchmentDocument.fromJson([
          {"insert": "\n"}
        ]);
    _controller = FleatherController(document: _document);

    _controller.document.changes.listen((change) {
      widget.onDocumentChanged?.call(_controller.document);
      _document = _controller.document; // Keep internal _document in sync
    });

    // Listen to selection changes if a callback is provided
    if (widget.onSelectionChanged != null) {
      _selectionTimer =
          Timer.periodic(const Duration(milliseconds: 200), (timer) {
        if (mounted && _controller.selection != _lastSelection) {
          _lastSelection = _controller.selection;
          widget.onSelectionChanged!(_controller.selection);
        }
      });
    }
  }

  TextSelection? _lastSelection;

  @override
  void didUpdateWidget(RichTextEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialDocument != oldWidget.initialDocument &&
        widget.initialDocument != _document) {
      _document = widget.initialDocument ??
          ParchmentDocument.fromJson([
            {"insert": "\n"}
          ]);
      // Dispose old controller before creating a new one to prevent memory leaks
      _controller.dispose();
      _controller = FleatherController(document: _document);
      _controller.document.changes.listen((change) {
        widget.onDocumentChanged?.call(_controller.document);
        _document = _controller.document;
      });
    }
    // No need to explicitly handle readOnly or focusNode changes here,
    // FleatherEditor and _effectiveFocusNode handle them.
  }

  @override
  void dispose() {
    _selectionTimer?.cancel();
    _controller.dispose();
    _internalFocusNode.dispose();
    // Clear image cache when editor is disposed to prevent memory leaks
    clearImageCache();
    super.dispose();
  }

  ParchmentDocument get currentDocument => _controller.document;

  void updateDocument(ParchmentDocument newDocument) {
    // Dispose old controller before creating a new one
    _controller.dispose();
    _document = newDocument;
    _controller = FleatherController(document: _document);
    // Re-attach listener
    _controller.document.changes.listen((change) {
      widget.onDocumentChanged?.call(_controller.document);
      // Update internal _document as well, in case the change came from the controller itself
      this._document = _controller.document;
    });
  }

  void replaceText(int index, int length, Object data,
      {TextSelection? selection}) {
    _controller.replaceText(index, length, data, selection: selection);
  }

  TextSelection get selection => _controller.selection;

  void setSelection(TextSelection selection) {
    _controller.updateSelection(selection, source: ChangeSource.remote);
  }

  // Method to insert image (called from toolbar)
  Future<void> pickAndInsertImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 1000,
        maxHeight: 1000,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        // 检查文件大小
        final fileSize = await pickedFile.length();
        if (fileSize > 10 * 1024 * 1024) {
          // 10MB限制
          if (mounted) {
            SnackbarHelper.showError(
                context: context, message: '图片文件过大，请选择小于10MB的图片');
          }
          return;
        }

        // 验证文件类型
        final fileName = pickedFile.name.toLowerCase();
        if (!fileName.endsWith('.jpg') &&
            !fileName.endsWith('.jpeg') &&
            !fileName.endsWith('.png') &&
            !fileName.endsWith('.gif')) {
          if (mounted) {
            SnackbarHelper.showError(
                context: context, message: '不支持的图片格式，请选择JPG、PNG或GIF格式');
          }
          return;
        }

        dynamic imageData;
        String sourceType;

        if (kIsWeb) {
          try {
            final bytes = await pickedFile.readAsBytes();

            // 验证图片数据
            if (bytes.isEmpty) {
              throw Exception('图片文件为空');
            }

            final base64String = base64Encode(bytes);
            String mimeType = 'image/png'; // Default
            if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
              mimeType = 'image/jpeg';
            } else if (fileName.endsWith('.gif')) {
              mimeType = 'image/gif';
            }
            imageData = 'data:$mimeType;base64,$base64String';
            sourceType = 'local_web';
          } catch (e) {
            if (mounted) {
              SnackbarHelper.showError(
                  context: context, message: '读取图片文件失败: $e');
            }
            return;
          }
        } else {
          // 验证文件路径
          if (pickedFile.path.isEmpty) {
            if (mounted) {
              SnackbarHelper.showError(context: context, message: '获取图片路径失败');
            }
            return;
          }

          // 验证文件是否存在
          final file = File(pickedFile.path);
          if (!await file.exists()) {
            if (mounted) {
              SnackbarHelper.showError(context: context, message: '图片文件不存在');
            }
            return;
          }

          imageData = pickedFile.path;
          sourceType = 'local_file';
        }

        final selection = _controller.selection;
        _controller.replaceText(
          selection.baseOffset,
          selection.extentOffset - selection.baseOffset,
          EmbeddableObject('image', inline: false, data: {
            'source_type': sourceType,
            'source': imageData,
            'isLocal': true,
            'uploadPending': true,
          }),
          selection: selection,
        );
        _controller.replaceText(
          selection.baseOffset + 1,
          0,
          '\n',
          selection: TextSelection.collapsed(offset: selection.baseOffset + 2),
        );
        widget.onDocumentChanged?.call(_controller.document);
      }
    } catch (e) {
      print('Error picking image: $e');
      if (mounted) {
        SnackbarHelper.showError(context: context, message: '选择图片失败: $e');
      }
    }
  }

  // Method to insert table (called from toolbar)
  void insertTable({int rows = 3, int columns = 3}) {
    final List<List<String>> tableData = List.generate(
      rows,
      (i) => List.filled(columns, '', growable: true),
      growable: true,
    );
    for (int j = 0; j < columns; j++) {
      tableData[0][j] = '标题 ${j + 1}';
    }
    final String tableId = const Uuid().v4();
    final tableObject = EmbeddableObject('table', inline: false, data: {
      'rows': rows,
      'columns': columns,
      'tableData': tableData,
      'tableId': tableId,
      'rowHeights': List.filled(rows, 40.0),
      'columnWidths': List.filled(columns, 100.0),
    });
    final selection = _controller.selection;
    _controller.replaceText(
      selection.baseOffset,
      selection.extentOffset - selection.baseOffset,
      tableObject,
    );
    _controller.replaceText(
      selection.baseOffset + 1,
      0,
      '\n',
      selection: TextSelection.collapsed(offset: selection.baseOffset + 2),
    );
    widget.onDocumentChanged?.call(_controller.document);
  }

  // Embed builder
  Widget _embedBuilder(BuildContext context, EmbedNode node) {
    if (node.value.type == 'image') {
      final sourceType = node.value.data['source_type'];
      final source = node.value.data['source'];
      final String imageId = 'img-${node.offset}-${source.hashCode}';
      return RepaintBoundary(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Container(
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
              maxHeight: 300,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildImageBySourceType(sourceType, source, imageId),
            ),
          ),
        ),
      );
    }

    if (node.value.type == 'table') {
      final Map<String, dynamic> data =
          Map<String, dynamic>.from(node.value.data);
      final int rows = data['rows'] as int? ?? 3;
      final int columns = data['columns'] as int? ?? 3;
      String? tableId = findValueInMap(data, 'tableId');
      if (tableId == null) {
        tableId = const Uuid().v4();
        data['tableId'] = tableId;
      }
      List<List<String>> cellsData = [];
      if (data.containsKey('tableData') && data['tableData'] is List) {
        final List tableData = data['tableData'] as List;
        for (int i = 0; i < min(tableData.length, rows); i++) {
          final rowData = tableData[i];
          List<String> row = [];
          if (rowData is List) {
            for (int j = 0; j < min(rowData.length, columns); j++) {
              row.add((rowData[j]?.toString() ?? ''));
            }
          }
          while (row.length < columns) row.add('');
          cellsData.add(row);
        }
      }
      while (cellsData.length < rows) {
        cellsData.add(List.filled(columns, '', growable: true));
      }
      List<double> rowHeights = List.filled(rows, 40.0);
      List<double> columnWidths = List.filled(columns, 100.0);
      if (data.containsKey('rowHeights') && data['rowHeights'] is List) {
        final List savedRowHeights = data['rowHeights'] as List;
        for (int i = 0; i < min(savedRowHeights.length, rows); i++) {
          if (savedRowHeights[i] is num) {
            rowHeights[i] = (savedRowHeights[i] as num).toDouble();
          }
        }
      }
      if (data.containsKey('columnWidths') && data['columnWidths'] is List) {
        final List savedColumnWidths = data['columnWidths'] as List;
        for (int i = 0; i < min(savedColumnWidths.length, columns); i++) {
          if (savedColumnWidths[i] is num) {
            columnWidths[i] = (savedColumnWidths[i] as num).toDouble();
          }
        }
      }
      return _buildSimpleTable(context, rows, columns, cellsData, rowHeights,
          columnWidths, node, tableId,
          isEditingMode: !widget.readOnly);
    }
    return defaultFleatherEmbedBuilder(context, node);
  }

  Widget _buildImageBySourceType(
      String? sourceType, dynamic source, String imageId) {
    if (_imageCache.containsKey(imageId)) {
      return _imageCache[imageId]!;
    }
    Widget placeholder = Container(
      color: AppTheme.lightGrayColor.withOpacity(0.2),
      child: const Center(
          child: SizedBox(
              width: 40,
              height: 40,
              child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor:
                      AlwaysStoppedAnimation<Color>(AppTheme.primaryColor)))),
    );
    Widget errorWidget = Container(
      width: 200,
      height: 150,
      decoration: BoxDecoration(
          color: AppTheme.lightGrayColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.mediumGrayColor)),
      child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
        Icon(Icons.broken_image, color: AppTheme.darkGrayColor, size: 40),
        const SizedBox(height: 8),
        Text('图片加载失败', style: TextStyle(color: AppTheme.darkGrayColor)),
      ]),
    );

    try {
      if (sourceType == 'assets') {
        Widget imageWidget = Image(
            image: AssetImage(source),
            fit: BoxFit.contain,
            frameBuilder: (context, child, frame, wasSynchronouslyLoaded) =>
                (wasSynchronouslyLoaded || frame != null)
                    ? child
                    : AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: frame != null ? child : placeholder),
            errorBuilder: (context, error, stackTrace) => errorWidget);
        _imageCache[imageId] = imageWidget;
        return imageWidget;
      } else if (sourceType == 'file' || sourceType == 'local_file') {
        Widget imageWidget = Image.file(File(source),
            fit: BoxFit.contain,
            cacheWidth: 1000,
            frameBuilder: (context, child, frame, wasSynchronouslyLoaded) =>
                (wasSynchronouslyLoaded || frame != null)
                    ? child
                    : AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: frame != null ? child : placeholder),
            errorBuilder: (context, error, stackTrace) => errorWidget);
        _imageCache[imageId] = imageWidget;
        return imageWidget;
      } else if (sourceType == 'url') {
        Widget imageWidget = Image.network(source,
            fit: BoxFit.contain,
            cacheWidth: 1000,
            loadingBuilder: (context, child, loadingProgress) =>
                (loadingProgress == null) ? child : placeholder,
            errorBuilder: (context, error, stackTrace) => errorWidget);
        _imageCache[imageId] = imageWidget;
        return imageWidget;
      } else if (sourceType == 'local_web' &&
          source.toString().startsWith('data:')) {
        final bytes = UriData.parse(source).contentAsBytes();
        Widget imageWidget = Image.memory(bytes,
            fit: BoxFit.contain,
            cacheWidth: 1000,
            frameBuilder: (context, child, frame, wasSynchronouslyLoaded) =>
                (wasSynchronouslyLoaded || frame != null)
                    ? child
                    : AnimatedSwitcher(
                        duration: const Duration(milliseconds: 300),
                        child: frame != null ? child : placeholder),
            errorBuilder: (context, error, stackTrace) => errorWidget);
        _imageCache[imageId] = imageWidget;
        return imageWidget;
      } else if (sourceType == 'placeholder') {
        // 处理占位符图片
        Widget placeholderWidget = Container(
          width: 200,
          height: 150,
          decoration: BoxDecoration(
              color: AppTheme.lightGrayColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.mediumGrayColor)),
          child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Icon(Icons.image, color: AppTheme.darkGrayColor, size: 40),
            const SizedBox(height: 8),
            Text('图片占位符', style: TextStyle(color: AppTheme.darkGrayColor)),
            const SizedBox(height: 4),
            Text('保存时将处理图片',
                style: TextStyle(color: AppTheme.darkGrayColor, fontSize: 12)),
          ]),
        );
        _addToImageCache(imageId, placeholderWidget);
        return placeholderWidget;
      }
    } catch (e) {
      print('Error loading image in _buildImageBySourceType: $e');
    }
    _addToImageCache(imageId, errorWidget);
    return errorWidget;
  }

  Widget _buildSimpleTable(
      BuildContext context,
      int rows,
      int columns,
      List<List<String>> cells,
      List<double> rowHeights,
      List<double> columnWidths,
      EmbedNode node,
      String? tableId,
      {bool isEditingMode = true}) {
    final screenWidth = MediaQuery.of(context).size.width;
    double totalTableWidth = columnWidths.fold(0, (sum, width) => sum + width);
    totalTableWidth = min(totalTableWidth, screenWidth * 0.95);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Align(
        alignment: Alignment.center,
        child: Container(
          constraints: BoxConstraints(maxWidth: totalTableWidth),
          decoration: BoxDecoration(
              border: Border.all(color: AppTheme.lightGrayColor),
              borderRadius: BorderRadius.circular(8)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Table(
                  border:
                      TableBorder.all(color: AppTheme.lightGrayColor, width: 1),
                  defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                  columnWidths: Map.fromIterable(
                      List.generate(columns, (index) => index),
                      key: (i) => i,
                      value: (i) => FixedColumnWidth(columnWidths[i])),
                  children: List.generate(
                      rows,
                      (i) => TableRow(
                            decoration: i == 0
                                ? BoxDecoration(
                                    color: AppTheme.lightGrayColor
                                        .withOpacity(0.3))
                                : null,
                            children: List.generate(
                                columns,
                                (j) => Container(
                                      height: rowHeights[i],
                                      constraints: BoxConstraints(
                                          minHeight: rowHeights[i],
                                          maxHeight: rowHeights[i]),
                                      padding: const EdgeInsets.all(8.0),
                                      child: Center(
                                          child: Text(
                                              i < cells.length &&
                                                      j < cells[i].length
                                                  ? cells[i][j]
                                                  : '',
                                              textAlign: TextAlign.center,
                                              overflow: TextOverflow.ellipsis,
                                              style: i == 0
                                                  ? const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold)
                                                  : null)),
                                    )),
                          )),
                ),
              ),
              if (isEditingMode)
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton.icon(
                        icon: const Icon(Icons.edit, size: 16),
                        label: const Text('编辑表格'),
                        style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            minimumSize: Size.zero,
                            tapTargetSize: MaterialTapTargetSize.shrinkWrap),
                        onPressed: () {
                          if (tableId == null) {
                            SnackbarHelper.showError(
                                context: context, message: '无法编辑此表格（缺少标识符）');
                            return;
                          }
                          _showTableEditDialog(context, rows, columns, cells,
                              rowHeights, columnWidths, node, tableId);
                        },
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Method to show a dialog for inserting a new table
  Future<Map<String, int>?> _showInsertTableDialog(BuildContext context) async {
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    int rows = 3;
    int columns = 3;

    return await showDialog<Map<String, int>?>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('插入表格'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                TextFormField(
                  initialValue: rows.toString(),
                  decoration: const InputDecoration(labelText: '行数 (1-20)'),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入行数';
                    }
                    final n = int.tryParse(value);
                    if (n == null || n < 1 || n > 20) {
                      return '请输入1到20之间的数字';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    rows = int.parse(value!);
                  },
                ),
                TextFormField(
                  initialValue: columns.toString(),
                  decoration: const InputDecoration(labelText: '列数 (1-10)'),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入列数';
                    }
                    final n = int.tryParse(value);
                    if (n == null || n < 1 || n > 10) {
                      return '请输入1到10之间的数字';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    columns = int.parse(value!);
                  },
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(dialogContext).pop(null);
              },
            ),
            TextButton(
              child: const Text('确定'),
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  Navigator.of(dialogContext)
                      .pop({'rows': rows, 'columns': columns});
                }
              },
            ),
          ],
        );
      },
    );
  }

  void _showTableEditDialog(
      BuildContext context, // This is the RichTextEditor's context
      int initialRows,
      int initialColumns,
      List<List<String>> initialTableData,
      List<double> initialRowHeightsData,
      List<double> initialColumnWidthsData,
      EmbedNode node,
      String tableId) {
    // These will be mutable within the dialog
    int currentRows = initialRows;
    int currentColumns = initialColumns;
    // Deep copy of data to make it editable
    List<List<String>> editableData =
        initialTableData.map((row) => List<String>.from(row)).toList();
    List<double> editableRowHeights = List.from(initialRowHeightsData);
    List<double> editableColumnWidths = List.from(initialColumnWidthsData);

    List<List<TextEditingController>> controllers = List.generate(
      currentRows,
      (i) => List.generate(
        currentColumns,
        (j) => TextEditingController(
            text: (i < editableData.length && j < editableData[i].length)
                ? editableData[i][j]
                : ''),
        growable: true,
      ),
      growable: true,
    );

    // Helper to rebuild controllers and adjust data structures when rows/columns change
    // This will be called by stfSetState after currentRows/currentColumns are updated
    void _synchronizeEditableDataAndControllers() {
      // Adjust editableData rows
      while (editableData.length < currentRows) {
        editableData.add(List.filled(currentColumns, '', growable: true));
      }
      while (editableData.length > currentRows && editableData.isNotEmpty) {
        editableData.removeLast();
      }

      // Adjust editableData columns for each row
      for (int i = 0; i < editableData.length; i++) {
        while (editableData[i].length < currentColumns) {
          editableData[i].add('');
        }
        while (editableData[i].length > currentColumns &&
            editableData[i].isNotEmpty) {
          editableData[i].removeLast();
        }
      }

      // Adjust rowHeights
      while (editableRowHeights.length < currentRows) {
        editableRowHeights.add(40.0); // Default height
      }
      while (editableRowHeights.length > currentRows &&
          editableRowHeights.isNotEmpty) {
        editableRowHeights.removeLast();
      }

      // Adjust columnWidths
      while (editableColumnWidths.length < currentColumns) {
        editableColumnWidths.add(100.0); // Default width
      }
      while (editableColumnWidths.length > currentColumns &&
          editableColumnWidths.isNotEmpty) {
        editableColumnWidths.removeLast();
      }

      // Dispose old controllers
      for (var rowCtrl in controllers) {
        for (var ctrl in rowCtrl) {
          ctrl.dispose();
        }
      }
      // Rebuild controllers
      controllers = List.generate(
        currentRows,
        (i) => List.generate(
          currentColumns,
          (j) => TextEditingController(
              text: (i < editableData.length && j < editableData[i].length)
                  ? editableData[i][j]
                  : ''),
          growable: true,
        ),
        growable: true,
      );
    }

    showDialog(
      context: context, // Use RichTextEditor's context for showDialog
      builder: (BuildContext dialogContext) {
        // This is the dialog's own context
        return StatefulBuilder(builder: (stfContext, stfSetState) {
          return WillPopScope(
            onWillPop: () async {
              // Dispose all controllers when dialog is closed
              for (var rowCtrl in controllers) {
                for (var ctrl in rowCtrl) {
                  ctrl.dispose();
                }
              }
              return true;
            },
            child: Dialog(
              insetPadding: const EdgeInsets.all(16),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                    maxWidth: min(MediaQuery.of(dialogContext).size.width - 32,
                        800), // Use dialogContext here
                    maxHeight: min(
                        MediaQuery.of(dialogContext).size.height - 80,
                        600)), // Use dialogContext here
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(children: [
                        const Text('编辑表格',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.bold)),
                        const Spacer(),
                        IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () => Navigator.of(dialogContext)
                                .pop()), // Use dialogContext here
                      ]),
                    ),
                    const Divider(),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    ElevatedButton.icon(
                                      icon:
                                          const Icon(Icons.add_circle_outline),
                                      label: const Text('添加行'),
                                      style: ElevatedButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8)),
                                      onPressed: () {
                                        stfSetState(() {
                                          currentRows++;
                                          _synchronizeEditableDataAndControllers();
                                        });
                                      },
                                    ),
                                    ElevatedButton.icon(
                                      icon: const Icon(
                                          Icons.remove_circle_outline),
                                      label: const Text('删除行'),
                                      style: ElevatedButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8)),
                                      onPressed: currentRows > 1
                                          ? () {
                                              stfSetState(() {
                                                currentRows--;
                                                _synchronizeEditableDataAndControllers();
                                              });
                                            }
                                          : null, // Disable if only one row
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    ElevatedButton.icon(
                                      icon:
                                          const Icon(Icons.add_circle_outline),
                                      label: const Text('添加列'),
                                      style: ElevatedButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8)),
                                      onPressed: () {
                                        stfSetState(() {
                                          currentColumns++;
                                          _synchronizeEditableDataAndControllers();
                                        });
                                      },
                                    ),
                                    ElevatedButton.icon(
                                      icon: const Icon(
                                          Icons.remove_circle_outline),
                                      label: const Text('删除列'),
                                      style: ElevatedButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8)),
                                      onPressed: currentColumns > 1
                                          ? () {
                                              stfSetState(() {
                                                currentColumns--;
                                                _synchronizeEditableDataAndControllers();
                                              });
                                            }
                                          : null, // Disable if only one column
                                    ),
                                  ],
                                ),
                              ),
                              const Divider(),
                              if (currentRows > 0 && currentColumns > 0)
                                Row(children: [
                                  Expanded(
                                      child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                        const Text('行高设置:',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold)),
                                        const SizedBox(height: 8),
                                        Row(children: [
                                          const Text('第1行(标题): '),
                                          Expanded(
                                              child: Slider(
                                                  value: editableRowHeights
                                                          .isNotEmpty
                                                      ? editableRowHeights[0]
                                                      : 40.0,
                                                  min: 30,
                                                  max: 100,
                                                  divisions: 14,
                                                  label: (editableRowHeights
                                                              .isNotEmpty
                                                          ? editableRowHeights[
                                                              0]
                                                          : 40.0)
                                                      .round()
                                                      .toString(),
                                                  onChanged: (value) {
                                                    stfSetState(() {
                                                      if (editableRowHeights
                                                          .isNotEmpty)
                                                        editableRowHeights[0] =
                                                            value;
                                                    });
                                                  })),
                                          Text(
                                              '${(editableRowHeights.isNotEmpty ? editableRowHeights[0] : 40.0).round()}px')
                                        ]),
                                        if (currentRows > 1)
                                          Row(children: [
                                            const Text('数据行: '),
                                            Expanded(
                                                child: Slider(
                                                    value: editableRowHeights
                                                                .length >
                                                            1
                                                        ? editableRowHeights[1]
                                                        : 40.0,
                                                    min: 30,
                                                    max: 100,
                                                    divisions: 14,
                                                    label: (editableRowHeights
                                                                    .length >
                                                                1
                                                            ? editableRowHeights[
                                                                1]
                                                            : 40.0)
                                                        .round()
                                                        .toString(),
                                                    onChanged: (value) {
                                                      stfSetState(() {
                                                        for (int i = 1;
                                                            i < currentRows;
                                                            i++) {
                                                          if (i <
                                                              editableRowHeights
                                                                  .length)
                                                            editableRowHeights[
                                                                i] = value;
                                                        }
                                                      });
                                                    })),
                                            Text(
                                                '${(editableRowHeights.length > 1 ? editableRowHeights[1] : 40.0).round()}px')
                                          ])
                                      ])),
                                  const SizedBox(width: 16),
                                  Expanded(
                                      child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                        const Text('列宽设置:',
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold)),
                                        const SizedBox(height: 8),
                                        Row(children: [
                                          const Text('所有列: '),
                                          Expanded(
                                              child: Slider(
                                                  value: editableColumnWidths
                                                          .isNotEmpty
                                                      ? editableColumnWidths[0]
                                                      : 100.0,
                                                  min: 60,
                                                  max: 200,
                                                  divisions: 14,
                                                  label: (editableColumnWidths
                                                              .isNotEmpty
                                                          ? editableColumnWidths[
                                                              0]
                                                          : 100.0)
                                                      .round()
                                                      .toString(),
                                                  onChanged: (value) {
                                                    stfSetState(() {
                                                      for (int i = 0;
                                                          i < currentColumns;
                                                          i++) {
                                                        if (i <
                                                            editableColumnWidths
                                                                .length)
                                                          editableColumnWidths[
                                                              i] = value;
                                                      }
                                                    });
                                                  })),
                                          Text(
                                              '${(editableColumnWidths.isNotEmpty ? editableColumnWidths[0] : 100.0).round()}px')
                                        ])
                                      ]))
                                ]),
                              const SizedBox(height: 20),
                              const Text('表格内容:',
                                  style:
                                      TextStyle(fontWeight: FontWeight.bold)),
                              const SizedBox(height: 8),
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: SingleChildScrollView(
                                  child: Table(
                                    border: TableBorder.all(),
                                    defaultVerticalAlignment:
                                        TableCellVerticalAlignment.middle,
                                    columnWidths: Map.fromIterable(
                                        List.generate(
                                            currentColumns, (index) => index),
                                        key: (i) => i,
                                        value: (i) => FixedColumnWidth(
                                            i < editableColumnWidths.length
                                                ? editableColumnWidths[i]
                                                : 100.0)),
                                    children: List.generate(
                                        currentRows,
                                        (i) => TableRow(
                                              decoration: i == 0
                                                  ? BoxDecoration(
                                                      color: AppTheme
                                                          .lightGrayColor
                                                          .withOpacity(0.3))
                                                  : null,
                                              children: List.generate(
                                                  currentColumns,
                                                  (j) => Container(
                                                        height: i <
                                                                editableRowHeights
                                                                    .length
                                                            ? editableRowHeights[
                                                                i]
                                                            : 40.0,
                                                        padding:
                                                            const EdgeInsets
                                                                .all(4),
                                                        child: Center(
                                                            child: TextField(
                                                          controller: (i <
                                                                      controllers
                                                                          .length &&
                                                                  j <
                                                                      controllers[
                                                                              i]
                                                                          .length)
                                                              ? controllers[i]
                                                                  [j]
                                                              : null, // Don't create unmanaged controllers
                                                          textAlign:
                                                              TextAlign.center,
                                                          style: i == 0
                                                              ? const TextStyle(
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold)
                                                              : null,
                                                          decoration:
                                                              const InputDecoration(
                                                                  border:
                                                                      InputBorder
                                                                          .none,
                                                                  contentPadding:
                                                                      EdgeInsets
                                                                          .all(
                                                                              4),
                                                                  isDense:
                                                                      true),
                                                          expands: true,
                                                          maxLines: null,
                                                          minLines: null,
                                                        )),
                                                      )),
                                            )),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const Divider(),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            OutlinedButton(
                                onPressed: () => Navigator.of(dialogContext)
                                    .pop(), // Use dialogContext here
                                child: const Text('取消')),
                            const SizedBox(width: 8),
                            ElevatedButton(
                                onPressed: () {
                                  // Save current text from controllers to editableData before closing
                                  for (int i = 0; i < currentRows; i++) {
                                    if (i >= editableData.length)
                                      editableData.add(List.filled(
                                          currentColumns,
                                          '')); // Should be handled by _synchronize
                                    for (int j = 0; j < currentColumns; j++) {
                                      if (j >= editableData[i].length)
                                        editableData[i].add(
                                            ''); // Should be handled by _synchronize
                                      if (i < controllers.length &&
                                          j < controllers[i].length) {
                                        editableData[i][j] =
                                            controllers[i][j].text;
                                      } else {
                                        // This case should ideally not happen if _synchronizeEditableDataAndControllers is correct
                                        editableData[i][j] = '';
                                      }
                                    }
                                  }
                                  Navigator.of(dialogContext)
                                      .pop(); // Use dialogContext here
                                  _updateTableInDocument(
                                      editableData,
                                      currentRows,
                                      currentColumns,
                                      editableRowHeights,
                                      editableColumnWidths,
                                      tableId);
                                },
                                child: const Text('保存')),
                          ]),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      },
    ).then((_) {
      // Dispose all controllers when dialog is closed (fallback)
      for (var rowCtrl in controllers) {
        for (var ctrl in rowCtrl) {
          try {
            ctrl.dispose();
          } catch (e) {
            // Controller already disposed, ignore
          }
        }
      }
    });
  }

  void _updateTableInDocument(
      List<List<String>> tableData,
      int rows,
      int columns,
      List<double> rowHeights,
      List<double> columnWidths,
      String tableId) {
    try {
      final deltaOperations = _document.toDelta().toList();
      int tableIndex = -1;
      int targetOffset = 0;

      for (int i = 0; i < deltaOperations.length; i++) {
        final op = deltaOperations[i];
        if (op.isInsert && op.data is Map<String, dynamic>) {
          final opData = op.data as Map<String, dynamic>;
          String? foundTableId = findValueInMap(opData, 'tableId');
          if (foundTableId == tableId) {
            tableIndex = i;
            break;
          }
        }
        targetOffset += op.length;
      }

      if (tableIndex >= 0) {
        // Recalculate targetOffset accurately
        targetOffset = 0;
        for (int i = 0; i < tableIndex; i++) {
          targetOffset += deltaOperations[i].length;
        }

        final tableObject = EmbeddableObject('table', inline: false, data: {
          'rows': rows,
          'columns': columns,
          'tableData': tableData,
          'rowHeights': rowHeights.map((h) => h.roundToDouble()).toList(),
          'columnWidths': columnWidths.map((w) => w.roundToDouble()).toList(),
          'tableId': tableId,
        });
        _controller.replaceText(targetOffset, 1, tableObject);
        widget.onDocumentChanged?.call(_controller.document);
        if (mounted) {
          SnackbarHelper.showSuccess(context: context, message: '表格已更新');
        }
      } else {
        if (mounted) {
          SnackbarHelper.showError(context: context, message: '无法找到原始表格进行更新。');
        }
      }
    } catch (e, stackTrace) {
      print('Error updating table: $e\n$stackTrace');
      if (mounted) {
        SnackbarHelper.showError(context: context, message: '表格更新失败: $e');
      }
    }
  }

  // Helper to find a value in a nested map
  String? findValueInMap(Map<String, dynamic> map, String keyToFind) {
    if (map.containsKey(keyToFind)) {
      return map[keyToFind]?.toString();
    }
    for (var value in map.values) {
      if (value is Map<String, dynamic>) {
        String? found = findValueInMap(value, keyToFind);
        if (found != null) return found;
      } else if (value is List) {
        for (var item in value) {
          if (item is Map<String, dynamic>) {
            String? found = findValueInMap(item, keyToFind);
            if (found != null) return found;
          }
        }
      }
    }
    return null;
  }

  Widget buildToolbar(BuildContext context) {
    // 判断是否为Web环境
    if (kIsWeb) {
      // Web环境下使用原有的工具栏实现
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          border: Border(
            top: BorderSide(
              color: Theme.of(context).dividerColor,
              width: 1,
            ),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min, // Ensure column takes minimum space
          children: [
            // 使用基本工具栏
            FleatherToolbar.basic(controller: _controller),
            // 添加自定义按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 自定义按钮：插入图片
                IconButton(
                  icon: const Icon(Icons.image, size: 20),
                  onPressed: () => pickAndInsertImage(
                      ImageSource.gallery), // Or ImageSource.camera
                  tooltip: '插入图片',
                ),
                // 自定义按钮：插入表格
                IconButton(
                  icon: const Icon(Icons.table_chart, size: 20),
                  onPressed: () async {
                    final result = await _showInsertTableDialog(context);
                    if (result != null) {
                      insertTable(
                          rows: result['rows']!, columns: result['columns']!);
                    }
                  },
                  tooltip: '插入表格',
                ),
                // 自定义按钮：粘贴富文本
                if (widget.onPaste != null)
                  IconButton(
                    icon: Icon(
                      Icons.content_paste,
                      size: 20,
                      color: widget.isPasting ? Colors.grey : null,
                    ),
                    onPressed:
                        widget.isPasting ? null : () => widget.onPaste?.call(),
                    tooltip: widget.isPasting ? '正在粘贴...' : '粘贴富文本',
                  ),
              ],
            ),
            if (widget.showAiSuggestionsHint)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.lightbulb_outline,
                      size: 14,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '输入时按Tab键接受智能预测',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.primaryColor,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      );
    } else {
      // 移动设备环境下使用滚动工具栏
      return LayoutBuilder(
        builder: (context, constraints) {
          // 获取屏幕宽度，用于响应式布局
          final screenWidth = MediaQuery.of(context).size.width;
          final isSmallScreen = screenWidth < 600; // 定义小屏幕的阈值

          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Column(
              mainAxisSize:
                  MainAxisSize.min, // Ensure column takes minimum space
              children: [
                // 使用SingleChildScrollView包装工具栏，使其可以在小屏幕上滚动
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    // 在小屏幕上减小内边距
                    padding:
                        EdgeInsets.symmetric(horizontal: isSmallScreen ? 4 : 8),
                    // 使用FleatherToolbar.basic
                    child: FleatherToolbar.basic(controller: _controller),
                  ),
                ),
                // 添加自定义按钮 - 使用Wrap替代Row，实现自动换行
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Wrap(
                    alignment: WrapAlignment.center,
                    spacing: 8, // 水平间距
                    runSpacing: 8, // 垂直间距
                    children: [
                      // 自定义按钮：插入图片
                      IconButton(
                        icon: const Icon(Icons.image, size: 20),
                        onPressed: () => pickAndInsertImage(
                            ImageSource.gallery), // Or ImageSource.camera
                        tooltip: '插入图片',
                        constraints: BoxConstraints.tightFor(
                            width: 40, height: 40), // 固定大小
                      ),
                      // 自定义按钮：插入表格
                      IconButton(
                        icon: const Icon(Icons.table_chart, size: 20),
                        onPressed: () async {
                          final result = await _showInsertTableDialog(context);
                          if (result != null) {
                            insertTable(
                                rows: result['rows']!,
                                columns: result['columns']!);
                          }
                        },
                        tooltip: '插入表格',
                        constraints: BoxConstraints.tightFor(
                            width: 40, height: 40), // 固定大小
                      ),
                      // 自定义按钮：粘贴富文本
                      if (widget.onPaste != null)
                        IconButton(
                          icon: Icon(
                            Icons.content_paste,
                            size: 20,
                            color: widget.isPasting ? Colors.grey : null,
                          ),
                          onPressed: widget.isPasting
                              ? null
                              : () => widget.onPaste?.call(),
                          tooltip: widget.isPasting ? '正在粘贴...' : '粘贴富文本',
                          constraints: const BoxConstraints.tightFor(
                              width: 40, height: 40), // 固定大小
                        ),
                    ],
                  ),
                ),
                if (widget.showAiSuggestionsHint)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.lightbulb_outline,
                          size: 14,
                          color: AppTheme.primaryColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '输入时按Tab键接受智能预测',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.primaryColor,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.readOnly) {
      // Read-only mode: FleatherEditor should not be Expanded and should not expand itself.
      // It should size itself based on content within the SingleChildScrollView in EditorPage.
      return FleatherEditor(
        controller: _controller,
        focusNode: _effectiveFocusNode,
        readOnly: true,
        padding: const EdgeInsets.all(16),
        scrollable:
            true, // FleatherEditor itself can scroll its content if it's too long
        expands:
            false, // Crucial: set to false so height is determined by content
        showCursor: false,
        enableInteractiveSelection:
            true, // Allow text selection in read-only mode
        embedBuilder: _embedBuilder,
      );
    } else {
      // Editing mode: maintain the original structure with Toolbar
      return Column(
        children: [
          Expanded(
            child: FleatherEditor(
              controller: _controller,
              focusNode: _effectiveFocusNode,
              readOnly: false,
              padding: const EdgeInsets.all(16),
              scrollable: true,
              expands:
                  true, // In edit mode, editor typically fills available space
              showCursor: true,
              enableInteractiveSelection: true,
              embedBuilder: _embedBuilder,
            ),
          ),
          buildToolbar(context), // Toolbar is only shown in edit mode
        ],
      );
    }
  }
}

// Helper function to provide default Fleather embed builder if needed elsewhere
Widget defaultFleatherEmbedBuilder(BuildContext context, EmbedNode node) {
  // This is a simplified version. You might need to adjust based on Fleather's actual default.
  if (node.value.type == 'hr') {
    return const Divider(height: 1, thickness: 1, color: Colors.grey);
  }
  // Add other default embed types if necessary
  return Container(
    padding: const EdgeInsets.all(8),
    color: Colors.grey.shade200,
    child: Text('Unsupported embed: ${node.value.type}'),
  );
}
