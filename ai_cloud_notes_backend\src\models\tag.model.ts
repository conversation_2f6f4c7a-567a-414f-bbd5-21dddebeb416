import mongoose, { Document, Schema } from 'mongoose';

/**
 * 标签文档接口
 */
export interface ITagDocument extends Document {
  name: string;
  color: string;
  owner: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 标签模式定义
 */
const TagSchema = new Schema(
  {
    // 标签名称
    name: {
      type: String,
      required: [true, '标签名称是必填项'],
      trim: true,
      maxlength: [30, '标签名称最多30个字符'],
    },
    
    // 标签颜色（CSS颜色格式）
    color: {
      type: String,
      default: '#5D5FEF', // 默认主题色
    },
    
    // 标签所有者
    owner: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    // 自动创建createdAt和updatedAt字段
    timestamps: true,
  }
);

// 索引设置，提高查询性能
TagSchema.index({ owner: 1 });
TagSchema.index({ name: 1, owner: 1 }, { unique: true }); // 对于同一用户，标签名称必须唯一

/**
 * 定义标签模型
 */
const Tag = mongoose.model<ITagDocument>('Tag', TagSchema);

export default Tag; 