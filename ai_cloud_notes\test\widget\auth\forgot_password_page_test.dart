import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ai_cloud_notes/screens/auth/forgot_password_page.dart';
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import '../../mocks/mock_api_service.dart';
import '../../test_config/test_helpers.dart';
import '../../test_config/test_app.dart';

void main() {
  group('ForgotPasswordPage 组件测试', () {
    late MockApiService mockApiService;
    late AuthProvider authProvider;

    setUp(() {
      mockApiService = MockApiService();
      authProvider = AuthProvider(mockApiService);
    });

    tearDown(() {
      mockApiService.reset();
    });

    Widget createForgotPasswordPage() {
      return TestApp(
        mockApiService: mockApiService,
        mockAuthProvider: authProvider,
        home: const ForgotPasswordPage(),
      );
    }

    group('UI渲染测试', () {
      testWidgets('应该正确渲染忘记密码页面的初始状态', (WidgetTester tester) async {
        await tester.pumpWidget(createForgotPasswordPage());

        // 验证AppBar标题
        TestHelpers.expectAppBarTitle('忘记密码');

        // 验证初始表单字段（只有邮箱字段）
        expect(find.byType(TextFormField), findsOneWidget);

        // 验证按钮
        TestHelpers.expectTextExists('发送重置邮件');
        TestHelpers.expectTextExists('返回登录');

        // 验证字段标签
        TestHelpers.expectTextExists('邮箱');
        TestHelpers.expectTextExists('请输入注册时使用的邮箱地址');
      });

      testWidgets('发送重置邮件后应该显示重置密码表单', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createForgotPasswordPage());

        // 输入邮箱
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');

        // 点击发送重置邮件按钮
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待状态更新
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 验证重置密码表单显示
        expect(find.byType(TextFormField), findsNWidgets(3)); // 邮箱、令牌、新密码

        // 验证新的字段标签
        TestHelpers.expectTextExists('重置令牌');
        TestHelpers.expectTextExists('新密码');

        // 验证新的按钮
        TestHelpers.expectTextExists('重置密码');
      });
    });

    group('表单验证测试', () {
      testWidgets('空邮箱应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createForgotPasswordPage());

        // 点击发送重置邮件按钮而不输入邮箱
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请输入邮箱地址');
      });

      testWidgets('无效邮箱应该显示验证错误', (WidgetTester tester) async {
        await tester.pumpWidget(createForgotPasswordPage());

        // 输入无效邮箱
        await TestHelpers.enterText(tester, 'email_field', 'invalid-email');

        // 点击发送重置邮件按钮
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 验证错误消息
        TestHelpers.expectFormValidationError('请输入有效的邮箱地址');
      });

      testWidgets('重置密码表单验证测试', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createForgotPasswordPage());

        // 先发送重置邮件
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待重置表单显示
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 测试空令牌验证
        await TestHelpers.tapButton(tester, '重置密码');
        await TestHelpers.waitForAsync(tester);
        TestHelpers.expectFormValidationError('请输入重置令牌');

        // 测试空新密码验证
        await TestHelpers.enterText(tester, 'token_field', 'valid_reset_token');
        await TestHelpers.tapButton(tester, '重置密码');
        await TestHelpers.waitForAsync(tester);
        TestHelpers.expectFormValidationError('请输入新密码');

        // 测试短密码验证
        await TestHelpers.enterText(tester, 'new_password_field', '123');
        await TestHelpers.tapButton(tester, '重置密码');
        await TestHelpers.waitForAsync(tester);
        TestHelpers.expectFormValidationError('密码长度至少为6位');
      });
    });

    group('发送重置邮件功能测试', () {
      testWidgets('发送重置邮件成功应该显示成功消息', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createForgotPasswordPage());

        // 输入有效邮箱
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');

        // 点击发送重置邮件按钮
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待成功处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 验证成功消息（通过SnackBar显示）
        await tester.pump(const Duration(milliseconds: 100));
      });

      testWidgets('发送重置邮件失败应该显示错误消息', (WidgetTester tester) async {
        // 设置API返回失败
        mockApiService.shouldSucceed = false;

        await tester.pumpWidget(createForgotPasswordPage());

        // 输入邮箱
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');

        // 点击发送重置邮件按钮
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待错误处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });

      testWidgets('网络错误应该显示相应错误消息', (WidgetTester tester) async {
        // 设置网络错误
        mockApiService.shouldSucceed = false;
        mockApiService.forceErrorType = 'network';

        await tester.pumpWidget(createForgotPasswordPage());

        // 输入邮箱
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');

        // 点击发送重置邮件按钮
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待错误处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });
    });

    group('重置密码功能测试', () {
      testWidgets('重置密码成功应该显示成功消息并导航', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createForgotPasswordPage());

        // 先发送重置邮件
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待重置表单显示
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 输入重置信息
        await TestHelpers.enterText(tester, 'token_field', 'valid_reset_token');
        await TestHelpers.enterText(tester, 'new_password_field', 'newpassword123');

        // 点击重置密码按钮
        await TestHelpers.tapButton(tester, '重置密码');
        await TestHelpers.waitForAsync(tester);

        // 等待成功处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });

      testWidgets('无效令牌应该显示错误消息', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createForgotPasswordPage());

        // 先发送重置邮件
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待重置表单显示
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 输入无效令牌
        await TestHelpers.enterText(tester, 'token_field', 'invalid_token');
        await TestHelpers.enterText(tester, 'new_password_field', 'newpassword123');

        // 点击重置密码按钮
        await TestHelpers.tapButton(tester, '重置密码');
        await TestHelpers.waitForAsync(tester);

        // 等待错误处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });

      testWidgets('重置密码失败应该显示错误消息', (WidgetTester tester) async {
        // 设置API返回失败
        mockApiService.shouldSucceed = false;

        await tester.pumpWidget(createForgotPasswordPage());

        // 先发送重置邮件（这次要成功）
        mockApiService.shouldSucceed = true;
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待重置表单显示
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 设置重置密码失败
        mockApiService.shouldSucceed = false;

        // 输入重置信息
        await TestHelpers.enterText(tester, 'token_field', 'valid_reset_token');
        await TestHelpers.enterText(tester, 'new_password_field', 'newpassword123');

        // 点击重置密码按钮
        await TestHelpers.tapButton(tester, '重置密码');
        await TestHelpers.waitForAsync(tester);

        // 等待错误处理
        await TestHelpers.waitForAsync(tester, milliseconds: 100);
      });
    });

    group('用户交互测试', () {
      testWidgets('点击返回登录应该导航到登录页面', (WidgetTester tester) async {
        await tester.pumpWidget(createForgotPasswordPage());

        // 点击返回登录链接
        await TestHelpers.tapButton(tester, '返回登录');
        await TestHelpers.waitForAnimations(tester);

        // 验证导航
      });

      testWidgets('应该能够输入邮箱', (WidgetTester tester) async {
        await tester.pumpWidget(createForgotPasswordPage());

        // 输入邮箱
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        expect(find.text('<EMAIL>'), findsOneWidget);
      });

      testWidgets('新密码字段应该是隐藏的', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createForgotPasswordPage());

        // 先发送重置邮件
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待重置表单显示
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 查找新密码字段
        final passwordField = find.byKey(const Key('new_password_field'));
        expect(passwordField, findsOneWidget);

        // 验证密码字段是隐藏的
        final textFormField = tester.widget<TextFormField>(passwordField);
        expect(textFormField.obscureText, isTrue);
      });
    });

    group('状态管理测试', () {
      testWidgets('加载状态应该禁用按钮', (WidgetTester tester) async {
        // 设置延迟以观察加载状态
        mockApiService.shouldDelay = true;
        mockApiService.delayMilliseconds = 200;

        await tester.pumpWidget(createForgotPasswordPage());

        // 输入邮箱
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');

        // 点击发送重置邮件按钮
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester, milliseconds: 50);

        // 验证加载状态
        TestHelpers.expectLoadingIndicator();

        // 等待操作完成
        await TestHelpers.waitForAsync(tester, milliseconds: 300);
      });

      testWidgets('应该正确响应AuthProvider状态变化', (WidgetTester tester) async {
        await tester.pumpWidget(createForgotPasswordPage());

        // 验证初始状态
        expect(authProvider.isLoading, isFalse);
        expect(authProvider.error, isNull);

        // 模拟状态变化
        mockApiService.shouldSucceed = true;
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.tapButton(tester, '发送重置邮件');

        // 等待状态更新
        await TestHelpers.waitForProviderUpdate(tester);
      });

      testWidgets('页面状态应该正确切换', (WidgetTester tester) async {
        // 设置API返回成功
        mockApiService.shouldSucceed = true;

        await tester.pumpWidget(createForgotPasswordPage());

        // 验证初始状态（只有邮箱表单）
        expect(find.byType(TextFormField), findsOneWidget);
        TestHelpers.expectTextExists('发送重置邮件');

        // 发送重置邮件
        await TestHelpers.enterText(tester, 'email_field', '<EMAIL>');
        await TestHelpers.tapButton(tester, '发送重置邮件');
        await TestHelpers.waitForAsync(tester);

        // 等待状态切换
        await TestHelpers.waitForAsync(tester, milliseconds: 100);

        // 验证切换后状态（重置密码表单）
        expect(find.byType(TextFormField), findsNWidgets(3));
        TestHelpers.expectTextExists('重置密码');
      });
    });
  });
}
