import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import mongoose from 'mongoose';
import Note from '../models/note.model';
import { logger } from '../utils/logger';
import redisClient from '../utils/redis';

// 搜索历史在Redis中的键前缀
const SEARCH_HISTORY_KEY_PREFIX = 'search:history:';
// 热门搜索在Redis中的键
const POPULAR_SEARCH_KEY = 'search:popular';
// 每个用户最多保存的搜索历史数量
const MAX_SEARCH_HISTORY = 20;
// 热门搜索保留的数量
const MAX_POPULAR_SEARCH = 10;

// 重新创建一些 Redis 方法来匹配我们控制器中的使用
const redis = {
  zrem: (key: string, value: string) => redisClient.zrem(key, value),
  zadd: (key: string, score: number, value: string) => redisClient.zadd(key, score, value),
  zremrangebyrank: (key: string, start: number, stop: number) => redisClient.zremrangebyrank(key, start, stop),
  zincrby: (key: string, increment: number, value: string) => redisClient.zincrby(key, increment, value),
  zrevrange: (key: string, start: number, stop: number, withScores?: string) => {
    if (withScores === 'WITHSCORES') {
      return redisClient.zrevrange(key, start, stop, 'WITHSCORES');
    }
    return redisClient.zrevrange(key, start, stop);
  },
  zscore: (key: string, value: string) => redisClient.zscore(key, value),
  del: (key: string) => redisClient.del(key)
};

/**
 * 高级搜索笔记
 * GET /api/search/advanced
 */
export const advancedSearch = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    // 提取查询参数
    const {
      q,
      page = 1,
      limit = 20,
      searchIn = 'all',
      startDate,
      endDate,
      tags,
      favorite
    } = req.query;

    // 构建查询条件
    const query: any = { owner: userId };

    // 处理文本搜索
    if (q && typeof q === 'string' && q.trim()) {
      // 根据searchIn参数决定搜索范围
      if (searchIn === 'title') {
        query.title = { $regex: q, $options: 'i' };
      } else if (searchIn === 'content') {
        query.content = { $regex: q, $options: 'i' };
      } else {
        // 默认在标题和内容中搜索
        query.$or = [
          { title: { $regex: q, $options: 'i' } },
          { content: { $regex: q, $options: 'i' } }
        ];
      }

      // 如果是有效的查询，记录到搜索历史
      await addToSearchHistory(userId, q);
    }

    // 处理日期范围
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate as string);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate as string);
      }
    }

    // 处理标签筛选
    if (tags) {
      const tagIds = (tags as string).split(',');
      if (tagIds.length > 0) {
        query.tags = { $in: tagIds };
      }
    }

    // 处理收藏筛选
    if (favorite === 'true') {
      query.isFavorite = true;
    }

    // 计算分页参数
    const pageNumber = parseInt(page as string, 10);
    const limitNumber = parseInt(limit as string, 10);
    const skip = (pageNumber - 1) * limitNumber;

    // 查询符合条件的笔记数量
    const total = await Note.countDocuments(query);

    // 查询笔记列表
    const notes = await Note.find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limitNumber)
      .populate('tags', 'name color');

    return res.status(200).json({
      success: true,
      data: {
        notes,
        pagination: {
          total,
          page: pageNumber,
          limit: limitNumber,
          pages: Math.ceil(total / limitNumber)
        }
      }
    });
  } catch (error: any) {
    logger.error(`高级搜索失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '搜索失败，请稍后再试'
      }
    });
  }
};

/**
 * 添加查询到搜索历史
 * @param userId 用户ID
 * @param query 搜索关键词
 */
async function addToSearchHistory(userId: string, query: string) {
  try {
    const key = `${SEARCH_HISTORY_KEY_PREFIX}${userId}`;
    
    // 检查历史中是否已存在该查询
    // 如果存在，则先删除它（将在后面重新添加到最前面）
    await redis.zrem(key, query);
    
    // 添加到有序集合，使用当前时间戳作为分数
    const timestamp = Date.now();
    await redis.zadd(key, timestamp, query);
    
    // 保持历史记录不超过最大数量
    await redis.zremrangebyrank(key, 0, -(MAX_SEARCH_HISTORY + 1));
    
    // 更新热门搜索
    await redis.zincrby(POPULAR_SEARCH_KEY, 1, query);
    
    // 保持热门搜索不超过最大数量
    await redis.zremrangebyrank(POPULAR_SEARCH_KEY, 0, -(MAX_POPULAR_SEARCH + 1));
  } catch (error) {
    logger.error(`添加搜索历史失败: ${error}`);
    // 这里不抛出错误，让搜索继续进行
  }
}

/**
 * 获取用户搜索历史
 * GET /api/search/history
 */
export const getSearchHistory = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { limit = 10 } = req.query;
    const limitNumber = Math.min(parseInt(limit as string, 10), MAX_SEARCH_HISTORY);
    const key = `${SEARCH_HISTORY_KEY_PREFIX}${userId}`;
    
    // 从Redis中获取搜索历史
    const searchHistory = await redis.zrevrange(key, 0, limitNumber - 1);
    
    // 为每条历史记录添加时间戳
    const formattedHistory = await Promise.all(
      searchHistory.map(async (query) => {
        const timestamp = await redis.zscore(key, query);
        return {
          query,
          timestamp: timestamp ? parseInt(timestamp) : Date.now()
        };
      })
    );

    return res.status(200).json({
      success: true,
      data: {
        history: formattedHistory
      }
    });
  } catch (error: any) {
    logger.error(`获取搜索历史失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取搜索历史失败，请稍后再试'
      }
    });
  }
};

/**
 * 添加搜索历史
 * POST /api/search/history
 */
export const addSearchHistory = async (req: Request, res: Response) => {
  try {
    // 验证请求数据
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const { query } = req.body;
    
    if (!query || typeof query !== 'string' || !query.trim()) {
      return res.status(400).json({
        success: false,
        error: {
          message: '搜索关键词不能为空'
        }
      });
    }

    // 添加到搜索历史
    await addToSearchHistory(userId, query.trim());

    return res.status(201).json({
      success: true,
      message: '搜索历史添加成功'
    });
  } catch (error: any) {
    logger.error(`添加搜索历史失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '添加搜索历史失败，请稍后再试'
      }
    });
  }
};

/**
 * 清除搜索历史
 * DELETE /api/search/history
 */
export const clearSearchHistory = async (req: Request, res: Response) => {
  try {
    // 从认证中间件获取用户ID
    // @ts-ignore
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          message: '未授权操作'
        }
      });
    }

    const key = `${SEARCH_HISTORY_KEY_PREFIX}${userId}`;
    
    // 删除Redis中的搜索历史
    await redis.del(key);
    
    return res.status(200).json({
      success: true,
      message: '搜索历史已清除'
    });
  } catch (error: any) {
    logger.error(`清除搜索历史失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '清除搜索历史失败，请稍后再试'
      }
    });
  }
};

/**
 * 获取热门搜索
 * GET /api/search/popular
 */
export const getPopularSearches = async (req: Request, res: Response) => {
  try {
    const { limit = 10 } = req.query;
    const limitNumber = Math.min(parseInt(limit as string, 10), MAX_POPULAR_SEARCH);
    
    // 从Redis中获取热门搜索
    const popularSearches = await redis.zrevrange(POPULAR_SEARCH_KEY, 0, limitNumber - 1, 'WITHSCORES');
    
    // 将结果转换为对象数组
    const formattedSearches: { query: string; count: number }[] = [];
    for (let i = 0; i < popularSearches.length; i += 2) {
      formattedSearches.push({
        query: popularSearches[i],
        count: parseInt(popularSearches[i + 1] || '0')
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        popularSearches: formattedSearches
      }
    });
  } catch (error: any) {
    logger.error(`获取热门搜索失败: ${error.message}`, { stack: error.stack });
    return res.status(500).json({
      success: false,
      error: {
        message: '获取热门搜索失败，请稍后再试'
      }
    });
  }
}; 