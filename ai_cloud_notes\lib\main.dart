import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart'; // 导入 shared_preferences
import 'package:ai_cloud_notes/providers/auth_provider.dart';
import 'package:ai_cloud_notes/providers/note_provider.dart';
import 'package:ai_cloud_notes/providers/tag_provider.dart';
import 'package:ai_cloud_notes/providers/user_provider.dart';
import 'package:ai_cloud_notes/providers/search_provider.dart';
import 'package:ai_cloud_notes/providers/theme_service.dart';
import 'package:ai_cloud_notes/routes/app_routes.dart';
import 'package:ai_cloud_notes/services/api_service.dart';
import 'package:ai_cloud_notes/providers/ai_provider.dart';
import 'package:flutter_web_plugins/flutter_web_plugins.dart';
import 'package:ai_cloud_notes/themes/app_theme.dart'; // 导入 AppTheme
import 'package:ai_cloud_notes/utils/snackbar_helper.dart'; // 导入 SnackbarHelper

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  setUrlStrategy(PathUrlStrategy());

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  final apiService = ApiService();
  await apiService.init();

  final authProvider = AuthProvider(apiService);
  await authProvider.init(); // 等待认证状态初始化完成

  // 在 runApp 之前异步获取 SharedPreferences 数据
  final prefs = await SharedPreferences.getInstance();
  final bool hasSeenOnboarding = prefs.getBool('hasSeenOnboarding') ?? false;

  runApp(MyApp(
    apiService: apiService,
    authProvider: authProvider,
    hasSeenOnboarding: hasSeenOnboarding,
  ));
}

class MyApp extends StatelessWidget {
  final ApiService apiService;
  final AuthProvider authProvider;
  final bool hasSeenOnboarding; // 添加 hasSeenOnboarding

  const MyApp({
    Key? key,
    required this.apiService,
    required this.authProvider,
    required this.hasSeenOnboarding, // 添加到构造函数
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<ApiService>.value(value: apiService),
        ChangeNotifierProvider<AuthProvider>.value(value: authProvider),
        ChangeNotifierProvider(create: (_) => NoteProvider()),
        ChangeNotifierProvider(create: (_) => TagProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => SearchProvider()),
        ChangeNotifierProxyProvider<AuthProvider, AIProvider>(
          create: (context) {
            // ApiService 应该在 AuthProvider 之前就已经通过 Provider.value 提供了
            // 并且在 main() 中已经初始化 apiService.init()
            final apiService = Provider.of<ApiService>(context, listen: false);
            final aiProvider = AIProvider();
            // 初始加载本地设置，并根据当前的认证状态（可能尚未完全确定）进行初步处理
            // 真正的服务器同步将在 update 回调中根据认证状态触发
            final authProvider =
                Provider.of<AuthProvider>(context, listen: false);
            // 使用 Future.microtask 确保在 Provider 系统完成当前构建周期后再调用
            Future.microtask(() async {
              // 在create阶段，authProvider.isAuthenticated 可能还不是最终状态
              // 但我们仍然可以调用一次，AIProvider内部会处理
              await aiProvider.updateAuthenticationStatusAndService(
                  authProvider.isAuthenticated, apiService);
            });
            return aiProvider;
          },
          update: (context, authProvider, previousAiProvider) {
            if (previousAiProvider == null)
              return AIProvider(); // Should not happen if create is correct

            final apiService = Provider.of<ApiService>(context, listen: false);
            // 当 AuthProvider 的状态改变时，这个 update 会被调用
            // 我们在这里将最新的认证状态和 ApiService 传递给 AIProvider
            // AIProvider 内部的 updateAuthenticationStatusAndService 会处理后续逻辑（如从服务器同步）
            // 使用 Future.microtask 避免在 build 过程中直接修改状态并触发不期望的重建
            Future.microtask(() async {
              await previousAiProvider.updateAuthenticationStatusAndService(
                  authProvider.isAuthenticated, apiService);
            });
            return previousAiProvider;
          },
        ),
        // ThemeService 依赖 ApiService 和 AuthProvider
        // AuthProvider 已经通过 .value 提供，并且在 main 中初始化完成
        ChangeNotifierProxyProvider<AuthProvider, ThemeService>(
          create: (context) {
            final localApiService = Provider.of<ApiService>(context,
                listen: false); // 从 Provider 获取
            final localAuthProvider = Provider.of<AuthProvider>(context,
                listen: false); // 从 Provider 获取
            final themeService = ThemeService(localApiService);
            // 在创建时，根据已初始化的 AuthProvider 更新认证状态
            themeService
                .updateAuthenticationStatus(localAuthProvider.isAuthenticated);
            return themeService;
          },
          update: (context, localAuthProvider, previousThemeService) {
            previousThemeService!
                .updateAuthenticationStatus(localAuthProvider.isAuthenticated);
            return previousThemeService;
          },
        ),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, auth, _) {
          if (auth.status == AuthStatus.uninitialized) {
            return const MaterialApp(
              debugShowCheckedModeBanner: false,
              home: Scaffold(
                body: Center(child: CircularProgressIndicator()),
              ),
            );
          }

          // AuthProvider 初始化完成后，确定初始路由
          String initialRoute;
          if (auth.isAuthenticated) {
            initialRoute = AppRoutes.home;
          } else {
            if (hasSeenOnboarding) {
              initialRoute = AppRoutes.login;
            } else {
              initialRoute = AppRoutes.welcome;
            }
          }

          return Consumer<ThemeService>(
            builder: (context, themeService, _) {
              debugPrint(
                  'MyApp rebuilding with ThemeService: mode=${themeService.themeMode}, color=${themeService.fontColor}, textSize=${themeService.textSize}');
              return MaterialApp(
                title: '智云笔记',
                debugShowCheckedModeBanner: false,
                theme: AppTheme.getDynamicThemeData(
                    themeService, Brightness.light),
                darkTheme:
                    AppTheme.getDynamicThemeData(themeService, Brightness.dark),
                themeMode: themeService.themeMode,
                initialRoute: initialRoute,
                routes: AppRoutes.routes,
                onGenerateRoute: AppRoutes.generateRoute,
                useInheritedMediaQuery: true,
                navigatorObservers: [
                  RouteObserver<ModalRoute<void>>(),
                  AuthGuardObserver(),
                ],
              );
            },
          );
        },
      ),
    );
  }
}

// 自定义 Navigator Observer 用于认证守卫
class AuthGuardObserver extends NavigatorObserver {
  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    if (route.settings.name != null) {
      final routeName = route.settings.name!;

      // 定义不需要认证的路由
      final unauthenticatedRoutes = [
        AppRoutes.welcome,
        AppRoutes.onboarding,
        AppRoutes.login,
        AppRoutes.register,
        AppRoutes.forgotPassword,
      ];

      // 检查是否是共享笔记查看页面
      bool isSharedNoteView =
          routeName.startsWith('${AppRoutes.sharedNoteView}/');

      // 检查是否需要认证
      bool requiresAuthentication =
          !unauthenticatedRoutes.contains(routeName) && !isSharedNoteView;

      // 获取 BuildContext
      final BuildContext? context = navigator?.context;

      if (context != null && requiresAuthentication) {
        // 使用 Future.microtask 延迟获取 AuthProvider，确保 Provider 已准备好
        Future.microtask(() {
          try {
            final authProvider =
                Provider.of<AuthProvider>(context, listen: false);
            if (!authProvider.isAuthenticated) {
              // 如果需要认证但用户未认证，重定向到登录页并显示提示
              // 使用 Navigator.pushReplacementNamed 防止用户返回受保护页面
              Navigator.pushReplacementNamed(context, AppRoutes.login);
              SnackbarHelper.showWarning(
                  context: context, message: '请登录以访问此页面。');
            }
          } catch (e) {
            // 处理 Provider not found 错误，通常不会发生如果 MyApp 配置正确
            debugPrint('Error accessing AuthProvider in AuthGuardObserver: $e');
          }
        });
      }
    }
  }
}
