import 'dart:async';
import 'dart:convert';
import 'package:ai_cloud_notes/services/api_service.dart';
import '../test_config/mock_data.dart';

/// Mock API服务类
/// 模拟所有API调用，返回预定义的响应数据
class MockApiService extends ApiService {
  // 控制API响应行为的标志
  bool shouldSucceed = true;
  bool shouldDelay = false;
  int delayMilliseconds = 500;
  String? forceErrorType; // 'network', 'server', 'unauthorized'

  // 存储的数据
  String? _storedToken;
  Map<String, dynamic>? _currentUser;
  List<Map<String, dynamic>> _notes = [];
  List<Map<String, dynamic>> _tags = [];

  MockApiService() {
    // 初始化一些测试数据
    _notes = List.from(MockData.notesList);
    _tags = List.from(MockData.tagsList);
  }

  /// 模拟网络延迟
  Future<void> _simulateDelay() async {
    if (shouldDelay) {
      await Future.delayed(Duration(milliseconds: delayMilliseconds));
    }
  }

  /// 获取错误响应
  Map<String, dynamic> _getErrorResponse() {
    switch (forceErrorType) {
      case 'network':
        return MockData.networkErrorResponse;
      case 'server':
        return MockData.serverErrorResponse;
      case 'unauthorized':
        return MockData.unauthorizedErrorResponse;
      default:
        return MockData.loginFailureResponse;
    }
  }

  // ==================== 认证相关API ====================

  @override
  Future<Map<String, dynamic>> login({
    required String usernameOrEmail,
    required String password,
  }) async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    // 模拟登录验证
    if ((usernameOrEmail == 'testuser' || usernameOrEmail == '<EMAIL>') &&
        password == 'password123') {
      _storedToken = MockData.loginSuccessResponse['data']['token'];
      _currentUser = Map.from(MockData.testUser);
      return MockData.loginSuccessResponse;
    } else {
      return MockData.loginFailureResponse;
    }
  }

  @override
  Future<Map<String, dynamic>> register({
    required String username,
    required String email,
    required String password,
    required String verificationCode,
  }) async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    // 模拟注册验证
    if (username == 'existinguser') {
      return MockData.registerFailureResponse;
    }

    if (verificationCode == '123456') {
      return MockData.registerSuccessResponse;
    } else {
      return {
        'success': false,
        'error': {'message': '验证码错误', 'code': 'INVALID_CODE'},
      };
    }
  }

  @override
  Future<Map<String, dynamic>> sendVerificationCode(String email) async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    return MockData.sendCodeSuccessResponse;
  }

  @override
  Future<Map<String, dynamic>> forgotPassword(String email) async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    return MockData.forgotPasswordSuccessResponse;
  }

  @override
  Future<Map<String, dynamic>> resetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    if (token == 'valid_reset_token') {
      return MockData.resetPasswordSuccessResponse;
    } else {
      return {
        'success': false,
        'error': {'message': '重置令牌无效或已过期', 'code': 'INVALID_TOKEN'},
      };
    }
  }

  @override
  Future<Map<String, dynamic>> getCurrentUser() async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    if (_storedToken != null && _currentUser != null) {
      return {
        'success': true,
        'data': {'user': _currentUser},
      };
    } else {
      return MockData.unauthorizedErrorResponse;
    }
  }

  @override
  Future<void> logout() async {
    await _simulateDelay();
    _storedToken = null;
    _currentUser = null;
  }

  // ==================== 令牌管理 ====================

  @override
  Future<void> setToken(String token) async {
    _storedToken = token;
  }

  @override
  Future<String?> getToken() async {
    return _storedToken;
  }

  @override
  Future<void> clearToken() async {
    _storedToken = null;
  }

  @override
  bool get hasValidToken => _storedToken != null;

  // ==================== 笔记相关API ====================

  @override
  Future<Map<String, dynamic>> getNotes({
    int page = 1,
    int limit = 20,
    String? search,
    List<String>? tags,
    bool? isFavorite,
    bool? isArchived,
  }) async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    var filteredNotes = List<Map<String, dynamic>>.from(_notes);

    // 应用过滤条件
    if (search != null && search.isNotEmpty) {
      filteredNotes = filteredNotes.where((note) =>
          note['title'].toString().toLowerCase().contains(search.toLowerCase()) ||
          note['content'].toString().toLowerCase().contains(search.toLowerCase())).toList();
    }

    if (tags != null && tags.isNotEmpty) {
      filteredNotes = filteredNotes.where((note) {
        final noteTags = List<String>.from(note['tags'] ?? []);
        return tags.any((tag) => noteTags.contains(tag));
      }).toList();
    }

    if (isFavorite != null) {
      filteredNotes = filteredNotes.where((note) => note['isFavorite'] == isFavorite).toList();
    }

    if (isArchived != null) {
      filteredNotes = filteredNotes.where((note) => note['isArchived'] == isArchived).toList();
    }

    return {
      'success': true,
      'data': {
        'notes': filteredNotes,
        'total': filteredNotes.length,
        'page': page,
        'limit': limit,
      },
    };
  }

  @override
  Future<Map<String, dynamic>> createNote({
    required String title,
    required String content,
    required String contentType,
    List<String>? tags,
  }) async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    final newNote = {
      'id': 'note_${DateTime.now().millisecondsSinceEpoch}',
      'title': title,
      'content': content,
      'contentType': contentType,
      'tags': tags ?? [],
      'owner': _currentUser?['id'] ?? 'test_user_id_123',
      'isFavorite': false,
      'isArchived': false,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
      'lastSyncedAt': DateTime.now().toIso8601String(),
    };

    _notes.add(newNote);

    return {
      'success': true,
      'data': {'note': newNote},
    };
  }

  // ==================== 标签相关API ====================

  @override
  Future<Map<String, dynamic>> getTags() async {
    await _simulateDelay();

    if (!shouldSucceed) {
      return _getErrorResponse();
    }

    return {
      'success': true,
      'data': {'tags': _tags},
    };
  }

  // ==================== 工具方法 ====================

  /// 重置Mock状态
  void reset() {
    shouldSucceed = true;
    shouldDelay = false;
    delayMilliseconds = 500;
    forceErrorType = null;
    _storedToken = null;
    _currentUser = null;
    _notes = List.from(MockData.notesList);
    _tags = List.from(MockData.tagsList);
  }

  /// 设置为登录状态
  void setLoggedIn() {
    _storedToken = MockData.loginSuccessResponse['data']['token'];
    _currentUser = Map.from(MockData.testUser);
  }

  /// 设置为登出状态
  void setLoggedOut() {
    _storedToken = null;
    _currentUser = null;
  }

  /// 添加测试笔记
  void addTestNote(Map<String, dynamic> note) {
    _notes.add(note);
  }

  /// 添加测试标签
  void addTestTag(Map<String, dynamic> tag) {
    _tags.add(tag);
  }
}
