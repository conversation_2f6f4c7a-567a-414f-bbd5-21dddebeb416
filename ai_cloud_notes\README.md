# 智云笔记

智云笔记是一款跨平台云笔记应用，旨在为用户提供简洁高效的笔记管理体验。应用基于Flutter开发，支持Web、Android和iOS平台，用户可在多设备间无缝切换和同步笔记内容。与传统笔记应用不同，智云笔记特别强调用户体验和界面美观，融入AI技术辅助用户更高效地创建和管理笔记。

## 项目结构

项目采用清晰的目录结构，遵循Clean Architecture架构模式：

```
lib/
├── main.dart             # 应用入口
├── routes/               # 路由管理
├── models/               # 数据模型
├── screens/              # 页面UI
├── providers/            # 状态管理
├── repositories/         # 数据仓库
├── services/             # 服务类
├── themes/               # 主题配置
├── utils/                # 工具类
└── widgets/              # 可复用组件
```

## 已实现功能

### 用户认证与个人资料

- 用户注册：通过邮箱+密码+验证码方式注册
- 用户登录：使用用户名/邮箱+密码登录
- 密码找回：通过邮箱验证方式重置密码
- 认证状态：管理用户登录状态，持久化存储

### 认证模块技术实现

用户认证模块基于JWT（JSON Web Token）实现，主要组件包括：

1. **API服务 (`api_service.dart`)**
   - 与后端API通信的接口封装
   - 实现用户注册、登录、获取验证码等功能
   - 管理JWT令牌存储与使用

2. **认证状态管理 (`auth_provider.dart`)**
   - 使用Provider包实现状态管理
   - 维护用户登录状态
   - 提供认证相关方法

3. **认证界面**
   - 登录页面 (`login_page.dart`)
   - 注册页面 (`register_page.dart`)
   - 忘记密码页面 (`forgot_password_page.dart`)

## 后端API接口

认证模块连接到后端API，主要接口包括：

- `/api/auth/send-verification-code`: 发送验证码
- `/api/auth/register`: 用户注册
- `/api/auth/login`: 用户登录
- `/api/auth/me`: 获取当前用户信息
- `/api/auth/forgot-password`: 忘记密码
- `/api/auth/reset-password`: 重置密码

## 本地开发

### 依赖安装

```bash
flutter pub get
```

### 启动开发服务器

```bash
flutter run
```

### 打包应用

```bash
flutter build apk  # Android
flutter build ios  # iOS
flutter build web  # Web
```

## 贡献指南

欢迎贡献代码或提出建议。请先fork项目，然后创建分支进行开发，完成后提交PR。 