# 服务器配置 - Server Configuration
PORT=8080                    # 服务器监听端口，应用将在此端口提供服务
NODE_ENV=development         # 环境模式：development(开发)、production(生产)、test(测试)

# 数据库配置 - Database Configuration
MONGODB_URI=mongodb://127.0.0.1:27017/ai_cloud_notes   # MongoDB连接URI，指定数据库地址和名称

# Redis配置 - Redis Configuration
REDIS_URI=redis://localhost:6379    # Redis连接URI，用于存储验证码和临时数据
REDIS_PREFIX=ai_cloud_notes:        # Redis键前缀，避免与其他应用的数据冲突
REDIS_EXPIRE=300                    # 默认Redis键过期时间(秒)，如验证码有效期

# JWT配置 - JWT Configuration
JWT_SECRET=智云笔记_jwt_secret_key_20240626    # JWT签名密钥，用于生成和验证令牌
JWT_EXPIRES_IN=7d                             # JWT令牌有效期，7d表示7天后过期

# 邮件配置 - Email Configuration (QQ邮箱)
EMAIL_HOST=smtp.qq.com              # 邮件服务器主机地址
EMAIL_PORT=465                      # 邮件服务器端口，QQ邮箱使用465端口(SSL)
EMAIL_USERNAME=XXX    # 发件人邮箱地址
EMAIL_PASSWORD=XXX   # QQ邮箱授权码（注意定期更新）

# 日志配置 - Logging Configuration
LOG_LEVEL=info                      # 日志级别：debug、info、warn、error

# 前端URL配置 - Frontend URL
FRONTEND_URL=http://localhost:8099  # 前端应用URL，用于生成重置密码和分享链接

# 文件上传配置 - File Upload Configuration
MAX_FILE_SIZE=5242880               # 最大文件上传大小(5MB)，单位为字节
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf  # 允许上传的文件类型

# 安全配置 - Security Configuration
CORS_ORIGIN=http://localhost:8099   # CORS允许的源，通常与前端URL一致
RATE_LIMIT_WINDOW=15                # 速率限制窗口时间(分钟)
RATE_LIMIT_MAX=100                  # 在时间窗口内允许的最大请求数

# AI功能配置 - AI Feature Configuration
AI_SERVICE_URL=https://api.siliconflow.cn/v1    # SiliconFlow API地址
AI_SERVICE_KEY=your_siliconflow_api_key        # SiliconFlow API密钥