import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:ai_cloud_notes/utils/date_time_helper.dart'; // 导入DateTimeHelper工具类
import 'package:ai_cloud_notes/utils/text_helper.dart'; // 导入TextHelper工具类

/// 笔记模型类
///
/// 用于表示应用中的笔记实体，包含笔记的所有属性和相关操作方法
class Note {
  /// 笔记ID
  final String id;

  /// 笔记标题
  final String title;

  /// 笔记内容
  final String content;

  /// 内容类型（如plain-text, rich-text, markdown等）
  final String contentType;

  /// 关联的标签ID数组
  final List<String> tagIds;

  /// 关联的标签名称数组（用于显示）
  final List<String> tags;

  /// 是否收藏
  final bool isFavorite;

  /// 是否归档
  final bool isArchived;

  /// 创建时间
  final DateTime createdAt;

  /// 更新时间
  final DateTime updatedAt;

  /// 最后同步时间
  final DateTime? lastSyncedAt;

  /// 分享令牌
  String? shareToken;

  /// 分享过期时间
  final DateTime? shareExpireAt;

  /// 是否公开分享
  bool isPublicShared;

  /// 分享访问类型（readonly或editable）
  final String? shareAccessType;

  /// 版本号（用于同步冲突解决）
  final int version;

  /// 软删除标志
  final bool isDeleted;

  /// 历史版本存档时间
  final DateTime? archivedAt;

  /// 构造函数
  Note({
    String? id,
    required this.title,
    this.content = '',
    this.contentType = 'rich-text',
    this.tagIds = const [],
    this.tags = const [],
    this.isFavorite = false,
    this.isArchived = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.lastSyncedAt,
    this.shareToken,
    this.shareExpireAt,
    this.isPublicShared = false,
    this.shareAccessType,
    this.version = 1,
    this.isDeleted = false,
    this.archivedAt,
  }) :
    this.id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
    this.createdAt = createdAt ?? DateTime.now(),
    this.updatedAt = updatedAt ?? DateTime.now();

  /// 从JSON映射创建Note对象
  factory Note.fromJson(Map<String, dynamic> json) {
    // 处理嵌套的 note 结构
    final noteData = json['note'] ?? json;

    // 获取 ID，优先使用 _id
    final id = noteData['_id']?.toString() ?? noteData['id']?.toString() ??
               DateTime.now().millisecondsSinceEpoch.toString();

    // 处理标签ID和标签名称
    List<String> tagIds = [];
    List<String> tagNames = [];

    // 处理标签字段 - 后端返回的是标签对象数组而不是ID数组
    if (noteData['tags'] != null) {

      if (noteData['tags'] is List) {
        List<dynamic> tagsList = noteData['tags'] as List;

        // 遍历标签对象数组，提取ID和名称
        for (var tagObj in tagsList) {
          if (tagObj is Map<String, dynamic>) {
            // 从对象中提取标签ID
            if (tagObj.containsKey('_id')) {
              String tagId = tagObj['_id'].toString();
              tagIds.add(tagId);

              // 同时提取标签名称
              if (tagObj.containsKey('name')) {
                String tagName = tagObj['name'].toString();
                tagNames.add(tagName);
              }
            }
          } else if (tagObj is String) {
            // 如果数组元素直接是字符串ID，也支持处理
            tagIds.add(tagObj);
          }
        }

      } else if (noteData['tags'] is String) {
        // 处理标签可能是字符串的情况
        try {
          // 尝试解析JSON字符串
          dynamic tagsData = jsonDecode(noteData['tags'] as String);
          if (tagsData is List) {
            for (var tagObj in tagsData) {
              if (tagObj is Map<String, dynamic> && tagObj.containsKey('_id')) {
                tagIds.add(tagObj['_id'].toString());
                if (tagObj.containsKey('name')) {
                  tagNames.add(tagObj['name'].toString());
                }
              } else if (tagObj is String) {
                tagIds.add(tagObj);
              }
            }
          }
        } catch (e) {
          // 解析失败，使用空列表
        }
      }
    }

    // 如果没有从标签对象提取到名称，尝试从tagNames字段获取
    if (tagNames.isEmpty && noteData['tagNames'] != null) {
      if (noteData['tagNames'] is List) {
        tagNames = List<String>.from(noteData['tagNames']);
      } else if (noteData['tagNames'] is String) {
        try {
          dynamic tagNamesData = jsonDecode(noteData['tagNames'] as String);
          if (tagNamesData is List) {
            tagNames = List<String>.from(tagNamesData);
          }
        } catch (e) {
          // 解析失败
        }
      }
    }

    final note = Note(
      id: id,
      title: noteData['title'] ?? '',
      content: noteData['content'] ?? '',
      contentType: noteData['contentType'] ?? 'rich-text',
      tagIds: tagIds,
      tags: tagNames,
      isFavorite: noteData['isFavorite'] ?? false,
      isArchived: noteData['isArchived'] ?? false,
      createdAt: noteData['createdAt'] != null
        ? DateTime.parse(noteData['createdAt'])
        : DateTime.now(),
      updatedAt: noteData['updatedAt'] != null
        ? DateTime.parse(noteData['updatedAt'])
        : DateTime.now(),
      lastSyncedAt: noteData['lastSyncedAt'] != null
        ? DateTime.parse(noteData['lastSyncedAt'])
        : null,
      shareToken: noteData['shareToken'],
      shareExpireAt: noteData['shareExpireAt'] != null
        ? DateTime.parse(noteData['shareExpireAt'])
        : null,
      isPublicShared: noteData['isPublicShared'] ?? false,
      shareAccessType: noteData['shareAccessType'],
      version: noteData['version'] ?? 1,
      isDeleted: noteData['isDeleted'] ?? false,
      archivedAt: noteData['archivedAt'] != null
        ? DateTime.parse(noteData['archivedAt'])
        : null,
    );

    return note;
  }

  /// 将Note对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'contentType': contentType,
      'tags': tagIds,
      'isFavorite': isFavorite,
      'isArchived': isArchived,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastSyncedAt': lastSyncedAt?.toIso8601String(),
      'shareToken': shareToken,
      'shareExpireAt': shareExpireAt?.toIso8601String(),
      'isPublicShared': isPublicShared,
      'shareAccessType': shareAccessType,
      'version': version,
      'isDeleted': isDeleted,
      'archivedAt': archivedAt?.toIso8601String(),
    };
  }

  /// 创建具有更新属性的新Note实例
  Note copyWith({
    String? id,
    String? title,
    String? content,
    String? contentType,
    List<String>? tagIds,
    List<String>? tags,
    bool? isFavorite,
    bool? isArchived,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastSyncedAt,
    String? shareToken,
    DateTime? shareExpireAt,
    bool? isPublicShared,
    String? shareAccessType,
    int? version,
    bool? isDeleted,
    DateTime? archivedAt,
  }) {
    return Note(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      contentType: contentType ?? this.contentType,
      tagIds: tagIds ?? this.tagIds,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
      isArchived: isArchived ?? this.isArchived,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastSyncedAt: lastSyncedAt ?? this.lastSyncedAt,
      shareToken: shareToken ?? this.shareToken,
      shareExpireAt: shareExpireAt ?? this.shareExpireAt,
      isPublicShared: isPublicShared ?? this.isPublicShared,
      shareAccessType: shareAccessType ?? this.shareAccessType,
      version: version ?? this.version,
      isDeleted: isDeleted ?? this.isDeleted,
      archivedAt: archivedAt ?? this.archivedAt,
    );
  }

  /// 获取笔记摘要(前100个字符)
  String get summary {
    final plainContent = content.replaceAll(RegExp(r'<[^>]*>'), '');
    return plainContent.length > 100
      ? '${plainContent.substring(0, 100)}...'
      : plainContent;
  }

  /// 获取格式化的创建日期
  String get formattedCreatedAt {
    return DateTimeHelper.formatDate(createdAt);
  }

  /// 获取格式化的更新日期
  String get formattedUpdatedAt {
    return DateTimeHelper.formatDate(updatedAt);
  }

  /// 计算笔记的字数
  int get wordCount {
    return content.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
  }

  /// 重写相等运算符
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Note &&
      other.id == id &&
      other.title == title &&
      other.content == content &&
      other.contentType == contentType &&
      listEquals(other.tagIds, tagIds) &&
      listEquals(other.tags, tags) &&
      other.isFavorite == isFavorite &&
      other.isArchived == isArchived &&
      other.version == version &&
      other.isDeleted == isDeleted;
  }

  /// 重写哈希码
  @override
  int get hashCode =>
    id.hashCode ^
    title.hashCode ^
    content.hashCode ^
    contentType.hashCode ^
    tagIds.hashCode ^
    tags.hashCode ^
    isFavorite.hashCode ^
    isArchived.hashCode ^
    version.hashCode ^
    isDeleted.hashCode;

  /// 获取富文本内容的纯文本版本
  String get plainTextContent {
    return TextHelper.extractTextFromDelta(content);
  }

  /// 创建一个空的Note对象
  /// 用于在找不到笔记时返回
  factory Note.empty() {
    return Note(
      title: '',
      content: '',
      contentType: 'rich-text',
      tagIds: const [],
      tags: const [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }
}

/// 辅助函数：比较两个列表是否相等
bool listEquals<T>(List<T>? a, List<T>? b) {
  if (a == null) return b == null;
  if (b == null || a.length != b.length) return false;

  for (int i = 0; i < a.length; i++) {
    if (a[i] != b[i]) return false;
  }

  return true;
}